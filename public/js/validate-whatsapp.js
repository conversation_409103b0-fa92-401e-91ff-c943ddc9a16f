class ValidateWhatsapp {
    constructor(options) {
        this.inputSelector = options.inputSelector;
        this.modalId = options.modalId;
        this.apiUrl = options.apiUrl;
        this.whatsappModal = $(`#${this.modalId}`);
        this.btnSendCode = $(`#${this.modalId}SendCode`);
        this.codeEntrySection = $(`#${this.modalId}CodeEntrySection`);
        this.whatsappVerificationCodeInput = $(`#${this.modalId}VerificationCode`);
        this.btnConfirmCode = $(`#${this.modalId}ConfirmCode`);
        this.btnResendCode = $(`#${this.modalId}ResendCode`);
        this.resendCountdownSpan = $(`#${this.modalId}ResendCountdown`);
        this.whatsappValidationFeedback = $(`#${this.modalId}ValidationFeedback`);
        this.whatsappInitialActions = $(`#${this.modalId}InitialActions`);
        this.whatsappCodeSentMessage = $(`#${this.modalId}CodeSentMessage`);
        this.whatsappVerificationCodeError = $(`#${this.modalId}VerificationCodeError`);
        this.phoneInput = $(this.inputSelector);
        this.errorElement = $(`#error-${this.phoneInput.attr('id')}`);
        this.validatedPhoneNumber = '';
        this.resendTimerInterval = null;
        this.resendBaseTime = parseInt(this.whatsappModal.data('resend-time')) || 180;
        this.countdown = this.resendBaseTime;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkInitialValidationState();
    }

    setupEventListeners() {
        this.phoneInput.on('input', this.debounce(this.handlePhoneInput.bind(this), 500));
        $(document).on('click', `[id^="btn-validate-whatsapp-"]`, this.handleValidateButtonClick.bind(this));
        this.btnSendCode.on('click', this.handleSendCodeClick.bind(this));
        this.btnConfirmCode.on('click', this.handleConfirmCodeClick.bind(this));
        this.btnResendCode.on('click', this.handleResendCodeClick.bind(this));
        this.whatsappModal.on('show.bs.modal', this.handleModalShow.bind(this));
        this.whatsappModal.on('hidden.bs.modal', this.handleModalHidden.bind(this));
    }

    checkInitialValidationState() {
        const initialPhoneNumber = this.phoneInput.val().replace(/\D/g, '');
        const phoneValidatedInput = $('#phone_validated'); // Assuming this hidden input exists
        if (phoneValidatedInput.length > 0 && phoneValidatedInput.val() === '1') {
            this.validatedPhoneNumber = initialPhoneNumber;
            this.phoneInput.addClass('is-valid');
            this.addValidatedMessage();
        }
        this.updateValidateButtonState();
    }

    handlePhoneInput() {
        const currentPhoneNumberRaw = this.phoneInput.val().replace(/\D/g, '');
        const phoneValidatedInput = $('#phone_validated');

        if (currentPhoneNumberRaw !== this.validatedPhoneNumber) {
            if (phoneValidatedInput.length > 0) {
                phoneValidatedInput.val('0');
            }
            this.phoneInput.removeClass('is-valid');
            this.removeValidatedMessage();
            // Assuming a submit button exists with id 'submit-button'
            const submitButton = $('#submit-button');
            if (submitButton.length > 0) {
                 submitButton.prop('disabled', true);
            }
        } else {
             if (phoneValidatedInput.length > 0) {
                phoneValidatedInput.val('1');
            }
            this.phoneInput.addClass('is-valid');
            this.addValidatedMessage();
             // Assuming a checkFormCompletionAndEnableSubmit function exists
            if (typeof checkFormCompletionAndEnableSubmit === 'function') {
                checkFormCompletionAndEnableSubmit();
            }
        }

        this.updateValidateButtonState();

        if (this.isPhoneNumberValid(currentPhoneNumberRaw)) {
            this.phoneInput.removeClass('is-invalid');
            this.errorElement.text('');
        } else {
            if (currentPhoneNumberRaw.length === 0) {
                 this.phoneInput.removeClass('is-invalid is-valid');
                 this.errorElement.text('');
            }
        }
    }

    handleValidateButtonClick() {
        const phoneNumber = this.phoneInput.val().replace(/\D/g, '');
        if (phoneNumber) {
            this.whatsappModal.data('phoneNumber', phoneNumber);
            this.whatsappModal.data('phoneInputId', this.phoneInput.attr('id'));
            this.whatsappModal.modal('show');
        } else {
            console.error("Número de telefone não encontrado para validação.");
        }
    }

    handleSendCodeClick() {
        const phoneNumber = this.whatsappModal.data('phoneNumber');
        if (!phoneNumber) {
            this.whatsappValidationFeedback.text('Erro: Número de telefone não encontrado.').removeClass('text-success').addClass('text-danger');
            return;
        }

        this.btnSendCode.prop('disabled', true).text('Enviando...');
        this.whatsappValidationFeedback.empty();

        $.ajax({
            url: `${this.apiUrl}/start-phone-validation`,
            type: 'POST',
            data: JSON.stringify({ phone_number: phoneNumber }),
            contentType: 'application/json',
            beforeSend: function (xhr) {
                // Assuming API_TOKEN is available globally or passed in options
                const apiToken = $('meta[name="api-token"]').attr('content'); // Example: get from meta tag
                if (apiToken) {
                    xhr.setRequestHeader('Authorization', `Bearer ${apiToken}`);
                } else {
                     console.warn("API Token not found. Request might fail.");
                }
            },
            success: (response) => {
                this.whatsappInitialActions.hide();
                this.codeEntrySection.show();
                this.whatsappCodeSentMessage.text('Código de 6 dígitos enviado para o seu WhatsApp. Por favor, insira-o abaixo:').removeClass('text-danger').addClass('text-success');
                this.whatsappVerificationCodeInput.focus();
                this.startResendTimer();
            },
            error: (jqXHR, textStatus, errorThrown) => {
                this.btnSendCode.prop('disabled', false).text('Enviar Código de Verificação');
                let errorMsg = "Falha ao solicitar código de validação.";
                if(jqXHR.responseJSON && jqXHR.responseJSON.message){
                    errorMsg = jqXHR.responseJSON.message;
                }
                this.whatsappValidationFeedback.text(errorMsg).removeClass('text-success').addClass('text-danger');
            }
        });
    }

    handleConfirmCodeClick() {
        const validationCode = this.whatsappVerificationCodeInput.val();
        const phoneNumber = this.whatsappModal.data('phoneNumber');
        const phoneInputId = this.whatsappModal.data('phoneInputId');
        // Assuming currentClientCpf or currentUserCpf is available globally or passed in options
        const currentCpf = $('meta[name="current-cpf"]').attr('content'); // Example: get from meta tag

        if (!validationCode || validationCode.length !== 6) {
            this.whatsappVerificationCodeError.text('Por favor, insira um código de 6 dígitos.');
            return;
        }

        this.whatsappVerificationCodeError.empty();
        this.whatsappValidationFeedback.empty();
        this.btnConfirmCode.prop('disabled', true).text('Confirmando...');

        $.ajax({
            url: `${this.apiUrl}/verify-phone-validation-code`,
            type: 'POST',
            data: JSON.stringify({
                phone_number: phoneNumber,
                validation_code: validationCode,
                current_cpf: currentCpf // Pass CPF for backend check
            }),
            contentType: 'application/json',
            beforeSend: function (xhr) {
                 const apiToken = $('meta[name="api-token"]').attr('content');
                 if (apiToken) {
                    xhr.setRequestHeader('Authorization', `Bearer ${apiToken}`);
                } else {
                     console.warn("API Token not found. Request might fail.");
                }
            },
            success: (response) => {
                clearInterval(this.resendTimerInterval);
                this.whatsappModal.modal('hide');

                const phoneInput = $(`#${phoneInputId}`);
                const errorElement = $(`#error-${phoneInputId}`);
                phoneInput.removeClass('is-invalid').addClass('is-valid');
                errorElement.text('');

                this.addValidatedMessage();

                $(`#btn-validate-whatsapp-${phoneInputId}`).remove();

                const phoneValidatedInput = $('#phone_validated');
                 if (phoneValidatedInput.length > 0) {
                    phoneValidatedInput.val('1');
                }
                this.validatedPhoneNumber = phoneNumber;

                // Assuming a checkFormCompletionAndEnableSubmit function exists
                if (typeof checkFormCompletionAndEnableSubmit === 'function') {
                    checkFormCompletionAndEnableSubmit();
                }
            },
            error: (jqXHR, textStatus, errorThrown) => {
                this.btnConfirmCode.prop('disabled', false).text('Confirmar Código');
                let errorMsg = 'Falha ao verificar código.';
                if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                    errorMsg = jqXHR.responseJSON.message;
                }
                this.whatsappValidationFeedback.text(errorMsg).removeClass('text-success').addClass('text-danger');
            }
        });
    }

    handleResendCodeClick() {
        const phoneNumber = this.whatsappModal.data('phoneNumber');
         if (!phoneNumber) {
            this.whatsappValidationFeedback.text('Erro: Número de telefone não encontrado para reenvio.').removeClass('text-success').addClass('text-danger');
            return;
        }

        this.btnResendCode.prop('disabled', true).text('Enviando...');
        this.whatsappValidationFeedback.empty();
        this.whatsappCodeSentMessage.text('Enviando novo código...').removeClass('text-success text-danger');

        $.ajax({
            url: `${this.apiUrl}/start-phone-validation`,
            type: 'POST',
            data: JSON.stringify({ phone_number: phoneNumber }),
            contentType: 'application/json',
            beforeSend: function (xhr) {
                 const apiToken = $('meta[name="api-token"]').attr('content');
                 if (apiToken) {
                    xhr.setRequestHeader('Authorization', `Bearer ${apiToken}`);
                } else {
                     console.warn("API Token not found. Request might fail.");
                }
            },
            success: (response) => {
                this.whatsappCodeSentMessage.text('Novo código de 6 dígitos enviado para o seu WhatsApp.').removeClass('text-danger').addClass('text-success');
                this.whatsappVerificationCodeInput.val('');
                this.whatsappVerificationCodeError.empty();
                this.startResendTimer();
            },
            error: (jqXHR, textStatus, errorThrown) => {
                this.btnResendCode.prop('disabled', false).text('Reenviar código');
                let errorMsg = "Falha ao solicitar novo código de validação.";
                if(jqXHR.responseJSON && jqXHR.responseJSON.message){
                    errorMsg = jqXHR.responseJSON.message;
                }
                this.whatsappValidationFeedback.text(errorMsg).removeClass('text-success').addClass('text-danger');
                this.whatsappCodeSentMessage.text('Falha no reenvio.').removeClass('text-success').addClass('text-danger');
            }
        });
    }

    handleModalShow() {
        this.whatsappInitialActions.show();
        this.codeEntrySection.hide();
        this.whatsappValidationFeedback.empty();
        this.whatsappVerificationCodeInput.val('');
        this.whatsappVerificationCodeError.empty();
        this.btnConfirmCode.prop('disabled', false).text('Confirmar Código');
        this.btnResendCode.prop('disabled', true).text('Reenviar código');
        $(`#${this.modalId}ResendTimer`).hide();
        clearInterval(this.resendTimerInterval);
    }

    handleModalHidden() {
        this.whatsappVerificationCodeInput.val('');
        this.whatsappVerificationCodeError.empty();
        this.whatsappValidationFeedback.empty();
        clearInterval(this.resendTimerInterval);
    }

    startResendTimer() {
        this.countdown = this.resendBaseTime;
        this.btnResendCode.prop('disabled', true).text('Reenviar código');
        this.resendCountdownSpan.text(this.countdown);
        $(`#${this.modalId}ResendTimer`).show();

        this.resendTimerInterval = setInterval(() => {
            this.countdown--;
            this.resendCountdownSpan.text(this.countdown);
            if (this.countdown <= 0) {
                clearInterval(this.resendTimerInterval);
                this.btnResendCode.prop('disabled', false).text('Reenviar código');
                $(`#${this.modalId}ResendTimer`).hide();
            }
        }, 1000);
    }

    isPhoneNumberValid(phoneNumber) {
        const regex = /^\d{10,11}$/;
        return regex.test(phoneNumber);
    }

    updateValidateButtonState() {
        const rawValue = this.phoneInput.val();
        const phoneNumber = rawValue.replace(/\D/g, '');
        const isFormatValid = this.isPhoneNumberValid(phoneNumber);
        let validateButton = $(`#btn-validate-whatsapp-${this.phoneInput.attr('id')}`);
        const isSameAsValidated = this.validatedPhoneNumber !== '' && phoneNumber === this.validatedPhoneNumber;
        const phoneValidatedInput = $('#phone_validated');
        const isAlreadyValidated = phoneValidatedInput.length > 0 && phoneValidatedInput.val() === '1';

        if ((isFormatValid && !isAlreadyValidated) || (phoneNumber !== this.validatedPhoneNumber && isAlreadyValidated)) {
             this.removeValidatedMessage();
        }

        if (isFormatValid) {
            if (isSameAsValidated && isAlreadyValidated) {
                 if (validateButton.length > 0) {
                     validateButton.remove();
                 }
                 this.addValidatedMessage();
            } else if (!isAlreadyValidated) {
                if (validateButton.length === 0) {
                    validateButton = $(`<button type="button" id="btn-validate-whatsapp-${this.phoneInput.attr('id')}" class="btn btn-sm btn-warning mt-2">Validar por WhatsApp</button>`);
                    const errorElement = $(`#error-${this.phoneInput.attr('id')}`);
                    if (errorElement.length > 0) {
                        errorElement.before(validateButton);
                    } else {
                        this.phoneInput.closest('.form-group').append(validateButton);
                    }
                }
                validateButton.prop('disabled', false);
            } else {
                 if (validateButton.length > 0) {
                     validateButton.remove();
                 }
            }
        } else {
            if (validateButton.length > 0) {
                validateButton.remove();
            }
        }
    }

    addValidatedMessage() {
        if (this.phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').length === 0) {
            const successMsg = $('<small class="text-success ml-2">Telefone validado!</small>');
            this.phoneInput.after(successMsg);
        }
    }

    removeValidatedMessage() {
        this.phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').remove();
    }

    debounce(func, delay) {
        let timer;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(context, args);
            }, delay);
        };
    }
}
