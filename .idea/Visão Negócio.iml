<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/Admin/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/Admin/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Admin/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Admin/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/Admin/tests/Feature" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/Admin/tests/Unit" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/Api/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/Api/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Api/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Api/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/Api/tests/Feature" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/Api/tests/Unit" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/Api/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/breeze" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/spatie/backtrace" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/spatie/error-solutions" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/spatie/flare-client-php" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/spatie/ignition" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/spatie/laravel-ignition" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/Admin/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/geekcom/validator-docs" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/lcobucci/clock" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/mtrajano/laravel-swagger" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpstan/phpdoc-parser" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/spatie/backtrace" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/spatie/error-solutions" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/spatie/flare-client-php" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/spatie/ignition" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/spatie/laravel-ignition" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/stella-maris/clock" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/thiagocfn/inscricaoestadual" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/tymon/jwt-auth" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/league/flysystem-aws-s3-v3" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/behat/behat" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/behat/gherkin" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/behat/transliterator" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/config" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/dependency-injection" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/Api/vendor/symfony/var-exporter" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>