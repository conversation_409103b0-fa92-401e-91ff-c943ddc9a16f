<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="LaravelPint">
    <laravel_pint_settings>
      <LaravelPintConfiguration tool_path="$PROJECT_DIR$/Api/vendor/bin/pint" />
    </laravel_pint_settings>
  </component>
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/Api/vendor/psr/log" />
      <path value="$PROJECT_DIR$/Api/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/Api/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/Api/vendor/mtrajano/laravel-swagger" />
      <path value="$PROJECT_DIR$/Api/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/Api/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/Api/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/Api/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/Api/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/Api/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/Api/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/Api/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/Api/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/Api/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/Api/vendor/stella-maris/clock" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/Api/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/Api/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/Api/vendor/league/config" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/Api/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/Api/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/Api/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/Api/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/Api/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/Api/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/Api/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/Api/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/Api/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/Api/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/Api/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/Api/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/Api/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/Api/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/Api/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/Api/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/Api/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/Api/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/Api/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/Api/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/Api/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/Api/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/Api/vendor/brick/math" />
      <path value="$PROJECT_DIR$/Api/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/Api/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/Api/vendor/spatie/flare-client-php" />
      <path value="$PROJECT_DIR$/Api/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/Api/vendor/spatie/backtrace" />
      <path value="$PROJECT_DIR$/Api/vendor/spatie/error-solutions" />
      <path value="$PROJECT_DIR$/Api/vendor/spatie/ignition" />
      <path value="$PROJECT_DIR$/Api/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/Api/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/Api/vendor/spatie/laravel-ignition" />
      <path value="$PROJECT_DIR$/Api/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/Api/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/Api/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/Api/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/Api/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/Api/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/Api/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/Api/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/Api/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/Api/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/Api/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/Api/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/Api/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/Api/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/Api/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/Api/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/Api/vendor/tymon/jwt-auth" />
      <path value="$PROJECT_DIR$/Api/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/Api/vendor/composer" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/Api/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/Api/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/Api/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/Api/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/Api/vendor/thiagocfn/inscricaoestadual" />
      <path value="$PROJECT_DIR$/Api/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/Api/vendor/psr/container" />
      <path value="$PROJECT_DIR$/Api/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/Api/vendor/geekcom/validator-docs" />
      <path value="$PROJECT_DIR$/Api/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/Api/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/Api/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/Admin/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/Admin/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/Admin/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/Admin/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/Admin/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/Admin/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/Admin/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/Admin/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/Admin/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/Admin/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/Admin/vendor/league/config" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/Admin/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/Admin/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/Admin/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/Admin/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/Admin/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/Admin/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/Admin/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/Admin/vendor/composer" />
      <path value="$PROJECT_DIR$/Admin/vendor/spatie/backtrace" />
      <path value="$PROJECT_DIR$/Admin/vendor/spatie/ignition" />
      <path value="$PROJECT_DIR$/Admin/vendor/spatie/flare-client-php" />
      <path value="$PROJECT_DIR$/Admin/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/Admin/vendor/spatie/laravel-ignition" />
      <path value="$PROJECT_DIR$/Admin/vendor/spatie/error-solutions" />
      <path value="$PROJECT_DIR$/Admin/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/Admin/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/Admin/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/container" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/Admin/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/Admin/vendor/psr/log" />
      <path value="$PROJECT_DIR$/Admin/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/Admin/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/Admin/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/Admin/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/Admin/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/Admin/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/Admin/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/Admin/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/Admin/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/Admin/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/Admin/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/Admin/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/Admin/vendor/brick/math" />
      <path value="$PROJECT_DIR$/Admin/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/Admin/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/Admin/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/Admin/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/Admin/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/Admin/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/Admin/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/Admin/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/Admin/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/breeze" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/Admin/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/Admin/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/Admin/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/Admin/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/Admin/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/Admin/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/Api/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/Api/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/Api/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/Api/vendor/league/flysystem-aws-s3-v3" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/dependency-injection" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/filesystem" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/config" />
      <path value="$PROJECT_DIR$/Api/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/Api/vendor/behat/transliterator" />
      <path value="$PROJECT_DIR$/Api/vendor/behat/behat" />
      <path value="$PROJECT_DIR$/Api/vendor/behat/gherkin" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.1" />
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/Admin/phpunit.xml" custom_loader_path="$PROJECT_DIR$/Admin/vendor/autoload.php" use_configuration_file="true" />
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/Api/phpunit.xml" custom_loader_path="$PROJECT_DIR$/Api/vendor/autoload.php" use_configuration_file="true" />
    </phpunit_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>