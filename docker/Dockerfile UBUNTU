# Usar a imagem base do Ubuntu
FROM ubuntu:latest

# Definir o diretório de trabalho
WORKDIR /app

# Atualizar pacotes e instalar dependências básicas
RUN apt update && apt install -y \
    software-properties-common \
    curl \
    git

# Configurar o fuso horário
ENV TZ=America/Recife
RUN ln -fs /usr/share/zoneinfo/$TZ /etc/localtime && \
    apt-get install -y tzdata

# Adicionar repositório do PHP
RUN add-apt-repository ppa:ondrej/php -y && apt update

# Instalar PHP e extensões necessárias
RUN apt install -y php8.1 php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip

# Instalar Composer
RUN curl -sS https://getcomposer.org/installer -o composer-setup.php && \
    php composer-setup.php --install-dir=/usr/local/bin --filename=composer && \
    rm composer-setup.php

# Copiar o código do frontend para /app
COPY . /app

# Instalar dependências do Laravel
RUN composer install

# Expor a porta que o Laravel usará
EXPOSE 8000

CMD ["bash"]
