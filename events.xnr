####################################
#         System information       #
####################################
# Date:                   2025:04:23 
# Time:                   09:55:21 
# Xnee program:           cnee 
# Xnee version:           3.19 
# Xnee home:              http://www.gnu.org/software/xnee/ 
# Xnee info mailing list: <EMAIL> 
# Xnee bug mailing list:  <EMAIL> 
# X version:              11 
# X revision:             0 
# X vendor:               The X.Org Foundation 
# X vendor release:       12302006 
# Record version major:   1
# Record version minor:   13
# OS name:                Linux 
# OS Release:             6.8.0-48-generic 
# OS Version:             #48-Ubuntu SMP PREEMPT_DYNAMIC Fri Sep 27 14:04:52 UTC 2024 
# Machine:                x86_64 
# Nodename:               alex-pc 
# Display name:           :0
# Dimension:              1920x1080



##############################################
#      Xnee application arguments            #
##############################################
#  cnee --record -o events.xnr --mouse --events-to-record 100 --time 2 


##############################################
#      Displays                              #
##############################################
# display :0
# distribute


##############################################
#      Files                                 #
##############################################
# out-file events.xnr
# err-file stderr


##############################################
#      Key Grabs                             #
##############################################
# stop-key         0
# pause-key        0
# resume-key       0
# insert-key       0
# exec-key         0
# exec-program-key         xnee-exec-no-program


##############################################
#      Recording limits etc                  #
##############################################

events-to-record        100
data-to-record          -1
seconds-to-record       -1
# first-last

# Record  all (including current) clients or only future ones
all-clients
# future-clients

# Store the starting mouse position 
# store-mouse-position


##############################################
#      Resolution                            #
##############################################

# Resolution
#recorded-resolution  1920x1080
#replay-resolution  1x1
#resolution-adjustment  0


##############################################
#      Speed                                 #
##############################################

# Speed
#speed-percent  100


##############################################
#      Replaying limits etc                  #
##############################################

max-threshold 20 
min-threshold 20 
tot-threshold 40 


##############################################
#      Feedback                              #
##############################################
#feedback-none
#feedback-stderr
feedback-xosd
 


##############################################
#      Various                               #
##############################################

# Plugin file 

# Modes (currently not used)
# synchronised-replay

# Replay offset
#replay-offset 0x0

# Human printout of X11 data (instead of Xnee format)
# human-printout  

# Delay before starting record/replay
# time 2

# No recording of ReparentNotify
# no-reparent-recording 

# Various
#########################################
#          Record settings              #
#########################################
#   data_flags          7
#   rState              0x56bdb0504340
#   xids[0]             3
#   xids[1]             0
# Number of Ranges      3
# RecordRange[0]
request-range            0-0
reply-range                   0-0 
extension-request-major-range  0-0 
extension-request-minor-range  0-0 
extension-reply-major-range   0-0
extension-reply-minor-range   0-0 
delivered-event-range         21-21 
device-event-range            4-6 
error-range                   0-0 
# RecordRange[1]
request-range            0-0
reply-range                   0-0 
extension-request-major-range  0-0 
extension-request-minor-range  0-0 
extension-reply-major-range   0-0
extension-reply-minor-range   0-0 
delivered-event-range         0-0 
device-event-range            66-66 
error-range                   0-0 
# RecordRange[2]
request-range            0-0
reply-range                   0-0 
extension-request-major-range  0-0 
extension-request-minor-range  0-0 
extension-reply-major-range   0-0
extension-reply-minor-range   0-0 
delivered-event-range         0-0 
device-event-range            69-71 
error-range                   0-0 
7,6,478,1079,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,542,1012,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,542,1012,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,540,1010,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,538,1008,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,534,1006,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,532,1004,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,528,1002,0,0,0,1144475,7,'xwayland-relative-pointer:15'
7,6,526,1000,0,0,0,1144476,7,'xwayland-relative-pointer:15'
7,6,524,998,0,0,0,1144478,7,'xwayland-relative-pointer:15'
7,6,522,996,0,0,0,1144478,7,'xwayland-relative-pointer:15'
7,6,520,994,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,518,992,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,516,990,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,514,988,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,512,986,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,510,982,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,510,980,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,508,978,0,0,0,1144486,7,'xwayland-relative-pointer:15'
7,6,508,974,0,0,0,1144487,7,'xwayland-relative-pointer:15'
