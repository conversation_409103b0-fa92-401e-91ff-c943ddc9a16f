services:
  db:
    image: mysql:latest
    container_name: visao-negocio-db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD:
      MYSQL_DATABASE: visao_negocio
      MYSQL_USER: alex
      <PERSON>_PASSWORD: 123456
      #set empty password
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
    ports:
      - 3307:3306
    volumes:
      - db_data:/var/lib/mysql

volumes:
  db_data:
    
     
  
