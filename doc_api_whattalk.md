# API de WhatsApp - Documentação dos Endpoints

## Introdução

Esta documentação descreve os endpoints disponíveis para interagir com a API do WhatsApp. A API permite realizar operações como inicializar uma instância do WhatsApp, enviar mensagens, recuperar QR Codes, e gerenciar instâncias.

## Autenticação

Para utilizar os endpoints, você deve autenticar suas requisições utilizando o token de acesso. Sempre inclua o token no cabeçalho da requisição, como mostrado nos exemplos abaixo.

1.  **Init Instance**
    * Método: `GET`
    * URL: `{{baseUrl}}/instance/init?key={{instance_key}}`
    * Descrição: Inicializa uma nova instância do WhatsApp.
    * Parâmetros de Query:
        * `webhookUrl` (Opcional): URL para o webhook.
        * `webhook` (Opcional): Permite configurar o Webhook.
        * `key` (Opcional): Chave personalizada da instância.
    * Exemplo de Requisição:
    ```curl
    curl -X GET "{{baseUrl}}/instance/init?key={{instance_key}}&webhook=true" -H "Authorization: Bearer {{token}}"
    ```

2.  **Scan QR**
    * Método: `GET`
    * URL: `{{baseUrl}}/instance/qr?key={{instance_key}}`
    * Descrição: Obtém o QR Code de uma instância.
    * Parâmetros de Query:
        * `key` (Obrigatório): Chave da instância.
    * Exemplo de Requisição:
    ```curl
    curl -X GET "{{baseUrl}}/instance/qr?key={{instance_key}}" -H "Authorization: Bearer {{token}}"
    ```

3.  **Get QR in Base64**
    * Método: `GET`
    * URL: `{{baseUrl}}/instance/qrbase64?key={{instance_key}}`
    * Descrição: Obtém o QR Code da instância em formato Base64.
    * Parâmetros de Query:
        * `key` (Obrigatório): Chave da instância.
    * Exemplo de Requisição:
    ```curl
    curl -X GET "{{baseUrl}}/instance/qrbase64?key={{instance_key}}" -H "Authorization: Bearer {{token}}"
    ```

4.  **Instance Info**
    * Método: `GET`
    * URL: `{{baseUrl}}/instance/info?key={{instance_key}}`
    * Descrição: Obtém informações sobre uma instância.
    * Parâmetros de Query:
        * `key` (Obrigatório): Chave da instância.
    * Exemplo de Requisição:
    ```curl
    curl -X GET "{{baseUrl}}/instance/info?key={{instance_key}}" -H "Authorization: Bearer {{token}}"
    ```

5.  **Restore All Instances**
    * Método: `GET`
    * URL: `{{baseUrl}}/instance/restore`
    * Descrição: Restaura todas as instâncias.
    * Exemplo de Requisição:
    ```curl
    curl -X GET "{{baseUrl}}/instance/restore" -H "Authorization: Bearer {{token}}"
    ```

6.  **Delete Instance**
    * Método: `DELETE`
    * URL: `{{baseUrl}}/instance/delete?key={{instance_key}}`
    * Descrição: Deleta uma instância do WhatsApp.
    * Parâmetros de Query:
        * `key` (Obrigatório): Chave da instância.
    * Exemplo de Requisição:
    ```curl
    curl -X DELETE "{{baseUrl}}/instance/delete?key={{instance_key}}" -H "Authorization: Bearer {{token}}"
    ```

7.  **Logout Instance**
    * Método: `DELETE`
    * URL: `{{baseUrl}}/instance/logout?key={{instance_key}}`
    * Descrição: Faz logout da instância no WhatsApp.
    * Parâmetros de Query:
        * `key` (Obrigatório): Chave da instância.
    * Exemplo de Requisição:
    ```curl
    curl -X DELETE "{{baseUrl}}/instance/logout?key={{instance_key}}" -H "Authorization: Bearer {{token}}"
    ```

8.  **List All Sessions**
    * Método: `GET`
    * URL: `{{baseUrl}}/instance/list`
    * Descrição: Lista todas as instâncias.
    * Parâmetros de Query:
        * `active` (Opcional): Filtra para listar apenas as instâncias ativas.
    * Exemplo de Requisição:
    ```curl
    curl -X GET "{{baseUrl}}/instance/list?active=true" -H "Authorization: Bearer {{token}}"
    ```

9.  **Send Text Message**
    * Método: `POST`
    * URL: `{{baseUrl}}/message/text?key={{instance_key}}`
    * Descrição: Envia uma mensagem de texto para um número ou grupo do WhatsApp.
    * Parâmetros de Corpo (Form URL Encoded):
        * `id` (Obrigatório): ID do destinatário ou ID do grupo.
        * `message` (Obrigatório): Mensagem a ser enviada.
    * Exemplo de Requisição:
    ```curl
    curl -X POST "{{baseUrl}}/message/text?key={{instance_key}}" \
    -H "Authorization: Bearer {{token}}" \
    -d "id=558199999999" \
    -d "message=olá"
    ```

10. **Send Image Message**
    * Método: `POST`
    * URL: `{{baseUrl}}/message/image?key={{instance_key}}`
    * Descrição: Envia uma mensagem com imagem para um número do WhatsApp.
    * Parâmetros de Corpo (Form Data):
        * `file` (Obrigatório): Arquivo da imagem.
        * `id` (Obrigatório): ID do destinatário ou ID do grupo.
        * `caption` (Opcional): Legenda da imagem.
    * Exemplo de Requisição:
    ```curl
    curl -X POST "{{baseUrl}}/message/image?key={{instance_key}}" \
    -H "Authorization: Bearer {{token}}" \
    -F "file=@path/to/image.jpg" \
    -F "id=558199999999" \
    -F "caption=Mensagem com imagem"
    ```

11. **Send Video Message**
    * Método: `POST`
    * URL: `{{baseUrl}}/message/video?key={{instance_key}}`
    * Descrição: Envia uma mensagem com vídeo para um número do WhatsApp.
    * Parâmetros de Corpo (Form Data):
        * `file` (Obrigatório): Arquivo de vídeo.
        * `id` (Obrigatório): ID do destinatário ou ID do grupo.
        * `caption` (Opcional): Legenda do vídeo.
    * Exemplo de Requisição:
    ```curl
    curl -X POST "{{baseUrl}}/message/video?key={{instance_key}}" \
    -H "Authorization: Bearer {{token}}" \
    -F "file=@path/to/video.mp4" \
    -F "id=558199999999" \
    -F "caption=Mensagem com vídeo"
    ```

12. **Send Audio Message**
    * Método: `POST`
    * URL: `{{baseUrl}}/message/audio?key={{instance_key}}`
    * Descrição: Envia uma mensagem de áudio para um número do WhatsApp.
    * Parâmetros de Corpo (Form Data):
        * `file` (Obrigatório): Arquivo de áudio.
        * `id` (Obrigatório): ID do destinatário ou ID do grupo.
    * Exemplo de Requisição:
    ```curl
    curl -X POST "{{baseUrl}}/message/audio?key={{instance_key}}" \
    -H "Authorization: Bearer {{token}}" \
    -F "file=@path/to/audio.mp3" \
    -F "id=558199999999"
    ```

13. **Send Document Message**
    * Método: `POST`
    * URL: `{{baseUrl}}/message/doc?key={{instance_key}}`
    * Descrição: Envia um documento para um número ou grupo do WhatsApp.
    * Parâmetros de Corpo (Form Data):
        * `file` (Obrigatório): Arquivo do documento.
        * `id` (Obrigatório): ID do destinatário ou ID do grupo.
        * `filename` (Opcional): Nome do arquivo personalizado.
    * Exemplo de Requisição:
    ```curl
    curl -X POST "{{baseUrl}}/message/doc?key={{instance_key}}" \
    -H "Authorization: Bearer {{token}}" \
    -F "file=@path/to/document.pdf" \
    -F "id=558199999999" \
    -F "filename=meuarquivo.pdf"
    ```

14. **Send File URL**
    * Método: `POST`
    * URL: `{{baseUrl}}/message/mediaurl?key={{instance_key}}`
    * Descrição: Envia um arquivo de mídia via URL.
    * Parâmetros de Corpo (Form URL Encoded):
        * `id` (Obrigatório): ID do destinatário ou ID do grupo.
        * `url` (Obrigatório): URL do arquivo de mídia.
        * `type` (Obrigatório): Tipo de mídia (imagem, vídeo, áudio).
    * Exemplo de Requisição:
    ```curl
    curl -X POST "{{baseUrl}}/message/mediaurl?key={{instance_key}}" \
    -H "Authorization: Bearer {{token}}" \
    -d "id=558199999999" \
    -d "url=https://example.com/file.jpg" \
    -d "type=image"
    ```