## Ao migrar esse projeto para servir de base para outro projeto, precisa renomear todos os arquivos e pastas para o nome do novo projeto.

## ApiLaravel - API

## Instalação

- Instalar o **laravel** (versão 10)
    - `composer global require laravel/installer`
- Instalar o [XAMPP](https://www.apachefriends.org/pt_br/index.html) ( PHP 8.1 ).
    - PHP 8.1.17: https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.1.17/xampp-windows-x64-8.1.17-0-VS16-installer.exe
- Instalar o [Composer](https://getcomposer.org/download/).

- Executar o comando `composer install`.
    - Caso ocorra o erro `'InvalidArgumentException in Compiler.php line 36: Please provide a valid cache path.'`, verifique as seguintes pastas estão criadas em **storage/framework/**: `cache`, `views`, `sessions`.
- Criar o banco de dados `servicos-prestados` do tipo `utf8_unicode_ci`.
- Popular o banco de dados `php artisan migrate --seed` ( caso o banco de dados já esteja criado, execute o comando `php artisan migrate:fresh --seed` ).
- Configurar o arquivo hosts ( C:\Windows\System32\drivers\etc\hosts ) com o seguinte conteúdo:
```
    127.0.0.1	maisservicosprestados.com
```
- Configurar o **.env** do Admin para apontar para a API:
    - `API_URL=http://maisservicosprestados.com:8080`. Coloquem a porta que a API está rodando.
- Executar o comando `php artisan serve` para iniciar o servidor.
- Quando alterar alguma rota, ou precisar limpar o cache, execute o script `clear.bat`.
## Detalhes do projeto
- Login default:
    - **E-mail**: <EMAIL>
    - **Senha**: 123456
## Iniciar o servidor
- No Admin, executar o comando `php artisan serve --port 8001` para iniciar o servidor.
- Na API, executar o comando `php artisan serve --port 8080` para iniciar o servidor.

### Dica para rodar os dois servidores simultaneamente
- Criar uma arquivo "start +Serviços Prestados.bat" com o seguinte conteúdo:
```
@echo off
cd /d X:\+SistemasTI\ServicosPrestados\Api
start php artisan serve --port 8080
cd /d X:\+SistemasTI\ServicosPrestados\Admin
start php artisan serve --port 8001
```
- Alterar o caminho do diretório para o caminho do projeto em sua máquina.
- Alterar as portas caso necessário.

## Executar os jobs com o scheduler do Laravel
- Configurar o .env do Api com o QUEUE_CONNECTION=sync.
- Executar o comando `php artisan schedule:run` para executar os jobs uma vez.
- Executar o comando `php artisan schedule:work` para executar os jobs periodicamente.
- Executar o comando `php artisan schedule:list` para ver os jobs que estão agendados.


## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).

### Swagger
>Acessar a documentação da api

> Ao alterar qualquer rota ou request execute:
> **php artisan laravel-swagger:generate --filter="/api"  > public/swagger/swagger.json**
> para atualizar a documentação.

>Acesse: http://localhost:8000/swagger/index.html para ver a documentação. (Substitua localhost:8000 pelo seu host)

<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

