# Use the base PHP image with Alpine Linux
FROM php:8.1.30-cli-alpine3.20

# Set the working directory
WORKDIR /app

# Install dependencies and PHP extensions required for the API
RUN apk update && apk add --no-cache \
    bash \
    libpng-dev \
    libjpeg-turbo-dev \
    libxml2-dev \
    libzip-dev \
    oniguruma-dev \
    curl-dev \
    tzdata \
    && docker-php-ext-install pdo_mysql mbstring xml curl zip \
    && cp /usr/share/zoneinfo/America/Recife /etc/localtime \
    && echo "America/Recife" > /etc/timezone \
    && apk del tzdata

# Instale o Composer globalmente
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Copie o conteúdo atual do projeto para o diretório de trabalho no container
COPY . /app/

# Instale as dependências do PHP usando o Composer
RUN composer install --no-dev --optimize-autoloader

# Expose the port the API will use
EXPOSE 8000

# Copy the entrypoint script to execute migrations and start the server
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set the entrypoint script
ENTRYPOINT ["entrypoint.sh"]

# Command to start the application
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8000"]
