# Guia de Uso do Docker para o Projeto VisaoNegocio

Este guia fornece instruções detalhadas para construir e executar os serviços **Admin** (frontend) e **Api** (backend) do projeto **VisaoNegocio** usando Docker.

## Sumário

- [Estrutura do Projeto](#estrutura-do-projeto)
- [Construindo e Executando Serviços Individualmente](#construindo-e-executando-serviços-individualmente)
    - [Frontend (Admin)](#frontend-admin)
    - [Backend (Api)](#backend-api)
- [Usando Docker Compose](#usando-docker-compose)
- [Configuração dos Arquivos .env](#configuração-dos-arquivos-env)

## Estrutura do Projeto

A estrutura do projeto deve ser organizada da seguinte forma:

```
VisaoNegocio/
├── Admin/    # Aplicação frontend (Laravel)
├── Api/      # Aplicação backend (Laravel)
├── docker-compose.yml
```

## Construindo e Executando Serviços Individualmente

### Frontend (Admin)

#### Construindo a Imagem do Frontend

Navegue até a raiz do projeto `VisaoNegocio` e execute o seguinte comando para construir a imagem do frontend:

```bash
docker build -t visao-negocio-frontend -f Admin/Dockerfile ./Admin
```

- `-t visao-negocio-frontend`: Nomeia a imagem como `visao-negocio-frontend`.
- `-f Admin/Dockerfile`: Especifica o Dockerfile localizado na pasta `Admin`.
- `./Admin`: Define o contexto de build para a pasta `Admin`.

#### Executando o container do Frontend

Após construir a imagem, execute o container:

```bash
docker run -d --name frontend \
  --env-file=Admin/.env.docker \
  -p 8001:8000 \
  visao-negocio-frontend
```

- `-d`: Executa o container em segundo plano.
- `--name frontend`: Nomeia o container como `frontend`.
- `--env-file=Admin/.env.docker`: Especifica o arquivo de ambiente.
- `-p 8001:8000`: Mapeia a porta 8000 do container para a porta 8001 do host.

### Backend (Api)

#### Construindo a Imagem do Backend

Execute o comando abaixo para construir a imagem do backend:

```bash
docker build -t visao-negocio-backend -f Api/Dockerfile ./Api
```

- `-t visao-negocio-backend`: Nomeia a imagem como `visao-negocio-backend`.
- `-f Api/Dockerfile`: Especifica o Dockerfile localizado na pasta `Api`.
- `./Api`: Define o contexto de build para a pasta `Api`.

#### Executando o container do Backend

Execute o container do backend:

```bash
docker run -d --name backend \
  --env-file=Api/.env.docker \
  -p 8080:8000 \
  visao-negocio-backend
```

- `-d`: Executa o container em segundo plano.
- `--name backend`: Nomeia o container como `backend`.
- `--env-file=Api/.env.docker`: Especifica o arquivo de ambiente.
- `-p 8080:8000`: Mapeia a porta 8000 do container para a porta 8080 do host.

### Observação Sobre o Banco de Dados MySQL

Para que os serviços frontend e backend funcionem corretamente, o banco de dados MySQL deve estar em execução. Você pode executar o MySQL usando:

```bash
docker run -d --name mysql \
  -e MYSQL_ROOT_PASSWORD=senha_segura \
  -e MYSQL_DATABASE=visao_negocio \
  -e MYSQL_USER=visao_negocio \
  -e MYSQL_PASSWORD=123456 \
  -p 3308:3306 \
  mysql:latest
```

## Usando Docker Compose

Crie o arquivo `docker-compose.yml` na pasta raiz que contem os dois projetos.

```yaml
services:
    frontend:
        build:
            context: ./Admin
            dockerfile: Dockerfile
        env_file:
            - path: ./Admin/.env.docker
        container_name: frontend
        working_dir: /app
        tty: true
        networks:
            - app-network
        ports:
            - "8001:8000"
        depends_on:
            - mysql

    backend:
        build:
            context: ./Api
            dockerfile: Dockerfile
        env_file:
            - path: ./Api/.env.docker
        container_name: backend
        working_dir: /app
        tty: true
        networks:
            - app-network
        ports:
            - "8080:8000"
        depends_on:
            - mysql

    mysql:
        image: mysql:latest
        container_name: visao-negocio-db-test-deploy
        restart: always
        environment:
            MYSQL_ROOT_PASSWORD: # Você pode definir uma senha segura aqui
            MYSQL_DATABASE: visao_negocio
            MYSQL_USER: visao_negocio
            MYSQL_PASSWORD: 123456
            MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
        ports:
            - "3308:3306"
        volumes:
            - db_data_test:/var/lib/mysql
        networks:
            - app-network

volumes:
    db_data_test:

networks:
    app-network:
        driver: bridge
```

O `docker-compose.yml` simplifica o processo de construção e execução dos serviços. Para usar o Docker Compose:

### Construindo e Executando os Serviços

Na raiz do projeto `VisaoNegocio`, execute:

```bash
docker compose up -d
```

- `-d`: Executa os containeres em segundo plano.
- Caso queira forçar a recriação das imagens, adicione a flag `--build`.

Isso irá:

- Construir as imagens do frontend e backend usando os Dockerfiles especificados.
- Iniciar os serviços `frontend`, `backend` e `mysql`.
- Criar uma rede Docker personalizada `app-network` para comunicação entre os containeres.

### Parando os Serviços

Para parar e remover os containeres, execute:

```bash
docker compose down
```

## Configuração dos Arquivos .env

Para garantir que os serviços se comuniquem corretamente dentro dos containeres Docker, configure os arquivos `.env` dos projetos para usar os nomes dos serviços definidos no `docker-compose.yml`.

### Configurando o .env do Backend (Api)

No arquivo `Api/.env.docker`, defina:

```env
DB_HOST=mysql
FRONTEND_URL=http://frontend:8000
```

- `DB_HOST=mysql`: Aponta para o serviço MySQL definido no Docker Compose.
- `FRONTEND_URL=http://frontend:8000`: Aponta para o serviço frontend.

### Configurando o .env do Frontend (Admin)

No arquivo `Admin/.env.docker`, defina:

```env
API_URL=http://backend:8000
```

- `API_URL=http://backend:8000`: Aponta para o serviço backend.

### Importante

- Certifique-se de que os arquivos `.env.docker` estejam no mesmo diretório que os respectivos `Dockerfile`.
- Os nomes dos serviços (`mysql`, `frontend`, `backend`) são usados como hostnames dentro da rede Docker criada pelo Docker Compose.


