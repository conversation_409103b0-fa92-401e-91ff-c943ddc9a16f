<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IsRequester
{
    public function handle(Request $request, Closure $next)
    {
        if(auth('api')->user()->type != 'requester') {
            return response()->json(['msg' => 'Requisição inválida. Apenas solicitantes podem realizar esta ação. Usuário não autorizado!'], 401);
        }
        return $next($request);
    }
}
