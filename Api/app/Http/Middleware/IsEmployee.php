<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IsEmployee
{
    public function handle(Request $request, Closure $next)
    {
        if(!in_array(auth('api')->user()->type, ['employee', 'admin'])) {
            return response()->json(['msg' => 'Requisição inválida. Apenas funcionários podem realizar esta ação. Usuário não autorizado!'], 401);
        }
        return $next($request);
    }
}
