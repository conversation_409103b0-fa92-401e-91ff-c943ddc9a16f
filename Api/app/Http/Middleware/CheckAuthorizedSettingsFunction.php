<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckAuthorizedSettingsFunction
{
    public function handle(Request $request, Closure $next)
    {
        $user = auth('api')->user();

        if($user->hasEmployee() && $user->employee->authorizedSettingsFunction->havePermission($request) || $user->hasAdmin()) {
            return $next($request);
        } else {
            return response()->json(['msg' => 'Usuário autenticado não tem permissão para esta funcionalidade.'], 403);
        }
    }
}
