<?php

namespace App\Http\Requests\Api;


use App\Rules\UniqueUserEmail;
use App\Rules\UniqueUserPhoneNumber;
use Illuminate\Support\Facades\Auth;

class UpdateUser extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $user =  Auth::user();
        return [
            'name' => ['required', 'min:5', 'max:255'],
            'email' => ['required', 'email', new UniqueUserEmail($user->id,$this->cpf)],
            'birth_date' => ['date', 'before:today'],
            'cpf' => ['required', 'numeric', 'digits:11'],
            'gender' => ['nullable', 'in:f,m,other'],
            'phone_number_1' => ['required', 'numeric', 'digits_between:10,11', new UniqueUserPhoneNumber($user->id,$this->cpf)],
            'phone_number_2' => ['nullable', 'numeric', 'digits_between:10,11'],
            'neighborhood' => ['required', 'min:2'],
            'street' => ['required', 'min:2'],
            'number' => ['required'],
            'city' => ['required'],
            'state' => ['required'],
            'cep' => ['required', 'string', 'numeric', 'digits:8'],
            'complement' => ['nullable', 'min:2'],
            'email_validated' => ['boolean'],
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.min' => 'O campo nome deve ter pelo menos :min caracteres.',
            'name.max' => 'O campo nome não deve ter mais de :max caracteres.',

            'email.email' => 'O campo email deve ser um endereço de e-mail válido.',
            'email.unique' => 'O email já está em uso para a cidade selecionada.',
            'email.required' => 'O campo email é obrigatório.',

            'password.min' => 'A senha deve ter pelo menos :min caracteres.',
            'password.confirmed' => 'A confirmação da senha não corresponde.',

            'birth_date.date' => 'O campo data de nascimento deve ser uma data válida.',
            'birth_date.before' => 'A data de nascimento deve ser anterior à data atual.',

            'cpf.numeric' => 'O campo CPF deve conter apenas números.',
            'cpf.digits' => 'O campo CPF deve ter :digits dígitos.',
            'cpf.unique' => 'O CPF já está em uso para a cidade selecionada.',

            'gender.in' => 'O campo gênero deve ser um dos seguintes valores: f, m, other.',

            'phone_number_1.required' => 'O campo telefone é obrigatório.',
            'phone_number_1.numeric' => 'O campo telefone deve conter apenas números.',
            'phone_number_1.digits_between' => 'O campo telefone deve ter entre :min e :max dígitos.',

            'phone_number_2.numeric' => 'O campo segundo telefone deve conter apenas números.',
            'phone_number_2.digits_between' => 'O campo segundo telefone deve ter entre :min e :max dígitos.',

            'neighborhood.required' => 'O campo bairro é obrigatório.',
            'neighborhood.min' => 'O campo bairro deve ter pelo menos :min caracteres.',

            'street.required' => 'O campo rua é obrigatório.',
            'street.min' => 'O campo rua deve ter pelo menos :min caracteres.',

            'number.required' => 'O campo número é obrigatório.',

            'city.required' => 'O campo cidade é obrigatório.',

            'state.required' => 'O campo estado é obrigatório.',

            'cep.required' => 'O campo CEP é obrigatório.',
            'cep.string' => 'O campo CEP deve ser uma string.',
            'cep.numeric' => 'O campo CEP deve conter apenas números.',
            'cep.digits' => 'O campo CEP deve ter :digits dígitos.',

            'complement.min' => 'O campo complemento deve ter pelo menos :min caracteres.',
        ];
    }
}
