<?php

namespace App\Http\Requests\Api\Forms;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFormRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'default' => 'required|boolean',
            'active' => 'required|boolean',
            'topics' => 'required|array',
            'topics.*.id' => 'required|exists:topics,id',
            'topics.*.order' => 'required|integer',
            'businesses' => 'required_if:default,false|array',
            'businesses.*' => 'required_if:default,false|exists:businesses,id',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.string' => 'O campo nome deve ser uma string.',
            'name.max' => 'O campo nome deve ter no máximo 255 caracteres.',
            'description.string' => 'O campo descrição deve ser uma string.',
            'default.required' => 'O campo default é obrigatório.',
            'default.boolean' => 'O campo default deve ser um booleano.',
            'active.required' => 'O campo ativo é obrigatório.',
            'active.boolean' => 'O campo ativo deve ser um booleano.',
            'topics.required' => 'O campo categorias é obrigatório.',
            'topics.array' => 'O campo categorias deve ser um array.',
            'topics.*.id.required' => 'O campo id da categoria é obrigatório.',
            'topics.*.id.exists' => 'O campo id da categoria deve existir na tabela de categorias.',
            'topics.*.order.required' => 'O campo ordem da categoria é obrigatório.',
            'topics.*.order.integer' => 'O campo ordem da categoria deve ser um inteiro.',
            'businesses.required_if' => 'O campo negocios é obrigatório quando o campo default é falso.',
            'businesses.array' => 'O campo negocios deve ser um array.',
            'businesses.*.required_if' => 'O campo id do negocio é obrigatório quando o campo default é falso.',
            'businesses.*.exists' => 'O campo id do negocio deve existir na tabela de negocios.',
        ];
    }
}
