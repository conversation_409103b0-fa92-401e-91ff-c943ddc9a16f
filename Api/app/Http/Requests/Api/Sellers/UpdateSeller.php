<?php

namespace App\Http\Requests\Api\Sellers;

use App\Rules\UniqueRule;

class UpdateSeller extends StoreSeller
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = parent::rules();
        $rules['email'] = ['required', 'email'];
        $rules['cpf'] = ['required', 'numeric', 'digits:11'];

        return $rules;
    }

    public function messages(): array
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.min' => 'O campo nome deve ter pelo menos :min caracteres.',
            'name.max' => 'O campo nome não deve ter mais de :max caracteres.',

            'email.email' => 'O campo email deve ser um endereço de e-mail válido.',
            'email.unique' => 'Esse e-mail já existe.',
            'email.required' => 'O campo email é obrigatório.',

            'birth_date.required' => 'O campo data de nascimento é obrigatório.',
            'birth_date.date' => 'O campo data de nascimento deve ser uma data válida.',
            'birth_date.before' => 'A data de nascimento deve ser anterior à data atual.',

            'cpf.required' => 'O campo CPF é obrigatório.',
            'cpf.numeric' => 'O campo CPF deve conter apenas números.',
            'cpf.digits' => 'O campo CPF deve ter :digits dígitos.',
            'cpf.unique' => 'Esse cpf já existe.',

            'gender.in' => 'O campo gênero deve ser um dos seguintes valores: f, m, other.',

            'phone_number_1.required' => 'O campo telefone é obrigatório.',
            'phone_number_1.numeric' => 'O campo telefone deve conter apenas números.',
            'phone_number_1.digits_between' => 'O campo telefone deve ter entre :min e :max dígitos.',

            'phone_number_2.numeric' => 'O campo segundo telefone deve conter apenas números.',
            'phone_number_2.digits_between' => 'O campo segundo telefone deve ter entre :min e :max dígitos.',

            'neighborhood.required' => 'O campo bairro é obrigatório.',
            'neighborhood.min' => 'O campo bairro deve ter pelo menos :min caracteres.',

            'street.required' => 'O campo rua é obrigatório.',
            'street.min' => 'O campo rua deve ter pelo menos :min caracteres.',

            'number.required' => 'O campo número é obrigatório.',

            'state.required' => 'O campo estado é obrigatório.',

            'cep.required' => 'O campo CEP é obrigatório.',
            'cep.string' => 'O campo CEP deve ser uma string.',
            'cep.numeric' => 'O campo CEP deve conter apenas números.',
            'cep.digits' => 'O campo CEP deve ter :digits dígitos.',

            'complement.min' => 'O campo complemento deve ter pelo menos :min caracteres.',
        ];
    }
}
