<?php

namespace App\Http\Requests\Api\Sellers;

use App\Rules\UniqueRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreSeller extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'min:5', 'max:255'],
            'email' => ['required', 'email'],
            'birth_date' => ['required','date', 'before:today'],
            'cpf' => ['required', 'numeric', 'digits:11'],
            'gender' => ['nullable', 'in:f,m,other'],
            'phone_number_1' => ['required', 'numeric', 'digits_between:10,11'],
            'phone_number_2' => ['nullable', 'numeric', 'digits_between:10,11'],
            'neighborhood'  => ['required', 'min:2'],
            'street' => ['required', 'min:2'],
            'number' => ['required'],
            'city' => ['required'],
            'state' => ['required'],
            'cep' => ['required', 'string', 'numeric', 'digits:8'],
            'complement' => ['nullable', 'min:2'],
            'register_sellers' => ['boolean'],
            'percentage_per_sale' => ['required','numeric', 'min:0', 'max:100'],
            'email_validated' => ['boolean'],
            'registered_by' => ['nullable', 'integer'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.min' => 'O nome deve conter no mínimo 5 caracteres.',
            'name.max' => 'O nome deve conter no máximo 255 caracteres.',

            'email.required' => 'O campo e-mail é obrigatório.',
            'email.email' => 'Por favor, insira um endereço de e-mail válido.',

            'birth_date.required' => 'O campo data de nascimento é obrigatório.',
            'birth_date.date' => 'Por favor, insira uma data de nascimento válida.',
            'birth_date.before' => 'A data de nascimento deve ser anterior à data de hoje.',

            'cpf.required' => 'O campo CPF é obrigatório.',
            'cpf.numeric' => 'O CPF deve conter apenas números.',
            'cpf.digits' => 'O CPF deve conter 11 dígitos.',

            'gender.in' => 'Por favor, selecione um gênero válido.',

            'phone_number_1.required' => 'O campo telefone principal é obrigatório.',
            'phone_number_1.numeric' => 'O telefone deve conter apenas números.',
            'phone_number_1.digits_between' => 'O telefone deve conter entre 10 e 11 dígitos.',

            'phone_number_2.numeric' => 'O telefone deve conter apenas números.',
            'phone_number_2.digits_between' => 'O telefone deve conter entre 10 e 11 dígitos.',

            'neighborhood.required' => 'O campo bairro é obrigatório.',
            'neighborhood.min' => 'O bairro deve conter no mínimo 2 caracteres.',

            'street.required' => 'O campo rua é obrigatório.',
            'street.min' => 'A rua deve conter no mínimo 2 caracteres.',

            'number.required' => 'O campo número é obrigatório.',

            'city.required' => 'O campo cidade é obrigatório.',

            'state.required' => 'O campo estado é obrigatório.',

            'cep.required' => 'O campo CEP é obrigatório.',
            'cep.string' => 'O CEP deve ser uma string.',
            'cep.numeric' => 'O CEP deve conter apenas números.',
            'cep.digits' => 'O CEP deve conter 8 dígitos.',

            'complement.min' => 'O complemento deve conter no mínimo 2 caracteres.',

            'register_sellers.boolean' => 'O campo cadastrar vendedores deve ser verdadeiro ou falso.',

            'percentage_per_sale.required' => 'O campo porcentagem por venda é obrigatório.',
            'percentage_per_sale.numeric' => 'A porcentagem por venda deve ser um número.',
            'percentage_per_sale.min' => 'A porcentagem por venda deve ser no mínimo 0%.',
            'percentage_per_sale.max' => 'A porcentagem por venda deve ser no máximo 100%.',

            'email_validated.boolean' => 'O campo e-mail validado deve ser verdadeiro ou falso.',
            'registered_by.integer' => 'O campo cadastrado por deve ser um número inteiro.',
        ];
    }
}
