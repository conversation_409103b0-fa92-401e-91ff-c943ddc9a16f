<?php

namespace App\Http\Requests\Api\Cashback;

use App\Enums\CashbackStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreCashbackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation()
    {
        if ($this->has('clients_id')) {
            $this->merge([
                'clients_id' => json_decode($this->input('clients_id'), true) ?? []
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => ['required', new Enum(CashbackStatus::class)],
            'expiration_date' => ['required', 'date'],
            'percentage' => ['required', 'numeric', 'min:0.1', 'max:20'],
            'business_id' => ['required', 'exists:businesses,id'],
            'clients_id' => ['required', 'array'],
            'clients_id.*' => ['exists:clients,id']
        ];
    }

    public function messages()
    {
        return [
            'status.required' => 'O campo status é obrigatório.',
            'status.in' => 'O status selecionado é inválido.',
            'expiration_date.required' => 'A data de expiração é obrigatória.',
            'expiration_date.date' => 'A data de expiração deve ser uma data e hora válida.',
            'expiration_date.format' => 'A data de expiração não está no formato correto.',
            'percentage.required' => 'O campo porcentagem é obrigatório.',
            'percentage.numeric' => 'O campo porcentagem deve ser um número.',
            'percentage.min' => 'O campo porcentagem deve ser no mínimo :min.',
            'percentage.max' => 'O campo porcentagem deve ser no máximo :max.',
            'business_id.required' => 'O campo negócio é obrigatório.',
            'business_id.exists' => 'O negócio selecionado é inválido.',
            'clients_id.required' => 'Pelo menos um cliente deve ser selecionado.',
            'clients_id.array' => 'O campo clientes deve ser um array.',
            'clients_id.*.required' => 'Cada cliente é obrigatório.',
            'clients_id.*.exists' => 'Algum dos clientes selecionados é inválido.',
        ];
    }
}
