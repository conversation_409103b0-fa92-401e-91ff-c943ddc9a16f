<?php

namespace App\Http\Requests\Api\Dashboard;

use App\Enums\DashboardPeriods;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class DashboardRequestShow extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'period' => ['required', new Enum(DashboardPeriods::class)],
            'generated_at' => ['required', 'date', 'before:now'],
        ];

        if ($this->period === DashboardPeriods::especific->value) {
            $rules['startDate'] = ['required', 'date'];
            $rules['endDate'] = ['required', 'date'];
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'period.required' => 'É necessário selecionar um período.',
            'period.in' => 'O período deve ser válido.',

            'startDate.required' => "A data de início não pode ser vazia.",
            'startDate.date' => "A data de início precisa ser válida.",
            
            'endDate.required' => "A data de fim não pode ser vazia.",
            'endDate.date' => "A data de fim precisa ser válida.",
        ];
    }
}
