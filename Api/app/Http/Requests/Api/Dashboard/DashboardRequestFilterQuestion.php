<?php

namespace App\Http\Requests\Api\Dashboard;

use App\Enums\DashboardPeriods;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class DashboardRequestFilterQuestion extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'filters.period' => ['nullable', new Enum(DashboardPeriods::class)],
            'filters.evaluation_type' => ['nullable', 'in:five_star,yes_no'],
            'filters.topic_id' => ['nullable', 'integer'],
            'filters.form_id' => ['nullable', 'integer'],
            'filters.question_id' => ['nullable', 'integer'],
            'filters.answer_type' => ['nullable', 'in:1,2,3,4,5,yes,no'],
        ];

        if (isset($this->filters['period']) && $this->filters['period'] === DashboardPeriods::especific->value) {
            $rules['filters.startDate'] = ['required', 'date'];
            $rules['filters.endDate'] = ['required', 'date'];
        }

        return $rules;
    }

    public function messages()
{
    return [        
        'filters.period.required' => 'É necessário selecionar um período.',
        'filters.period.in' => 'O período selecionado deve ser válido.',
        'filters.startDate.required' => 'A data de início é obrigatória.',
        'filters.startDate.date' => 'A data de início deve ser uma data válida.',
        'filters.endDate.required' => 'A data de fim é obrigatória.',
        'filters.endDate.date' => 'A data de fim deve ser uma data válida.',
        'filters.evaluation_type.in' => 'O tipo de avaliação deve ser "five_star" ou "yesOrNo".',
        'filters.topic_id.integer' => 'O ID da categoria deve ser um número.',
        'filters.question_id.integer' => 'O ID da pergunta deve ser um número.',
    ];
}
}
