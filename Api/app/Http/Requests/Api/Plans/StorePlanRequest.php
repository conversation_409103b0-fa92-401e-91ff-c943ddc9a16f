<?php

namespace App\Http\Requests\Api\Plans;

use Exception;
use Illuminate\Foundation\Http\FormRequest;

class StorePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3'],
            'description' => ['string', 'nullable', 'min:3'],
            'value' => ['required', 'min:0', 'numeric'],
            'employees' => ['nullable', 'boolean'],
            'manage_business_profile' => ['nullable', 'boolean'],
            'topics' => ['nullable', 'boolean'],
            'update_topics' => ['nullable', 'boolean'],
            'forms' => ['nullable', 'boolean'],
            'update_forms' => ['nullable', 'boolean'],
            'publicly_visible' => ['nullable', 'boolean'],
            'active' => ['nullable', 'boolean'],
            'cashback' => ['nullable', 'boolean'],
            'update_cashback' => ['nullable', 'boolean'],
            'clients' => ['nullable', 'boolean'],
            'update_clients' => ['nullable', 'boolean'],
            'evaluations' => ['nullable', 'boolean'],
            'notifications' => ['nullable', 'boolean'],
            'update_notifications' => ['nullable', 'boolean'],
            'dashboard' => ['nullable', 'boolean'],
            'parameters' => ['nullable', 'boolean'],
            'payment_system_tag' => ['nullable', 'string', 'regex:/^[A-Za-z0-9_]+$/'],
            'total_employees' => ['required', 'integer', 'min:0'],
            'total_email_sends' => ['required', 'integer', 'min:0'],
            'email_sends_type' => ['required', 'string', 'in:monthly,daily'],
            'email_sends_days' => ['nullable', 'integer', 'min:1', 'required_if:email_sends_type,daily'],
            'total_whatsapp_sends' => ['required', 'integer', 'min:0'],
            'whatsapp_sends_type' => ['required', 'string', 'in:monthly,daily'],
            'whatsapp_sends_days' => ['nullable', 'integer', 'min:1', 'required_if:whatsapp_sends_type,daily'],
            'email_sending' => ['nullable', 'boolean'],
            'whatsapp_sending' => ['nullable', 'boolean'],
            'site_notifications' => ['nullable', 'boolean'],
            'update_site_notifications' => ['nullable', 'boolean'],
        ];
    }

    public function messages() : array
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.string' => 'O campo nome deve ser uma string.',
            'name.min' => 'O campo nome deve ter no mínimo 3 caracteres.',
            'description.string' => 'O campo descrição deve ser uma string.',
            'description.min' => 'O campo descrição deve ter no mínimo 3 caracteres.',
            'value.required' => 'O campo valor é obrigatório.',
            'value.min' => 'O campo valor deve ser um número maior ou igual a 0.',
            'value.numeric' => 'O campo valor deve ser um número.',
            'employees.boolean' => 'O campo employees deve ser um booleano.',
            'manage_business_profile.boolean' => 'O campo manage_business_profile deve ser um booleano.',
            'topics.boolean' => 'O campo topics deve ser um booleano.',
            'update_topics.boolean' => 'O campo update_topics deve ser um booleano.',
            'forms.boolean' => 'O campo forms deve ser um booleano.',
            'update_forms.boolean' => 'O campo update_forms deve ser um booleano.',
            'publicly_visible.boolean' => 'O campo publicly_visible deve ser um booleano.',
            'active.boolean' => 'O campo active deve ser um booleano.',
            'cashback.boolean' => 'O campo cashback deve ser um booleano.',
            'update_cashback.boolean' => 'O campo update_cashback deve ser um booleano.',
            'clients.boolean' => 'O campo clients deve ser um booleano.',
            'update_clients.boolean' => 'O campo update_clients deve ser um booleano.',
            'evaluations.boolean' => 'O campo evaluations deve ser um booleano.',
            'notifications.boolean' => 'O campo notifications deve ser um booleano.',
            'update_notifications.boolean' => 'O campo update_notifications deve ser um booleano.',
            'dashboard.boolean' => 'O campo dashboard deve ser um booleano.',
            'parameters.boolean' => 'O campo parameters deve ser um booleano.',
            'payment_system_tag.string' => 'O campo Tag do sistema de pagamento deve ser uma string.',
            'payment_system_tag.regex' => 'A Tag do sistema de pagamento deve conter apenas letras(sem acentos), números e underline.(A-Z, a-z, 0-9, _)',
            'total_employees.required' => 'O campo total de funcionários é obrigatório.',
            'total_employees.integer' => 'O campo total de funcionários deve ser um número inteiro.',
            'total_employees.min' => 'O campo total de funcionários deve ser maior ou igual a 0.',
            'total_email_sends.required' => 'O campo total de envios de email é obrigatório.',
            'total_email_sends.integer' => 'O campo total de envios de email deve ser um número inteiro.',
            'total_email_sends.min' => 'O campo total de envios de email deve ser maior ou igual a 0.',
            'email_sends_type.required' => 'O campo tipo de envios de email é obrigatório.',
            'email_sends_type.string' => 'O campo tipo de envios de email deve ser uma string.',
            'email_sends_type.in' => 'O tipo de envios de email deve ser mensal ou diário.',
            'email_sends_days.required_if' => 'O campo dias de email é obrigatório quando o tipo de envios é diário.',
            'email_sends_days.integer' => 'O campo dias de email deve ser um número inteiro.',
            'email_sends_days.min' => 'O campo dias de email deve ser maior ou igual a 1.',
            'total_whatsapp_sends.required' => 'O campo total de envios de WhatsApp é obrigatório.',
            'total_whatsapp_sends.integer' => 'O campo total de envios de WhatsApp deve ser um número inteiro.',
            'total_whatsapp_sends.min' => 'O campo total de envios de WhatsApp deve ser maior ou igual a 0.',
            'whatsapp_sends_type.required' => 'O campo tipo de envios de WhatsApp é obrigatório.',
            'whatsapp_sends_type.string' => 'O campo tipo de envios de WhatsApp deve ser uma string.',
            'whatsapp_sends_type.in' => 'O tipo de envios de WhatsApp deve ser mensal ou diário.',
            'whatsapp_sends_days.required_if' => 'O campo dias de WhatsApp é obrigatório quando o tipo de envios é diário.',
            'whatsapp_sends_days.integer' => 'O campo dias de WhatsApp deve ser um número inteiro.',
            'whatsapp_sends_days.min' => 'O campo dias de WhatsApp deve ser maior ou igual a 1.',
            'email_sending.boolean' => 'O campo envio por e-mail deve ser verdadeiro ou falso.',
            'whatsapp_sending.boolean' => 'O campo envio por WhatsApp deve ser verdadeiro ou falso.',
            'site_notifications.boolean' => 'O campo notificações do site deve ser um booleano.',
            'update_site_notifications.boolean' => 'O campo gerenciar notificações do site deve ser um booleano.',
        ];
    }

    public function checkPermissions()
    {
        $anyPermission = false;
        array_map(function ($value) use (&$anyPermission) {
            if ($value) {
                $anyPermission = true;
            }
        }, array_values($this->getPermissions()));

        if (!$anyPermission) {
            throw new Exception('Nenhuma permissão selecionada.');
        }
    }

    public function getPermissions()
    {
        $request = request();
        return [
            'employees' => $request->boolean('employees'),
            'manage_business_profile' => $request->boolean('manage_business_profile'),
            'topics' => $request->boolean('topics'),
            'update_topics' => $request->boolean('update_topics'),
            'forms' => $request->boolean('forms'),
            'update_forms' => $request->boolean('update_forms'),
            'cashback' => $request->boolean('cashback'),
            'update_cashback' => $request->boolean('update_cashback'),
            'clients' => $request->boolean('clients'),
            'update_clients' => $request->boolean('update_clients'),
            'evaluations' => $request->boolean('evaluations'),
            'notifications' => $request->boolean('notifications'),
            'update_notifications' => $request->boolean('update_notifications'),
            'dashboard' => $request->boolean('dashboard'),
            'parameters' => $request->boolean('parameters'),
            'site_notifications' => $request->boolean('site_notifications'),
            'update_site_notifications' => $request->boolean('update_site_notifications'),
            'total_employees' => $request->input('total_employees'),
            'total_email_sends' => $request->input('total_email_sends'),
            'email_sends_type' => $request->input('email_sends_type'),
            'email_sends_days' => $request->input('email_sends_days'),
            'total_whatsapp_sends' => $request->input('total_whatsapp_sends'),
            'whatsapp_sends_type' => $request->input('whatsapp_sends_type'),
            'whatsapp_sends_days' => $request->input('whatsapp_sends_days'),
            'email_sending' => $request->boolean('email_sending'),
            'whatsapp_sending' => $request->boolean('whatsapp_sending'),
        ];
    }
}
