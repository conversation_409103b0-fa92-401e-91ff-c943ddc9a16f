<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class StoreClientIncompleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'min:5', 'max:255'],
            'email' => ['required_without:phone_number_1', 'nullable', 'email'],
            'phone_number_1' => ['required_without:email', 'nullable', 'numeric', 'digits_between:10,11']
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'O nome é obrigatório',
            'name.min' => 'O nome deve ter no mínimo 5 caracteres',
            'name.max' => 'O nome deve ter no máximo 255 caracteres',
            'email.required_without' => 'O e-mail é obrigatório quando o telefone não é fornecido',
            'email.email' => 'O e-mail informado não é válido',
            'phone_number_1.required_without' => 'O telefone é obrigatório quando o e-mail não é fornecido',
            'phone_number_1.numeric' => 'O telefone deve conter apenas números',
            'phone_number_1.digits_between' => 'O telefone deve ter entre 10 e 11 dígitos'
        ];
    }
}
