<?php

namespace App\Http\Requests\Api\Business;

use Illuminate\Foundation\Http\FormRequest;

class PublicRegisterBusinessRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'business_name' => 'required|string|max:255',
            'plan_id' => 'required|integer|exists:plans,id',
            'seller_id' => 'nullable|integer|exists:sellers,id',
            'employee_id' => 'nullable|integer|exists:employees,id',
            'is_cnpj_optional' => 'boolean',
            'cnpj' => 'nullable|string|size:14|required_if:is_cnpj_optional,false',
            'isCreateNewEmployeeAndUser' => 'required|boolean',
            'isEditEmployee' => 'required|boolean',
            'isCreateNewEmployee' => 'required|boolean',
            'business_deleted' => 'required|boolean',
            'email_validated' => 'required|boolean',
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'cpf' => 'required|string|size:11',
            'phone_number' => 'required|string|max:11',
        ];
    }

    public function messages() : array
    {
        return [
            'business_name.required' => 'O campo nome é obrigatório.',
            'business_name.string' => 'O campo nome deve ser uma string.',
            'business_name.max' => 'O campo nome deve ter no máximo :max caracteres.',

            'plan_id.required' => 'O campo plano é obrigatório.',
            'plan_id.integer' => 'O campo plano deve ser um número inteiro.',

            'seller_id.integer' => 'O campo vendedor deve ser um número inteiro.',
            'seller_id.exists' => 'O campo vendedor deve ser um vendedor válido.',

            'employee_id.integer' => 'O campo funcionário deve ser um número inteiro.',
            'employee_id.exists' => 'O campo funcionário deve ser um funcionário válido.',

            'is_cnpj_optional.boolean' => 'O campo is_cnpj_optional deve ser um booleano.',
            'cnpj.required_if' => 'O campo CNPJ é obrigatório quando não for marcado como opcional.',
            'cnpj.string' => 'O campo CNPJ deve ser uma string.',
            'cnpj.size' => 'O campo CNPJ deve ter :size caracteres.',

            'isCreateNewEmployeeAndUser.required' => 'O campo isCreateNewEmployeeAndUser é obrigatório.',
            'isCreateNewEmployeeAndUser.boolean' => 'O campo isCreateNewEmployeeAndUser deve ser um booleano.',

            'isEditEmployee.required' => 'O campo isEditEmployee é obrigatório.',
            'isEditEmployee.boolean' => 'O campo isEditEmployee deve ser um booleano.',

            'isCreateNewEmployee.required' => 'O campo isCreateNewEmployee é obrigatório.',
            'isCreateNewEmployee.boolean' => 'O campo isCreateNewEmployee deve ser um booleano.',

            'business_deleted.required' => 'O campo business_deleted é obrigatório.',
            'business_deleted.boolean' => 'O campo business_deleted deve ser um booleano.',

            'email_validated.required' => 'O campo email_validated é obrigatório.',
            'email_validated.boolean' => 'O campo email_validated deve ser um booleano.',

            'name.required' => 'O campo nome é obrigatório.',
            'name.string' => 'O campo nome deve ser uma string.',
            'name.max' => 'O campo nome deve ter no máximo :max caracteres.',

            'email.required' => 'O campo email é obrigatório.',
            'email.string' => 'O campo email deve ser uma string.',
            'email.email' => 'O campo email deve ser um email válido.',
            'email.max' => 'O campo email deve ter no máximo :max caracteres.',

            'cpf.required' => 'O campo cpf é obrigatório.',
            'cpf.string' => 'O campo cpf deve ser uma string.',
            'cpf.size' => 'O campo cpf deve ter :size caracteres.',

            'phone_number.required' => 'O campo telefone é obrigatório.',
            'phone_number.string' => 'O campo telefone deve ser uma string.',
            'phone_number.max' => 'O campo telefone deve ter no máximo :max caracteres.',
        ];
    }
}
