<?php

namespace App\Http\Requests\Api\Business;

use Illuminate\Foundation\Http\FormRequest;

class ValidateCnpjRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cnpj' => 'nullable|string|size:14|unique:businesses',
        ];
    }

    public function messages() {
        return [
            'cnpj.string' => 'O campo CNPJ deve ser uma string.',
            'cnpj.size' => 'O campo CNPJ deve ter exatamente :size caracteres.',
            'cnpj.unique' => 'Esse CNPJ já está em uso por outro negócio.',
        ];
    }
}
