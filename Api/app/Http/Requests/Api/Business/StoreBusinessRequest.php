<?php

namespace App\Http\Requests\Api\Business;

use Illuminate\Foundation\Http\FormRequest;

class StoreBusinessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cnpj_optional' => 'boolean',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'plan_id' => 'required|integer|exists:plans,id',
            'seller_id' => 'nullable|integer|exists:sellers,id',
            'cnpj' => ['nullable', 'string', 'size:14', 'required_if:cnpj_optional,false'],
            'corporate_reason' => 'nullable|string|max:255',
            'neighborhood' => 'nullable|string|max:255',
            'street' => 'nullable|string|max:255',
            'number' => 'nullable|string|max:20',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|size:2',
            'cep' => 'nullable|string|size:8',
            'complement' => 'nullable|string|max:255',
            'url_facebook' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
            'url_instagram' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
            'url_google' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
            'url_linkedin' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
        ];
    }

    public function messages() : array
    {
        return [
            'is_cnpj_optional.boolean' => 'O campo is_cnpj_optional deve ser um valor booleano.',
            'name.required' => 'O campo nome é obrigatório.',
            'name.string' => 'O campo nome deve ser uma string.',
            'name.max' => 'O campo nome deve ter no máximo :max caracteres.',

            'description.string' => 'O campo descrição deve ser uma string.',
            'description.max' => 'O campo descrição deve ter no máximo :max caracteres.',

            'latitude.numeric' => 'O campo latitude deve ser um número.',
            'latitude.between' => 'O campo latitude deve estar entre -90 e 90.',

            'longitude.numeric' => 'O campo longitude deve ser um número.',
            'longitude.between' => 'O campo longitude deve estar entre -180 e 180.',

            'plan_id.required' => 'O campo plano é obrigatório.',
            'plan_id.integer' => 'O campo plano deve ser um número inteiro.',
            'plan_id.exists' => 'O plano selecionado é inválido.',

            'seller_id.integer' => 'O campo vendedor deve ser um número inteiro.',
            'seller_id.exists' => 'O vendedor selecionado é inválido.',

            'cnpj.required_if' => 'O campo CNPJ é obrigatório quando is_cnpj_optional é false.',
            'cnpj.string' => 'O campo CNPJ deve ser uma string.',
            'cnpj.size' => 'O campo CNPJ deve ter exatamente :size caracteres.',
            'cnpj.unique' => 'Este CNPJ já está sendo utilizado.',

            'corporate_reason.string' => 'O campo razão social deve ser uma string.',
            'corporate_reason.max' => 'O campo razão social deve ter no máximo :max caracteres.',

            'neighborhood.string' => 'O campo bairro deve ser uma string.',
            'neighborhood.max' => 'O campo bairro deve ter no máximo :max caracteres.',

            'street.string' => 'O campo rua deve ser uma string.',
            'street.max' => 'O campo rua deve ter no máximo :max caracteres.',

            'number.string' => 'O campo número deve ser uma string.',
            'number.max' => 'O campo número deve ter no máximo :max caracteres.',

            'city.string' => 'O campo cidade deve ser uma string.',
            'city.max' => 'O campo cidade deve ter no máximo :max caracteres.',

            'state.string' => 'O campo estado deve ser uma string.',
            'state.size' => 'O campo estado deve ter exatamente :size caracteres.',

            'cep.string' => 'O campo CEP deve ser uma string.',
            'cep.size' => 'O campo CEP deve ter exatamente :size caracteres.',

            'complement.string' => 'O campo complemento deve ser uma string.',
            'complement.max' => 'O campo complemento deve ter no máximo :max caracteres.',
            
            'url_facebook.regex' => 'O URL do Facebook deve ser um endereço válido.',
            'url_facebook.min' => 'O URL do Facebook deve ter pelo menos 5 caracteres.',
            'url_instagram.regex' => 'O URL do Instagram deve ser um endereço válido.',
            'url_instagram.min' => 'O URL do Instagram deve ter pelo menos 5 caracteres.',
            'url_google.regex' => 'O URL do Google deve ser um endereço válido.',
            'url_google.min' => 'O URL do Google deve ter pelo menos 5 caracteres.',
            'url_linkedin.regex' => 'O URL do LinkedIn deve ser um endereço válido.',
            'url_linkedin.min' => 'O URL do LinkedIn deve ter pelo menos 5 caracteres.',
        ];
    }
}
