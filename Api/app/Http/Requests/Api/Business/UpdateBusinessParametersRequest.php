<?php

namespace App\Http\Requests\Api\Business;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBusinessParametersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'evaluation_expiration_hours' => 'required|integer|min:1',
            'cashback_expiration_hours' => 'required|integer|min:1',
            'cashback_percentage' => 'required|numeric|min:0|max:100',
            'form_id' => 'nullable|exists:forms,id',
            'can_set_cashback_value' => 'nullable|boolean',
            // Campos do WhatsApp
            'whatsapp_instance_key' => 'nullable|string',
            'whatsapp_phone_number' => 'nullable|string',
        ];
    }
}
