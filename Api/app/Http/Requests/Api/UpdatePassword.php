<?php

namespace App\Http\Requests\Api;

class UpdatePassword extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password' => ['required', 'string'],
            'new_password' => ['required', 'string', 'min:8', 'confirmed']
        ] + parent::rules();
    }

    public function messages()
    {
        return [
            'password.required' => 'A senha atual é obrigatória',
            'new_password.required' => 'A nova senha é obrigatórias',
            'new_password.min' => 'A nova senha deve ter no mínimo 8 caracteres',
            'new_password.confirmed' => 'A nova senha e a confirmação devem ser iguais'
        ];
    }
}
