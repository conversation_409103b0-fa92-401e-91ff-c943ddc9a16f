<?php

namespace App\Http\Requests\Api\Evaluations;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEvaluationRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'sending_type' => 'required|in:QR_CODE,EMAIL,WHATSAPP,SMS',
            'email' => 'nullable|email|required_if:sending_type,EMAIL',
            'number_phone' => 'nullable|string|required_if:sending_type,WHATSAPP,SMS',
            'shipping_date' => 'required|date',
            'form_id' => 'required|exists:forms,id',
            'shipping_status' => 'required|in:SENT,RESPONDED,EXPIRED',
            'expiration_date' => 'required|date|after_or_equal:shipping_date',
            'business_id' => 'required|exists:businesses,id',
            'cashback_percentage' => 'nullable|numeric|max:20',
            'cashback_expiration_date' => 'nullable|date|after_or_equal:shipping_date',
            'client_id' => 'nullable|exists:clients,id',
        ];
    }
}
