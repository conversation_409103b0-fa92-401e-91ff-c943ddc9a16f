<?php

namespace App\Http\Requests\Api\Evaluations;

use Illuminate\Foundation\Http\FormRequest;

class StoreEvaluationRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'sending_type' => 'required|in:QR_CODE,EMAIL,WHATSAPP,SMS',
            'anonymous' => 'required|boolean',
            'amount' => 'required|decimal:0,2',
            'email' => 'nullable|email|required_if:sending_type,EMAIL',
            'number_phone' => [
                'nullable',
                'string',
                'required_if:sending_type,WHATSAPP,SMS',
                'regex:/^\d{10,11}$/'
            ],
            'form_id' => 'required|exists:forms,id',
            'name' => 'nullable|string',
            'with_cashback' => 'boolean',
        ];
    }

    public function messages()
    {
        return [
            'anonymous.required' => 'O campo "Avaliação Anônima" é obrigatório.',
            'anonymous.boolean' => 'O campo "Avaliação Anônima" deve ser um booleano.',

            'amount.required' => "O campo valor do Produto/Serviço é obrigatório.",
            'amount.decimal' => "O campo valor do Produto/Serviço deve ser decimal",

            'sending_type.required' => 'O tipo de envio é obrigatório.',
            'sending_type.in' => 'O tipo de envio deve ser um dos seguintes valores: QR_CODE, EMAIL, WHATSAPP ou SMS.',

            'email.required_if' => 'O campo email é obrigatório quando o tipo de envio é EMAIL.',
            'email.email' => 'O campo email deve conter um endereço de email válido.',

            'number_phone.required_if' => 'O número de contato é obrigatório quando o tipo de envio é WhatsApp ou SMS.',
            'number_phone.string' => 'O número de contato deve ser uma string válida.',
            'number_phone.regex' => 'O número de contato deve ter 10 ou 11 dígitos e conter apenas números.',

            'form_id.required' => 'O formulário de envio é obrigatório.',
            'form_id.exists' => 'O formulário selecionado é inválido.',

            'expiration_option.required' => 'A opção de tempo de expiração é obrigatória.',
            'expiration_option.in' => 'A opção de tempo de expiração deve ser uma das seguintes: 24, 12, 6, 3, 1 ou outra.',

            'expiration_date.date' => 'A data de expiração deve ser uma data válida.',
            'expiration_date.after' => 'A data de expiração deve ser posterior à data atual.',

            'cashback_percentage.numeric' => 'A porcentagem de cashback deve ser um número.',
            'cashback_percentage.max' => 'A porcentagem de cashback não pode exceder 20%.',

            'cashback_expiration_date.date' => 'A data de expiração do cashback deve ser uma data válida.',
            'cashback_expiration_date.after' => 'A data de expiração do cashback deve ser posterior à data atual.',
        ];
    }

}
