<?php

namespace App\Http\Requests\Api;


class UpdateSystemContact extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' =>['nullable', 'email'],
            'phone_number' =>['nullable', 'regex:/^\(?\d{2}\)?\s?(?:\d{4}|\d{5})\-?\d{4}$/'],
        ] + parent::rules();
    }

    public function messages()
    {
        return [
            'email.email' => 'O campo email deve ser um email válido.',
            'phone_number.regex' => 'O campo telefone deve ser um telefone válido.',
        ];
    }
}
