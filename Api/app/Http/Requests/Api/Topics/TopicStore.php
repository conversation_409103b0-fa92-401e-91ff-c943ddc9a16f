<?php

namespace App\Http\Requests\Api\Topics;

use Illuminate\Foundation\Http\FormRequest;

class TopicStore extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:255',
            'default' => 'required|boolean',
            'active' => 'required|boolean',
            'required' => 'required|boolean',
            'businesses' => 'nullable|array',
            'businesses.*' => 'integer|exists:businesses,id',
            'questions' => 'nullable|array|min:1',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Nome é obrigatório',
            'name.string' => 'Nome deve ser uma string',
            'name.max' => 'Nome deve ter no máximo 255 caracteres',
            'description.string' => 'Descrição deve ser uma string',
            'description.max' => 'Descrição deve ter no máximo 255 caracteres',
            'default.required' => 'Categoria Padrão é obrigatória',
            'default.boolean' => 'Categoria Padrão deve ser um boolean',
            'active.required' => 'Ativo é obrigatório',
            'active.boolean' => 'Ativo deve ser um boolean',
            'required.required' => 'Categoria Requerida é obrigatória',
            'required.boolean' => 'Categoria Requerida deve ser um boolean',
            'businesses.array' => 'Negócios deve ser um array',
            'businesses.*.integer' => 'Negócios deve ser um array de id',
            'businesses.*.exists' => 'Negócios deve ser um array de id existentes',
            'questions.array' => 'Questões deve ser um array',
        ];
    }
}
