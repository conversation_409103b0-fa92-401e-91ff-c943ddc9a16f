<?php

namespace App\Http\Requests\Api\SiteNotifications;

use Illuminate\Foundation\Http\FormRequest;

class MarkAsReadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:user_site_notifications,id',
        ];
    }

    public function messages(): array
    {
        return [
            'ids.required' => 'O campo IDs é obrigatório.',
            'ids.array' => 'O campo IDs deve ser um array.',
            'ids.*.integer' => 'Cada ID deve ser um número inteiro.',
            'ids.*.exists' => 'Essa notificação não existe',
        ];
    }
}
