<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreSiteNotificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            "title" => ['required', 'max:255', 'min:3','string'],
            "description" => ['required', 'string', 'min: 3'],
            "icon" => ['nullable', 'string', 'min: 3'],
            'filters' => ['nullable', 'array'],
            'userIds' => ['required_if:allUsers,false,null', 'array'],
            'userIds.*' => ['required', 'exists:users,id'],
            'exceptIds' => ['nullable', 'array'],
            'exceptIds.*' => ['nullable', 'exists:users,id'],
            "url_click" => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
        ];

        return $rules;
    }

    public function messages() {
        return [
            'type.required' => 'O campo tipo de notificação é obrigatório.',
            'type.in' => 'O tipo de envio deve ser válido. Opções permitidas: employee, seller, client.',
    
            'title.required' => 'O campo título é obrigatório.',
            'title.min' => 'O campo título deve ter pelo menos :min caracteres.',
            'title.max' => 'O campo título não deve ter mais de :max caracteres.',
            'title.string' => 'O campo título deve ser um texto válido.',
    
            'description.required' => 'O campo descrição é obrigatório.',
            'description.min' => 'O campo descrição deve ter pelo menos :min caracteres.',
            'description.string' => 'O campo descrição deve ser um texto válido.',
    
            'icon.string' => 'O campo ícone deve ser um texto válido.',
            'icon.min' => 'O campo ícone deve ter pelo menos :min caracteres.',
    
            'filters.required' => 'O campo filtros é obrigatório.',
            'filters.json' => 'O campo filtros deve estar no formato JSON válido.',
    
            'userIds.required' => 'É necessário selecionar pelo menos um usuário.',
            'userIds.array' => 'O campo usuários deve ser um array.',
            
            'userIds.*.required' => 'Cada usuário selecionado deve ser válido.',
            'userIds.*.exists' => 'Usuário selecionado não encontrado no sistema.',
    
            'exceptIds.array' => 'O campo usuários excluídos deve ser um array.',
            'exceptIds.*.exists' => 'Usuário excluído não encontrado no sistema.',
    
            'allUsers.boolean' => 'O campo todos os usuários deve ser verdadeiro ou falso.',
    
            'url_click.min' => 'O campo link deve ter pelo menos :min caracteres.',
            'url_click.url' => 'O campo link deve ser um link válido.',
            'url_click.string' => 'O campo link deve ser um texto válido.',
        ];
    }
    
}
