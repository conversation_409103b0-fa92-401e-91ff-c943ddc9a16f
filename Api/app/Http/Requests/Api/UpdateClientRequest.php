<?php

namespace App\Http\Requests\Api;

use App\Rules\UniqueClientPhoneNumber;
use App\Rules\UniqueClientUserEmail;
use Illuminate\Foundation\Http\FormRequest;

class UpdateClientRequest extends StoreClientRequest {
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = parent::rules();
        $rules['businessesIds'] = ['array'];

        $rules['phone_number_1'] = ['required', 'numeric', 'digits_between:10,11', new UniqueClientPhoneNumber($this->route('id') ?? $this->route('client'),$this->cpf)];
        $rules['email'] = ['required', 'email', new UniqueClientUserEmail($this->route('id') ?? $this->route('client'),$this->cpf)];
        return $rules;
    }
}
