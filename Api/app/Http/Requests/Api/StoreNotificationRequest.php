<?php

namespace App\Http\Requests\Api;

use App\Enums\NotificationSendingType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreNotificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
//            "sending_type" => ['required', new Enum(NotificationSendingType::class)],
            "sending_type" => ['required'],
            "title" => ['required', 'max:255', 'min:3','string'],
            "description" => ['required', 'string', 'min: 3'],
            "number_clients" => ['required', 'integer', 'min:1'],
            "url_click" => ['nullable', 'string', 'min:5'],
        ];

        switch ($this->sending_type) {
            case 'email':
                $rules['title'] = ['required', 'max:255', 'min:3', 'string'];
                $rules['description'] = ['required', 'max:2000', 'min:3', 'string'];
            break;

            case 'notification_push':
                $rules['title'] = ['required', 'max:50', 'min:3', 'string'];
                $rules['description'] = ['required', 'max:150', 'min:3', 'string'];
            break;

            case 'whatsapp':
                $rules['title'] = ['required', 'max:255', 'min:3', 'string'];
                $rules['description'] = ['required', 'max:1000', 'min:3', 'string'];
            break;
        }

        return $rules;
    }

    public function messages() {
        return [
            'sending_type.required' => 'O campo tipo de campanha é obrigatório.',
            'sending_type.in' => 'O tipo de envio deve ser válido.',

            'title.required' => 'O campo título é obrigatório.',
            'title.min' => 'O campo título deve ter pelo menos :min caracteres.',
            'title.max' => 'O campo título não deve ter mais de :max caracteres.',

            'description.required' => 'O campo descrição é obrigatório.',
            'description.min' => 'O campo descrição deve ter pelo menos :min caracteres.',
            'description.max' => 'O campo descrição não deve ter mais de :max caracteres.',

            'number_clients.required' => 'Deve ser listado pelo menos um cliente.',
            'number_clients.min' => 'Pelo menos um cliente deve ser listado.',

            'url_click.min' => 'O campo link deve ter pelo menos :min caracteres.',
            'url_click.max' => 'O campo link não deve ter mais de :max caracteres.',
        ];
    }
}
