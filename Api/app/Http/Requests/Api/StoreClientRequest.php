<?php

namespace App\Http\Requests\Api;

use App\Rules\UniqueClientPhoneNumber;
use App\Rules\UniqueClientUserEmail;
use Illuminate\Foundation\Http\FormRequest;

class   StoreClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'min:5', 'max:255'],
            'email' => ['required', 'email', new UniqueClientUserEmail(null,$this->cpf)],
            'birth_date' => ['nullable','date', 'before:today'],
            'cpf' => ['required', 'numeric', 'digits:11'],
            'gender' => ['nullable', 'in:f,m,other'],
            'phone_number_1' => ['required', 'numeric', 'digits_between:10,11', new UniqueClientPhoneNumber(null,$this->cpf)],
            'phone_number_2' => ['nullable', 'numeric', 'digits_between:10,11'],
            'is_entrepreneur' => ['required', 'boolean']
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.min' => 'O campo nome deve ter pelo menos :min caracteres.',
            'name.max' => 'O campo nome não deve ter mais de :max caracteres.',

            'email.email' => 'O campo email deve ser um endereço de e-mail válido.',
            'email.unique' => 'O email já está em uso para a cidade selecionada.',
            'email.required' => 'O campo email é obrigatório.',

            'birth_date.required' => 'O campo data de nascimento é obrigatório.',
            'birth_date.date' => 'O campo data de nascimento deve ser uma data válida.',
            'birth_date.before' => 'A data de nascimento deve ser anterior à data atual.',

            'cpf.required' => 'O campo CPF é obrigatório.',
            'cpf.numeric' => 'O campo CPF deve conter apenas números.',
            'cpf.digits' => 'O campo CPF deve ter :digits dígitos.',
            'cpf.unique' => 'O CPF já está em uso para a cidade selecionada.',

            'gender.in' => 'O campo gênero deve ser um dos seguintes valores: f, m, other.',

            'phone_number_1.required' => 'O campo telefone é obrigatório.',
            'phone_number_1.numeric' => 'O campo telefone deve conter apenas números.',
            'phone_number_1.digits_between' => 'O campo telefone deve ter entre :min e :max dígitos.',

            'phone_number_2.numeric' => 'O campo segundo telefone deve conter apenas números.',
            'phone_number_2.digits_between' => 'O campo segundo telefone deve ter entre :min e :max dígitos.',

            'is_entrepreneur.required' => 'O campo empreendedor é obrigatório',
            'is_entrepreneur.boolean' => 'O campo empreendedor deve ser um booleano'
        ];
    }
}
