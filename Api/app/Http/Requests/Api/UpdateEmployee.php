<?php

namespace App\Http\Requests\Api;

use App\Rules\UniqueEmployeePhoneNumber;
use App\Rules\UniqueEmployeeUserEmail;

class UpdateEmployee extends UpdateUser
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = parent::rules();

        $rules['phone_number_1'] = ['required', 'numeric', 'digits_between:10,11', new UniqueEmployeePhoneNumber($this->route('id') ?? $this->route('employee'),$this->cpf)];
        $rules['email'] = ['required', 'email', new UniqueEmployeeUserEmail($this->route('id') ?? $this->route('employee'),$this->cpf)];
        return array_merge($rules, [
            'employees' => ['boolean'],
            'sellers' => ['boolean'],
            'businessesIds' => ['array'],
            'release_all_business' => ['in:true,false,on,off,1,0'],
            'plans' => ['boolean'],
            'parameters' => ['boolean'],
            'initial_screen' => ['required', 'string'],
        ]);
    }

    public function messages()
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.min' => 'O campo nome deve ter pelo menos :min caracteres.',
            'name.max' => 'O campo nome não deve ter mais de :max caracteres.',

            'email.email' => 'O campo email deve ser um endereço de e-mail válido.',
            'email.unique' => 'O email já está em uso para a cidade selecionada.',
            'email.required' => 'O campo email é obrigatório.',

            'password.min' => 'A senha deve ter pelo menos :min caracteres.',
            'password.confirmed' => 'A confirmação da senha não corresponde.',

            'birth_date.date' => 'O campo data de nascimento deve ser uma data válida.',
            'birth_date.before' => 'A data de nascimento deve ser anterior à data atual.',

            'cpf.numeric' => 'O campo CPF deve conter apenas números.',
            'cpf.digits' => 'O campo CPF deve ter :digits dígitos.',
            'cpf.unique' => 'O CPF já está em uso para a cidade selecionada.',

            'gender.in' => 'O campo gênero deve ser um dos seguintes valores: f, m, other.',

            'phone_number_1.required' => 'O campo telefone é obrigatório.',
            'phone_number_1.numeric' => 'O campo telefone deve conter apenas números.',
            'phone_number_1.digits_between' => 'O campo telefone deve ter entre :min e :max dígitos.',

            'phone_number_2.numeric' => 'O campo segundo telefone deve conter apenas números.',
            'phone_number_2.digits_between' => 'O campo segundo telefone deve ter entre :min e :max dígitos.',

            'neighborhood.required' => 'O campo bairro é obrigatório.',
            'neighborhood.min' => 'O campo bairro deve ter pelo menos :min caracteres.',

            'street.required' => 'O campo rua é obrigatório.',
            'street.min' => 'O campo rua deve ter pelo menos :min caracteres.',

            'number.required' => 'O campo número é obrigatório.',

            'city.required' => 'O campo cidade é obrigatório.',

            'state.required' => 'O campo estado é obrigatório.',

            'cep.required' => 'O campo CEP é obrigatório.',
            'cep.string' => 'O campo CEP deve ser uma string.',
            'cep.numeric' => 'O campo CEP deve conter apenas números.',
            'cep.digits' => 'O campo CEP deve ter :digits dígitos.',

            'complement.min' => 'O campo complemento deve ter pelo menos :min caracteres.',

            'initial_screen.required' => 'O campo tela inicial é obrigatório.',
            'initial_screen.string' => 'O campo tela inicial deve ser uma string.',
        ];
    }

}
