<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Str;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    public function getResponseExceptionBody($exception): array
    {
        return ['msg' => $exception->getMessage(), 'code' => $exception->getCodeException(), 'exception' => get_class($exception)];
    }

    private function normalize($string)
    {
        $slug = Str::slug($string, ' ');
        return Str::lower($slug);
    }
}
