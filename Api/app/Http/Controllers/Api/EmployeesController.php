<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\MaisLuzExceptions\CityNotFoundException;
use App\Exceptions\MaisLuzExceptions\CpfExistsException;
use App\Exceptions\MaisLuzExceptions\EmailExistsException;
use App\Exceptions\MaisLuzExceptions\EmployeeNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\StoreEmployee;
use App\Http\Requests\Api\UpdateEmployee;
use App\Http\Resources\Api\EmployeeResource;
use App\Http\Resources\Api\UserResource;
use App\Mail\EmployeeResetPasswordMail;
use App\Mail\EmployeeWelcomeMail;
use App\Models\Business;
use App\Models\Employee;
use App\Http\Requests\Api\BaseRequest as Request;
use App\Models\EmployeeBusiness;
use App\Models\Plan;
use App\Models\User;
use Auth;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class EmployeesController extends Controller
{
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;
            $per_page = $request->per_page ?? 5;
            $exceptId = $employee ? $employee->id : 0;

            $query = Employee::withUser()->where('employees.id', '<>', $exceptId)
                ->where('admin', false);

            if ($employee->admin || $employee->authorizedFunction->release_all_business) {
                if ($business_selected_id) {
                    $query->whereHas('businesses', function ($query) use ($business_selected_id) {
                        $query->where('businesses.id', $business_selected_id);
                    });
                }
            } else {
                $query->whereHas('businesses', function ($query) use ($business_selected_id) {
                    $query->where('businesses.id', $business_selected_id);
                });

                // **Exclui administradores do negócio selecionado**
                $query->whereDoesntHave('businesses', function ($q) use ($business_selected_id) {
                    $q->where('businesses.id', $business_selected_id)
                        ->where('employee_business.admin', 1);
                });
            }

            if ($employee->authorizedFunction->release_all_business) {
                $query->orWhereHas('authorizedFunction', function ($query) {
                    $query->where('release_all_business', true);
                })->where('admin', false)->where('employees.id', '<>', $exceptId);
            }

            $query->orderBy('users.name');

            $employees = $per_page <= 0 ? $query->get() : $query->paginate($per_page);
            $employees->load(['user', 'authorizedFunction', 'authorizedSettingsFunction', 'businesses']);

            $employees->transform(function ($employee) {
                $employee['businesses_canceled'] = $employee->businesses_canceled;
                return $employee;
            });

            return response()->json(['employees' => $employees]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Busca funcionários aplicando as regras de permissão e filtrando pelo termo de busca.
     * A busca é aplicada sobre o conjunto completo de funcionários visíveis.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;
            $search = $request->search;
            $per_page = $request->per_page ?? 5;
            $exceptId = isset(auth('api')->user()->employee) ? auth('api')->user()->employee->id : 0; // ID do funcionário logado

            // Query base: Seleciona funcionários (não o próprio logado) que não são admin geral
            $query = Employee::withUser()->where('employees.id', '<>', $exceptId)
                ->where('admin', false);

            // --- Lógica de Visibilidade Principal ---
            // Define QUEM o usuário logado pode ver, antes de aplicar a busca
            $query->where(function ($visibilityQuery) use ($employee, $business_selected_id) {
                // Se admin geral ou tem acesso total
                if ($employee->admin || $employee->authorizedFunction->release_all_business) {
                    // Se um negócio específico está selecionado, filtra por ele
                    if ($business_selected_id) {
                        $visibilityQuery->whereHas('businesses', function ($q) use ($business_selected_id) {
                            $q->where('businesses.id', $business_selected_id);
                        });
                    }
                    // Se business_selected_id é nulo, NENHUM filtro de negócio é aplicado, buscando em todos.
                } else {
                    // Se funcionário comum:
                    // OBRIGATÓRIO estar no negócio selecionado E NÃO ser admin desse negócio específico
                    if ($business_selected_id) {
                        $visibilityQuery->whereHas('businesses', function ($q) use ($business_selected_id) {
                            $q->where('businesses.id', $business_selected_id);
                        })->whereDoesntHave('businesses', function ($q) use ($business_selected_id) {
                            $q->where('businesses.id', $business_selected_id)
                                ->where('employee_business.admin', 1);
                        });
                    } else {
                        // Funcionário comum SEM negócio selecionado não deve ver ninguém.
                        $visibilityQuery->whereRaw('1 = 0');
                    }
                }
            }); // --- Fim da Lógica de Visibilidade Principal ---


            // --- Aplica a Busca DEPOIS de definir a visibilidade ---
            if ($search) {
                $searchTerm = $search;
                $digitsOnly = preg_replace('/[^0-9]/', '', $searchTerm);

                // Aplica a busca na query que já contém as regras de visibilidade
                $query->where(function ($searchQuery) use ($searchTerm, $digitsOnly) {
                    if (filter_var($searchTerm, FILTER_VALIDATE_EMAIL)) {
                        // Search by Email
                        $searchQuery->where('users.email', 'like', "%$searchTerm%");
                    } elseif (strlen($digitsOnly) === 11) {
                        $phoneNumberV2 = '';
                        $codes = substr($digitsOnly, 0, 2);
                        $numberDigits = substr($digitsOnly, 2);
                        if (strlen($numberDigits) === 9) { $phoneNumberV2 = $codes . substr($numberDigits, 1); }
                        else if (strlen($numberDigits) === 8) { $phoneNumberV2 = $codes . '9' . $numberDigits; }
                        // Search by CPF (prioritized) OR 11-digit Phone
                        $searchQuery->where('users.cpf', 'like', "$digitsOnly%")
                          ->orWhere('users.phone_number_1', 'like', "%$digitsOnly%")
                          ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                    } elseif (strlen($digitsOnly) >= 8 && strlen($digitsOnly) <= 10) {
                        $phoneNumberV2 = '';
                        $codes = substr($digitsOnly, 0, 2);
                        $numberDigits = substr($digitsOnly, 2);
                        if (strlen($numberDigits) === 9) { $phoneNumberV2 = $codes . substr($numberDigits, 1); }
                        else if (strlen($numberDigits) === 8) { $phoneNumberV2 = $codes . '9' . $numberDigits; }
                        // Search by 8, 9 or 10-digit Phone
                        $searchQuery->where('users.phone_number_1', 'like', "%$digitsOnly%")
                            ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                    } else {
                        // Search by Name as fallback
                        $searchQuery->where('users.name', 'like', "%$searchTerm%");
                    }
                });
            }

            // Ordenação e Paginação
            $query->orderBy('users.name');
            $employees = $per_page <= 0 ? $query->get() : $query->paginate($per_page);
            $employees->load(['user', 'authorizedFunction', 'authorizedSettingsFunction', 'businesses']);

            // Adiciona informações extras
            $employees->transform(function ($employee) {
                $employee['businesses_canceled'] = $employee->businesses_canceled;
                return $employee;
            });

            return response()->json(['employees' => $employees, 'search' => $request->search]);
        } catch (\Exception $e) {
            Log::error('Error in EmployeesController@search: ' . $e->getMessage());
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function store(StoreEmployee $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            // Verifica se já existe um usuário com o CPF fornecido, incluindo os deletados
            $existingUserByCpf = User::withTrashed()->where('cpf', $request->cpf)->first();

            // Verifica se já existe um usuário com o e-mail fornecido
            $existingUserByEmail = User::where('email', $request->email)
                ->whereNotNull('cpf')
                ->first();

            // Se o CPF já estiver associado a um funcionário ativo, lança exceção
            if ($existingUserByCpf && !$existingUserByCpf->trashed() && $existingUserByCpf->hasEmployee()) {
                throw new CpfExistsException();
            }

            // Se o e-mail já existir e pertencer a outro usuário, lança exceção
            if ($existingUserByEmail && $existingUserByEmail->cpf != $request->cpf) {
                throw new EmailExistsException();
            }

            $userData = $request->all();
            $userData['login_type'] = 'employee';

            if (isset($userData['gender']) && !in_array($userData['gender'], ['f', 'm'])) {
                $userData['gender'] = null;
            }

            $shouldGeneratePassword = false;
            $randomPassword = null;

            if ($existingUserByCpf) {
                if ($existingUserByCpf->trashed()) {
                    $existingUserByCpf->restore();
                    $shouldGeneratePassword = true;
                }

                $existingUserByCpf->update($userData);

                $employee = $existingUserByCpf->employee()->withTrashed()->first();
                if ($employee) {
                    // Restaura o funcionário caso esteja deletado
                    if ($employee->trashed()) {
                        $employee->restore();

                        if ($employee->authorizedFunction()->withTrashed()->exists()) {
                            $employee->authorizedFunction()->restore();
                        }

                        if ($employee->authorizedSettingsFunction()->withTrashed()->exists()) {
                            $employee->authorizedSettingsFunction()->restore();
                        }
                    }
                } else {
                    // Cria o perfil de funcionário caso não exista
                    $employee = $existingUserByCpf->employee()->create();
                }
            } else {
                // Usuário não encontrado, cria um novo
                $shouldGeneratePassword = true;

                $randomPassword = Str::random(10);
                $hashedPassword = bcrypt($randomPassword);
                $userData['password'] = $hashedPassword;

                $user = User::create($userData);
                $employee = $user->employee()->create();
            }

            $employee->update([
                'business_selected_id' => null,
                'initial_screen' => $request->initial_screen ?? '',
            ]);

            $emailValidated = $request->email_validated ?? false;
            if ($emailValidated) {
                $employee->user->update(['email_verified_at' => now()]);
            } elseif ($existingUserByCpf && $existingUserByCpf->email != $request->email) {
                $employee->user->update(['email_verified_at' => null]);
            }

            if ($shouldGeneratePassword) {
                if (!$randomPassword) {
                    $randomPassword = Str::random(10);
                    $hashedPassword = bcrypt($randomPassword);
                    $employee->user->update(['password' => $hashedPassword]);
                }
                Mail::to($employee->user->email)->send(new EmployeeWelcomeMail($employee->user, $randomPassword));
            }

            // Verifica e cria ou atualiza as permissões
            $authorizedFunctionData = [
                'employees' => isset($request->employees),
                'settings' => isset($request->settings),
                'release_all_business' => isset($request->release_all_business),
                'manage_business_profile' => isset($request->manage_business_profile),
                'topics' => isset($request->topics),
                'default_topics' => isset($request->default_topics),
                'update_topics' => isset($request->update_topics),
                'sellers' => $request->boolean('sellers'),
                'plans' => $request->boolean('plans'),
                'forms' => isset($request->forms),
                'default_forms' => isset($request->default_forms),
                'update_forms' => isset($request->update_forms),
                'update_business' => isset($request->update_business),
                'cashback' => isset($request->cashback),
                'update_cashback' => isset($request->update_cashback),
                'clients' => isset($request->clients),
                'update_clients' => isset($request->update_clients),
                'evaluations' => isset($request->evaluations),
                'notifications' => isset($request->notifications),
                'update_notifications' => isset($request->update_notifications),
                'dashboard' => isset($request->dashboard),
                'parameters' => isset($request->parameters),
                'site_notifications' => isset($request->site_notifications),
                'update_site_notifications' => isset($request->update_site_notifications),
            ];

            $authorizedSettingsFunctionData = [
                'contact' => isset($request->contact),
                'system_parameters' => isset($request->system_parameters),
            ];

            $employee->authorizedFunction()->updateOrCreate([], $authorizedFunctionData);
            $employee->authorizedSettingsFunction()->updateOrCreate([], $authorizedSettingsFunctionData);

            $businessesIds = $request->businessesIds ?? [];
            $businessesIdsSaved = $employee->businesses->pluck('id')->toArray();

            $businessesIdsToDelete = array_diff($businessesIdsSaved, $businessesIds);
            $businessesIdsToInsert = array_diff($businessesIds, $businessesIdsSaved);

            $employee->businesses()->detach($businessesIdsToDelete);
            $employee->businesses()->attach($businessesIdsToInsert);
            $employee->businesses()->updateExistingPivot($businessesIdsToInsert, ['created_at' => now(), 'updated_at' => now()]);
            
            $clientController = new ClientController();
            if (!$employee->user->hasClient()) {
                $clientController->findOrCreateClientForEmployeeOrUser($employee);
            }

            if($employee->user->hasClient()){
                $clientController->consolidateIncompleteClientData($employee->user->client);
            }
            DB::commit();

            return response()->json(['employee' => EmployeeResource::make($employee)]);
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody(new CityNotFoundException()), 400);
        } catch (CpfExistsException|EmailExistsException $e) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody($e), 403);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        try {
            $employee = Employee::findOrFail($id);
            $employee->load('businesses', 'authorizedFunction');
            return response()->json(['employee' => EmployeeResource::make($employee)]);
        } catch (ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new EmployeeNotFoundException()), 400);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function update(UpdateEmployee $request, $id): JsonResponse
    {
        DB::beginTransaction();

        try {
            $employee = Employee::findOrFail($id);

            $userFindCpf = User::where('cpf', $request->cpf)->first();
            if ($userFindCpf && $userFindCpf->id != $employee->user->id) {
                throw new CpfExistsException();
            }

            $userFindEmail = User::where('email', $request->email)->first();
            if ($userFindEmail && $userFindEmail->id != $employee->user->id) {
                throw new EmailExistsException();
            }

            $data = $request->all();
            if (isset($data['gender']) && !in_array($data['gender'], ['f', 'm'])) {
                $data['gender'] = null;
            }
            $email_changed = $employee->user->email != $request->email;
            $employee->update($data);

            $email_validated = $request->email_validated ?? false;

            if ($email_validated) {
                $employee->user->update(['email_verified_at' => now()]);
            } elseif ($email_changed) {
                $employee->user->update(['email_verified_at' => null]);
            }

            $employee->authorizedFunction->update([
                'employees' => isset($request->employees),
                'settings' => isset($request->settings),
                'release_all_business' => isset($request->release_all_business),
                'manage_business_profile' => isset($request->manage_business_profile),
                'topics' => isset($request->topics),
                'default_topics' => isset($request->default_topics),
                'update_topics' => isset($request->update_topics),
                'plans' => $request->boolean('plans'),
                'sellers' => $request->boolean('sellers'),
                'forms' => isset($request->forms),
                'default_forms' => isset($request->default_forms),
                'update_forms' => isset($request->update_forms),
                'update_business' => isset($request->update_business),
                'cashback' => isset($request->cashback),
                'update_cashback' => isset($request->update_cashback),
                'clients' => isset($request->clients),
                'update_clients' => isset($request->update_clients),
                'evaluations' => isset($request->evaluations),
                'notifications' => isset($request->notifications),
                'update_notifications' => isset($request->update_notifications),
                'dashboard' => isset($request->dashboard),
                'parameters' => isset($request->parameters),
                'site_notifications' => isset($request->site_notifications),
                'update_site_notifications' => isset($request->update_site_notifications),
            ]);

            $employee->authorizedSettingsFunction->update([
                'contact' => isset($request->contact),
                'system_parameters' => isset($request->system_parameters),
            ]);

            $employee->user->login_type = 'employee';

            $resetPassword = false;
            if ($request->has('reset_password') && $request->reset_password) {
                $resetPassword = true;
                $randomPassword = Str::random(10);
                $hashedPassword = bcrypt($randomPassword);
                $employee->user->password = $hashedPassword;
                $employee->user->save();
            }

            $employee->user->update($data);

            $businessesIds = $request->businessesIds ?? [];
            $businessesIdsSaved = $employee->businesses->pluck('id')->toArray();

            $businessesIdsToDelete = array_diff($businessesIdsSaved, $businessesIds);
            $businessesIdsToInsert = array_diff($businessesIds, $businessesIdsSaved);

            $employee->businesses()->detach($businessesIdsToDelete);
            $employee->businesses()->attach($businessesIdsToInsert);
            $employee->businesses()->updateExistingPivot($businessesIdsToInsert, ['created_at' => now(), 'updated_at' => now()]);

            if ($resetPassword) {
                Mail::to($employee->user->email)->send(new EmployeeResetPasswordMail($employee->user, $randomPassword));
            }

            if($employee->user->hasClient()){
                $clientController = new ClientController();
                $clientController->consolidateIncompleteClientData($employee->user->client);
            }

            DB::commit();

            return response()->json([
                'msg' => 'Funcionário atualizado.',
                'employee' => EmployeeResource::make($employee),
            ]);
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody(new EmployeeNotFoundException()), 400);
        } catch (CpfExistsException|EmailExistsException $e) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody($e), 403);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function updateWithoutPermissionsAndAddBussinesses(UpdateEmployee $request, $id): JsonResponse
    {
        DB::beginTransaction();

        try {
            $employee = Employee::findOrFail($id);

            $userFindCpf = User::where('cpf', $request->cpf)->first();
            if ($userFindCpf && $userFindCpf->id != $employee->user->id) {
                throw new CpfExistsException();
            }

            $userFindEmail = User::where('email', $request->email)->first();
            if ($userFindEmail && $userFindEmail->id != $employee->user->id) {
                throw new EmailExistsException();
            }

            $data = $request->all();
            if (isset($data['gender']) && !in_array($data['gender'], ['f', 'm'])) {
                $data['gender'] = null;
            }
            $email_changed = $employee->user->email != $request->email;
            $employee->update($data);

            $email_validated = $request->email_validated;

            if ($email_validated) {
                $employee->user->update(['email_verified_at' => now()]);
            } elseif ($email_changed) {
                $employee->user->update(['email_verified_at' => null]);
            }

            $employee->user->login_type = 'employee';

            $resetPassword = false;
            if ($request->has('reset_password') && $request->reset_password) {
                $resetPassword = true;
                $randomPassword = Str::random(10);
                $hashedPassword = bcrypt($randomPassword);
                $employee->user->password = $hashedPassword;
                $employee->user->save();
            }

            $employee->user->update($data);

            $businessesIds = $request->businessesIds ?? [];
            $businessesIdsSaved = $employee->businesses->pluck('id')->toArray();

            $businessesIdsToInsert = array_diff($businessesIds, $businessesIdsSaved);

            $employee->businesses()->attach($businessesIdsToInsert);
            $employee->businesses()->updateExistingPivot($businessesIdsToInsert, ['created_at' => now(), 'updated_at' => now()]);

            if ($resetPassword) {
                Mail::to($employee->user->email)->send(new EmployeeResetPasswordMail($employee->user, $randomPassword));
            }

            if ($employee->user->hasClient()){
                $clientController = new ClientController();
                $clientController->consolidateIncompleteClientData($employee->user->client);
            }

            DB::commit();

            return response()->json([
                'msg' => 'Funcionário atualizado.',
                'employee' => EmployeeResource::make($employee),
            ]);

        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody(new EmployeeNotFoundException()), 400);
        } catch (CpfExistsException|EmailExistsException $e) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody($e), 403);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function updatePermissionsFromPlan($planId, ?array $employeeIds, ?int $businessId = null): void
    {
        try {
            $plan = Plan::findOrFail($planId);

            $authorizedFunctionsPlan = $plan->authorizedFunctions;
            // Filtra apenas as permissões que são true no plano
            $authorizedFunctionsTrue = collect($authorizedFunctionsPlan->toArray())
                ->filter(function ($value) {
                    return $value === true;
                });

            // Se a lista de IDs for nula, busca os funcionários administradores
            if (is_null($employeeIds)) {
                // Verifica se um negócio específico foi fornecido
                if ($businessId) {
                    // Verifica se o business está associado ao plano
                    $business = Business::where('id', $businessId)
                        ->where('plan_id', $planId)
                        ->first();

                    if (!$business) {
                        throw new \Exception("O negócio selecionado não está associado ao plano fornecido.");
                    }

                    // Busca os administradores do negócio específico
                    $employeeBusinesses = EmployeeBusiness::where('business_id', $businessId)
                        ->where('admin', true)
                        ->get();
                } else {
                    // Busca os negócios associados a esse plano
                    $businesses = Business::where('plan_id', $planId)->get();

                    // Busca os EmployeeBusiness onde admin é true e o business_id está na lista de businesses
                    $employeeBusinesses = EmployeeBusiness::whereIn('business_id', $businesses->pluck('id'))
                        ->where('admin', true)
                        ->get();
                }

                // Extrai os IDs dos funcionários
                $employeeIds = $employeeBusinesses->pluck('employee_id')->unique()->toArray();
            }

            // Itera sobre cada ID de funcionário
            foreach ($employeeIds as $id) {
                $employee = Employee::findOrFail($id);
                $employee->authorizedFunction()->updateOrCreate([], $authorizedFunctionsTrue->toArray());
            }
        } catch (\Exception $e) {
            throw new \Exception("Erro ao atualizar permissões dos funcionários: " . $e->getMessage());
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $employee = Employee::findOrFail($id);
            $authorizedFunction = $employee->authorizedFunction;
            $authorizedSettingsFunction = $employee->authorizedSettingsFunction;

            $authorizedFunction->delete();
            $authorizedSettingsFunction->delete();
            EmployeeBusiness::where('employee_id', $employee->id)->delete();

            $user = $employee->user;

            if ($user->typesCount() == 1 && $user->hasEmployee()) {
                $user->delete();
            }
            $employee->delete();

            return response()->json(['msg' => 'Funcionário deletado.']);
        } catch (ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new EmployeeNotFoundException()), 400);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function validateStore(StoreEmployee $request): JsonResponse
    {
        $user = User::where('cpf', $request->cpf)->first();
        if ($user && $user->hasEmployee()) {
            return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro funcionário']], 422);
        }

        $user = User::where('email', $request->email)
            ->whereNotNull('cpf')
            ->first();
        if ($user && $user->cpf != $request->cpf) {
            return response()->json(['errors' => ['email' => 'Email já cadastrado para outro funcionário']], 422);
        }

        return response()->json(['success' => true]);
    }

    public function validateUpdate(UpdateEmployee $request, $id): JsonResponse
    {
        $employee = Employee::findOrFail($id);
        if ($employee != null) {
            if ($employee->user->cpf != $request->cpf) {
                $user = User::where('cpf', $request->cpf)->first();
                if ($user && $user->id != $employee->user->id) {
                    return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro funcionário']], 422);
                }
            }
            if ($employee->user->email != $request->email) {
                $user = User::where('email', $request->email)->first();
                if ($user && $user->id != $employee->user->id) {
                    return response()->json(['errors' => ['email' => 'Email já cadastrado para outro funcionário']], 422);
                }
            }
        }

        return response()->json(['success' => true]);
    }

    public function validateCpfExist(Request $request)
    {
        try {
            if ($request->cpf) {
                $userAux = User::where('cpf', $request->cpf)->first();
                if ($userAux && $userAux->id != $request->idUser) {
                    return response()->json(['exist' => true]);
                }
            }

            return response()->json(['exist' => false]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function validateEmailExist(Request $request)
    {
        try {
            $cpf = $request->cpf;
            $email = $request->email;
            $idEmployee = $request->idEmployee;
            $isCreateNewEmployeeAndUser = $request->isCreateNewEmployeeAndUser;
            $isEditEmployee = $request->isEditEmployee;
            $isCreateNewEmployee = $request->isCreateNewEmployee;

            if ($isCreateNewEmployeeAndUser) {
                $user = User::where('email', $email)->first();
                if ($user) {
                    return response()->json(['exist' => true]);
                }
                return response()->json(['exist' => false]);
            } elseif ($isEditEmployee) {
                $user = User::where('email', $email)->first();
                if ($user && $user->hasEmployee() && $user->employee->id != $idEmployee) {
                    return response()->json(['exist' => true]);
                }
                return response()->json(['exist' => false]);
            } elseif ($isCreateNewEmployee) {
                $user = User::where('email', $email)->first();
                if ($user && $user->cpf != $cpf) {
                    return response()->json(['exist' => true]);
                }
                return response()->json(['exist' => false]);
            }
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function validateEmailExistPublic(Request $request)
    {
        try {
            $email = $request->email;
            $user = User::where('email', $email)->first();
            if ($user) {
                return response()->json(['exist' => true]);
            }

            return response()->json(['exist' => false]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function toggleInformativeFlag(Request $request) {
        try {
            $user = Auth::user();
            $employee = $user->employee;

            if (!$employee)
                throw new Exception('Funcionário não encontrado.');
            $storedValue = filter_var($employee->is_informative_read, FILTER_VALIDATE_BOOLEAN);
            $employee->is_informative_read = !$storedValue;
            $employee->save();
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }
}
