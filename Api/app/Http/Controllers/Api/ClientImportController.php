<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessClientImportBatch;
use App\Jobs\ProcessIncompleteClientImportBatch;
use App\Models\ClientImportBatch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;

class ClientImportController extends Controller
{
    public function store(Request $request)
    {
        try {
            Log::info('Iniciando importação de clientes', [
                'request_data' => $request->except(['file'])
            ]);

            // Lista de tipos MIME permitidos
            $allowedMimes = [
                'text/csv',
                'text/plain',
                'application/csv',
                'application/vnd.ms-excel',
                'application/excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];

            Log::info('Validando dados do arquivo', [
                'file_name' => $request->file_name,
                'file_type' => $request->file_type
            ]);

            // Validação dos dados
            $validator = Validator::make($request->all(), [
                'file' => 'required|string',
                'file_name' => 'required|string',
                'file_type' => 'required|string',
                'mapping' => 'required|array',
                'has_headers' => 'boolean',
                'separator' => 'string|max:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Dados inválidos',
                    'errors' => $validator->errors()
                ], 422);
            }

            if (!$request->boolean('has_headers', true)) {
                foreach ($request->mapping as $field => $columnIndex) {
                    if (!is_numeric($columnIndex)) {
                        return response()->json([
                            'message' => 'Mapeamento inválido',
                            'errors' => [
                                'mapping' => ["O índice da coluna para o campo '$field' deve ser numérico quando não há headers"]
                            ]
                        ], 422);
                    }

                    if ($columnIndex < 0) {
                        return response()->json([
                            'message' => 'Mapeamento inválido',
                            'errors' => [
                                'mapping' => ["O índice da coluna para o campo '$field' não pode ser negativo"]
                            ]
                        ], 422);
                    }
                }
            }

            // Decodifica o arquivo base64
            try {
                $fileContent = base64_decode($request->file);
                if ($fileContent === false) {
                    throw new \Exception('Arquivo base64 inválido');
                }
            } catch (\Exception $e) {
                Log::error('Erro ao decodificar base64', ['error' => $e->getMessage()]);
                throw new \Exception('Erro ao processar arquivo: ' . $e->getMessage());
            }

            // Cria arquivo temporário
            $tempFile = tempnam(sys_get_temp_dir(), 'import_');
            file_put_contents($tempFile, $fileContent);

            // Valida o tipo do arquivo
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $tempFile);
            finfo_close($finfo);

            if (!in_array($mimeType, $allowedMimes) &&
                !in_array(strtolower(pathinfo($request->file_name, PATHINFO_EXTENSION)), ['csv', 'xlsx', 'xls'])) {
                unlink($tempFile);
                throw new \Exception('Tipo de arquivo não permitido');
            }

            // Define o caminho do arquivo
            $fileName = $request->file_name;
            $path = 'imports/clients/' . now()->format('Y/m/d') . '/' . uniqid() . '_' . $fileName;

            // Faz upload do arquivo temporário para o S3
            try {
                Log::info('Iniciando upload para S3', [
                    'path' => $path,
                    'mime_type' => $mimeType
                ]);

                $uploaded = Storage::disk('s3')->put(
                    $path,
                    file_get_contents($tempFile)
                );

                // Remove o arquivo temporário
                unlink($tempFile);

                if (!$uploaded) {
                    throw new \Exception('Falha ao fazer upload do arquivo para o S3');
                }

                Log::info('Upload para S3 concluído com sucesso');
            } catch (\Exception $e) {
                // Garante que o arquivo temporário seja removido em caso de erro
                if (file_exists($tempFile)) {
                    unlink($tempFile);
                }

                Log::error('Erro no upload para S3', [
                    'error' => $e->getMessage(),
                    'path' => $path
                ]);
                throw new \Exception('Erro no upload para S3: ' . $e->getMessage());
            }

            $mapping = $request->mapping;

            // Obtém o ID do usuário e do negócio selecionado
            $employee = auth()->user()->employee;
            $employeeId = $employee->id;
            $businessId = $employee->business_selected_id;

            if (!$businessId) {
                throw new \Exception('Você deve selecionar um negócio antes de importar clientes.');
            }

            // Cria o registro do lote com as configurações
            $batch = ClientImportBatch::create([
                'file_path' => $path,
                'file_name' => $fileName,
                'status' => ClientImportBatch::STATUS_PENDING,
                'created_by_id' => $employeeId,
                'business_id' => $businessId,
                'config' => [
                    'mapping' => $mapping,
                    'has_headers' => $request->boolean('has_headers', true),
                    'separator' => $request->input('separator', ',')
                ]
            ]);

            // Usa a configuração do batch
            $config = $batch->config;

            // Dispara o job para processar o lote
            ProcessClientImportBatch::dispatch($batch, $config);

            return response()->json([
                'message' => 'Importação iniciada com sucesso',
                'batch_id' => $batch->id
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao processar importação de clientes', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            $errorDetails = [
                'message' => 'Erro ao iniciar importação',
                'error' => $e->getMessage()
            ];


            $errorDetails['debug'] = [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'class' => get_class($e)
            ];


            return response()->json($errorDetails, 422);
        }
    }

    public function storeIncomplete(Request $request)
    {
        try {
            Log::info('Iniciando importação de clientes (INCOMPLETA)', [
                'request_data' => $request->except(['file'])
            ]);

            // Lista de tipos MIME permitidos
            $allowedMimes = [
                'text/csv',
                'text/plain',
                'application/csv',
                'application/vnd.ms-excel',
                'application/excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];

            Log::info('Validando dados do arquivo', [
                'file_name' => $request->file_name,
                'file_type' => $request->file_type
            ]);

            // Validação dos dados
            $validator = Validator::make($request->all(), [
                'file' => 'required|string',
                'file_name' => 'required|string',
                'file_type' => 'required|string',
                'mapping' => 'required|array',
                'has_headers' => 'boolean',
                'separator' => 'string|max:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Dados inválidos',
                    'errors' => $validator->errors()
                ], 422);
            }

            if (!$request->boolean('has_headers', true)) {
                foreach ($request->mapping as $field => $columnIndex) {
                    if (!is_numeric($columnIndex)) {
                        return response()->json([
                            'message' => 'Mapeamento inválido',
                            'errors' => [
                                'mapping' => ["O índice da coluna para o campo '$field' deve ser numérico quando não há headers"]
                            ]
                        ], 422);
                    }

                    if ($columnIndex < 0) {
                        return response()->json([
                            'message' => 'Mapeamento inválido',
                            'errors' => [
                                'mapping' => ["O índice da coluna para o campo '$field' não pode ser negativo"]
                            ]
                        ], 422);
                    }
                }
            }

            // Decodifica o arquivo base64
            try {
                $fileContent = base64_decode($request->file);
                if ($fileContent === false) {
                    throw new \Exception('Arquivo base64 inválido');
                }
            } catch (\Exception $e) {
                Log::error('Erro ao decodificar base64', ['error' => $e->getMessage()]);
                throw new \Exception('Erro ao processar arquivo: ' . $e->getMessage());
            }

            // Cria arquivo temporário
            $tempFile = tempnam(sys_get_temp_dir(), 'import_');
            file_put_contents($tempFile, $fileContent);

            // Valida o tipo do arquivo
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $tempFile);
            finfo_close($finfo);

            if (!in_array($mimeType, $allowedMimes) &&
                !in_array(strtolower(pathinfo($request->file_name, PATHINFO_EXTENSION)), ['csv', 'xlsx', 'xls'])) {
                unlink($tempFile);
                throw new \Exception('Tipo de arquivo não permitido');
            }

            // Define o caminho do arquivo
            $fileName = $request->file_name;
            $path = 'imports/clients/' . now()->format('Y/m/d') . '/' . uniqid() . '_' . $fileName;

            // Faz upload do arquivo temporário para o S3
            try {
                Log::info('Iniciando upload para S3', [
                    'path' => $path,
                    'mime_type' => $mimeType
                ]);

                $uploaded = Storage::disk('s3')->put(
                    $path,
                    file_get_contents($tempFile)
                );

                // Remove o arquivo temporário
                unlink($tempFile);

                if (!$uploaded) {
                    throw new \Exception('Falha ao fazer upload do arquivo para o S3');
                }

                Log::info('Upload para S3 concluído com sucesso');
            } catch (\Exception $e) {
                // Garante que o arquivo temporário seja removido em caso de erro
                if (file_exists($tempFile)) {
                    unlink($tempFile);
                }

                Log::error('Erro no upload para S3', [
                    'error' => $e->getMessage(),
                    'path' => $path
                ]);
                throw new \Exception('Erro no upload para S3: ' . $e->getMessage());
            }

            $mapping = $request->mapping;

            // Obtém o ID do usuário e do negócio selecionado
            $employee = auth()->user()->employee;
            $employeeId = $employee->id;
            $businessId = $employee->business_selected_id;

            if (!$businessId) {
                throw new \Exception('Você deve selecionar um negócio antes de importar clientes.');
            }

            // Cria o registro do lote com as configurações
            $batch = ClientImportBatch::create([
                'file_path' => $path,
                'file_name' => $fileName,
                'status' => ClientImportBatch::STATUS_PENDING,
                'created_by_id' => $employeeId,
                'business_id' => $businessId,
                'config' => [
                    'mapping' => $mapping,
                    'has_headers' => $request->boolean('has_headers', true),
                    'separator' => $request->input('separator', ',')
                ]
            ]);

            // Usa a configuração do batch
            $config = $batch->config;

            // Dispara o job para processar o lote
            ProcessIncompleteClientImportBatch::dispatch($batch, $config);

            return response()->json([
                'message' => 'Importação iniciada com sucesso',
                'batch_id' => $batch->id
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao processar importação de clientes', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            $errorDetails = [
                'message' => 'Erro ao iniciar importação',
                'error' => $e->getMessage()
            ];


            $errorDetails['debug'] = [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'class' => get_class($e)
            ];


            return response()->json($errorDetails, 422);
        }
    }

    public function show($id)
    {
        try {
            $batch = ClientImportBatch::with(['items' => function ($query) {
                $query->select('id', 'batch_id', 'raw_data', 'status', 'error_message', 'row_number', 'client_id')
                    ->orderBy('row_number')->with('client');
            },
            ])->findOrFail($id);

            return response()->json([
                'batch' => $batch,
                'items' => $batch->items
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erro ao buscar detalhes da importação',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    public function index()
    {
        try {
            $businessId = auth()->user()->employee->business_selected_id;

            $query = ClientImportBatch::query()
                ->where('business_id', $businessId);

            // Filtro por nome do arquivo
            if ($fileName = request('file_name')) {
                $query->where('file_name', 'like', '%' . $fileName . '%');
            }

            // Filtro por data
            if ($startDate = request('start_date')) {
                $query->whereDate('created_at', '>=', $startDate);
            }

            if ($endDate = request('end_date')) {
                $query->whereDate('created_at', '<=', $endDate);
            } else {
                // Por padrão, filtrar últimos 30 dias se não houver data final
                if (!request('start_date')) {
                    $query->whereDate('created_at', '>=', now()->subDays(30));
                }
            }

            $batches = $query->orderBy('created_at', 'desc')
                ->paginate(request('per_page', 10));

            return response()->json($batches);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Erro ao listar importações',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getDownloadUrl($id)
    {
        try {
            $batch = ClientImportBatch::findOrFail($id);
            
            if (!Storage::disk('s3')->exists($batch->file_path)) {
                return response()->json([
                    'message' => 'Arquivo não encontrado no S3'
                ], 404);
            }

            $url = Storage::disk('s3')->temporaryUrl(
                $batch->file_path,
                now()->addHour(),
                [
                    'ResponseContentDisposition' => 'attachment; filename="' . $batch->file_name . '"'
                ]
            );

            return response()->json(['url' => $url]);

        } catch (\Exception $e) {
            Log::error('Erro ao gerar URL de download', [
                'error' => $e->getMessage(),
                'batch_id' => $id
            ]);

            return response()->json([
                'message' => 'Erro ao gerar URL de download',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
