<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\MaisLuzExceptions\NoRequestFound;
use App\Helpers\SystemHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SiteNotifications\MarkAsReadRequest;
use App\Http\Requests\Api\StoreSiteNotificationRequest;
use App\Models\SiteNotification;
use App\Models\User;
use App\Models\UserSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

class SiteNotificationController extends Controller
{
    // Listar as notificações do negócio
    public function index(Request $request) {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $user = $request->user();
        $canSeeSellers = $user->employee->authorizedFunction->sellers ?? false;
        $selectedBusinessId = $user->employee->business_selected_id ?? null;

        try {            
            $notificationsQuery = SiteNotification::query();
            if ($selectedBusinessId) {
                $notificationsQuery->where('business_id', $selectedBusinessId);
            } elseif (!$selectedBusinessId) 
                throw new Exception('Você precisa selecionar um negócio!');
            
            if (!$canSeeSellers) {
                $notificationsQuery->whereHas('userSiteNotifications', function ($q) {
                    $q->where('type', '<>', 'seller');
                });
            }
            $notificationsQuery->when($search, function($query) use($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%$search%")
                      ->orWhere('description', 'like', "%$search%");
                });
            });
            $notificationsQuery->orderByDesc('created_at');
            $notifications = $perPage <= 0 ? 
                $notificationsQuery->get() : $notificationsQuery->paginate($perPage);
            $notifications->load(['business']);
            return response()->json(['notifications' => $notifications]);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function validateStore(StoreSiteNotificationRequest $request) {
        return response()->json(['success' => true]);
    }

    public function store(StoreSiteNotificationRequest $request)
    {
        $data = $request->all();
        $user = $request->user();
        $usersIds = $request->input('userIds', []);

        try {
            $selectedBusinessId = $user->employee->business_selected_id ?? null;
            $canReleaseAllBusiness = $user->employee->authorizedFunction->release_all_business || false;

            if (!$selectedBusinessId && !$canReleaseAllBusiness) 
                throw new Exception("Você deve selecionar um negócio!");

            $users_ids = $usersIds;
            
            if (empty($users_ids)) 
                throw new Exception('Pelo menos um usuário deve ser listado!');

            $userSiteNotifications = [];
            foreach ($users_ids as $userId) {
                $user = User::findOrFail($userId);
                $userSiteNotifications[$userId] = [
                    'type' => $user->getUserPriorityType(),
                    'user_id' => $userId
                ];
            }
            $data['business_id'] = $selectedBusinessId;
            DB::beginTransaction();
            $notification = SiteNotification::create($data);
            if ($request->logo) {
                $logobase64 = $request->logo;
                $image = base64_decode(
                    preg_replace("#^data:image/\w+;base64,#i", "", $logobase64)
                );
                $file_name = $request->logo_file;
                $file_path = "Visao_Negocio/images/siteNotification/$notification->id/$file_name";
                Storage::disk("s3")->put($file_path, $image);
                $notification->icon = $file_path;
            }
            $notification->save();
            $notification->userSiteNotifications()->createMany($userSiteNotifications);
            DB::commit();
            return response()->json(['notification' => $notification], 201);
        } catch(ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 400);
        } catch(Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    public function getUserNotifications(Request $request) {
        try {
            $perPage = $request->input('per_page', 10);
            $search = $request->input('search', '');
            $page = $request->input('page', 1);
            $user = $request->user();
            $viewed = $request->input('viewed', 'all');
            $markAsRead = filter_var($request->input('markAsRead', false), FILTER_VALIDATE_BOOLEAN);

            $notifications = UserSiteNotification::with('siteNotification')
            ->where('user_id', $user->id)
            ->when($search, function ($query) use ($search) {
                $query->whereHas('siteNotification', function ($q) use ($search) {
                    $q->where('title', 'like', "%$search%")
                        ->orWhere('description', 'like', "%$search%");
                });
            })
            ->when($viewed !== 'all', function ($query) use ($viewed) {
                $viewed = filter_var($viewed, FILTER_VALIDATE_BOOLEAN);
                $query->where('viewed', $viewed);
            })
            ->orderByDesc('created_at')
            ->paginate($perPage, ['*'], 'page', $page);
            if ($markAsRead) {
                $unviewedNotifications = collect($notifications->items())->filter(function ($notification) {
                    return $notification->viewed == false;
                });
    
                if ($unviewedNotifications->isNotEmpty()) {
                    UserSiteNotification::whereIn('id', $unviewedNotifications->pluck('id'))
                        ->update(['viewed' => true]);
                }
            }
            return response()->json(['notifications' => $notifications]);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    public function getUnreadCount (Request $request) {
        try {
            $user = $request->user();
            $notifications = UserSiteNotification::query()
            ->where('user_id', $user->id)
            ->where('viewed', false)
            ->orderByDesc('created_at')
            ->count();
            return response()->json(['count' => $notifications]);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    public function markAsRead(MarkAsReadRequest $request) {
        try {
            $user = $request->user();
            $ids = $request->input('ids', []);

            if (count($ids) === 0)
                throw new NotFoundResourceException('Nenhuma notificação encontrada');
            UserSiteNotification::where('user_id', $user->id)
                ->withTrashed()
                ->whereIn('id', $ids)
                ->update(['viewed' => true, 'viewed_at' => Carbon::now()]);

            return response()->json(['msg' => "Mensagens marcadas como visualizadas!"]);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    public function deleteUserNotification(Request $request, $id) {
        try {
            $user = $request->user();
            $notification = UserSiteNotification::where('id', $id,)
                            ->where('user_id', $user->id)
                            ->firstOrFail();
            $notification->delete();
            return response()->json(['msg' => "Notificação deletada"]);
        } catch(ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 400);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    public function getUsersByNotification(Request $request, $id) {
        try {
            $user = $request->user();
            $perPage = $request->input("per_page", 5);
            $search = $request->input('search', null);
            $selectedBusinessId = $user->employee->business_selected_id ?? null;
            $notification = SiteNotification::findOrFail($id);
            $userNotifications = $notification->userSiteNotifications()
                                ->withTrashed()
                                ->with('user');
            $canSeeSellers = $user->employee->authorizedFunction->sellers ?? false;
            $canReleaseAllBusiness = $user->employee->authorizedFunction->release_all_business ?? false;
            
            if (!isset($notification->business) && !$canReleaseAllBusiness) 
                throw new UnauthorizedException('Você não possui permissão!');

            if (isset($notification->business) && $notification->business->id !== $selectedBusinessId && !$canReleaseAllBusiness)
                throw new UnauthorizedException('Você não possui permissão!');

            if ($search) {
                $cpf = preg_replace('/[^0-9]/', '', $search);
                $userNotifications->whereHas('user', function ($query) use ($cpf, $search) {
                    if (preg_match('/^\d{1,11}$/', $cpf)) {
                        $query->where('cpf', 'like', "$cpf%");
                    } else {
                        $query->where('name', 'like', "%$search%");
                    }
                });
            }
            
            if (!$canSeeSellers) {
                $userNotifications = $userNotifications->where('type', '<>', 'seller');
            }

            $userNotifications = $perPage <= 0 ? $userNotifications->get() : $userNotifications->paginate($perPage);

            return response()->json(['notifications' => $userNotifications]);
        } catch(ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 400);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    public function show(Request $request, $id) {
        try {
            $user = $request->user();
            $selectedBusinessId = $user->employee->business_selected_id ?? null;
            $notification = SiteNotification::findOrFail($id);
            $canReleaseAllBusiness = $user->employee->authorizedFunction->release_all_business ?? false;
            
            if (!isset($notification->business) && !$canReleaseAllBusiness) 
                throw new UnauthorizedException('Você não possui permissão!');

            if (isset($notification->business) && $notification->business->id !== $selectedBusinessId && !$canReleaseAllBusiness)
                throw new UnauthorizedException('Você não possui permissão!');

            return response()->json(['notification' => $notification]);
        } catch(ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 400);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    // @DEPRECATED
    // public function getUsers(Request $request) {
    //     try {
    //         $user = $request->user();
    //         $employee = $user->employee;
    //         $business_selected_id = $employee->business_selected_id;
    //         $canReleaseAllBusiness = $user->employee->authorizedFunction->release_all_business ?? false;
    //         $canSeeSellers = $user->employee->authorizedFunction->sellers ?? false;
    //         $search = $request->search;
    //         $userProfile = $request->input('profile', 'all');
    //         $isAdmin = $request->input('isAdmin', 'all');
    //         $isEntrepreneur = $request->input('isEntrepreneur', 'all');
    //         $searchBusiness = $request->input('searchBusiness', null);
    //         $per_page = $request->per_page ?? 5;
    //         $exceptId = auth('api')->user()->id ?? 0;
    //         $query = User::where('id', '<>', $exceptId);
    //         $adminBusiness = (int) SystemHelper::findInArrayByCodeName(SystemHelper::get(),'ADMIN_BUSINESS_ID');
    //         $isAdminBusinessSelected = $business_selected_id === $adminBusiness;

    //         if ($search) {
    //             $cpf = preg_replace('/[^0-9]/', '', $search);
    //             if (preg_match('/^\d{1,11}$/', $cpf))
    //                 $query->where('users.cpf', 'like', "$cpf%");
    //             else
    //                 $query->where(function($q) use($search) {
    //                     $q->where('users.name', 'like', "%$search%")
    //                         ->orWhere('users.email', 'like', "%$search%");
    //                 });
    //         }

    //         // o filtro de CNPJ/Nome do negócio só é permitido na visão do negócio ADMIN
    //         if ($searchBusiness) {
    //             if (!$canReleaseAllBusiness || !$isAdminBusinessSelected)
    //                 throw new UnauthorizedException('Você não possui permissão');

    //             $cnpj = preg_replace('/[^0-9]/', '', $searchBusiness);
    //             if (preg_match('/^\d{1,14}$/', $cnpj))
    //                 $query->where(function ($q) use ($cnpj) {
    //                     $q->whereHas('employee.businesses', function ($query) use($cnpj) {
    //                         $query->where('businesses.cnpj', 'like', "$cnpj%");
    //                     })->orWhereHas('client.businesses', function ($query) use($cnpj) {
    //                         $query->where('businesses.cnpj', 'like', "$cnpj%");
    //                     });
    //                 });
    //             else $query->where(function ($q) use ($searchBusiness) {
    //                 $q->whereHas('employee.businesses', function ($query) use($searchBusiness) {
    //                     $query->where('businesses.name','like', "%$searchBusiness%");
    //                 })->orWhereHas('client.businesses', function ($query) use($searchBusiness) {
    //                     $query->where('businesses.name', 'like', "%$searchBusiness%");
    //                 });
    //             });
    //         }


    //         switch($userProfile) {
    //             case 'client':
    //                 $query->whereHas('client')
    //                     ->whereDoesntHave('employee')
    //                     ->whereDoesntHave('seller');
    //             break;
    //             case 'employee':
    //                 $query->whereHas('employee')
    //                     ->whereDoesntHave('client')
    //                     ->whereDoesntHave('seller');
    //             break;
    //             case 'seller':
    //                 if (!$canSeeSellers)
    //                     throw new UnauthorizedException('Você não possui permissão.');
    //                 $query->whereHas('seller')
    //                     ->whereDoesntHave('employee')
    //                     ->whereDoesntHave('client');
    //             break;
    //             default:
    //             break;
    //         }

    //         if ($employee->admin || $employee->authorizedFunction->release_all_business) {
    //             if ($business_selected_id && !$isAdminBusinessSelected) {
    //                 $query->where(function ($q) use ($business_selected_id, $canSeeSellers) {
    //                     $q->whereHas('employee.businesses', function ($query) use ($business_selected_id) {
    //                         $query->where('businesses.id', $business_selected_id);
    //                     })->orWhereHas('client.businesses', function ($query) use ($business_selected_id) {
    //                         $query->where('businesses.id', $business_selected_id);
    //                     });

    //                     if ($canSeeSellers)
    //                         $q->orWhereHas('seller');
    //                 });

    //             }
    //         } else {
    //             $query->where(function ($q) use ($business_selected_id, $canSeeSellers) {
    //                 $q->whereHas('employee.businesses', function ($query) use ($business_selected_id) {
    //                     $query->where('businesses.id', $business_selected_id);
    //                 })->orWhereHas('client.businesses', function ($query) use ($business_selected_id) {
    //                     $query->where('businesses.id', $business_selected_id);
    //                 });

    //                 if ($canSeeSellers)
    //                     $q->orWhereHas('seller');
    //             });
    //         }

    //         if ($isAdmin !== 'all' || $isEntrepreneur !== 'all') {
    //             if (!$canReleaseAllBusiness)
    //                 throw new UnauthorizedException('Você não tem permissão.');

    //             if ($isAdmin === 'yes') {
    //                 // Verifica se existe pelo menos um relacionamento que satisfaça
    //                 $query->whereHas('employee.businesses', function($q) use($business_selected_id, $isAdminBusinessSelected, $isAdmin) {
    //                     if (!$isAdminBusinessSelected && $business_selected_id) {
    //                         $q->where('businesses.id', $business_selected_id);
    //                     }

    //                     $q->where('employee_business.admin', true);
    //                 });
    //             } elseif ($isAdmin === 'no') {
    //                 // Verifica se o funcionário não é admin de NENHUM negócio
    //                 $query->whereDoesntHave('employee.businesses', function($q) use($business_selected_id, $isAdminBusinessSelected, $isAdmin) {
    //                     if (!$isAdminBusinessSelected && $business_selected_id) {
    //                         $q->where('businesses.id', $business_selected_id);
    //                     }
    //                     $q->where('employee_business.admin', true);
    //                 });
    //             }

    //             if ($isEntrepreneur === 'yes') {
    //                 $query->where(function($q) use($business_selected_id, $isAdminBusinessSelected) {
    //                     // se o cliente for também funcionário e for administrador de algum negócio, ele também deve ser considerado Empreendedor
    //                     $q->whereHas('employee.businesses', function($q) use($business_selected_id, $isAdminBusinessSelected) {
    //                         if(!$isAdminBusinessSelected && $business_selected_id)
    //                             $q->where('businesses.id', $business_selected_id);
    //                         $q->where('employee_business.admin', true);
    //                     })->orWhereHas('client', function($q) {
    //                         $q->where('is_entrepreneur', true);
    //                     });
    //                 });
    //             } elseif ($isEntrepreneur === 'no') {
    //                 // Usuário não pode ser admin e nem empreendedor na tabela de cliente
    //                 $query->where(function($q) use($business_selected_id, $isAdminBusinessSelected) {
    //                     $q->whereDoesntHave('employee.businesses', function($q) use($business_selected_id, $isAdminBusinessSelected) {
    //                         if (!$isAdminBusinessSelected && $business_selected_id) {
    //                             $q->where('businesses.id', $business_selected_id);
    //                         }
    //                         $q->where('employee_business.admin', true);
    //                     })->whereDoesntHave('client', function ($q) {
    //                         $q->where('is_entrepreneur', true);
    //                     });
    //                 });
    //             }
    //         }


    //         $users = $per_page <= 0 ? $query->get() : $query->paginate($per_page);
    //         $users->load(['employee.businesses', 'client.businesses']);

    //         if ($isAdmin !== 'all' || $isEntrepreneur !== 'all') {
    //             if (!$canReleaseAllBusiness)
    //                 throw new UnauthorizedException('Você não tem permissão.');

    //             $users->transform(function ($user) use (
    //                 $business_selected_id, 
    //                 $isAdmin,
    //                 $isEntrepreneur,
    //                 $isAdminBusinessSelected) 
    //             {
    //                 $employee = optional($user)->employee;
    //                 $client = optional($user)->client;

    //                 if ($isAdmin === 'all') {
    //                     $user['is_admin_of_business'] = $this->isEmployeeAdminOfBusiness(
    //                         $employee, 
    //                         $business_selected_id, 
    //                         $isAdminBusinessSelected
    //                     );
    //                 }else $user['is_admin_of_business'] = $isAdmin === 'yes';

    //                 if ($isEntrepreneur === 'all') {
    //                     if ($employee && $business_selected_id) {
    //                         if ($this->isEmployeeAdminOfBusiness(
    //                             $employee, 
    //                             $business_selected_id, 
    //                             $isAdminBusinessSelected
    //                         ))
    //                             $user['is_entrepreneur'] = true;
    //                     } else {
    //                         $user['is_entrepreneur'] = $client ? $client->is_entrepreneur : false;
    //                     }
    //                 } else $user['is_entrepreneur'] = $isEntrepreneur === 'yes';

    //                 return $user;
    //             });
    //         }

    //         return response()->json([
    //             'users' => $users, 
    //             'search' => $request->search, 
    //             'profile' => $request->profile, 
    //             'isAdmin' => $request->isAdmin, 
    //             'isEntrepreneuer' => $request->isEntrepreneuer, 
    //             'searchBusiness' => $request->searchBusiness, 
    //         ]);
    //     } catch (Exception $e) {
    //         return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
    //     }
    // }

    private function isEmployeeAdminOfBusiness($employee, $business_selected_id, $isAdminBusinessSelected) {
        if (!$employee) {
            return false;
        }
    
        $query = $employee->businesses();
        if ($business_selected_id && !$isAdminBusinessSelected) {
            $query->where('businesses.id', $business_selected_id);
        }
        return $query->wherePivot('admin', true)->exists();
    }
}
