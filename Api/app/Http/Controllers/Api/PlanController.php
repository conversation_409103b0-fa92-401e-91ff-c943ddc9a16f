<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Plans\StorePlanRequest;
use App\Http\Requests\Api\Plans\UpdatePlanRequest;
use App\Models\Plan;
use App\Services\PlanService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    public function __construct(private PlanService $plans)
    {

    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        return response()->json([
            'plans' => $this->plans->getAll($request->input('per_page', 10), $request->search),
        ]);
    }

    public function getActivePlans(Request $request): JsonResponse
    {
        return response()->json([
            'plans' => $this->plans->getActivePlans(
                $request->input('per_page', 0),
                $request->search,
                $request->page ?? 1
            ),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePlanRequest $request): JsonResponse
    {
        if (Plan::where('name', $request->name)->exists()) {
            return response()->json(['errors' => ['name' => ['Já existe um plano com esse nome.']]], 422);
        }

        $planDeleted = Plan::withTrashed()->where('name', $request->name)->first();
        if ($planDeleted) {
            $planDeleted->restore();
            $planDeleted->authorizedFunctions()->restore();
            $plan = $this->plans->update(
                $planDeleted,
                $request->name,
                $request->description,
                $request->value,
                $request->active,
                $request->publicly_visible,
                $request->payment_system_tag,
                $request->getPermissions()
            );
        } else {
            $plan = $this->plans->store(
                $request->name,
                $request->description,
                $request->value,
                $request->active,
                $request->publicly_visible,
                $request->payment_system_tag,
                $request->getPermissions()
            );
        }
        return response()->json($plan->toArray());
    }

    public function validateStore(StorePlanRequest $request)
    {
        try {
            $request->checkPermissions();
        } catch (Exception $e) {
            return response()->json(['errors' => ['employees' => ['Nenhuma permissão selecionada.']]], 422);
        }

        if (Plan::where('name', $request->name)->exists()) {
            return response()->json(['errors' => ['name' => ['Já existe um plano com esse nome.']]], 422);
        }

        return response()->json(['success' => true]);
    }

    public function show(Plan $plan)
    {
        return response()->json($plan->toArray());
    }


    public function update(UpdatePlanRequest $request, Plan $plan)
    {
        try {
            $request->checkPermissions();
        } catch (Exception $e) {
            return response()->json(['errors' => ['employees' => ['Nenhuma permissão selecionada.']]], 422);
        }

        if (Plan::where('name', $request->name)->where('id', '!=', $plan->id)->exists()) {
            return response()->json(['errors' => ['name' => ['Já existe um plano com esse nome.']]], 422);
        }

        $plan = $this->plans->update(
            $plan,
            $request->name,
            $request->description,
            $request->value,
            $request->active,
            $request->publicly_visible,
            $request->payment_system_tag,
            $request->getPermissions()
        );
        return response()->json($plan->toArray());
    }

    public function validateUpdate(UpdatePlanRequest $request, Plan $plan)
    {
        try {
            $request->checkPermissions();
        } catch (Exception $e) {
            return response()->json(['errors' => ['employees' => ['Nenhuma permissão selecionada.']]], 422);
        }

        if (Plan::where('name', $request->name)->where('id', '!=', $plan->id)->exists()) {
            return response()->json(['errors' => ['name' => ['Já existe um plano com esse nome.']]], 422);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plan $plan)
    {
        $this->plans->delete($plan);
        return response()->json($plan->toArray());
    }

    /**
     * Verifica se um plano pode ser excluído, checando vínculos com negócios ativos.
     *
     * @param Plan $plan
     * @return JsonResponse
     */
    public function checkDeletion(Plan $plan): JsonResponse
    {
        try {
            $activeBusinessesCount = $plan->businesses()->count();

            $can_delete = $activeBusinessesCount === 0;
            $message = $can_delete
                ? 'O plano pode ser excluído.'
                : 'Este plano não pode ser excluído pois está vinculado a negócios ativos.';

            return response()->json([
                'can_delete' => $can_delete,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'can_delete' => false,
                'message' => 'Erro ao verificar a possibilidade de exclusão do plano.',
                'exception' => get_class($e)
            ], 500);
        }
    }

    public function hasBusiness($id)
    {
        try {
            $plan = Plan::findOrFail($id);
            return response()->json(['hasBusiness' => $plan->businesses()->count() > 0]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function getByTag(string $tag): JsonResponse
    {
        if (empty($tag)) {
            return response()->json(['errors' => ['tag' => ['The tag field is required.']]], 422);
        }

        $plan = $this->plans->getByTag($tag);

        return response()->json(['plan' => $plan]);
    }
}
