<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ApiUser;
use App\Helpers\ApiWhatsapp;
use App\Helpers\SystemHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UpdatePassword;
use App\Http\Requests\Api\UpdateUser;
use App\Http\Requests\Api\UpdateUserLoginType;
use App\Http\Requests\Api\ValidateEmail;
use App\Http\Resources\Api\ClientResource;
use App\Http\Resources\Api\EmployeeResource;
use App\Http\Resources\Api\SellerResource;
use App\Http\Resources\Api\UserResource;
use App\Models\Business;
use App\Models\EmployeeBusiness;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\UnauthorizedException;

class UserController extends Controller
{
    public function showByCpf(Request $request)
    {
        try {
            $cpf = $request->cpf;

            $result = [
                'seller' => null,
                'client' => null,
                'employee' => null,
                'user' => null,
            ];

            $user = User::where('cpf', $cpf)->first();
            if ($user) {
                $result['user'] = UserResource::make($user);
                if ($user->hasEmployee()) {
                    $employee = $user->employee;
                    $result['employee'] = EmployeeResource::make($employee);
                }

                if ($user->hasSeller()) {
                    $result['seller'] = SellerResource::make($user->seller);
                }

                if ($user->hasClient()) {
                    $result['client'] = ClientResource::make($user->client);
                }
            }

            if ($result['user'] == null) {
                return response()->json([
                    'msg' => 'Usuário não encontrado.',
                ], 404);
            }

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Verifica publicamente se um usuário existe com base no CPF fornecido.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkCpfExists(Request $request)
    {
        try {
            $validated = $request->validate([
                'cpf' => 'required|string|digits:11', // Exemplo: Apenas dígitos, 11 caracteres
            ]);

            $cpf = $validated['cpf'];
            $result = [
                'seller' => null,
                'client' => null,
                'employee' => null,
                'user' => null,
                'isAdmin' => null,
            ];

            $user = User::where('cpf', $cpf)->first();
            if ($user) {
                $result['user'] = true;
                if ($user->hasEmployee()) {
                    $employee = $user->employee;
                    $result['employee'] = true;
                    $result['isAdmin'] = $employee->admin;
                }

                if ($user->hasSeller()) {
                    $result['seller'] = true;
                }

                if ($user->hasClient()) {
                    $result['client'] = true;
                }
            }

            return response()->json($result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error("Erro ao verificar CPF: " . $e->getMessage(), ['exception' => get_class($e)]);
            return response()->json(['msg' => 'Ocorreu um erro ao processar a solicitação.'], 500);
        }
    }

    public function checkCpf($cpf): JsonResponse
    {
        try {
            $cleanCpf = preg_replace('/\D/', '', $cpf);

            // Use withTrashed to include soft-deleted users
            $user = User::withTrashed()->where('cpf', $cleanCpf)->first();

            if (!$user) {
                return response()->json(['status' => 'not_found']);
            }

            // Prepare basic user data, regardless of status
            $userData = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'birth_date' => $user->birth_date,
                'gender' => $user->gender,
                'phone_number_1' => $user->phone_number_1,
                'phone_number_2' => $user->phone_number_2,
                // Add other fields as needed for pre-filling
            ];

            if ($user->trashed()) {
                // Return 'deleted' status along with user data
                return response()->json(['status' => 'deleted', 'userData' => $userData]);
            }

            // Determine the status based on existing profiles for active users
            $status = 'found_user'; // Default if user exists but no specific profile found yet
            if ($user->hasClient()) {
                $status = 'found_client';
            } elseif ($user->hasEmployee()) {
                $status = 'found_employee';
            } elseif ($user->hasSeller()) {
                $status = 'found_seller';
            }

            // Return the determined status and user data
            return response()->json(['status' => $status, 'userData' => $userData]);

        } catch (\Exception $e) {
            // Log the error for debugging purposes
            Log::error("Error checking CPF: " . $e->getMessage());
            return response()->json(['msg' => 'Erro ao verificar CPF.', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Verifica se o número de telefone já está associado a outro usuário.
     * Utilizado no form de funcionário.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkPhoneNumber(Request $request): JsonResponse
    {
        try {
            $phoneNumber = $request->phone_number;
            $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9

            $codes = substr($phoneNumber, 0, 2);
            $numberDigits = substr($phoneNumber, 2);
            if (strlen($numberDigits) === 9) {
                $phoneNumberV2 = $codes . substr($numberDigits, 1);
            } else if (strlen($numberDigits) === 8) {
                $phoneNumberV2 = $codes . '9' . $numberDigits;
            }

            $users = User::whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2])
                ->get();

            $result = $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'cpf_status' => $user->cpf === null ? 'incompleto' : 'completo',
                    'cpf' => $user->cpf,
                ];
            });

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error("Error checking phone number: " . $e->getMessage());
            return response()->json(['msg' => 'Erro ao verificar número de telefone.', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Verifica se o email já está associado a outro usuário.
     * Utilizado no form de funcionário.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkEmail(Request $request): JsonResponse
    {
        try {
            $email = $request->email;

            $user = User::where('email', $email)->first();

            if ($user) {
                return response()->json([
                    'id' => $user->id,
                    'cpf_status' => $user->cpf === null ? 'incompleto' : 'completo',
                    'cpf' => $user->cpf,
                ]);
            }

            return response()->json([
                'msg' => 'Email não encontrado.',
            ], 404);

        } catch (\Exception $e) {
            Log::error("Error checking email: " . $e->getMessage());
            return response()->json(['msg' => 'Erro ao verificar email.', 'error' => $e->getMessage()], 500);
        }
    }

    public function update(UpdateUser $request)
    {
        DB::beginTransaction();
        try {
            $user = Auth::user();
            $removeImgFlag = $request->input('avatar_remove', false);
            if ($request->has('avatar') && $request->avatar) {
                if ($user->avatar) {
                    Storage::disk("s3")->delete($user->avatar);
                }

                $avatarB64 = $request->avatar;
                $image = base64_decode(
                    preg_replace("#^data:image/\w+;base64,#i", "", $avatarB64)
                );
                $file_name = $request->avatar_file;
                $file_path = "Visao_Negocio/images/profile/{$user->id}/{$file_name}";

                // Salva a imagem no armazenamento (exemplo: S3)
                Storage::disk("s3")->put($file_path, $image);

                // Atualiza o campo 'avatar' do usuário com o caminho da imagem
                $user->avatar = $file_path;
            } elseif ($removeImgFlag) {
                if ($user->avatar) {
                    Storage::disk("s3")->delete($user->avatar);
                }
                $user->avatar = null;
            }

            $email_changed = $user->email != $request->email;
            $email_validated = $request->email_validated ?? false;

            if ($email_validated) {
                $user->email_verified_at = Carbon::now();
            } elseif ($email_changed) {
                $user->email_verified_at = null;
            }

            $user->fill($request->except(['password']));
            $user->save();

            if ($user->hasClient()) {
                $clientController = new ClientController();
                $clientController->consolidateIncompleteClientData($user->client);
            }

            DB::commit();
            return response()->json(['msg' => 'user updated!']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function updateLoginType(UpdateUserLoginType $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $type = $request->login_type;
            if ($user->login_type == $type) {
                return response()->json(['msg' => 'login type already updated!']);
            }
            if ($type == 'admin') {
                if (!$user->hasEmployee() || !$user->employee->admin) {
                    throw new Exception('Usuário não é administrador!');
                }
            } else if ($type == 'employee') {
                if (!$user->hasEmployee()) {
                    throw new Exception('Usuário não é funcionário!');
                }
            } else if ($type == 'seller') {
                if (!$user->hasSeller()) {
                    throw new Exception('Usuário não é vendedor!');
                }
            } else if ($type == 'client') {
                if (!$user->hasClient()) {
                    throw new Exception('Usuário não é cliente!');
                }
            }
            $user->login_type = $request->login_type;
            $user->save();

            return response()->json(['msg' => 'login type updated!']);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function validateUpdate(UpdateUser $request)
    {
        $user = Auth::user();
        if ($user->cpf != $request->cpf) {
            $userAux = User::where('cpf', $request->cpf)->first();
            if ($userAux) {
                return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro usuário']], 422);
            }
        }
        if ($user->email != $request->email) {
            $userAux = User::where('email', $request->email)
                ->whereNotNull('cpf')
                ->first();
            if ($userAux) {
                return response()->json(['errors' => ['email' => 'Email já cadastrado para outro usuário']], 422);
            }
        }
        try {
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function validateEmail(ValidateEmail $request): JsonResponse
    {
        try {
            $request_email = $request->email;

            $userAux = User::where('email', $request_email)->first();
            if ($userAux) {
                return response()->json(['errors' => ['email' => 'Email já cadastrado para outro usuário']], 422);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function updatePassword(UpdatePassword $request)
    {
        try {
            $user = Auth::user();
            if (!Hash::check($request->password, $user->password)) {
                throw new Exception('Senha atual incorreta!');
            }
            $new_password = Hash::make($request->new_password);
            $user->update(['password' => $new_password]);

            return response()->json(['msg' => 'Senha atualizada!']);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function updateEmailVerifiedAt(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $user->email_verified_at = now();
            $user->email = $request->email;
            $user->save();

            return response()->json(['msg' => 'email verified at updated!']);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public static function updateLastLogin($user): User
    {
        $user->last_login_at = now();
        $user->save();

        return $user;
    }

    public static function selectInitialBusinessForLogin($user): void
    {
        if ($user->hasEmployee() && Business::query()->count() > 0) {
            // Verifica se o negócio selecionado é null, deletado ou sem relação com o funcionário
            $selectedId = $user->employee->business_selected_id;
            $isBusinessSelectedDeleted = $selectedId && Business::withTrashed()->find($selectedId)->deleted_at != null;
            $hasAccess = $user->employee->authorizedFunction->release_all_business ? true : $user->employee->hasAccessToSelectedBusiness();
            if (!$selectedId || $isBusinessSelectedDeleted || !$hasAccess) {
                // Se deletado ou sem acesso, limpa seleção antiga
                if ($isBusinessSelectedDeleted || !$hasAccess) {
                    $user->employee->business_selected_id = null;
                    $user->employee->save();
                }
                if ($user->employee->authorizedFunction->release_all_business) {
                    $business = Business::query()
                        ->orderBy('created_at', 'desc')
                        ->orderBy('name', 'asc')
                        ->first();
                    $user->employee->business_selected_id = $business->id;
                    $user->employee->save();
                } else {
                    $businesses = $user->employee->businesses()
                        ->orderBy('name', 'asc')
                        ->get();
                    if ($businesses->count() > 0) {
                        $user->employee->business_selected_id = $businesses->first()->id;
                        $user->employee->save();
                    }
                }
            }
        }
    }

    public static function verifyUserTypes($user): void
    {
        if ($user->login_type != null) {
            //verificar se o tipo foi deletado e remove o login_type
            if ($user->login_type == 'admin') {
                if (!$user->hasEmployee() || !$user->employee->admin) {
                    $user->login_type = null;
                    $user->save();
                }
            } else if ($user->login_type == 'employee') {
                if (!$user->hasEmployee() || ($user->hasEmployee() && $user->employee->businesses()->count() == 0 && !$user->employee->authorizedFunction->release_all_business)) {
                    $user->login_type = null;
                    $user->save();
                }
            } else if ($user->login_type == 'seller') {
                if (!$user->hasSeller()) {
                    $user->login_type = null;
                    $user->save();
                }
            } else if ($user->login_type == 'client') {
                if (!$user->hasClient()) {
                    $user->login_type = null;
                    $user->save();
                }
            }
        }
        // Se o tipo não foi definido, verifica se o usuário tem algum tipo de perfil e atribui o tipo correspondente
        if ($user->login_type == null) {
            if ($user->hasEmployee() && $user->employee->businesses()->count() > 0) {
                if ($user->employee->admin) {
                    $user->login_type = 'admin';
                } else {
                    $user->login_type = 'employee';
                }
                $user->save();
            } else if ($user->hasSeller()) {
                $user->login_type = 'seller';
                $user->save();
            } else if ($user->hasClient()) {
                $user->login_type = 'client';
                $user->save();
            }
        }
    }

    public function validateClients(): JsonResponse
    {
        try {
            $userAuth = Auth::user();
            $selectedBusinessId = $userAuth->employee->business_selected_id;

            if (!$selectedBusinessId) {
                return response()->json([
                    'error' => true,
                    'msg' => 'Nenhum negócio selecionado'
                ], 400);
            }

            $clientCount = \App\Models\Client::query()
                ->whereHas('businesses', function ($q) use ($selectedBusinessId) {
                    $q->where('client_business.business_id', $selectedBusinessId);
                })
                ->count();

            return response()->json([
                'error' => false,
                'data' => [
                    'total_clients' => $clientCount,
                    'total_clients_plan' => 'unlimited'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'msg' => 'Erro ao validar clientes: ' . $e->getMessage()
            ], 400);
        }
    }

    public function validateEmployees(): JsonResponse
    {
        try {
            $userAuth = Auth::user();
            $selectedBusinessId = $userAuth->employee->business_selected_id;

            if (!$selectedBusinessId) {
                return response()->json([
                    'error' => true,
                    'msg' => 'Nenhum negócio selecionado'
                ], 400);
            }

            $plan = $userAuth->employee->businessSelected->plan;
            if (!$plan) {
                return response()->json([
                    'error' => true,
                    'msg' => 'Negócio não possui plano associado'
                ], 400);
            }

            $employeeCount = EmployeeBusiness::where('business_id', $selectedBusinessId)
                ->where('admin', false)
                ->count();
            $remainingEmployees = max(0, $plan->authorizedFunctions->total_employees - $employeeCount);

            return response()->json([
                'error' => false,
                'data' => [
                    'total_employees_plan' => $plan->authorizedFunctions->total_employees,
                    'total_employees' => $employeeCount,
                    'remaining_employees' => $remainingEmployees
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'msg' => 'Erro ao validar funcionários: ' . $e->getMessage()
            ], 400);
        }
    }

    public function getLoggedUser(): JsonResponse
    {
        $user = Auth::user();
        self::verifyUserTypes($user);
        if ($user->login_type == 'admin' || $user->login_type == 'employee') {
            return response()->json($user->employee->toArray());
        } elseif ($user->login_type == 'client') {
            return response()->json($user->client->toArray());
        } elseif ($user->login_type == 'seller') {
            return response()->json($user->seller->toArray());
        }
        return response()->json($user);
    }

    /**
     * Verifica se um usuário existe pelo email ou telefone e retorna o nome.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkUserByContact(Request $request): JsonResponse
    {
        $contactValue = $request->input('contact_value');
        $contactType = $request->input('contact_type'); // 'email' or 'phone'

        if (!$contactValue || !$contactType) {
            return response()->json(['error' => 'Parâmetros inválidos.'], 400);
        }

        try {
            $query = User::query();

            if ($contactType === 'email') {
                $query->where('email', $contactValue);
            } elseif ($contactType === 'phone') {
                $phoneNumber = preg_replace('/\D/', '', $contactValue);
                $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9

                $codes = substr($phoneNumber, 0, 2);
                $numberDigits = substr($phoneNumber, 2);
                if (strlen($numberDigits) === 9) {
                    $phoneNumberV2 = $codes . substr($numberDigits, 1);
                } else if (strlen($numberDigits) === 8) {
                    $phoneNumberV2 = $codes . '9' . $numberDigits;
                }

                $query->where(function ($q) use ($phoneNumber, $phoneNumberV2) {
                    $q->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
                });
            } else {
                return response()->json(['error' => 'Tipo de contato inválido.'], 400);
            }

            $user = $query->first();

            if ($user) {
                return response()->json([
                    'found' => true,
                    'name' => $user->name
                ]);
            } else {
                return response()->json(['found' => false]);
            }

        } catch (\Exception $e) {
            Log::error("Erro ao verificar contato do usuário: " . $e->getMessage());
            return response()->json(['error' => 'Erro interno ao verificar contato.'], 500);
        }
    }

    // Retorna todos os usuários com seus perfís associados do negócio cadastrado
    // No caso do negócio admin, retorna todos os usuários ATIVOS cadastrados
    public function getUsers(Request $request)
    {
        try {
            $userLogged = auth()->user();
            $employee = $userLogged->employee;
            $canReleaseAllBusiness = $employee->authorizedFunction->release_all_business ?? false;
            $business_selected_id = $employee->business_selected_id;
            $adminBusiness = (int) SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'ADMIN_BUSINESS_ID');
            $isAdminBusinessSelected = $business_selected_id === $adminBusiness;
            $perPage = $request->input('per_page', 10);
            $page = $request->input('page', 1);

            $filters = $request->only([
                'search',
                'profile',
                'isAdmin',
                'isEntrepreneur',
                'searchBusiness'
            ]);

            $userIds = User::getUserIdsFiltered($filters);

            $users = User::with(['employee.businesses', 'client', 'seller'])
                ->whereIn('id', $userIds)->orderBy('name')
                ->paginate($perPage, ['*'], 'page', $page);

            // Pega os parâmetros de novo
            $isAdmin = $filters['isAdmin'] ?? 'all';
            $isEntrepreneur = $filters['isEntrepreneur'] ?? 'all';

            // Aplica as flags de 'is_admin_of_business', 'is_entrepreneur' e 'isFromGetUsers'
            $users->transform(function ($user) use ($business_selected_id, $isAdmin, $isEntrepreneur, $isAdminBusinessSelected, $canReleaseAllBusiness) {
                if ($canReleaseAllBusiness) {
                    $employee = optional($user)->employee;
                    $client = optional($user)->client;

                    if ($isAdmin === 'all') {
                        $user['is_admin_of_business'] = User::isEmployeeAdminOfBusiness(
                            $employee,
                            $business_selected_id,
                            $isAdminBusinessSelected
                        );
                    } else {
                        $user['is_admin_of_business'] = $isAdmin === 'yes';
                    }

                    if ($isEntrepreneur === 'all') {
                        if ($employee && $business_selected_id) {
                            if (
                                User::isEmployeeAdminOfBusiness(
                                    $employee,
                                    $business_selected_id,
                                    $isAdminBusinessSelected
                                )
                            ) {
                                $user['is_entrepreneur'] = true;
                            } else {
                                $user['is_entrepreneur'] = $client ? $client->is_entrepreneur : false;
                            }
                        } else {
                            $user['is_entrepreneur'] = $client ? $client->is_entrepreneur : false;
                        }
                    } else {
                        $user['is_entrepreneur'] = $isEntrepreneur === 'yes';
                    }
                }

                $user['isFromGetUsers'] = true;
                return $user;
            });

            return response()->json([
                'users' => $users,
                'usersIds' => $userIds,
                'search' => $filters['search'] ?? null,
                'profile' => $filters['profile'] ?? null,
                'isAdmin' => $isAdmin,
                'isEntrepreneur' => $isEntrepreneur,
                'searchBusiness' => $filters['searchBusiness'] ?? null,
            ]);
        } catch (Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Inicia o processo de validação de telefone enviando um código via WhatsApp.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function startPhoneValidation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string|min:10|max:15', // Ajuste as regras conforme necessário
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $phoneNumber = $request->input('phone_number');
        $cleanedPhoneNumber = preg_replace('/\D/', '', $phoneNumber); // Limpa para usar no cache e envio

        try {
            // Gera um código de validação numérico aleatório de 6 dígitos
            $validationCode = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

            // Armazena o código no Cache do Laravel com tempo de expiração de 10 minutos
            Cache::put('phone_validation_code_' . $cleanedPhoneNumber, $validationCode, now()->addMinutes(10));

            // Obter a whatsapp_instance_key do negócio padrão
            $defaultBusinessId = (int)SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'ADMIN_BUSINESS_ID');
            if (!$defaultBusinessId) {
                Log::error('ADMIN_BUSINESS_ID não está configurado no sistema');
                return response()->json(['message' => 'Erro de configuração do servidor.'], 500);
            }

            $business = \App\Models\Business::find($defaultBusinessId);
            if (!$business || !$business->parameters) {
                Log::error("Negócio padrão com ID {$defaultBusinessId} não encontrado ou não possui parâmetros.");
                return response()->json(['message' => 'Erro de configuração do negócio padrão.'], 500);
            }

            $whatsappInstanceKey = $business->parameters->whatsapp_instance_key;
            if (empty($whatsappInstanceKey)) {
                Log::error("whatsapp_instance_key não configurada para o negócio padrão ID {$defaultBusinessId}.");
                return response()->json(['message' => 'Erro na configuração da instância do WhatsApp.'], 500);
            }

            // Envia o código de validação via WhatsApp usando a instanceKey obtida
            ApiWhatsapp::sendValidationCode($whatsappInstanceKey, $cleanedPhoneNumber, $validationCode);

            return response()->json(['message' => 'Código de validação enviado com sucesso.']);

        } catch (Exception $e) {
            if ($e->getMessage() == 'Número de telefone inválido, verifique o DDD.') {
                return response()->json(['message' => 'Número de telefone inválido, verifique o DDD.'], 422);
            }
            if ($e->getMessage() == 'Nenhum whatsapp encontrado para o número informado.') {
                return response()->json(['message' => 'Nenhum whatsapp encontrado para o número informado.'], 422);
            }
            Log::error("Erro ao iniciar validação de telefone para {$cleanedPhoneNumber}: " . $e->getMessage());
            return response()->json(['message' => 'Falha ao enviar código de validação.', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Verifica o código de validação e desassocia o telefone de outros usuários.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyPhoneValidationCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone_number' => 'required|string|min:10|max:15',
            'validation_code' => 'required|string|digits:6',
            'current_cpf' => 'required|string|digits:11',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $phoneNumber = $request->input('phone_number');
        $validationCode = $request->input('validation_code');
        $currentCpf = $request->input('current_cpf');

        $cleanedPhoneNumber = preg_replace('/\D/', '', $phoneNumber);

        try {
            $cachedCode = Cache::get('phone_validation_code_' . $cleanedPhoneNumber);

            if (!$cachedCode || $cachedCode !== $validationCode) {
                return response()->json(['message' => 'Código de validação inválido'], 422);
            }

            // Lógica de Desassociação
            $phoneNumberToDisassociate = $cleanedPhoneNumber;
            $phoneNumberToDisassociateV2 = ''; // Versão alternativa do número

            // Remove DDI se presente
            if (strpos($phoneNumberToDisassociate, '55') === 0 && strlen($phoneNumberToDisassociate) > 11) {
                $phoneNumberToDisassociate = substr($phoneNumberToDisassociate, 2);
            }

            $ddd = substr($phoneNumberToDisassociate, 0, 2);
            $numberDigits = substr($phoneNumberToDisassociate, 2);

            if (strlen($numberDigits) === 9) { // Se tem 9 dígitos (ex: 9xxxx-xxxx)
                $phoneNumberToDisassociateV2 = $ddd . substr($numberDigits, 1); // Remove o primeiro 9
            } elseif (strlen($numberDigits) === 8) { // Se tem 8 dígitos (ex: xxxx-xxxx)
                $phoneNumberToDisassociateV2 = $ddd . '9' . $numberDigits; // Adiciona o 9
            }

            $usersToUpdate = User::where(function ($query) use ($phoneNumberToDisassociate, $phoneNumberToDisassociateV2) {
                $query->where('phone_number_1', $phoneNumberToDisassociate)
                    ->orWhere('phone_number_1', $phoneNumberToDisassociateV2);
            })
                ->where('cpf', '!=', $currentCpf) // Não desassociar do usuário atual
                ->get();

            foreach ($usersToUpdate as $user) {
                $updated = false;
                if ($user->phone_number_1 === $phoneNumberToDisassociate || $user->phone_number_1 === $phoneNumberToDisassociateV2) {
                    $user->phone_number_1 = null;
                    $updated = true;
                }
                if ($updated) {
                    $user->save();
                }
            }

            Cache::forget('phone_validation_code_' . $cleanedPhoneNumber);

            return response()->json(['message' => 'Telefone validado e desassociado de outros usuários com sucesso.']);

        } catch (Exception $e) {
            Log::error("Erro ao verificar código de validação para {$cleanedPhoneNumber}: " . $e->getMessage());
            return response()->json(['message' => 'Falha ao verificar código de validação.', 'error' => $e->getMessage()], 500);
        }
    }
}
