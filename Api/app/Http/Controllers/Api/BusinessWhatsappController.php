<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Helpers\ApiWhatsapp;
use App\Models\Business;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BusinessWhatsappController extends Controller
{
    public function initInstance(Request $request, $id): JsonResponse
    {
        try {
            $business = Business::findOrFail($id);
            if ($business->parameters->whatsapp_instance_key) {
                try {
                    ApiWhatsapp::deleteInstance($business->parameters->whatsapp_instance_key);
                }catch (Exception $e){
                    Log::error('Erro ao deletar instância: '.$e->getMessage());
                }
            }
            $instanceData = ApiWhatsapp::initBusinessInstance($id);

            if (isset($instanceData['error']) && $instanceData['error']) {
                throw new Exception($instanceData['message'] ?? 'Erro ao inicializar instância');
            }

            // Salva a chave da instância nos parâmetros do negócio
            $business->parameters()->update([
                'whatsapp_instance_key' => $instanceData['key'],
                'whatsapp_phone_number' => null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Instância inicializada com sucesso',
                'data' => $instanceData
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function getQrCode(Request $request, $id): JsonResponse
    {
        try {
            $business = Business::findOrFail($id);
            $parameters = $business->parameters;

            if (!$parameters->whatsapp_instance_key) {
                throw new Exception('Instância não inicializada');
            }

            $qrcode = ApiWhatsapp::getInstanceQrCode($parameters->whatsapp_instance_key);

            return response()->json([
                'success' => true,
                'qrcode' => $qrcode
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function checkStatus(Request $request, $id): JsonResponse
    {
        try {
            $business = Business::findOrFail($id);
            $parameters = $business->parameters;

            if (!$parameters->whatsapp_instance_key) {
                throw new Exception('Instância não inicializada');
            }

            $status = ApiWhatsapp::checkInstanceStatus($parameters->whatsapp_instance_key);

            if (isset($status['error']) && $status['error']) {
                throw new Exception($status['message'] ?? 'Erro ao verificar status');
            }

            $isConnected = isset($status['instance_data']['phone_connected']) && $status['instance_data']['phone_connected'];
            $phoneNumber = null;

            if ($isConnected && isset($status['instance_data']['user']['id'])) {
                // Extrai apenas o número do formato ID (ex: 558788200443:<EMAIL>)
                $phoneNumber = explode(':', $status['instance_data']['user']['id'])[0];

                // Atualiza o número de telefone se mudou
                if ($phoneNumber !== $parameters->whatsapp_phone_number) {
                    $parameters->update([
                        'whatsapp_phone_number' => $phoneNumber
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'connected' => $isConnected,
                    'phone_number' => $phoneNumber,
                    'instance_data' => $status['instance_data']
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function disconnect(Request $request, $id): JsonResponse
    {
        try {
            $business = Business::findOrFail($id);
            $parameters = $business->parameters;

            if (!$parameters->whatsapp_instance_key) {
                throw new Exception('Instância não inicializada');
            }
            ApiWhatsapp::logoutInstance($parameters->whatsapp_instance_key);
            $result = ApiWhatsapp::deleteInstance($parameters->whatsapp_instance_key);

            if ($result['error'] ?? false) {
                throw new Exception($result['message'] ?? 'Erro ao desconectar instância');
            }

            // Limpa os dados da instância
            $parameters->update([
                'whatsapp_instance_key' => null,
                'whatsapp_phone_number' => null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'WhatsApp desconectado com sucesso'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
