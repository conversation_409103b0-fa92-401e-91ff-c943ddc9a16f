<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\MaisLuzExceptions\ClientNotFoundException;
use App\Exceptions\MaisLuzExceptions\CpfExistsException;
use App\Exceptions\MaisLuzExceptions\EmailExistsException;
use App\Helpers\SystemHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PublicStoreClientRequest;
use App\Http\Requests\Api\StoreClientIncompleteRequest;
use App\Http\Requests\Api\StoreClientRequest;
use App\Http\Requests\Api\UpdateClientRequest;
use App\Http\Resources\Api\ClientResource;
use App\Mail\CLientResetPasswordMail;
use App\Mail\CLientWelcomeMail;
use App\Models\Client;
use App\Models\ClientBusiness;
use App\Models\SystemLog;
use App\Models\User;
use Auth;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Models\Evaluation;
use App\Models\Cashback;
use App\Models\Employee;
use Illuminate\Support\Facades\Log;

class ClientController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        try {
            $userLogged = $request->user();
            $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
            $isAdmin = $userLogged->employee->admin;
            $userLoggedBusinessesIds = $userLogged->employee->businesses()->pluck('id')->toArray();
            $userLoggedSelectedBusinessId = $userLogged->employee->business_selected_id;
            $query = Client::query();
            $query->join('users', 'users.id', '=', 'clients.user_id')
                ->select('clients.*');

            if (!$canReleaseAllBusinesses && !$isAdmin) {
                if ($userLoggedSelectedBusinessId && !in_array($userLoggedSelectedBusinessId, $userLoggedBusinessesIds)) {
                    throw new Exception("Você não possui permissão.");
                }

                $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                    $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
                });
            } elseif ($userLoggedSelectedBusinessId) {
                $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                    $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
                });
            }

            if ($search) {
                $searchTerm = $search;
                $digitsOnly = preg_replace('/[^0-9]/', '', $searchTerm);

                $query->where(function ($q) use ($searchTerm, $digitsOnly) {
                    if (filter_var($searchTerm, FILTER_VALIDATE_EMAIL)) {
                        // Search by Email
                        $q->where('users.email', 'like', "%$searchTerm%");
                    } elseif (strlen($digitsOnly) === 11) {
                        $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                        $codes = substr($digitsOnly, 0, 2);
                        $numberDigits = substr($digitsOnly, 2);
                        if (strlen($numberDigits) === 9) {
                            $phoneNumberV2 = $codes . substr($numberDigits, 1);
                        } else if (strlen($numberDigits) === 8) {
                            $phoneNumberV2 = $codes . '9' . $numberDigits;
                        }

                        // Search by CPF (prioritized) OR 11-digit Phone
                        $q->where('users.cpf', 'like', "$digitsOnly%")
                          ->orWhere('users.phone_number_1', 'like', "%$digitsOnly%")
                          ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                    } elseif (strlen($digitsOnly) >= 8 && strlen($digitsOnly) <= 10) { // Ajustado para incluir 8, 9 e 10 dígitos
                        $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                        $codes = substr($digitsOnly, 0, 2);
                        $numberDigits = substr($digitsOnly, 2);
                        if (strlen($numberDigits) === 9) {
                            $phoneNumberV2 = $codes . substr($numberDigits, 1);
                        } else if (strlen($numberDigits) === 8) {
                            $phoneNumberV2 = $codes . '9' . $numberDigits;
                        }

                        // Search by 8, 9 or 10-digit Phone
                        $q->where('users.phone_number_1', 'like', "%$digitsOnly%")
                            ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                    } else {
                        // Search by Name as fallback
                        $q->where('users.name', 'like', "%$searchTerm%");
                    }
                });
            }

            $query->orderBy('users.name');
            $clients = $perPage <= 0 ? $query->get() : $query->paginate($perPage);
            $clients->load(['user', 'businesses' => function ($q) use ($userLoggedBusinessesIds, $canReleaseAllBusinesses, $isAdmin) {
                if (!$canReleaseAllBusinesses || !$isAdmin) {
                    $q->whereIn('client_business.business_id', $userLoggedBusinessesIds);
                }
            }]);
            return response()->json(['clients' => $clients]);
        } catch (Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreClientRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $businessesIds = $data['businessesIds'] ?? [];
            $cpf = $data['cpf'];
            $email = $data['email'];
            $is_entrepreneur = $data['is_entrepreneur'] ?? false;
            $userLogged = $request->user();
            $userLoggedBusinesses = $userLogged->employee->businesses()->pluck('id')->toArray();
            $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business ?? false;
            $isAdmin = $userLogged->employee->admin ?? false;

            // Verificação de permissão para os negócios selecionados
            if (empty($businessesIds) && (!$canReleaseAllBusinesses || !$isAdmin)) {
                throw new Exception("Nenhum negócio foi selecionado!");
            }

            if (!$canReleaseAllBusinesses || !$isAdmin) {
                foreach ($businessesIds as $businessId) {
                    if (!in_array($businessId, $userLoggedBusinesses)) {
                        throw new Exception("Você não possui permissão para associar o cliente ao negócio ID: $businessId");
                    }
                }
            }

            // Verificação de gênero
            if (isset($data['gender']) && !in_array($data['gender'], ['f', 'm'])) {
                $data['gender'] = null;
            }

            $shouldGeneratePassword = false;
            $randomPassword = null;

            // Busca usuário existente pelo CPF, incluindo deletados
            $existingUserByCpf = User::withTrashed()->where('cpf', $cpf)->first();

            // Busca usuário existente pelo e-mail
            $existingUserByEmail = User::where('email', $email)->first();

            // Verifica se o CPF já está associado a um cliente ativo
            if ($existingUserByCpf && !$existingUserByCpf->trashed() && $existingUserByCpf->hasClient()) {
                throw new Exception("Cliente com esse CPF já está cadastrado!");
            }

            $data['login_type'] = 'client';
            $email_changed = $existingUserByCpf && $existingUserByCpf->email != $email;

            if ($existingUserByCpf) {
                // Usuário existente
                if ($existingUserByCpf->trashed()) {
                    $existingUserByCpf->restore();
                    $shouldGeneratePassword = true;
                }

                // Atualiza os dados do usuário
                $existingUserByCpf->update($data);

                // Verifica se o cliente já existe, incluindo deletados
                $client = $existingUserByCpf->client()->withTrashed()->first();

                if ($client) {
                    if ($client->trashed()) {
                        $client->restore();
                        // Restaura associações de negócios
                        ClientBusiness::where('client_id', $client->id)->withTrashed()->forceDelete();
                    }
                } else {
                    // Cria o perfil de cliente
                    $client = $existingUserByCpf->client()->create(['is_entrepreneur' => $is_entrepreneur]);
                }
            } else {
                // Usuário não encontrado, cria um novo
                $shouldGeneratePassword = true;
                $randomPassword = Str::random(10);
                $data['password'] = bcrypt($randomPassword);

                $user = User::create($data);
                $client = $user->client()->create(['is_entrepreneur' => $is_entrepreneur]);
            }

            $email_validated = $data['email_validated'] ?? false;

            if ($email_validated) {
                $client->user->update(['email_verified_at' => now()]);
            } elseif ($email_changed) {
                $client->user->update(['email_verified_at' => null]);
            }

            if ($shouldGeneratePassword) {
                if (!$randomPassword) {
                    $randomPassword = Str::random(10);
                    $client->user->update(['password' => bcrypt($randomPassword)]);
                }
                Mail::to($client->user->email)->send(new CLientWelcomeMail($client->user, $randomPassword));
            }

            // Associa o cliente aos negócios selecionados
            $client->businesses()->attach($businessesIds);
            $client->businesses()->updateExistingPivot($businessesIds, ['created_at' => now(), 'updated_at' => now()]);

            DB::commit();

            // Após criar/atualizar o cliente com sucesso, tenta consolidar dados de clientes incompletos
            try {
                $this->consolidateIncompleteClientData($client);
            } catch (Exception $consolidationError) {
                Log::error("Erro ao consolidar dados de cliente incompleto para o cliente ID {$client->id}: " . $consolidationError->getMessage(), ['exception' => $consolidationError]);
            }

            return response()->json(ClientResource::make($client));
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    public function publicStore(PublicStoreClientRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $defaultBusinessId = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'ADMIN_BUSINESS_ID') ?? 3;

            $cpf = $data['cpf'];
            $email = $data['email'];
            $is_entrepreneur = $data['is_entrepreneur'] ?? false;

            // Verificação de gênero
            if (isset($data['gender']) && !in_array($data['gender'], ['f', 'm'])) {
                $data['gender'] = null;
            }

            $isCreateNewClientAndUser = false; // Inicializa a flag
            $randomPassword = null;

            // Busca usuário existente pelo CPF, incluindo deletados
            $existingUserByCpf = User::withTrashed()->where('cpf', $cpf)->first();

            // Busca usuário existente pelo e-mail
            $existingUserByEmail = User::where('email', $email)->first();

            // Verifica se o CPF já está associado a um cliente ativo
            if ($existingUserByCpf && !$existingUserByCpf->trashed() && $existingUserByCpf->hasClient()) {
                // Se existe mas não está no negócio, apenas associa
                $client = $existingUserByCpf->client;
                $isCreateNewClientAndUser = false; // Não é novo

                if ($client->trashed()) {
                    $client->restore();
                }
                // Atualiza flag de empreendedor se necessário
                $client->update(['is_entrepreneur' => $is_entrepreneur]);
            } else {
                $data['login_type'] = 'client';
                $email_changed = $existingUserByCpf && $existingUserByCpf->email != $email;

                if ($existingUserByCpf) {
                    // Usuário existente (mas sem cliente ativo ou deletado)
                    if ($existingUserByCpf->trashed()) {
                        $existingUserByCpf->restore();
                        $isCreateNewClientAndUser = true; // Restaurado, considera como novo para fins de senha
                        $randomPassword = Str::random(10);
                        $data['password'] = bcrypt($randomPassword);
                    } else {
                        $isCreateNewClientAndUser = false; // Usuário já existia, não é totalmente novo
                    }

                    // Atualiza os dados do usuário
                    $existingUserByCpf->update($data);

                    // Verifica se o cliente já existe, incluindo deletados
                    $client = $existingUserByCpf->client()->withTrashed()->first();

                    if ($client) {
                        if ($client->trashed()) {
                            $client->restore();
                        }
                        // Atualiza flag de empreendedor se necessário
                        $client->update(['is_entrepreneur' => $is_entrepreneur]);

                    } else {
                        // Cria o perfil de cliente
                        $client = $existingUserByCpf->client()->create(['is_entrepreneur' => $is_entrepreneur]);
                    }
                } else {
                    // Usuário não encontrado, cria um novo
                    $isCreateNewClientAndUser = true; // Definitivamente novo
                    $randomPassword = Str::random(10);
                    $data['password'] = bcrypt($randomPassword);

                    $user = User::create($data);
                    $client = $user->client()->create(['is_entrepreneur' => $is_entrepreneur]);
                }

                $email_validated = $data['email_validated'] ?? false;

                if ($email_validated) {
                    $client->user->update(['email_verified_at' => now()]);
                } elseif ($email_changed) {
                    $client->user->update(['email_verified_at' => null]);
                }
            }

            if ($isCreateNewClientAndUser && $randomPassword) {
                // Envia email apenas se um novo usuário foi criado e uma senha gerada
                Mail::to($client->user->email)->send(new CLientWelcomeMail($client->user, $randomPassword));
            }

            try {
                // Verifica se o relacionamento com o negócio padrão já existe
                $clientBusiness = ClientBusiness::withTrashed()
                    ->where('client_id', $client->id)
                    ->where('business_id', $defaultBusinessId)
                    ->first();

                if ($clientBusiness && $clientBusiness->trashed()) {
                    // Se existir e estiver deletado, restaura
                    DB::table('client_business')
                        ->where('client_id', $client->id)
                        ->where('business_id', $defaultBusinessId)
                        ->update(['deleted_at' => null, 'updated_at' => now()]);
                } elseif (!$clientBusiness) {
                    // Se não existir, cria um novo relacionamento
                    $client->businesses()->attach($defaultBusinessId);
                }
                // Se existir e não estiver deletado, não faz nada (já está associado)
            } catch (Exception $e) {
                Log::error("Erro ao associar/restaurar cliente ao negócio padrão: " . $e->getMessage(), ['exception' => $e]);
            }

            DB::commit();

            // Após criar/atualizar o cliente com sucesso, tenta consolidar dados de clientes incompletos
            try {
                $this->consolidateIncompleteClientData($client);
            } catch (Exception $consolidationError) {
                Log::error("Erro ao consolidar dados de cliente incompleto para o cliente ID {$client->id}: " . $consolidationError->getMessage(), ['exception' => $consolidationError]);
            }

            return response()->json([
                'client' => ClientResource::make($client),
                'isCreateNewClientAndUser' => $isCreateNewClientAndUser
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Erro no publicStore: " . $e->getMessage(), ['exception' => $e, 'data' => $request->all()]);
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, $id)
    {
        try {
            $userLogged = $request->user();
            $userLoggedBusinessesIds = $userLogged->employee->businesses()->pluck('id')->toArray();
            $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
            $isAdmin = $userLogged->employee->admin;
            $client = Client::findOrFail($id);
            $client->load(['businesses' => function ($q) use ($userLoggedBusinessesIds, $canReleaseAllBusinesses, $isAdmin) {
                if (!$canReleaseAllBusinesses && !$isAdmin) {
                    $q->whereIn('client_business.business_id', $userLoggedBusinessesIds);
                }
            }]);
            return response()->json(['client' => ClientResource::make($client)]);
        } catch (ModelNotFoundException $mex) {
            return response()->json($this->getResponseExceptionBody(new ClientNotFoundException()), 400);
        } catch (Exception $ex) {
            return response()->json(['msg' => $ex->getMessage(), 'exception' => get_class($ex)]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateClientRequest $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $clientFound = Client::findOrFail($id);

            $user = User::where('cpf', $data['cpf'])->first();
            if ($user && $user->id != $clientFound->user->id)
                throw new CpfExistsException();

            $businessesIds = $data['businessesIds'] ?? [];
            $userLogged = $request->user();
            $userLoggedBussinessIds = $userLogged->employee->businesses()->pluck('id')->toArray();
            $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
            $isAdmin = $userLogged->employee->admin;
            $email_validated = $request->email_validated ?? false;
            $email_changed = $clientFound != null && $clientFound->user['email'] != $data['email'];

            if (!$canReleaseAllBusinesses || !$isAdmin) {
                foreach ($businessesIds as $businessId) {
                    if (!in_array($businessId, $userLoggedBussinessIds))
                        throw new Exception("Você não possui permissão!");
                }
                $businessSavedIds = $clientFound->businesses()->pluck('id')->toArray();
                $onlyClientBusiness = array_diff($businessSavedIds, $userLoggedBussinessIds);
                $businessesIds = array_merge($onlyClientBusiness, $businessesIds);
            }
            if (isset($data['gender']) && !in_array($data['gender'], ['f', 'm'])) {
                $data['gender'] = null;
            }

            $resetPassword = false;
            if ($request->has('reset_password') && $request->reset_password) {
                $resetPassword = true;
                $randomPassword = Str::random(10);
                $hashedPassword = bcrypt($randomPassword);
                $clientFound->user->password = $hashedPassword;
                $clientFound->user->save();
            }

            if ($email_validated) {
                $clientFound->user->update(['email_verified_at' => now()]);
            } elseif ($email_changed) {
                $clientFound->user->update(['email_verified_at' => null]);
            }

            $data['login_type'] = 'client';

            $clientFound->update($data);
            $clientFound->user->update($data);

            $clientFound->businesses()->syncWithPivotValues($businessesIds, [
                'created_at' => now(),
                'updated_at' => now(),
                'deleted_at' => null
            ]);

            if ($resetPassword) {
                Mail::to($clientFound->user->email)->send(new CLientResetPasswordMail($clientFound->user, $randomPassword));
            }

            DB::commit();
            return response()->json(response()->json([
                'msg' => 'Cliente atualizado.',
                'client' => ClientResource::make($clientFound),
            ]));
        } catch (ModelNotFoundException $mex) {
            DB::rollBack();
            return response()->json($this->getResponseExceptionBody(new ClientNotFoundException()), 400);
        } catch (EmailExistsException $ex) {
            DB::rollBack();
            return response()->json(['errors' => ['email' => $ex->getMessage()]], 422);
        } catch (CpfExistsException $ex) {
            DB::rollBack();
            return response()->json(['errors' => ['cpf' => $ex->getMessage()]], 422);
        } catch (Exception $ex) {
            DB::rollBack();
            return response()->json(['msg' => $ex->getMessage(), 'exception' => get_class($ex)], 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $id)
    {
        try {
            $clientFound = Client::findOrFail($id);
            $userLogged = $request->user();
            $userLoggedSelectedBusiness = $userLogged->employee->business_selected_id;
            $clientBusinessesIds = $clientFound->businesses()->pluck('id')->toArray();
            $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
            $userAssociated = $clientFound->user;
            $adminBusiness = (int) SystemHelper::findInArrayByCodeName(SystemHelper::get(),'ADMIN_BUSINESS_ID');
            $isAdminBusinessSelected = $userLoggedSelectedBusiness === $adminBusiness;

            // APagar só no negócio selecionado
            if ($userLoggedSelectedBusiness && !$isAdminBusinessSelected) {
                if (!in_array($userLoggedSelectedBusiness, $clientBusinessesIds))
                    throw new Exception('Você não possui permissão.');
                ClientBusiness::where('business_id', $userLoggedSelectedBusiness)
                    ->where('client_id', $id)->delete();
                return response()->noContent();
            }

            // apagar no banco (se tiver permissão)
            if ($isAdminBusinessSelected && $canReleaseAllBusinesses) {
                Client::destroy($id);
                ClientBusiness::where('client_id', $id)->delete();
                if ($userAssociated->typesCount() == 0) { // 0 pois tinha apenas client e client foi removido
                    $userAssociated->delete();
                }
                return response()->noContent();
            }

            throw new Exception('Você não possui permissão.');
        } catch (Exception $ex) {
            return response()->json(['msg' => $ex->getMessage(), 'exception' => get_class($ex)], 400);
        }
    }

    public function validateStore(StoreClientRequest $request): JsonResponse
    {
        $data = $request->all();
        $user = User::where('cpf', $data['cpf'])->first();

        if ($user && $user->hasClient()) {
            return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro cliente!']], 422);
        }

        $user = User::where('email', $data['email'])
            ->whereNotNull('cpf')
            ->first();
        if ($user && $user->cpf != $data['cpf']) {
            return response()->json(['errors' => ['email' => 'Email já cadastrado para outro usuário!']], 422);
        }

        return response()->json(['success' => true]);
    }

    public function validatePublicStore(PublicStoreClientRequest $request): JsonResponse
    {
        $data = $request->all();
        $user = User::where('cpf', $data['cpf'])->first();

        if ($user && $user->hasClient()) {
            return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro cliente!']], 422);
        }

        $user = User::where('email', $data['email'])
            ->whereNotNull('cpf')
            ->first();
        if ($user && $user->cpf != $data['cpf']) {
            return response()->json(['errors' => ['email' => 'Email já cadastrado para outro usuário!']], 422);
        }

        return response()->json(['success' => true]);
    }

    public function validateUpdate(UpdateClientRequest $request, $id): JsonResponse
    {
        $data = $request->all();
        $client = Client::findOrFail($id);
        if ($client != null) {
            if ($client->user->cpf != $data['cpf']) {
                $user = User::where('cpf', $data['cpf'])->first();
                if ($user && $user->id != $client->user->id) {
                    return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro cliente']], 422);
                }
            }
            if ($client->user->email != $data['email']) {
                $user = User::where('email', $data['email'])->first();
                if ($user && $user->id != $client->user->id) {
                    return response()->json(['errors' => ['email' => 'Email já cadastrado para outro cliente']], 422);
                }
            }
        }
        return response()->json(['success' => true]);
    }

    public function getClientsByBusiness(Request $request, $perPage = 5, $search = '', $page = 1)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $is_entrepreneur = $request->input('is_entrepreneur', 'all');
        $hasDevices = filter_var($request->input('has_devices', false), FILTER_VALIDATE_BOOLEAN);
        try {
            $userLogged = $request->user();
            $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
            $isAdmin = $userLogged->employee->admin;
            $userLoggedBusinessesIds = $userLogged->employee->businesses()->pluck('id')->toArray();
            $userLoggedSelectedBusinessId = $userLogged->employee->business_selected_id;
            $query = Client::query();
            $query->join('users', 'users.id', '=', 'clients.user_id')
                ->select('clients.*');

            if (!$canReleaseAllBusinesses || !$isAdmin) {
                if ($userLoggedSelectedBusinessId && !in_array($userLoggedSelectedBusinessId, $userLoggedBusinessesIds)) {
                    throw new Exception("Você não possui permissão.");
                }
                $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                    $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
                });
            } else if ($userLoggedSelectedBusinessId) {
                $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                    $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
                });
            }

            if ($hasDevices) {
                $query->whereHas('devices');
            }

            if ($search) {
                $cpf = preg_replace('/[^0-9]/', '', $search);
                if (preg_match('/^\d{1,11}$/', $cpf))
                    $query->where('users.cpf', 'like', "$cpf%");
                else
                    $query->where('users.name', 'like', "%$search%");
            }

            if ($is_entrepreneur !== 'all') {
                if ($is_entrepreneur == 'true') {
                    $query->where('clients.is_entrepreneur', true);
                } else $query->where('clients.is_entrepreneur', false);
            }

            $query->orderBy('users.name');
            $clients = $perPage <= 0 ? $query->get() : $query->paginate($perPage);
            $clients->load(['user', 'businesses' => function ($q) use ($userLoggedSelectedBusinessId, $canReleaseAllBusinesses, $isAdmin) {
                if (!$canReleaseAllBusinesses || !$isAdmin) {
                    $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
                }
            }]);
            return response()->json(['clients' => $clients]);
        } catch (Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Check if a client exists based on CPF.
     */
    public function check(Request $request): JsonResponse
    {
        try {
            // Valida que o CPF foi fornecido corretamente na requisição
            $cpf = $request->input('cpf');
            if (!$cpf) {
                return response()->json(['error' => 'CPF não fornecido.'], 422);
            }

            // Remove quaisquer caracteres não numéricos do CPF (apenas números)
            $cleanCpf = preg_replace('/\D/', '', $cpf);

            // Busca um usuário com esse CPF
            $user = User::where('cpf', $cleanCpf)->first();

            // Verifica se o usuário possui um cliente associado
            if ($user && $user->hasClient()) {
                return response()->json([
                    'exists' => true,
                    'client' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone_number_1,
                    ]
                ]);
            } else {
                return response()->json(['exists' => false]);
            }
        } catch (Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 500);
        }
    }

    /**
     * Handles client registration or update based on CPF check status for evaluation response.
     *
     * @param array $clientData Data from the client registration form.
     * @param string $cpfCheckStatus Status returned by the CPF check ('not_found', 'deleted', 'found_client', 'found_employee', 'found_seller').
     * @param int $businessId The ID of the business associated with the evaluation.
     * @param int|null $incompleteClientId The ID of the incomplete client record created initially for the evaluation, if any.
     * @return Client The client model instance after processing.
     * @throws Exception
     */
    public function handleClientRegistrationForEvaluation(array $clientData, string $cpfCheckStatus, int $businessId, ?int $incompleteClientId = null): Client
    {
        DB::beginTransaction();
        try {
            $cpf = preg_replace('/\D/', '', $clientData['cpf']);
            $email = $clientData['email'];
            $phone_number_1 = isset($clientData['phone_number_1']) ? preg_replace('/\D/', '', $clientData['phone_number_1']) : null;
            $phone_number_2 = isset($clientData['phone_number_2']) ? preg_replace('/\D/', '', $clientData['phone_number_2']) : null;
            $is_entrepreneur = $clientData['is_entrepreneur'] ?? false;
            $email_validated = $clientData['email_validated'] ?? false;

            $user = null;
            $client = null;

            // Find the incomplete user/client associated with this evaluation, if it exists
            $incompleteClient = $incompleteClientId ? Client::find($incompleteClientId) : null;
            $incompleteUser = $incompleteClient ? $incompleteClient->user()->withTrashed()->first() : null;


            switch ($cpfCheckStatus) {
                case 'not_found':
                    // Create new User and Client
                    $user = User::create([
                        'name' => $clientData['name'],
                        'email' => $email,
                        'cpf' => $cpf,
                        'birth_date' => $clientData['birth_date'],
                        'gender' => $clientData['gender'] == 'other' ? null : $clientData['gender'],
                        'phone_number_1' => $phone_number_1,
                        'phone_number_2' => $phone_number_2,
                        'login_type' => 'client',
                        'password' => bcrypt($clientData['password']),
                        'email_verified_at' => $email_validated ? now() : null,
                    ]);
                    $client = $user->client()->create(['is_entrepreneur' => $is_entrepreneur]);

                    // Delete the incomplete user/client if they exist
                    if ($incompleteClient) {
                        $incompleteClient->forceDelete();
                    }
                    if ($incompleteUser && $incompleteUser->typesCount() == 0) {
                        $incompleteUser->forceDelete();
                    }

                    break;

                case 'deleted':
                    // Find and restore the deleted User
                    $user = User::withTrashed()->where('cpf', $cpf)->firstOrFail();
                    $user->restore();

                    // Update User data
                    $user->update([
                        'name' => $clientData['name'],
                        'email' => $email,
                        'birth_date' => $clientData['birth_date'],
                        'gender' => $clientData['gender'] == 'other' ? null : $clientData['gender'],
                        'phone_number_1' => $phone_number_1,
                        'phone_number_2' => $phone_number_2,
                        'login_type' => 'client',
                        'password' => bcrypt($clientData['password']), // Assuming password is required for re-activation
                        'email_verified_at' => $email_validated ? now() : null,
                    ]);

                    // Find and restore the deleted Client associated with this user
                    $client = $user->client()->withTrashed()->first();
                    if ($client) {
                        $client->restore();
                        $client->update(['is_entrepreneur' => $is_entrepreneur]);
                    } else {
                        // If no client profile existed for the deleted user, create one
                        $client = $user->client()->create(['is_entrepreneur' => $is_entrepreneur]);
                    }

                    // Delete the incomplete user/client if they exist
                    if ($incompleteClient) {
                        $incompleteClient->forceDelete();
                    }
                    if ($incompleteUser && $incompleteUser->typesCount() == 0) {
                        $incompleteUser->forceDelete();
                    }

                    break;

                case 'found_client':
                    // Find the existing User and Client
                    $user = User::where('cpf', $cpf)->firstOrFail();
                    $client = $user->client()->firstOrFail();

                    // Update User contact info if provided in the form and different
                    $updateUserData = [];
                    if (isset($clientData['email']) && $user->email !== $email) {
                        $updateUserData['email'] = $email;
                        $updateUserData['email_verified_at'] = $email_validated ? now() : null;
                    }
                    if (isset($clientData['phone_number_1']) && $user->phone_number_1 !== $phone_number_1) {
                        $updateUserData['phone_number_1'] = $phone_number_1;
                    }
                    if (isset($clientData['phone_number_2']) && $user->phone_number_2 !== $phone_number_2) {
                        $updateUserData['phone_number_2'] = $phone_number_2;
                    }

                    if (!empty($updateUserData)) {
                        $user->update($updateUserData);
                    }

                    // Delete the incomplete user/client if they exist
                    if ($incompleteClient) {
                        $incompleteClient->forceDelete();
                    }
                    if ($incompleteUser && $incompleteUser->typesCount() == 0) {
                        $incompleteUser->forceDelete();
                    }

                    break;

                case 'found_employee':
                case 'found_seller':
                    // Find the existing User
                    $user = User::where('cpf', $cpf)->firstOrFail();

                    // Create a new Client profile for this existing User
                    $client = $user->client()->create(['is_entrepreneur' => $is_entrepreneur]);

                    // Update User contact info if provided in the form and different
                    $updateUserData = [];
                    if (isset($clientData['email']) && $user->email !== $email) {
                        $updateUserData['email'] = $email;
                        $updateUserData['email_verified_at'] = $email_validated ? now() : null;
                    }
                    if (isset($clientData['phone_number_1']) && $user->phone_number_1 !== $phone_number_1) {
                        $updateUserData['phone_number_1'] = $phone_number_1;
                    }
                    if (isset($clientData['phone_number_2']) && $user->phone_number_2 !== $phone_number_2) {
                        $updateUserData['phone_number_2'] = $phone_number_2;
                    }

                    if (!empty($updateUserData)) {
                        $user->update($updateUserData);
                    }

                    // Delete the incomplete user/client if they exist
                    if ($incompleteClient) {
                        $incompleteClient->forceDelete();
                    }
                    if ($incompleteUser && $incompleteUser->typesCount() == 0) {
                        $incompleteUser->forceDelete();
                    }

                    break;

                default:
                    // Should not happen if frontend logic is correct, but handle defensively
                    throw new Exception("Status de verificação de CPF inválido: " . $cpfCheckStatus);
            }

            // Ensure the client is linked to the business
            // Use syncWithoutDetaching to add the link if it doesn't exist, without removing others
            if ($client && $businessId) {
                $client->businesses()->syncWithoutDetaching([$businessId]);
            }
            DB::commit();
            return $client;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Finds or creates a client based on contact data (email/phone) for a specific business evaluation.
     * Creates a user with incomplete data if not found.
     * Ensures the client is linked to the specified business.
     *
     * @param array $contactData ['email' => string|null, 'number_phone' => string|null, 'name' => string|null]
     * @param int $businessId
     * @return Client
     * @throws Exception
     */
    public function findOrCreateClientForEvaluation(array $contactData, int $businessId): Client
    {
        $email = $contactData['email'] ?? null;
        $phoneNumber = isset($contactData['number_phone']) ? preg_replace('/\D/', '', $contactData['number_phone']) : null;
        $name = $contactData['name'] ?? null;

        if (!$email && !$phoneNumber) {
            throw new Exception('É necessário fornecer e-mail ou telefone para identificar o cliente.');
        }

        $user = null;

        if ($email) {
            $user = User::where('email', $email)->first();
            if (!$user) {
                $user = User::withTrashed()->where('email', $email)->first();
            }
        }

        if (!$user && $phoneNumber) {
            $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
            $codes = substr($phoneNumber, 0, 2);
            $numberDigits = substr($phoneNumber, 2);
            if (strlen($numberDigits) === 9) {
                $phoneNumberV2 = $codes . substr($numberDigits, 1);
            } else if (strlen($numberDigits) === 8) {
                $phoneNumberV2 = $codes . '9' . $numberDigits;
            }

            $user = User::where(function ($query) use ($phoneNumber, $phoneNumberV2) {
                $query->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
            })->first();

            if (!$user) {
                $user = User::withTrashed()->where(function ($query) use ($phoneNumber, $phoneNumberV2) {
                    $query->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
                })->first();
            }
        }

        $client = null;

        DB::beginTransaction();
        try {
            if ($user) {
                // User found
                if ($user->trashed()) {
                    // Restore user if soft-deleted
                    $user->restore();
                    // resetar os dados antigos e deixar apenas os novos
                    $user->update([
                        'name' => $name ?? $user->name,
                        'email' => $email,
                        'phone_number_1' => $phoneNumber,
                        'phone_number_2' => null,
                        'email_verified_at' => null,
                        'birth_date' => null,
                        'cpf' => null,
                        'neighborhood' => null,
                        'street' => null,
                        'number' => null,
                        'city' => null,
                        'state' => null,
                        'cep' => null,
                        'complement' => null,
                        'login_type' => 'client'
                    ]);
                }

                // Check if they have a client profile (including trashed)
                $client = $user->client()->withTrashed()->first();

                if ($client) {
                    if ($client->trashed()) {
                        // Restore client profile if soft-deleted
                        $client->restore();
                    }
                } else {
                    // User exists but doesn't have a client profile, create one
                    $client = $user->client()->create(['is_entrepreneur' => false]);
                }
            } else {
                // User not found, create a new user with incomplete data
                $userData = [
                    'name' => $name ?? 'Usuário incompleto',
                    'login_type' => 'client',
                    'password' => bcrypt(Str::random(10)),
                    'email_verified_at' => null,
                ];
                if ($email) {
                    $userData['email'] = $email;
                }
                if ($phoneNumber) {
                    // Store in phone_number_1
                    $userData['phone_number_1'] = $phoneNumber;
                }

                $user = User::create($userData);
                $client = $user->client()->create(['is_entrepreneur' => false]);
            }

            // Ensure the client is linked to the business
            // Use syncWithoutDetaching to add the link if it doesn't exist, without removing others
            $client->businesses()->syncWithoutDetaching([$businessId]);

            // restore ClientBusiness
            $clientBusiness = ClientBusiness::withTrashed()
                ->where('client_id', $client->id)
                ->where('business_id', $businessId)
                ->first();
            if ($clientBusiness && $clientBusiness->trashed()) {
                DB::table('client_business')
                    ->where('client_id', $client->id)
                    ->where('business_id', $businessId)
                    ->update(['deleted_at' => null]);
            }
            DB::commit();
            return $client;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Finds or creates a client for an employee or user and associates businesses to the client.
     * If an employee is provided, associates the employee's businesses + default business.
     * If a user is provided, associates only the default business.
     *
     * @param Employee|User $employeeOrUser The employee or user for whom to create/find a client
     * @return Client The client associated with the employee/user
     * @throws Exception
     */
    public function findOrCreateClientForEmployeeOrUser($employeeOrUser): Client
    {
        if (!$employeeOrUser instanceof Employee && !$employeeOrUser instanceof User) {
            throw new Exception('O parâmetro deve ser uma instância de Employee ou User.');
        }

        // Get the user object
        $user = $employeeOrUser instanceof Employee ? $employeeOrUser->user : $employeeOrUser;
        
        if (!$user) {
            throw new Exception('Usuário não encontrado.');
        }

        // Get default business ID
        $defaultBusinessId = (int)SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'ADMIN_BUSINESS_ID');
        $businessesToAssociate = [$defaultBusinessId];

        // If it's an employee, also get their associated businesses
        if ($employeeOrUser instanceof Employee) {
            $employeeBusinessesIds = $employeeOrUser->businesses()->pluck('business_id')->toArray();
            $businessesToAssociate = array_unique(array_merge($employeeBusinessesIds, [$defaultBusinessId]));
        }

        DB::beginTransaction();
        try {
            // Check if user already has a client profile (including trashed)
            $client = $user->client()->withTrashed()->first();

            if ($client) {
                if ($client->trashed()) {
                    // Restore client profile if soft-deleted
                    $client->restore();
                }
            } else {
                $client = $user->client()->create(['is_entrepreneur' => false]);
            }

            // Ensure the client is linked to the businesses
            $client->businesses()->syncWithoutDetaching($businessesToAssociate);

            // Restore any soft-deleted ClientBusiness relationships
            foreach ($businessesToAssociate as $businessId) {
                $clientBusiness = ClientBusiness::withTrashed()
                    ->where('client_id', $client->id)
                    ->where('business_id', $businessId)
                    ->first();

                if ($clientBusiness && $clientBusiness->trashed()) {
                    DB::table('client_business')
                        ->where('client_id', $client->id)
                        ->where('business_id', $businessId)
                        ->update(['deleted_at' => null, 'updated_at' => now()]);
                }
            }

            DB::commit();
            return $client;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Transfere dados (avaliações, cashbacks) de um cliente para outro e deleta o cliente de origem.
     *
     * @param int $sourceClientId ID do cliente de origem
     * @param int $targetClientId ID do cliente de destino
     * @throws \InvalidArgumentException Se os IDs de origem e destino forem iguais.
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException Se o cliente de origem ou destino não for encontrado.
     * @throws \Exception Em caso de outros erros durante a transferência.
     */
    public function transferClientData(int $sourceClientId, int $targetClientId): void
    {
        if ($sourceClientId == $targetClientId) {
            throw new \InvalidArgumentException('Os IDs dos clientes de origem e destino não podem ser iguais.');
        }

        DB::beginTransaction();
        try {
            // 1. Encontrar os clientes
            $sourceClient = Client::findOrFail($sourceClientId);
            $targetClient = Client::findOrFail($targetClientId);
            $sourceUser = $sourceClient->user;

            Log::info("Iniciando transferência do cliente ID {$sourceClientId} para {$targetClientId}");

            $daysToTransferCashbackAndEvaluation = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'DAYS_TO_TRANSFER_CASHBACK_AND_EVALUATION');

            $dateThreshold = now()->subDays($daysToTransferCashbackAndEvaluation);

            $evaluationsTransferred = Evaluation::where('client_id', $sourceClientId)
                ->where('created_at', '>=', $dateThreshold)
                ->update(['client_id' => $targetClientId]);

            // 3. Transferir Cashbacks
            $cashbacksTransferred = Cashback::where('client_id', $sourceClientId)
                ->where('created_at', '>=', $dateThreshold)
                ->update(['client_id' => $targetClientId]);

            // 4. Obter associações de negócios do cliente de origem
            $sourceBusinessIds = $sourceClient->businesses()->pluck('business_id')->toArray();
            Log::info("Negócios associados ao cliente de origem ID {$sourceClientId}: " . (!empty($sourceBusinessIds) ? implode(', ', $sourceBusinessIds) : 'Nenhum'));

            // 5. Associar/Restaurar negócios ao cliente de destino
            if (!empty($sourceBusinessIds)) {
                foreach ($sourceBusinessIds as $businessId) {
                    try {
                        $clientBusiness = ClientBusiness::withTrashed()
                            ->where('client_id', $targetClientId)
                            ->where('business_id', $businessId)
                            ->first();

                        if ($clientBusiness && $clientBusiness->trashed()) {
                            DB::table('client_business')
                                ->where('client_id', $targetClientId)
                                ->where('business_id', $businessId)
                                ->update(['deleted_at' => null, 'updated_at' => now()]);
                        } elseif (!$clientBusiness) {
                            $targetClient->businesses()->attach($businessId);
                        }
                        // Se existir e não estiver deletado, não faz nada.
                    } catch (Exception $e) {
                        Log::error("Erro ao associar/restaurar negócio ID {$businessId} ao cliente destino ID {$targetClientId}: " . $e->getMessage(), ['exception' => $e]);
                    }
                }
                Log::info("Processamento de associação/restauração de negócios concluído para cliente destino ID {$targetClientId}.");
            }

            // 6. Deletar o cliente de origem (e possivelmente o usuário associado)
            $sourceClient->businesses()->detach();
            $sourceClient->delete();
            if ($sourceUser) {
                $sourceUser->refresh();
                if ($sourceUser->typesCount() == 0) {
                    $sourceUser->delete();
                    Log::info("Usuário associado ID {$sourceUser->id} marcado como deletado.");
                } else {
                    Log::info("Usuário associado ID {$sourceUser->id} não foi deletado pois possui outros tipos.");
                }
            } else {
                Log::warning("Usuário associado ao cliente de origem ID {$sourceClientId} não encontrado para possível exclusão.");
            }


            DB::commit();
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            Log::error("Erro na transferência: Cliente não encontrado.", ['exception' => $e]);
            // Lança a exceção para ser tratada por quem chamar a função
            throw $e;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Erro na transferência: " . $e->getMessage(), ['exception' => $e]);
            // Lança a exceção para ser tratada por quem chamar a função
            throw $e;
        }
    } // Fim da função transferClientData

    /**
     * Busca clientes incompletos (sem CPF) com o mesmo email ou telefone do cliente alvo
     * e transfere seus dados (avaliações, cashbacks) para o cliente alvo.
     *
     * @param Client $targetClient O cliente completo recém-criado/atualizado.
     */
    public function consolidateIncompleteClientData(Client $targetClient): void
    {
        DB::beginTransaction();
        try {
            $targetUser = $targetClient->user;

            $email = $targetUser->email;
            $phone = $targetUser->phone_number_1;

            Log::info("Iniciando busca por clientes incompletos para consolidar com cliente ID {$targetClient->id} (Email: {$email}, Telefone: {$phone})");

            // Busca clientes cujo usuário associado tem CPF nulo E (email ou telefone igual ao do alvo)
            $incompleteClients = Client::where('id', '!=', $targetClient->id)
                ->whereHas('user', function ($query) use ($email, $phone) {
                    $query->whereNull('cpf')
                        ->where(function ($subQuery) use ($email, $phone) {
                            if ($email) {
                                $subQuery->orWhere('email', $email);
                            }
                            if ($phone) {
                                $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                                $codes = substr($phone, 0, 2);
                                $numberDigits = substr($phone, 2);
                                if (strlen($numberDigits) === 9) {
                                    $phoneNumberV2 = $codes . substr($numberDigits, 1);
                                } else if (strlen($numberDigits) === 8) {
                                    $phoneNumberV2 = $codes . '9' . $numberDigits;
                                }

                                $subQuery->orWhere('phone_number_1', $phone)
                                    ->orWhere('phone_number_1', $phoneNumberV2);
                            }
                        });
                })
                ->with('user')
                ->get();

            if ($incompleteClients->isEmpty()) {
                Log::info("Nenhum cliente incompleto encontrado para consolidação com cliente ID {$targetClient->id}.");
                return;
            }

            Log::info("Encontrados {$incompleteClients->count()} clientes incompletos para consolidar com cliente ID {$targetClient->id}. IDs: " . $incompleteClients->pluck('id')->implode(', '));

            foreach ($incompleteClients as $sourceClient) {
                try {
                    Log::info("Tentando transferir dados do cliente incompleto ID {$sourceClient->id} para o cliente ID {$targetClient->id}.");
                    $this->transferClientData($sourceClient->id, $targetClient->id);
                    Log::info("Transferência concluída com sucesso do cliente ID {$sourceClient->id} para {$targetClient->id}.");
                } catch (Exception $e) {
                    Log::error("Falha ao transferir dados do cliente incompleto ID {$sourceClient->id} para o cliente ID {$targetClient->id}: " . $e->getMessage(), ['exception' => $e]);
                }
            }
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Erro ao consolidar dados de cliente incompleto: " . $e->getMessage(), ['exception' => $e]);
            throw $e;
        } finally {
            DB::commit();
        }

    }

    public function toggleInformativeFlag(Request $request)
    {
        try {
            $user = Auth::user();
            $client = $user->client;

            if (!$client)
                throw new Exception('Cliente não encontrado.');

            $storedValue = filter_var($client->is_informative_read, FILTER_VALIDATE_BOOLEAN);
            $client->is_informative_read = !$storedValue;
            $client->save();
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Manipula a restauração e atualização de um usuário existente
     */
    protected function handleRestoreExistingUser(User $user, array $userData, bool $hasEmail, bool $hasPhone): void
    {
        // Verifica se o email já está sendo usado por outro usuário
        $emailInUse = $hasEmail
            ? User::where('email', $userData['email'])
                ->where('id', '<>', $user->id)
                ->exists()
            : false;

        // Retorna os dados que vão ser utilizados no cadastro
        $updateData = $this->prepareUserUpdateData($userData, $hasEmail, $hasPhone, $emailInUse, $user);

        $user->update($updateData);
        $user->restore();

        // Registra log se o email estava em uso
        if ($emailInUse) {
            SystemLog::warning(
                description: sprintf(
                    "Cliente importado: %s (ID: %d), mas o email %s já estava cadastrado para outro usuário",
                    $userData['name'],
                    $user->id,
                    $userData['email']
                ),
                origin: 'Import Client Registration',
            );
        }
    }

    /**
     * Prepara os dados para atualização do usuário
     */
    protected function prepareUserUpdateData(
        array $userData,
        bool $hasEmail,
        bool $hasPhone,
        bool $emailInUse,
        User $existingUser
    ): array {
        $baseData = [
            'name' => $userData['name'] ?? $existingUser->name,
            'phone_number_2' => null,
            'email_verified_at' => null,
            'birth_date' => null,
            'cpf' => null,
            'neighborhood' => null,
            'street' => null,
            'number' => null,
            'city' => null,
            'state' => null,
            'cep' => null,
            'complement' => null,
            'login_type' => 'client'
        ];

        // Tratamento especial para email se já estiver em uso
        $emailValue = $hasEmail
            ? ($emailInUse ? null : $userData['email'])
            : null;

        return array_merge($baseData, [
            'email' => $emailValue,
            'phone_number_1' => $hasPhone ? $userData['phone_number_1'] : null
        ]);
    }

    public function storeIncompleteClient(StoreClientIncompleteRequest $request) {
        try {
            $data = $request->all();
            $employee = $request->user()->employee;
            $userBusinessSelectedId = $employee->business_selected_id ?? null;

            if (!$userBusinessSelectedId)
                throw new Exception('Você precisa selecionar um negócio!');

            $hasEmail = !empty($data['email']);
            $hasPhone = !empty($data['phone_number_1']);

            $existingUser = null;

            // Busca usuário existente pelo número
            if ($hasPhone) {
                $phoneNumber = $data['phone_number_1'];
                $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                $codes = substr($phoneNumber, 0, 2);
                $numberDigits = substr($phoneNumber, 2);

                if (strlen($numberDigits) === 9) {
                    $phoneNumberV2 = $codes . substr($numberDigits, 1);
                } else if (strlen($numberDigits) === 8) {
                    $phoneNumberV2 = $codes . '9' . $numberDigits;
                }

                // busca nos ativos usuarios com telefone igual (com digito 9 ou sem)
                $existingUser = User::where(function ($query) use ($phoneNumber, $phoneNumberV2) {
                    $query->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
                })->first();

                // busca nos usuarios deletados com telefone igual (com digito 9 ou sem)
                if (!$existingUser) {
                    $existingUser = User::withTrashed()->where(function ($query) use ($phoneNumber, $phoneNumberV2) {
                        $query->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
                    })->first();
                }

                // Verifica se o email passado é igual ao email cadastrado no banco
                if ($hasEmail && $existingUser && isset($existingUser->email)) {
                    SystemLog::warning(
                        description: "Cliente importado: {$data['name']}, encontrado pelo telefone, mas o email é diferente. {$data['email']}",
                        origin: 'Import Client Registration',
                    );
                }
            }

            // Busca usuário existente pelo email
            if (!$existingUser && $hasEmail) {
                $email = $data['email'];
                $existingUser = User::where('email', $email)->first();

                if (!$existingUser) {
                    $existingUser = User::withTrashed()
                                    ->where('email', $email)
                                    ->first();
                }
            }

            $user = $existingUser;
            if ($user) {
                if ($user->trashed()) {
                    $this->handleRestoreExistingUser($user, $data, $hasEmail, $hasPhone);
                }
            } else {
                $password = Str::random(10);
                $data['password'] = bcrypt($password);
                $user = User::create($data);
            }

            // Verifica se já existe um cliente para este usuário
            $client = $user->client()->withTrashed()->first();

            if ($client) {
                if ($client->trashed()) {
                    $client->restore();
                }

                // Associa o cliente ao negócio do batch se não estiver já associado
                if (!$client->businesses()->where('business_id', $userBusinessSelectedId)->exists()) {
                    // Verifica se o relacionamento já existe deletado (soft-deleted)
                    $existingRelationship = DB::table('client_business')
                        ->where('client_id', $client->id)
                        ->where('business_id', $userBusinessSelectedId)
                        ->whereNotNull('deleted_at')
                        ->first();

                    if ($existingRelationship) {
                        // Restaura o relacionamento soft-deleted usando update direto
                        DB::table('client_business')
                            ->where('client_id', $client->id)
                            ->where('business_id', $userBusinessSelectedId)
                            ->update([
                                'deleted_at' => null,
                                'updated_at' => now()
                            ]);
                    } else {
                        // Cria o relacionamento somente se não existir (nem mesmo deletado)
                        $client->businesses()->attach($userBusinessSelectedId, [
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    }
                }

                return response()->json([
                    'client' => ClientResource::make($client)
                ]);
            }

            // Cria novo cliente
            $client = $user->client()->create([
                'is_entrepreneur' => false
            ]);

            // Associa o cliente ao negócio do batch
            $client->businesses()->attach($userBusinessSelectedId, [
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'client' => ClientResource::make($client)
            ]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }
}

