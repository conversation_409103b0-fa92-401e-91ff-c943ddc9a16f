<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\MaisLuzExceptions\CpfExistsException;
use App\Exceptions\MaisLuzExceptions\EmailExistsException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Sellers\StoreSeller;
use App\Http\Requests\Api\Sellers\UpdateSeller;
use App\Http\Resources\Api\SellerResource;
use App\Models\Seller;
use App\Models\User;
use App\Services\SellerService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SellerController extends Controller
{
    public function __construct(
        private SellerService $sellers,
    )
    {
    }

    public function index(Request $request): JsonResponse
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $sellerType = $request->input('seller_type', 'all');

        return response()->json([
            'sellers' => $this->sellers->getAllPaginated(
                $perPage,
                $search,
                $page,
                $sellerType
            ),
            'search' => $search,
            'per_page' => $perPage,
            'page' => $page,
            'seller_type' => $sellerType,
        ]);
    }

    public function getAllSellers(Request $request): JsonResponse
    {
        return response()->json([
            'sellers' => $this->sellers->getAllSellers(
                $request->input('per_page', 0),
                $request->search,
                $request->page ?? 1
            ),
        ]);
    }

    public function getAllSellersForSeller(Request $request): JsonResponse
    {
        return response()->json([
            'sellers' => $this->sellers->getAllSellersForSeller(
                $request->input('per_page', 0),
                $request->search,
                $request->page ?? 1
            ),
        ]);
    }



    public function store(StoreSeller $request)
    {
        $userFindCpf = User::where('cpf', $request->cpf)->first();
        if ($userFindCpf && $userFindCpf->hasSeller()) {
            return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro vendedor']], 422);
        }
        $userFindEmail = User::where('email', $request->email)->first();
        if ($userFindEmail && $userFindEmail->cpf != $request->cpf) {
            return response()->json(['errors' => ['email' => 'Email já cadastrado para outro vendedor']], 422);
        }
        DB::beginTransaction();
        try {
            $user = $this->sellers->store(
                $request->name,
                $request->email,
                Carbon::parse($request->birth_date),
                $request->cpf,
                $request->gender,
                $request->phone_number_1,
                $request->phone_number_2,
                $request->neighborhood,
                $request->street,
                $request->number,
                $request->city,
                $request->state,
                $request->cep,
                $request->complement,
                $request->boolean('register_sellers'),
                $request->percentage_per_sale,
                $request->boolean('email_validated'),
                $request->registered_by,
            );

            $clientController = new ClientController();
            if (!$user->hasClient()) {
                $clientController->findOrCreateClientForEmployeeOrUser($user);
            }

            if($user->hasClient()){
                $clientController->consolidateIncompleteClientData($user->client);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }

        return response()->json($user);
    }

    public function storeForSeller(StoreSeller $request): JsonResponse
    {
        $user = $request->user();
        $seller = $user->seller;
        $new_request = $request->merge([
            'register_sellers' => false,
            'registered_by' => $seller->id,
        ]);
        return $this->store($new_request);
    }

    public function validateStore(StoreSeller $request)
    {
        $userFindCpf = User::where('cpf', $request->cpf)->first();
        if ($userFindCpf && $userFindCpf->hasSeller()) {
            return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro vendedor']], 422);
        }
        $userFindEmail = User::where('email', $request->email)->first();
        if ($userFindEmail && $userFindEmail->cpf != $request->cpf) {
            return response()->json(['errors' => ['email' => 'Email já cadastrado para outro vendedor']], 422);
        }

        return response()->json(['success' => true]);
    }

    public function show(Seller $seller)
    {
        return response()->json($seller);
    }

    public function getForSeller($id): JsonResponse
    {
        return response()->json(SellerResource::make(Seller::findOrFail($id)));
    }

    public function update(UpdateSeller $request, Seller $seller)
    {
        $userFindCpf = User::where('cpf', $request->cpf)->first();
        if ($userFindCpf && $userFindCpf->id != $seller->user->id) {
            return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro vendedor']], 422);
        }

        $userFindEmail = User::where('email', $request->email)->first();
        if ($userFindEmail && $userFindEmail->id != $seller->user->id) {
            return response()->json(['errors' => ['email' => 'Email já cadastrado para outro vendedor']], 422);
        }

        DB::beginTransaction();
        try {
            $oldEmail = $seller->user->email;

            $user = $this->sellers->update(
                $seller,
                $request->name,
                $request->email,
                Carbon::parse($request->birth_date),
                $request->cpf,
                $request->gender,
                $request->phone_number_1,
                $request->phone_number_2,
                $request->neighborhood,
                $request->street,
                $request->number,
                $request->city,
                $request->state,
                $request->cep,
                $request->complement,
                $request->boolean('register_sellers'),
                $request->percentage_per_sale,
                $request->boolean('email_validated'),
                $oldEmail,
                $request->boolean('reset_password'),
            );

            if($user->hasClient()){
                $clientController->consolidateIncompleteClientData($user->client);
            }
            
            DB::commit();
            return response()->json($user);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function updateForSeller(UpdateSeller $request, $id): JsonResponse
    {
        return $this->update($request, Seller::findOrFail($id));
    }


    public function validateUpdate(UpdateSeller $request, Seller $seller): JsonResponse
    {
        $data = $request->validated();
        $userFindCpf = User::where('cpf', $data['cpf'])->first();
        if ($userFindCpf && $userFindCpf->id != $seller->user->id) {
            return response()->json(['errors' => ['cpf' => 'CPF já cadastrado para outro vendedor']], 422);
        }

        $userFindEmail = User::where('email', $data['email'])->first();
        if ($userFindEmail && $userFindEmail->id != $seller->user->id) {
            return response()->json(['errors' => ['email' => 'Email já cadastrado para outro vendedor']], 422);
        }

        return response()->json(['success' => true]);
    }

    public function destroy($id): JsonResponse
    {
        return $this->sellers->delete($id);
    }

    public function deleteForSeller($id): JsonResponse
    {
        return $this->sellers->delete($id);
    }


    public function getByCpf(string $cpf): JsonResponse
    {
        if (empty($cpf)) {
            return response()->json(['errors' => ['cpf' => 'O CPF é obrigatório']], 422);
        }

        $seller = $this->sellers->findByCpf($cpf);

        if (!$seller) {
            return response()->json(['errors' => ['cpf' => 'Nenhum vendedor encontrado com este CPF']], 404);
        }

        return response()->json(SellerResource::make($seller));
    }

}
