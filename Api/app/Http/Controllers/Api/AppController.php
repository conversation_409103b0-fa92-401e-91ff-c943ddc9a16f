<?php

namespace App\Http\Controllers\Api;

use App\Helpers\SystemHelper;
use App\Http\Controllers\Controller;
use App\Models\AppUpdateVersion;
use App\Http\Requests\Api\BaseRequest as Request;

class App<PERSON>ontroller extends Controller
{
    public function app_latest_version(Request $request)
    {
        $device_type = $request->input('device_type');
        if ($device_type != 'android' && $device_type != 'ios') {
            return response()->json([
                'message' => 'Invalid device type'
            ], 400);
        }

        $app_update_version = AppUpdateVersion::where('device_type', $device_type)
            ->orderBy('number_version', 'desc')
            ->first();

        if ($app_update_version) {
            return response()->json([
                'number_version' => $app_update_version->number_version,
            ], 200);
        }
        return response()->json([
            'number_version' => null,
        ], 200);

    }

    public function get_app_link(Request $request)
    {
        $device_type = $request->input('device_type');
        if ($device_type != 'android' && $device_type != 'ios') {
            return response()->json([
                'message' => 'Invalid device type'
            ], 400);
        }

        $system = SystemHelper::getAppLinks();

        if ($device_type == 'android') {
            return response()->json([
                'link' => $system['link_app_android'],
            ], 200);
        } else {
            return response()->json([
                'link' => $system['link_app_ios'],
            ], 200);
        }
    }

    public function checkPermission(Request $request)
    {
        $permission = $request->permission;
        $user = auth('api')->user();

        if($user->employee->authorizedFunction->$permission) {
            return response()->json(['permission' => true], 200);
        }else{
            return response()->json(['permission' => false], 200);
        }
    }
}


