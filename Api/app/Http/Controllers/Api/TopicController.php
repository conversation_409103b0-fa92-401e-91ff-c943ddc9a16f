<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ApiUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Topics\TopicStore;
use App\Http\Requests\Api\Topics\TopicUpdate;
use App\Models\Topic;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TopicController extends Controller
{
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;
            $per_page = $request->per_page ?? 5;
            $hasDefaultPermission = ApiUser::hasUserFunction('default_topics');

            $query = Topic::query();

            if (!$employee->authorizedFunction->release_all_business) {
                $query->where(function ($query) use ($business_selected_id, $hasDefaultPermission) {
                    $query->where(function ($query) use ($hasDefaultPermission) {
                        $query->where('default', true);
                        if (!$hasDefaultPermission) {
                            $query->where('active', true);
                        }
                    })->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                        $query->where('business_id', $business_selected_id);
                    });
                });
            } else if ($employee->authorizedFunction->release_all_business) {
                $query->where(function ($query) use ($business_selected_id, $hasDefaultPermission) {
                    $query->where(function ($query) use ($hasDefaultPermission) {
                        $query->where('default', true);
                        if (!$hasDefaultPermission) {
                            $query->where('active', true);
                        }
                    })->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                        $query->where('business_id', $business_selected_id);
                    });
                });
            }

            $filter_default = $request->filter_default;

            if ($filter_default != null && $filter_default != "") {
                if ($filter_default == 'default') {
                    $query->where('default', true);
                } elseif ($filter_default == 'not_default') {
                    $query->where('default', false);
                }
            }

            if ($request->search) {
                $query->where('name', 'like', "%{$request->search}%");
            }

            $query->orderBy('name');

            $topics = $per_page <= 0 ? $query->get() : $query->paginate($per_page);
            $topics->load('questions', 'businesses');

            return response()->json(['topics' => $topics]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function getActiveRequiredTopics(Request $request)
    {
        try {
            $user = $request->user();
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;

            $query = Topic::query()->where('active', true)
                ->where('required', true)
                ->where('default', true);

            if ($user->login_type != 'admin' && !$employee->authorizedFunction->release_all_business) {
                if ($business_selected_id) {
                    $query->where(function ($query) use ($business_selected_id) {
                        $query->where('default', true)
                            ->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                                $query->where('business_id', $business_selected_id);
                            });
                    });
                } else {
                    $query->where(function ($query) use ($employee) {
                        $query->where('default', true)
                            ->orWhereHas('businesses', function ($query) use ($employee) {
                                $query->join('employee_business as eb', 'businesses.id', '=', 'eb.business_id')
                                    ->where('eb.employee_id', $employee->id);
                            });
                    });
                }
            } else if ($user->login_type != 'admin' && $employee->authorizedFunction->release_all_business) {
                if ($business_selected_id) {
                    $query->where(function ($query) use ($business_selected_id) {
                        $query->where('default', true)
                            ->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                                $query->where('business_id', $business_selected_id);
                            });
                    });
                } else {
                    $query->where('default', true);
                }
            } else if ($user->login_type == 'admin') {
                if ($business_selected_id) {
                    $query->where(function ($query) use ($business_selected_id) {
                        $query->where('default', true)
                            ->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                                $query->where('business_id', $business_selected_id);
                            });
                    });
                }
            }

            $query->orderBy('name');

            $topics = $query->get();
            $topics->load('questions', 'businesses');

            return response()->json(['topics' => $topics]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function getAllTopics(Request $request)
    {
        try {
            $user = $request->user();
            $isPaginate = filter_var($request->input('is_paginate', true), FILTER_VALIDATE_BOOLEAN);
            $formId = $request->input('form_id', null);
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;
            $per_page = $request->per_page ?? 5;
            $is_default_form = ($request->is_default_form ?? false) == 'true';

            $businesses_selected_ids = $request->businesses_selected_ids ?? [];

            $query = Topic::query();

            if ($formId) {
                $query->whereHas("forms", function ($q) use ($formId) {
                    $q->where("forms.id", $formId);
                });
            }

            if ($business_selected_id && !in_array($business_selected_id, $businesses_selected_ids)) {
                $businesses_selected_ids[] = $business_selected_id;
            }

            $query->where(function ($q) use ($is_default_form, $businesses_selected_ids) {
                if ($is_default_form) {
                    $q->where('default', true);
                } else {
                    // Tópicos que pertencem a QUALQUER um dos negócios OU são padrão
                    $q->where(function ($subQ) use ($businesses_selected_ids) {
                        if (!empty($businesses_selected_ids)) {
                            $subQ->whereHas('businesses', function ($businessQuery) use ($businesses_selected_ids) {
                                $businessQuery->whereIn('business_id', $businesses_selected_ids);
                            });
                        }
                        $subQ->orWhere('default', true);
                    });
                }
            });

            if ($request->search) {
                $query->where('name', 'like', "%{$request->search}%");
            }

            $query->orderBy('name');
            if ($isPaginate) {
                $topics = $per_page <= 0 ? $query->get() : $query->paginate($per_page);
            } else $topics = $query->get();
            $topics->load('questions', 'businesses');

            return response()->json([
                'topics' => $topics
            ]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function getActiveTopics(Request $request)
    {
        try {
            $user = $request->user();
            $isPaginate = filter_var($request->input('is_paginate', true), FILTER_VALIDATE_BOOLEAN);
            $formId = $request->input('form_id', null);
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;
            $per_page = $request->per_page ?? 5;
            $is_default_form = ($request->is_default_form ?? false) == 'true';

            $businesses_selected_ids = $request->businesses_selected_ids ?? [];

            $query = Topic::query()->where('active', true);

            if ($formId) {
                $query->whereHas("forms", function ($q) use ($formId) {
                    $q->where("forms.id", $formId);
                });
            }

            if ($business_selected_id && !in_array($business_selected_id, $businesses_selected_ids)) {
                $businesses_selected_ids[] = $business_selected_id;
            }

            $query->where(function ($q) use ($is_default_form, $businesses_selected_ids) {
                if ($is_default_form) {
                    $q->where('default', true);
                } else {
                    // Tópicos que pertencem a QUALQUER um dos negócios OU são padrão
                    $q->where(function ($subQ) use ($businesses_selected_ids) {
                        if (!empty($businesses_selected_ids)) {
                            $subQ->whereHas('businesses', function ($businessQuery) use ($businesses_selected_ids) {
                                $businessQuery->whereIn('business_id', $businesses_selected_ids);
                            });
                        }
                        $subQ->orWhere('default', true);
                    });
                }
            });

            if ($request->search) {
                $query->where('name', 'like', "%{$request->search}%");
            }

            $query->orderBy('name');
            if ($isPaginate) {
                $topics = $per_page <= 0 ? $query->get() : $query->paginate($per_page);
            } else $topics = $query->get();
            $topics->load('questions', 'businesses');

            return response()->json([
                'topics' => $topics
            ]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }


    public function store(TopicStore $request)
    {
        try {
            $data = $request->validated();
            $employee = $request->user()->employee ?? null;
            $business_selected_id = $employee->business_selected_id ?? false;
            if (!$business_selected_id) {
                if (!$data['default'])
                    throw new \Exception("Você precisa selecionar um negócio para cadastrar.");
            }
            if (!isset($data['default']) || !$data['default']) {
                $data['active'] = true;
                $data['required'] = false;
            }
            $topic = Topic::create($data);
            if (!$data['default']) {
                $topic->businesses()->sync($data['businesses'] ?? []);
            } else {
                $topic->businesses()->sync([]);
            }
            $questions = $data['questions'] ?? [];
            $topic->questions()->createMany($questions);
            $topic->load('questions', 'businesses');
            return response()->json(['topic' => $topic]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function show(string $id): JsonResponse
    {
        try {
            $topic = Topic::with('businesses')->findOrFail($id);
            $questions = $topic->questions()->where('business_id', null)->orderBy('order')->get();
            $topic->questions = $questions;

            $user = auth()->user();
            $business_selected_id = $user->employee->business_selected_id;

            if ($business_selected_id != null) {
                $questions_business = $topic->questions()->where('business_id', $business_selected_id)->orderBy('order')->get();
                $topic->questions_business = $questions_business;
            } else {
                $topic->questions_business = [];
            }

            return response()->json(['topic' => $topic]);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Categoria não existe!'], 400);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function showWithQuestionsBusiness(Request $request, string $id): JsonResponse
    {
        try {
            $topic = Topic::with('businesses')->findOrFail($id);
            $business_selected_id = $request->user()->employee->business_selected_id;

            $questions = $topic->questions()->where(function ($query) use ($business_selected_id) {
                $query->where('business_id', $business_selected_id);
            })->orderBy('order')->get();

            $topic->questions = $questions;

            $questions_default = $topic->questions()->where('business_id', null)->orderBy('order')->get();
            $topic->questions_default = $questions_default;

            return response()->json(['topic' => $topic]);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Categoria não existe!'], 400);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function update(TopicUpdate $request, string $id)
    {
        try {
            $data = $request->validated();
            $employee = $request->user()->employee ?? null;
            $business_selected_id = $employee->business_selected_id ?? false;
            if (!$business_selected_id) {
                if (!$data['default'])
                    return response()->json(['erorrs' => ['msg' => 'Você precisa selecionar um negócio!']], 422);
            }

            if (!$data['default']) {
                $data['active'] = true;
                $data['required'] = false;
            }

            $topic = Topic::findOrFail($id);
            $topic->update($data);

            if (!$data['default']) {
                $topic->businesses()->sync($data['businesses'] ?? []);
            } else {
                $topic->businesses()->sync([]);
            }

            $questions = $data['questions'] ?? [];
            $existingQuestions = $topic->questions()->get();
            $existingQuestionsById = $existingQuestions->keyBy('id');
            $existingQuestionIds = $existingQuestions->pluck('id')->toArray();
            $submittedQuestionIds = [];

            foreach ($questions as $questionData) {
                if (isset($questionData['id']) && in_array($questionData['id'], $existingQuestionIds)) {
                    // update existing question
                    $question = $existingQuestionsById[$questionData['id']];
                    $question->update([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                    ]);
                    $submittedQuestionIds[] = $questionData['id'];
                } else {
                    // new question
                    $newQuestion = $topic->questions()->create([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                        'business_id' => null,
                    ]);
                    $submittedQuestionIds[] = $newQuestion->id;
                }
            }

            $questionsToDelete = $existingQuestions->whereNotIn('id', $submittedQuestionIds);
            foreach ($questionsToDelete as $question) {
                $question->delete();
            }

            $topic->load('questions', 'businesses');
            return response()->json(['topic' => $topic]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function updateDefault(Request $request, string $id)
    {
        try {
            $data = $request->all();
            $topic = Topic::findOrFail($id);
            $topic->update(
                [
                    'name' => $data['name'],
                    'description' => $data['description'],
                    'required' => isset($data['required']),
                    'active' => isset($data['active']),
                ]
            );

            $user = $request->user();

            $questions = $data['questions'] ?? [];

            $existingQuestions = $topic->questions()
                ->where('business_id', null)
                ->get();

            $existingQuestionsById = $existingQuestions->keyBy('id');
            $existingQuestionIds = $existingQuestions->pluck('id')->toArray();
            $submittedQuestionIds = [];

            foreach ($questions as $questionData) {
                if (isset($questionData['id']) && in_array($questionData['id'], $existingQuestionIds)) {
                    // Atualizar pergunta existente
                    $question = $existingQuestionsById[$questionData['id']];
                    $question->update([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                    ]);
                    $submittedQuestionIds[] = $questionData['id'];
                } else {
                    // Nova pergunta
                    $newQuestion = $topic->questions()->create([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                        'business_id' => null,
                    ]);
                    $submittedQuestionIds[] = $newQuestion->id;
                }
            }

            // Remover apenas as perguntas que não foram enviadas
            $topic->questions()
                ->where('business_id', null)
                ->whereNotIn('id', $submittedQuestionIds)
                ->forceDelete();

            $topic->load(['questions']);

            // Atualizar perguntas de negócios
            $business_selected_id = $user->employee->business_selected_id;

            $questionsBusiness = $data['questions_business'] ?? [];
            $existingQuestionsBusiness = $topic->questions()
                ->where('business_id', $business_selected_id)
                ->get();

            $existingQuestionsBusinessById = $existingQuestionsBusiness->keyBy('id');
            $existingQuestionBusinessIds = $existingQuestionsBusiness->pluck('id')->toArray();
            $submittedQuestionBusinessIds = [];

            foreach ($questionsBusiness as $questionData) {
                if (isset($questionData['id']) && in_array($questionData['id'], $existingQuestionBusinessIds)) {
                    // Atualizar pergunta existente
                    $question = $existingQuestionsBusinessById[$questionData['id']];
                    $question->update([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                    ]);
                    $submittedQuestionBusinessIds[] = $questionData['id'];
                } else {
                    // Nova pergunta
                    $newQuestion = $topic->questions()->create([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                        'business_id' => $business_selected_id,
                    ]);
                    $submittedQuestionBusinessIds[] = $newQuestion->id;
                }
            }

            // Remover apenas as perguntas que não foram enviadas
            $topic->questions()
                ->where('business_id', $business_selected_id)
                ->whereNotIn('id', $submittedQuestionBusinessIds)
                ->forceDelete();

            return response()->json(['topic' => $topic]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }


    public function updateDefaultForBusiness(Request $request, string $id)
    {
        try {
            $data = $request->all();
            $topic = Topic::findOrFail($id);

            $user = $request->user();
            $business_selected_id = $user->employee->business_selected_id;

            $questions = $data['questions'] ?? [];

            $existingQuestions = $topic->questions()
                ->where('business_id', $business_selected_id)
                ->get();

            $existingQuestionsById = $existingQuestions->keyBy('id');
            $existingQuestionIds = $existingQuestions->pluck('id')->toArray();
            $submittedQuestionIds = [];

            foreach ($questions as $questionData) {
                if (isset($questionData['id']) && in_array($questionData['id'], $existingQuestionIds)) {
                    // Atualizar pergunta existente
                    $question = $existingQuestionsById[$questionData['id']];
                    $question->update([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                    ]);
                    $submittedQuestionIds[] = $questionData['id'];
                } else {
                    // Nova pergunta
                    $newQuestion = $topic->questions()->create([
                        'description' => $questionData['description'],
                        'evaluation_type' => $questionData['evaluation_type'],
                        'order' => $questionData['order'],
                        'business_id' => $business_selected_id,
                    ]);
                    $submittedQuestionIds[] = $newQuestion->id;
                }
            }

            // Remover apenas as perguntas que não foram enviadas
            $topic->questions()
                ->where('business_id', $business_selected_id)
                ->whereNotIn('id', $submittedQuestionIds)
                ->forceDelete();

            $topic->load(['questions' => function ($query) use ($business_selected_id) {
                $query->where('business_id', $business_selected_id);
            }]);

            return response()->json(['topic' => $topic]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }


    public function destroy(string $id)
    {
        try {
            $topic = Topic::findOrFail($id);
            $topic->questions()->delete();
            $topic->delete();
            return response()->json(['msg' => 'Categoria deletada']);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Verifica se um tópico pode ser excluído, checando vínculos com formulários ativos.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function checkDeletion(string $id): JsonResponse
    {
        try {
            $topic = Topic::withCount(['forms' => function ($query) {
                $query->where('active', true);
            }])->findOrFail($id);

            $can_delete = $topic->forms_count === 0;
            $message = $can_delete ? 'A Categoria pode ser excluída.' : 'Está categoria não pode ser excluída pois está vinculada a formulários ativos.';

            return response()->json([
                'can_delete' => $can_delete,
                'message' => $message
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Categoria não existe!', 'can_delete' => false], 404);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e), 'can_delete' => false], 400);
        }
    }

    public function getTopicsByForm(Request $request)
    {
        $formId = $request->input('form_id', null);
        $user = $request->user();
        $business_selected_id = $user->employee->business_selected_id ?? null;
        $topics = Topic::query()
            ->select('id', 'name')
            ->where(function ($query) use ($business_selected_id) {
                $query->when($business_selected_id, function ($q) use ($business_selected_id) {
                    $q->whereHas('businesses', function ($q2) use ($business_selected_id) {
                        $q2->where('business_topic.business_id', $business_selected_id);
                    });
                });
                $query->orWhere('topics.default', true);
            })
            ->when($formId, function ($query) use ($formId) {
                $query->whereHas('forms', function ($q) use ($formId) {
                    $q->where('form_id', $formId);
                });
            })
            ->with(['questions' => function ($query) use ($business_selected_id) {
                $query->where(function ($q) use ($business_selected_id) {
                    $q->where('questions.business_id', $business_selected_id)
                        ->orWhere('questions.business_id', null);
                });
            }])
            ->where('topics.active', true)
            ->get();
        return response()->json(['topics' => $topics]);
    }
}
