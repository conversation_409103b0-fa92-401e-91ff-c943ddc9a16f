<?php

namespace App\Http\Controllers\Api;

use App\Helpers\SystemHelper;
use App\Helpers\ApiWhatsapp;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Evaluations\StoreEvaluationRequest;
use App\Http\Requests\Api\Evaluations\UpdateEvaluationRequest;
use App\Mail\EvaluationLinkMail;
use App\Models\Business;
use App\Models\Cashback;
use App\Models\Client;
use App\Models\Employee;
use App\Models\Evaluation;
use App\Models\EvaluationResponse;
use App\Models\Notification;
use App\Models\NotificationClient;
use App\Models\Seller;
use App\Models\User;
use App\SiteNotifications\SendEvaluationSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Symfony\Component\Uid\Ulid;

class EvaluationController extends Controller
{
    /**
     * Lista todas as avaliações.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $per_page = $request->per_page ?? 5;

            $query = Evaluation::query();

            if ($request->has('shipping_status') && !empty($request->shipping_status)) {
                $query->where('shipping_status', $request->shipping_status);
            }

            if ($request->has('form_id') && !empty($request->form_id)) {
                $query->where('form_id', $request->form_id);
            }

            if ($request->has('cashback_filter') && !empty($request->cashback_filter)) {
                if ($request->cashback_filter === 'with_cashback') {
                    $query->where('with_cashback', true);
                } elseif ($request->cashback_filter === 'without_cashback') {
                    $query->where('with_cashback', false);
                }
            }

            $selected_business_id = Auth::user()->employee->business_selected_id;
            $query->where('business_id', $selected_business_id);

            $query->orderBy('shipping_date', 'desc');

            $evaluations = $per_page <= 0 ? $query->get() : $query->paginate($per_page);

            // Carregar relações incluindo soft-deleted com campos otimizados
            $evaluations->load([
                'form' => function ($query) {
                    $query->withTrashed()->select(['id', 'name', 'deleted_at']);
                },
                'business' => function ($query) {
                    $query->withTrashed()->select(['id', 'name', 'deleted_at']);
                },
                'client' => function ($query) {
                    $query->withTrashed()->select(['id', 'user_id', 'deleted_at'])
                          ->with(['user' => function ($userQuery) {
                              $userQuery->withTrashed()->select(['id', 'name', 'cpf', 'email', 'deleted_at']);
                          }]);
                },
                'responses' => function ($query) {
                    $query->select(['id', 'evaluation_id', 'question_id', 'response', 'justification'])
                          ->with(['question' => function ($q) {
                              $q->withTrashed()
                                ->select(['id', 'topic_id', 'description', 'evaluation_type', 'deleted_at'])
                                ->with(['topic' => function ($topicQuery) {
                                    $topicQuery->withTrashed()->select(['id', 'name', 'deleted_at']);
                                }]);
                          }]);
                },
            ]);

            // Transformar os itens para o formato desejado
            $isPaginated = $per_page > 0;
            $itemsToTransform = $isPaginated ? $evaluations->items() : $evaluations;

            $transformedItems = collect($itemsToTransform)->map(function ($evaluation) {
                $evalData = $evaluation->only([
                    'id', 'token', 'anonymous', 'sending_type', 'email', 'number_phone',
                    'shipping_date', 'form_id', 'shipping_status', 'expiration_date',
                    'business_id', 'cashback_percentage', 'cashback_expiration_date',
                    'client_id', 'cashback_created_at_send', 'created_at', 'updated_at',
                    'notified_expiration_at', 'amount', 'with_cashback', 'notified_responded_at'
                ]);

                if ($evaluation->relationLoaded('form') && $evaluation->form) {
                    $evalData['form'] = $evaluation->form->only(['id', 'name', 'deleted_at']);
                } else {
                    $evalData['form'] = null;
                }

                if ($evaluation->relationLoaded('business') && $evaluation->business) {
                    $evalData['business'] = $evaluation->business->only(['id', 'name', 'deleted_at']);
                } else {
                    $evalData['business'] = null;
                }

                if ($evaluation->relationLoaded('client') && $evaluation->client) {
                    $clientOutput = $evaluation->client->only(['id', 'user_id', 'deleted_at']);
                    if ($evaluation->client->relationLoaded('user') && $evaluation->client->user) {
                        $clientOutput['user'] = $evaluation->client->user->only(['id', 'name', 'cpf', 'email', 'deleted_at']);
                    } else {
                        $clientOutput['user'] = null;
                    }
                    $evalData['client'] = $clientOutput;
                } else {
                    $evalData['client'] = null;
                }

                if ($evaluation->relationLoaded('responses')) {
                    $evalData['responses'] = $evaluation->responses->map(function ($response) {
                        $responseData = $response->only(['id', 'evaluation_id', 'question_id', 'response', 'justification']);
                        if ($response->relationLoaded('question') && $response->question) {
                            $questionData = $response->question->only(['id', 'topic_id', 'description', 'evaluation_type', 'deleted_at']);
                            if ($response->question->relationLoaded('topic') && $response->question->topic) {
                                $questionData['topic'] = $response->question->topic->only(['id', 'name', 'deleted_at']);
                            } else {
                                $questionData['topic'] = null;
                            }
                            $responseData['question'] = $questionData;
                        } else {
                            $responseData['question'] = null;
                        }
                        return $responseData;
                    });
                } else {
                    $evalData['responses'] = [];
                }
                return $evalData;
            });

            if ($isPaginated) {
                $responsePayload = new \Illuminate\Pagination\LengthAwarePaginator(
                    $transformedItems,
                    $evaluations->total(),
                    $evaluations->perPage(),
                    $evaluations->currentPage(),
                    ['path' => request()->url(), 'query' => request()->query()]
                );
            } else {
                $responsePayload = $transformedItems;
            }

            return response()->json(['evaluations' => $responsePayload], 200);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Erro ao listar avaliações.', 'error' => $e->getMessage()], 400);
        }
    }

    public function businessesClient(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            if ($user->login_type !== 'client') {
                throw new Exception("Você deve entrar como cliente!");
            }

            $evaluations = Evaluation::where('client_id', $user->client->id)
                ->where('anonymous', false)
                ->get();

            $businessIds = $evaluations->pluck('business_id')->unique()->toArray();

            $businesses = Business::whereIn('id', $businessIds)->get();

            return response()->json(['businesses' => $businesses], 200);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Erro ao listar negócios.', 'error' => $e->getMessage()], 400);
        }
    }

    public function indexClient(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            if ($user->login_type !== 'client') {
                throw new Exception("Você deve entrar como cliente!");
            }
            $per_page = $request->per_page ?? 5;

            $query = Evaluation::query()
                ->where('client_id', $user->client->id)
                ->orderBy('shipping_date', 'desc')
                ->with([
                    'form' => fn($q) => $q->withTrashed(),
                    'business' => fn($q) => $q->withTrashed()->with(['parameters']),
                    'client' => fn($q) => $q->withTrashed(),
                    'responses.question' => fn($q) => $q->withTrashed()->with(['topic' => fn($q) => $q->withTrashed()]),
                ]);

            if ($request->filled('shipping_status')) {
                $query->where('shipping_status', $request->shipping_status);
            }
            if ($request->filled('business_id')) {
                $query->where('business_id', $request->business_id);
            }

            $evaluations = $per_page <= 0
                ? $query->get()
                : $query->paginate($per_page);


            $evaluations->transform(function ($evaluation) {
                $evaluation->link = config('app.frontend_url') . '/evaluations/respond/' . $evaluation->token;
                return $evaluation;
            });

            return response()->json(['evaluations' => $evaluations], 200);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Erro ao listar avaliações.', 'error' => $e->getMessage()], 400);
        }
    }

    /**
     * Armazena uma nova avaliação.
     */
    public function store(StoreEvaluationRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();
            $data['token'] = Ulid::generate();
            $selected_business_id = Auth::user()->employee->business_selected_id;
            $business = Business::findOrFail($selected_business_id);
            $planAuthFunctions = $business->plan->authorizedFunctions->toArray();
            $parameters = $business->parameters;
            $data['business_id'] = $selected_business_id;
            $data['shipping_status'] = 'SENT';
            $data['anonymous'] = false;
            $data['shipping_date'] = Carbon::now();
            $data['cashback_percentage'] = $data['with_cashback'] ? $parameters->cashback_percentage : 0;
            $cashback_expiration_hours = $parameters->cashback_expiration_hours;
            $canSetCashbackValue = $parameters->can_set_cashback_value;
            $withCashback = $data['with_cashback'] ?? false;
            $data['cashback_expiration_date'] = Carbon::now()->addHours($cashback_expiration_hours);
            $data['amount'] = $canSetCashbackValue ? $request->input('amount', 0) : 0;

            $evaluation_expiration_hours = $parameters->evaluation_expiration_hours;
            $data['expiration_date'] = Carbon::now()->addHours($evaluation_expiration_hours);

            if ($canSetCashbackValue && $data['amount'] == 0 && $withCashback)
                throw new Exception('Você deve fornecer um valor de produto/serviço válido.');

            // Obter o intervalo mínimo de dias para novas avaliações
            $business = Business::findOrFail($data['business_id']);
            $evaluation_interval_day = $business->parameters->evaluation_interval_day;

            // Obter o intervalo mínimo de dias para novas avaliações
            $business = Business::findOrFail($data['business_id']);
            $evaluation_interval_day = $business->parameters->evaluation_interval_day;

            $clientController = new ClientController();
            $contactData = [
                'email' => $data['email'] ?? null,
                'number_phone' => $data['number_phone'] ?? null,
                'name' => $data['name'] ?? null,
            ];
            $client = $clientController->findOrCreateClientForEvaluation($contactData, $data['business_id']);

            $data['client_id'] = $client->id;

            // Cliente existe, verificar se pode responder a uma nova avaliação para o mesmo formulário
            $last_evaluation = Evaluation::where('client_id', $client->id)
                ->where('shipping_status', 'RESPONDED')
                ->where('form_id', $data['form_id'])
                ->where('business_id', $data['business_id'])
                ->orderBy('shipping_date', 'desc')
                ->first();

            $data['cashback_created_at_send'] = false; // Default value

            if ($withCashback && $last_evaluation) {
                $next_allowed_date = $last_evaluation->shipping_date->addDays($evaluation_interval_day);
                //Para ter o cashback automatico, o cliente deve ter
                // - respondido a avaliação recentemente para o mesmo formulário dentro do intervalo mínimo de dias
                // - A avaliação deve ter cashback
                if (Carbon::now()->lt($next_allowed_date) && $withCashback) {
                    $cashbackAmount = floatval($data['amount']) * (floatval($parameters->cashback_percentage) / 100);
                    // criar Cashback automaticamente
                    Cashback::create([
                        'status' => 'pending',
                        'expiration_date' => Carbon::parse($data['cashback_expiration_date']),
                        'percentage' => $parameters->cashback_percentage,
                        'amount' => $cashbackAmount,
                        'business_id' => $data['business_id'],
                        'client_id' => $client->id,
                        'sending_type' => $data['sending_type'],
                        'creation_type' => 'evaluation',
                    ]);

                    // Marcar que o cashback foi criado no envio
                    $data['cashback_created_at_send'] = true;
                }
            }

            $evaluation = Evaluation::create($data);
            $evaluationLink = config('app.frontend_url') . '/evaluations/respond/' . $data['token'];

            if ($planAuthFunctions['site_notifications']) {
                (new SendEvaluationSiteNotification(
                    $business->logo,
                    $business->id,
                    $business->name,
                    $evaluationLink
                ))->send([$client->user_id]);
            }

            if ($data['sending_type'] === 'EMAIL') {
                $logo = $business->logo != null ?
                    Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;

                Mail::to($data['email'])->send(new EvaluationLinkMail(
                    $evaluationLink,
                    $business->name,
                    $logo,
                    $business->url_facebook,
                    $business->url_google,
                    $business->url_instagram,
                    $business->url_linkedin
                ));
            } else if ($data['sending_type'] === 'WHATSAPP') {
                $userPhone = $data['number_phone'];
                $title = 'Olá! ' . explode(" ", $client->user->name)[0];
                $description = "Tá liberado ser sincero, viu? 😅\n" .
                    "Queremos sua opinião real oficial!\n" .
                    "Se gostou, elogia. Se não gostou, puxa a orelha. Mas fala com a gente! 💬\n" .
                    ($withCashback ? "\nE ainda tem cashback te esperando! 💸\n\n" : "") .
                    "👉 Avalia rapidinho:\n" .
                    $evaluationLink . "\n\n" .
                    "Com carinho (e coração aberto!),\n" .
                    "*$business->name*\n\n" .
                    "⚠️ Caso o link não tenha ficado clicável, responda com um \"i\"";
                if($parameters->whatsapp_instance_key == null) {
                    throw new Exception('Whatsapp não conectado.');
                }
                try {
                    ApiWhatsapp::sendMessageWithInstance(
                        $parameters->whatsapp_instance_key,
                        $userPhone,
                        $title,
                        $description,
                        $business->name
                    );
                } catch (Exception $ex) {
                    // se o usuário for cadastrado, tenta enviar via email a avaliação
                    if($ex->getMessage() == 'Whatsapp não conectado.'){
                        throw $ex;
                    }
                    if($ex->getMessage() == 'Nenhum whatsapp encontrado para o número informado.'){
                        return response()->json(['msg' => 'Nenhum whatsapp encontrado para o número informado.'], 404);
                    }
                    if ($client && $client->user && $client->user->email && $client->user->email != "") {
                        $logo = $business->logo
                            ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7))
                            : null;
                        Mail::to($client->user->email)->send(new EvaluationLinkMail(
                            $evaluationLink,
                            $business->name,
                            $logo,
                            $business->url_facebook,
                            $business->url_google,
                            $business->url_instagram,
                            $business->url_linkedin
                        ));
                    } else throw new Exception('Não foi possível enviar a avaliação via WhatsApp e o cliente não possui email cadastrado.');
                }
            }

            DB::commit();

            if ($data['cashback_created_at_send']) {
                $msg = 'Avaliação enviada. Já registramos o cashback para o cliente informado, pois ele respondeu uma avaliação recentemente para o mesmo formulário.';
            } else {
                $msg = 'Avaliação enviada com sucesso.';
            }

            return response()->json([
                'evaluation' => $evaluation,
                'msg' => $msg,
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            if($e->getMessage() == 'Whatsapp não conectado.'){
                $parameters->whatsapp_instance_key = null;
                $parameters->save();

                return response()->json(['msg' => 'Whatsapp não conectado.'], 400);
            }
            return response()->json(['msg' => 'Erro ao criar avaliação: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Exibe uma avaliação específica.
     */
    public function show($id)
    {
        try {
            $evaluation = Evaluation::with(['form', 'business', 'client', 'responses.question'])->findOrFail($id);
            return response()->json(['evaluation' => $evaluation], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Avaliação não encontrada.'], 404);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Erro ao exibir avaliação.', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Exibe uma avaliação específica por token.
     */
    public function getByToken($token): JsonResponse
    {
        try {
            $evaluation = Evaluation::where('token', $token)
                ->with([
                    'form',
                    'form.topics',
                    'business',
                    'client',
                    'responses.question'])
                ->firstOrFail();

            //load questions of topics where business_id is equal to the business_id of the evaluation or null
            $evaluation->form->topics->load(['questions' => function ($query) use ($evaluation) {
                $query->where('business_id', $evaluation->business_id)
                    ->orWhereNull('business_id');
            }]);

            if ($evaluation->business->logo != null) {
                $evaluation->business->logo = Storage::disk('s3')->temporaryUrl($evaluation->business->logo, Carbon::now()->addHour());
            }

            $isExpired = $evaluation->shipping_status === 'EXPIRED' || Carbon::parse($evaluation->expiration_date)->isPast();

            return response()->json([
                'evaluation' => $evaluation,
                'isExpired' => $isExpired
            ], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Avaliação não encontrada.'], 404);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Erro ao  buscar avaliação.', 'error' => $e->getMessage()], 500);
        }
    }

    public function storeResponse(Request $request, $token): JsonResponse
    {
        DB::beginTransaction();

        try {
            $evaluation = Evaluation::where('token', $token)->firstOrFail();

            $evaluation->anonymous = $request->anonymous;
            $evaluation->email = $request->email ?? $evaluation->email;
            $evaluation->number_phone = isset($request->phone_number) ? preg_replace('/\D/', '', $request->phone_number) : $evaluation->number_phone;
            $evaluation->shipping_status = 'RESPONDED';

            $evaluation->save();

            if (!$evaluation->cashback_created_at_send && $evaluation->client_id != null && $evaluation->with_cashback) {
                $cashbackAmount = $evaluation->amount * ($evaluation->cashback_percentage / 100);
                Cashback::create([
                    'status' => 'pending',
                    'expiration_date' => $evaluation->cashback_expiration_date,
                    'percentage' => $evaluation->cashback_percentage,
                    'amount' => $cashbackAmount,
                    'business_id' => $evaluation->business_id,
                    'client_id' => $evaluation->client_id,
                    'sending_type' => $evaluation->sending_type,
                    'creation_type' => 'evaluation',
                ]);
            }

            $evaluation->responses()->delete();

            foreach ($request->responses as $questionId => $responseValue) {
                EvaluationResponse::create([
                    'evaluation_id' => $evaluation->id,
                    'question_id' => $questionId,
                    'response' => $responseValue,
                    'justification' => $request->justifications[$questionId] ?? null,
                ]);
            }
            DB::commit();

            $response = [
                'error' => false,
                'msg' => 'Resposta enviada com sucesso.',
                'evaluation' => $evaluation,
            ];

            return response()->json($response, 200);
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json([
                'error' => true,
                'msg' => 'Avaliação não encontrada.'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => true,
                'msg' => 'Erro ao processar a avaliação. ' . $e->getMessage()
            ]);
        }
    }

    public function validateEmail(Request $request): JsonResponse
    {
        $email = $request->email;
        $cpf = $request->cpf;

        $client = Client::whereHas('user', function ($query) use ($email) {
            $query->where('email', $email);
        })->first();

        $employee = Employee::whereHas('user', function ($query) use ($email) {
            $query->where('email', $email);
        })->first();

        $seller = Seller::whereHas('user', function ($query) use ($email) {
            $query->where('email', $email);
        })->first();

        $message = null;

        if ($client && $client->user->cpf !== $cpf) {
            $message = 'Já existe um cliente cadastrado com este e-mail.';
        } else if ($employee && $employee->user->cpf !== $cpf) {
            $message = 'Já existe um funcionário cadastrado com este e-mail.';
        } else if ($seller && $seller->user->cpf !== $cpf) {
            $message = 'Já existe um vendedor cadastrado com este e-mail.';
        }

        return response()->json([
            'error' => $message !== null,
            'msg' => $message,
        ]);
    }

    /**
     * Atualiza uma avaliação existente.
     */
    public function update(UpdateEvaluationRequest $request, $id)
    {
        try {
            $evaluation = Evaluation::findOrFail($id);
            $evaluation->update($request->validated());

            return response()->json(['evaluation' => $evaluation], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Avaliação não encontrada.'], 404);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Erro ao atualizar avaliação.', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove uma avaliação.
     */
    public function destroy($id)
    {
        try {
            $evaluation = Evaluation::findOrFail($id);
            $evaluation->delete();

            return response()->json(['msg' => 'Avaliação deletada com sucesso.'], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Avaliação não encontrada.'], 404);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Erro ao deletar avaliação.', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Valida o total de envios de um negócio
     */
    public function validateBusinessShipments(): JsonResponse
    {
        try {
            $selected_business_id = Auth::user()->employee->business_selected_id;

            if (!$selected_business_id) {
                throw new Exception("Você deve selecionar um negócio.");
            }

            $business = Business::with('plan')->findOrFail($selected_business_id);
            $plan = $business->plan;

            if (!$plan) {
                throw new Exception("Negócio não possui plano associado.");
            }

            // Contar avaliações do mês atual ou do período de dias conforme o plano
            $emailEvaluationQuery = Evaluation::where('business_id', $selected_business_id)
                ->where('sending_type', 'EMAIL');
            $whatsappEvaluationQuery = Evaluation::where('business_id', $selected_business_id)
                ->where('sending_type', 'WHATSAPP');

            // Contar total de envios de notificações usando o number_clients
            $emailNotificationQuery = Notification::where('business_id', $selected_business_id)
                ->where('sending_type', 'EMAIL');
            $whatsappNotificationQuery = Notification::where('business_id', $selected_business_id)
                ->where('sending_type', 'WHATSAPP');

            // Contar cashbacks diretos
            $emailDirectCashbackQuery = Cashback::where('business_id', $selected_business_id)
                ->where('sending_type', 'EMAIL')
                ->where('creation_type', 'direct');
            $whatsappDirectCashbackQuery = Cashback::where('business_id', $selected_business_id)
                ->where('sending_type', 'WHATSAPP')
                ->where('creation_type', 'direct');

            // Definir datas para email
            if ($plan->authorizedFunctions->email_sends_type === 'monthly') {
                $emailStartDate = Carbon::now()->startOfMonth();
                $emailEndDate = Carbon::now()->endOfMonth();
            } else {
                $emailStartDate = Carbon::now()->subDays($plan->authorizedFunctions->email_sends_days - 1)->startOfDay();
                $emailEndDate = Carbon::now()->endOfDay();
            }

            // Definir datas para whatsapp
            if ($plan->authorizedFunctions->whatsapp_sends_type === 'monthly') {
                $whatsappStartDate = Carbon::now()->startOfMonth();
                $whatsappEndDate = Carbon::now()->endOfMonth();
            } else {
                $whatsappStartDate = Carbon::now()->subDays($plan->authorizedFunctions->whatsapp_sends_days - 1)->startOfDay();
                $whatsappEndDate = Carbon::now()->endOfDay();
            }

            // Contagem de envios de email
            $emailEvaluationQuery->whereBetween('shipping_date', [$emailStartDate, $emailEndDate]);
            $totalEmailEvaluations = $emailEvaluationQuery->count();
            $totalEmailNotifications = $emailNotificationQuery
                ->whereBetween('created_at', [$emailStartDate, $emailEndDate])
                ->whereNull('canceled_at')
                ->sum('number_clients');
            $totalEmailDirectCashbacks = $emailDirectCashbackQuery
                ->whereBetween('created_at', [$emailStartDate, $emailEndDate])
                ->count();
            $totalEmailShipments = $totalEmailEvaluations + $totalEmailNotifications + $totalEmailDirectCashbacks;

            // Contagem de envios de whatsapp
            $whatsappEvaluationQuery->whereBetween('shipping_date', [$whatsappStartDate, $whatsappEndDate]);
            $totalWhatsappEvaluations = $whatsappEvaluationQuery->count();
            $totalWhatsappNotifications = $whatsappNotificationQuery
                ->whereBetween('created_at', [$whatsappStartDate, $whatsappEndDate])
                ->whereNull('canceled_at')
                ->sum('number_clients');
            $totalWhatsappDirectCashbacks = $whatsappDirectCashbackQuery
                ->whereBetween('created_at', [$whatsappStartDate, $whatsappEndDate])
                ->count();
            $totalWhatsappShipments = $totalWhatsappEvaluations + $totalWhatsappNotifications + $totalWhatsappDirectCashbacks;

            // Calcular envios restantes (já incluindo cashbacks diretos no totalShipments)
            $remainingEmailShipments = max(0, $plan->authorizedFunctions->total_email_sends - $totalEmailShipments);
            $remainingWhatsappShipments = max(0, $plan->authorizedFunctions->total_whatsapp_sends - $totalWhatsappShipments);

            return response()->json([
                'error' => false,
                'data' => [
                    // Email data
                    'total_email_sends_plan' => $plan->authorizedFunctions->total_email_sends,
                    'email_sends_type' => $plan->authorizedFunctions->email_sends_type,
                    'email_sends_days' => $plan->authorizedFunctions->email_sends_days,
                    'total_email_evaluations' => $totalEmailEvaluations,
                    'total_email_notifications' => $totalEmailNotifications,
                    'total_email_direct_cashbacks' => $totalEmailDirectCashbacks,
                    'total_email_shipments' => $totalEmailShipments,
                    'remaining_email_shipments' => $remainingEmailShipments,
                    'email_start_date' => $emailStartDate->format('Y-m-d H:i:s'),
                    'email_end_date' => $emailEndDate->format('Y-m-d H:i:s'),

                    // WhatsApp data
                    'total_whatsapp_sends_plan' => $plan->authorizedFunctions->total_whatsapp_sends,
                    'whatsapp_sends_type' => $plan->authorizedFunctions->whatsapp_sends_type,
                    'whatsapp_sends_days' => $plan->authorizedFunctions->whatsapp_sends_days,
                    'total_whatsapp_evaluations' => $totalWhatsappEvaluations,
                    'total_whatsapp_notifications' => $totalWhatsappNotifications,
                    'total_whatsapp_direct_cashbacks' => $totalWhatsappDirectCashbacks,
                    'total_whatsapp_shipments' => $totalWhatsappShipments,
                    'remaining_whatsapp_shipments' => $remainingWhatsappShipments,
                    'whatsapp_start_date' => $whatsappStartDate->format('Y-m-d H:i:s'),
                    'whatsapp_end_date' => $whatsappEndDate->format('Y-m-d H:i:s'),
                ],
                'success' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'msg' => 'Erro ao validar envios. ' . $e->getMessage()
            ], 400);
        }
    }
}
