<?php

namespace App\Http\Controllers\Api;

use App\Enums\DashboardPeriods;
use App\Helpers\ApiUser;
use App\Http\Requests\Api\Dashboard\DashboardRequest;
use App\Http\Requests\Api\Dashboard\DashboardRequestFilterQuestion;
use App\Http\Requests\Api\Dashboard\DashboardRequestShow;
use App\Models\Business;
use App\Models\EvaluationResponse;
use App\Models\Question;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Form;
use App\Models\Topic;
use Exception;

class DashboardController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(DashboardRequest $request)
    {
        $query = Topic::query()
            ->join('questions', 'topic_id', '=', 'topics.id')
            ->join('evaluation_responses', 'question_id', '=', 'questions.id')
            ->join('evaluations', 'evaluation_responses.evaluation_id', '=', 'evaluations.id')
            ->where('topics.active', true)
            ->select('topics.id', 'topics.name', 'topics.default')
            ->selectRaw('AVG(CASE WHEN questions.evaluation_type = "five_star"
                                            AND evaluation_responses.response IS NOT NULL
                                            AND evaluation_responses.response != "" THEN
                                                evaluation_responses.response END) as average_response')
            ->groupBy('topics.id', 'topics.name', 'topics.default');

        $userLogged = $request->user();
        $userSelectedBusinessId = $userLogged->employee->business_selected_id;
        $form_id = $request->input('form_id', null);
        $periodSelected = $request->input('period', DashboardPeriods::all->value);
        $now = Carbon::now();
        $periods = [
            DashboardPeriods::today->value => [
                'startDate' => $now->copy()->startOfDay(),
                'endDate' => $now->copy()->endOfDay(),
            ],
            DashboardPeriods::last_30days->value => [
                'startDate' => $now->copy()->sub('30 days')->startOfMonth(),
                'endDate' => $now->copy()->endOfDay(),
            ],
        ];

        if (!$userSelectedBusinessId) {
            throw new Exception("Você precisa selecionar um negócio para continuar!");
        }

        if ($form_id) {
            $query->where('evaluations.form_id', $form_id);
        }

        if ($periodSelected !== DashboardPeriods::all->value) {
            $startDate = $periods[$periodSelected]['startDate'] ?? null;
            $endDate = $periods[$periodSelected]['endDate'] ?? null;

            if ($periodSelected === DashboardPeriods::especific->value) {
                $startDate = Carbon::parse($request->input('startDate', null))->startOfDay();
                $endDate = Carbon::parse($request->input('endDate', null))->endOfDay();

                if (!$startDate || !$endDate)
                    throw new Exception("Você precisa especificar as datas de início e fim!");
            }

            $query->whereBetween('evaluations.shipping_date', [$startDate, $endDate])
                ->whereBetween('evaluation_responses.created_at', [$startDate, $endDate]);
        }

        $query->where('evaluations.business_id', $userSelectedBusinessId);

        $query = $query->get()->toArray();

        $topicsDefault = array_values(array_filter($query, function ($element) {
            return $element['default'];
        }));

        $topicsNonDefault = array_values(array_filter($query, function ($element) {
            return !$element['default'];
        }));

        return response()->json(['topicsDefault' => $topicsDefault, 'topicsNonDefault' => $topicsNonDefault]);
    }

    public function relatorySeller(DashboardRequest $request, $businessId)
    {
        $query = Topic::query()
            ->join('questions', 'topic_id', '=', 'topics.id')
            ->join('evaluation_responses', 'question_id', '=', 'questions.id')
            ->join('evaluations', 'evaluation_responses.evaluation_id', '=', 'evaluations.id')
            ->where('topics.active', true)
            ->select('topics.id', 'topics.name', 'topics.default')
            ->selectRaw('AVG(CASE WHEN questions.evaluation_type = "five_star"
                                            AND evaluation_responses.response IS NOT NULL
                                            AND evaluation_responses.response != "" THEN
                                                evaluation_responses.response END) as average_response')
            ->groupBy('topics.id', 'topics.name', 'topics.default');

        $userSelectedBusinessId = $businessId;
        $form_id = $request->input('form_id', null);
        $periodSelected = $request->input('period', DashboardPeriods::all->value);
        $now = Carbon::now();
        $periods = [
            DashboardPeriods::today->value => [
                'startDate' => $now->copy()->startOfDay(),
                'endDate' => $now->copy()->endOfDay(),
            ],
            DashboardPeriods::last_30days->value => [
                'startDate' => $now->copy()->sub('30 days')->startOfMonth(),
                'endDate' => $now->copy()->endOfDay(),
            ],
        ];

        if (!$userSelectedBusinessId) {
            throw new Exception("Você precisa selecionar um negócio para continuar!");
        }

        if ($form_id) {
            $query->where('evaluations.form_id', $form_id);
        }

        if ($periodSelected !== DashboardPeriods::all->value) {
            $startDate = $periods[$periodSelected]['startDate'] ?? null;
            $endDate = $periods[$periodSelected]['endDate'] ?? null;

            if ($periodSelected === DashboardPeriods::especific->value) {
                $startDate = Carbon::parse($request->input('startDate', null))->startOfDay();
                $endDate = Carbon::parse($request->input('endDate', null))->endOfDay();

                if (!$startDate || !$endDate)
                    throw new Exception("Você precisa especificar as datas de início e fim!");
            }

            $query->whereBetween('evaluations.shipping_date', [$startDate, $endDate])
                ->whereBetween('evaluation_responses.created_at', [$startDate, $endDate]);
        }

        $query->where('evaluations.business_id', $userSelectedBusinessId);

        $query = $query->get()->toArray();

        $topicsDefault = array_values(array_filter($query, function ($element) {
            return $element['default'];
        }));

        $topicsNonDefault = array_values(array_filter($query, function ($element) {
            return !$element['default'];
        }));

        $business = Business::find($businessId);

        return response()->json([
            'topicsDefault' => $topicsDefault,
            'topicsNonDefault' => $topicsNonDefault,
            'business' => $business
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(DashboardRequestShow $request, string $id)
    {
        try {
            // Verificar se o tópico realmente existe
            Topic::findOrFail($id);

            $userLogged = $request->user();
            $userSelectedBusinessId = $userLogged->employee->business_selected_id;
            $periodSelected = $request->input('period', DashboardPeriods::all->value);
            $now = Carbon::parse($request->input('generated_at', null));
            $periods = [
                DashboardPeriods::today->value => [
                    'startDate' => $now->copy()->startOfDay(),
                    'endDate' => $now->copy()->endOfDay(),
                ],
                DashboardPeriods::last_30days->value => [
                    'startDate' => $now->copy()->sub('30 days')->startOfMonth(),
                    'endDate' => $now->copy()->endOfDay(),
                ],
            ];

            if (!$userSelectedBusinessId) {
                throw new Exception("Você precisa selecionar um negócio para continuar!");
            }

            // Pegar todas as questões e as médias (FIVE_STAR)
            $fiveStarQuery = Question::withTrashed()
                ->join('evaluation_responses', 'question_id', '=', 'questions.id')
                ->join('evaluations', 'evaluation_responses.evaluation_id', '=', second: 'evaluations.id')
                ->where(['topic_id' => $id, 'evaluation_type' => 'five_star', 'evaluations.business_id' => $userSelectedBusinessId])
                ->select('questions.id', 'questions.description', 'questions.evaluation_type')
                ->selectRaw('AVG(evaluation_responses.response) as average_response')
                ->groupBy('questions.id', 'questions.description', 'questions.evaluation_type');

            // Pegar todas as questões e as quantiadades de sim/não (YES/NO)
            $yesNoQuery = Question::withTrashed()
                ->join('evaluation_responses', 'question_id', '=', 'questions.id')
                ->join('evaluations', 'evaluation_responses.evaluation_id', '=', second: 'evaluations.id')
                ->where(['topic_id' => $id, 'evaluation_type' => 'yes_no', 'evaluations.business_id' => $userSelectedBusinessId])
                ->select('questions.id', 'questions.description', 'questions.evaluation_type')
                ->selectRaw("SUM(CASE WHEN evaluation_responses.response = 'yes' THEN 1 ELSE 0 END) as yes_count")
                ->selectRaw("SUM(CASE WHEN evaluation_responses.response = 'no' THEN 1 ELSE 0 END) as no_count")
                ->groupBy('questions.id', 'questions.description', 'questions.evaluation_type');

            if ($periodSelected !== DashboardPeriods::all->value) {
                $startDate = $periods[$periodSelected]['startDate'] ?? null;
                $endDate = $periods[$periodSelected]['endDate'] ?? null;

                if ($periodSelected === DashboardPeriods::especific->value) {
                    $startDate = Carbon::parse($request->input('startDate', null))->startOfDay();
                    $endDate = Carbon::parse($request->input('endDate', null))->endOfDay();

                    if (!$startDate || !$endDate)
                        throw new Exception("Você precisa especificar as datas de início e fim!");
                }

                $fiveStarQuery->whereBetween('evaluations.shipping_date', [$startDate, $endDate])
                    ->whereBetween('evaluation_responses.created_at', [$startDate, $endDate]);
                $yesNoQuery->whereBetween('evaluations.shipping_date', [$startDate, $endDate])
                    ->whereBetween('evaluation_responses.created_at', [$startDate, $endDate]);
            }

            $fiveStarQuery = $fiveStarQuery->get()->toArray();
            $yesNoQuery = $yesNoQuery->get()->toArray();

            // Forçar a tipagem de yes_count e no_count
            $yesNoQuery = array_map(function ($question) {
                return [
                    'id' => $question['id'],
                    'description' => $question['description'],
                    'yes_count' => (int)$question['yes_count'],
                    'no_count' => (int)$question['no_count'],
                ];
            }, $yesNoQuery);

            return response()->json(['fiveStarQuestions' => $fiveStarQuery, 'yesNoQuestions' => $yesNoQuery]);
        } catch (ModelNotFoundException $ex) {
            return response()->json(['msg' => 'Categoria não encontrada.'], 404);
        }
    }

    public function getEvaluationsFiltered(DashboardRequestFilterQuestion $request)
    {
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        $filters = $request->input('filters', []);
        $periodSelected = $filters['period'] ?? DashboardPeriods::today->value;
        $formId = $filters['form_id'] ?? null;
        $topicId = $filters['topic_id'] ?? null;
        $questionId = $filters['question_id'] ?? null;
        $evaluationType = $filters['evaluation_type'] ?? 'five_star';
        $answerType = $filters['answer_type'] ?? null;
        $businessSelected = $request->user()->employee->business_selected_id ?? null;
        $now = Carbon::now();
        $periods = [
            DashboardPeriods::today->value => [
                'startDate' => $now->copy()->startOfDay(),
                'endDate' => $now->copy()->endOfDay(),
            ],
            DashboardPeriods::last_30days->value => [
                'startDate' => $now->copy()->sub('30 days')->startOfMonth(),
                'endDate' => $now->copy()->endOfDay(),
            ],
        ];
        try {
            if (!isset($businessSelected))
                throw new Exception("Negócio não encontrado!");

            $query = EvaluationResponse::query()
                ->join('questions', 'evaluation_responses.question_id', '=', 'questions.id')
                ->join('evaluations', 'evaluation_responses.evaluation_id', '=', 'evaluations.id')
                ->join('forms', 'evaluations.form_id', '=', 'forms.id')
                ->where('evaluations.business_id', $businessSelected)
                ->where(function ($query) use ($businessSelected) {
                    $query->where('questions.business_id', $businessSelected)
                        ->orWhereNull('questions.business_id');
                })
                ->select('evaluation_responses.*', 'forms.name as form_name')
                ->with(['question.topic']);

            if (isset($periodSelected) && $periodSelected !== 'ALL') {
                $startDate = $periods[$periodSelected]['startDate'] ?? null;
                $endDate = $periods[$periodSelected]['endDate'] ?? null;

                if ($periodSelected === DashboardPeriods::especific->value) {
                    $startDate = Carbon::parse($filters['startDate'])->startOfDay();
                    $endDate = Carbon::parse($filters['endDate'])->endOfDay();

                    if (!$startDate || !$endDate)
                        throw new Exception("Você precisa especificar as datas de início e fim!");
                }
                $query->whereBetween('evaluation_responses.created_at', [$startDate, $endDate]);
            }

            if (!empty($formId)) {
                $query->where('evaluations.form_id', $formId);
            }

            if (!empty($topicId)) {
                $query->where('questions.topic_id', $topicId);
            }

            if (!empty($questionId)) {
                $query->where('questions.id', $questionId);
            }

            if (!empty($evaluationType)) {
                $query->where('questions.evaluation_type', $evaluationType);
            }

            if (!empty($answerType)) {
                $query->where('response', $answerType);
            }

            $query->orderByDesc('evaluation_responses.created_at');
            $paginator = $perPage <= 0 ?
                $query->get() :
                $query->paginate(
                    $perPage,
                    ['*'],
                    'page',
                    $page
                );
            return response()->json(['responses' => $paginator, 'filters' => $filters]);
        } catch (Exception $ex) {
            return response()->json(['msg' => $ex->getMessage(), 'exception' => get_class($ex)], 422);
        }
    }

    public function getEvaluationsFilteredSeller(DashboardRequestFilterQuestion $request, $businessId)
    {
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        $filters = $request->input('filters', []);
        $periodSelected = $filters['period'] ?? DashboardPeriods::today->value;
        $formId = $filters['form_id'] ?? null;
        $topicId = $filters['topic_id'] ?? null;
        $questionId = $filters['question_id'] ?? null;
        $evaluationType = $filters['evaluation_type'] ?? 'five_star';
        $answerType = $filters['answer_type'] ?? null;
        $now = Carbon::now();
        $periods = [
            DashboardPeriods::today->value => [
                'startDate' => $now->copy()->startOfDay(),
                'endDate' => $now->copy()->endOfDay(),
            ],
            DashboardPeriods::last_30days->value => [
                'startDate' => $now->copy()->sub('30 days')->startOfMonth(),
                'endDate' => $now->copy()->endOfDay(),
            ],
        ];
        try {
            $query = EvaluationResponse::query()
                ->join('questions', 'evaluation_responses.question_id', '=', 'questions.id')
                ->join('evaluations', 'evaluation_responses.evaluation_id', '=', 'evaluations.id')
                ->join('forms', 'evaluations.form_id', '=', 'forms.id')
                ->where('evaluations.business_id', $businessId)
                ->where(function ($query) use ($businessId) {
                    $query->where('questions.business_id', $businessId)
                        ->orWhereNull('questions.business_id');
                })
                ->select('evaluation_responses.*', 'forms.name as form_name')
                ->with(['question.topic']);

            if (isset($periodSelected) && $periodSelected !== 'ALL') {
                $startDate = $periods[$periodSelected]['startDate'] ?? null;
                $endDate = $periods[$periodSelected]['endDate'] ?? null;

                if ($periodSelected === DashboardPeriods::especific->value) {
                    $startDate = Carbon::parse($filters['startDate'])->startOfDay();
                    $endDate = Carbon::parse($filters['endDate'])->endOfDay();

                    if (!$startDate || !$endDate)
                        throw new Exception("Você precisa especificar as datas de início e fim!");
                }
                $query->whereBetween('evaluation_responses.created_at', [$startDate, $endDate]);
            }
            if (!empty($formId)) {
                $query->where('evaluations.form_id', $formId);
            }

            if (!empty($topicId)) {
                $query->where('questions.topic_id', $topicId);
            }

            if (!empty($questionId)) {
                $query->where('questions.id', $questionId);
            }

            if (!empty($evaluationType)) {
                $query->where('questions.evaluation_type', $evaluationType);
            }

            if (!empty($answerType)) {
                $query->where('response', $answerType);
            }

            $query->orderByDesc('evaluation_responses.created_at');
            $paginator = $perPage <= 0 ?
                $query->get() :
                $query->paginate(
                    $perPage,
                    ['*'],
                    'page',
                    $page
                );
            return response()->json([
                'responses' => $paginator,
                'filters' => $filters,
                'business' => Business::find($businessId),
            ]);
        } catch (Exception $ex) {
            return response()->json(['msg' => $ex->getMessage(), 'exception' => get_class($ex)], 422);
        }
    }

    public function getFormsWithoutUserPermission(Request $request)
    {
        try {
            $user = $request->user();
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;
            
            if (!$business_selected_id) {
                throw new Exception("Você precisa selecionar um negócio para continuar!");
            }

            $forms = Form::with(['businesses'])
                    ->where(function ($query) use ($business_selected_id) {
                        $query->where(function ($query) {
                            $query->where('default', true);
                            $query->where('active', true);
                        })->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                            $query->where('business_id', $business_selected_id);
                        });
                    })
                    ->orderBy('name', 'asc')
                    ->get();

            return response()->json(['forms' => $forms]);
        } catch (Exception $ex) {
            return response()->json(['msg' => $ex->getMessage(), 'exception' => get_class($ex)], 422);
        }
    }

}
