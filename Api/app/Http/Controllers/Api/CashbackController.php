<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Cashback\StoreCashbackRequest;
use App\Http\Requests\Api\Cashback\UpdateCashbackRequest;
use App\Models\Business;
use App\Models\Cashback;
use App\Models\User;
use App\Models\Client;
use App\Models\Evaluation;
use App\Models\Notification;
use App\Jobs\SendCashbackDirectNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Mail\CashbackValidationCodeMail;
use App\Helpers\ApiWhatsapp;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;


class CashbackController extends Controller
{
    public function __construct(){}

    public function index(Request $request) {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $status = $request->input('status', 'pending');
        $query = Cashback::query();
        $query->join('clients', 'cashbacks.client_id', '=', 'clients.id')
            ->join('users', 'clients.user_id', '=', 'users.id')
            ->select('cashbacks.*', 'users.name as client_name', 'users.phone_number_1 as client_phone', 'users.email as client_email', 'users.cpf as client_cpf');

        $user = $request->user();
        $canReleaseAllBusiness = $user->employee->authorizedFunction->release_all_business || false;
        $selectedBusinessId = $user->employee->business_selected_id;

        if ($canReleaseAllBusiness) {
            if ($selectedBusinessId) {
                $query->where('cashbacks.business_id', $selectedBusinessId);
            }
        } else {
            if ($selectedBusinessId) {
                $query->where('cashbacks.business_id', $selectedBusinessId);
            }
        }

        if ($status != 'all')
            $query->where('cashbacks.status', '=', $status);

        if ($search) {
            $searchTerm = $search;
            $digitsOnly = preg_replace('/[^0-9]/', '', $searchTerm);

            $query->where(function ($q) use ($searchTerm, $digitsOnly) {
                if (filter_var($searchTerm, FILTER_VALIDATE_EMAIL)) {
                    // Search by Email
                    $q->where('users.email', 'like', "%$searchTerm%");
                } elseif (strlen($digitsOnly) === 11) {
                    $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                    $codes = substr($digitsOnly, 0, 2);
                    $numberDigits = substr($digitsOnly, 2);
                    if (strlen($numberDigits) === 9) {
                        $phoneNumberV2 = $codes . substr($numberDigits, 1);
                    } else if (strlen($numberDigits) === 8) {
                        $phoneNumberV2 = $codes . '9' . $numberDigits;
                    }

                    // Search by CPF (prioritized) OR 11-digit Phone
                    $q->where('users.cpf', 'like', "$digitsOnly%")
                      ->orWhere('users.phone_number_1', 'like', "%$digitsOnly%")
                      ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                } elseif (strlen($digitsOnly) === 10) {
                    $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                    $codes = substr($digitsOnly, 0, 2);
                    $numberDigits = substr($digitsOnly, 2);
                    if (strlen($numberDigits) === 9) {
                        $phoneNumberV2 = $codes . substr($numberDigits, 1);
                    } else if (strlen($numberDigits) === 8) {
                        $phoneNumberV2 = $codes . '9' . $numberDigits;
                    }

                    // Search by 10-digit Phone
                    $q->where('users.phone_number_1', 'like', "%$digitsOnly%")
                        ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                } else {
                    $q->where('users.name', 'like', "%$searchTerm%");
                }
            });
        }

        $query->orderByDesc('cashbacks.expiration_date')
              ->orderBy('client_name', 'asc');
        $totalAmount = $query->sum('amount');
        $paginator = $perPage <= 0 ?
            $query->get() :
            $query->paginate(
                $perPage,
                ['*'],
                'page',
                $page
            );
        return response()->json([
            'cashbacks' => $paginator,
            'totalAmount' => $totalAmount,
            'search' => $search,
            'status' => $status,
        ]);
    }

    public function create() {
    }

    public function store(StoreCashbackRequest $request) {
        $validatedData = $request->validated();

        $cashbacks = [];
        foreach($validatedData['clients_id'] as $client_id){
            $cashbackData = [
                'status' => $validatedData['status'],
                'expiration_date' => Carbon::parse($validatedData['expiration_date'])->setTimezone('America/Sao_Paulo'),
                'percentage' => $validatedData['percentage'],
                'business_id' => $validatedData['business_id'],
                'client_id' => $client_id,
            ];
            array_push($cashbacks, Cashback::create($cashbackData));
        }

        return response()->json(['cashbacks' => $cashbacks], 201);
    }

    public function show(int $id)
    {
        try {
            $cashback = Cashback::query()
                        ->join('clients', 'cashbacks.client_id', '=', 'clients.id')
                        ->join('users', 'clients.user_id', '=', 'users.id')
                        ->join('businesses', 'cashbacks.business_id', '=', 'businesses.id')
                        ->select('cashbacks.*',
                            'users.name as client_name',
                            'users.cpf as client_cpf',
                            'businesses.name as business_name'
                        )
                        ->findOrFail($id);
            return response()->json($cashback);
        } catch (ModelNotFoundException $ex) {
            return response()->json(['msg' => 'Cashback não encontrado.'], 404);
        }
    }

    public function edit(int $id) {
    }

    public function update(UpdateCashbackRequest $request, Cashback $cashback) {
        $validatedData = $request->validated();
        $cashback = Cashback::findOrFail($cashback->id);
        $cashback->status = $validatedData['status'];
        $cashback->save();
        return response()->json(['cashback' => $cashback], 200);
    }

    public function destroy($id) {
        try {
            Cashback::destroy($id);
            return response()->json(['msg' => 'Cashback deletado com sucesso!']);
        } catch (ModelNotFoundException $ex) {
            return response()->json(['msg' => 'Cashback não encontrado.'], 404);
        }
    }

    public function validateStore(StoreCashbackRequest $request) {
        $now = Carbon::now();
        $expirationDate = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('expiration_date'));
        if ($expirationDate->isBefore($now)) {
            return response()->json([
                'msg' => 'Os dados fornecidos são inválidos.',
                'errors' => [
                    'expiration_date' => ['A data de expiração deve ser posterior ao momento atual.']
                ]
            ], 422);
        }

        return response()->json(['success' => true]);
    }

    public function validateUpdate(UpdateCashbackRequest $request, Cashback $id) {
        return response()->json(['success' => true]);
    }

    public function businessesClient(Request $request)
    {
        try {
            $user = $request->user();
            if ($user->login_type !== 'client') {
                throw new Exception("Você deve entrar como cliente!");
            }

            $cashbacks = Cashback::where('client_id', $user->client->id)
                ->get();

            $businessIds = $cashbacks->pluck('business_id')->unique()->toArray();

            $businesses = Business::whereIn('id', $businessIds)->get();

            return response()->json(['businesses' => $businesses], 200);
        } catch (Exception $e) {
            return response()->json(['msg' => 'Erro ao listar negócios.', 'error' => $e->getMessage()], 400);
        }
    }

    public function indexClient(Request $request) {
        $client = $request->user();
        if ($client == null || $client['login_type'] != 'client')
            return response()->json(['msg' => "Você precisa ser um cliente."], 401);

        $perPage = $request->input('per_page', 10);
        $business_id = $request->input('business_id', null);
        $page = $request->input('page', 1);
        $status = $request->input('status', 'pending');
        $client_id = $client->client->id;
        $query = Cashback::query();
        $query->join('clients', 'cashbacks.client_id', '=', 'clients.id')
            ->join('businesses', 'cashbacks.business_id', '=', 'businesses.id')
            ->where('clients.id', '=', $client_id)
            ->select('cashbacks.*', 'businesses.name as business_name');
        if ($status != 'all')
            $query->where('cashbacks.status', '=', $status);

        if ($business_id)
            $query->where('cashbacks.business_id', '=', $business_id);

        $query->orderByDesc('cashbacks.expiration_date')
              ->orderBy('business_name', 'asc');

        $totalAmount = Cashback::where('client_id', $client_id)
            ->whereHas('business.parameters', function ($q) {
                $q->where('can_set_cashback_value', true);
            })
            ->when($status != 'all', function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->when($business_id, function ($q) use ($business_id) {
                return $q->where('business_id', $business_id);
            })
            ->sum('amount');

        $paginator = $perPage <= 0 ?
            $query->get() :
            $query->paginate(
                $perPage,
                ['*'],
                'page',
                $page
            );
        $paginator->load('business.parameters');
        return response()->json([
            'cashbacks' => $paginator,
            'totalAmount' => $totalAmount,
            'business_id' => $business_id,
            'status' => $status,
        ]);
    }
    public function showClientCashback(Request $request, int $id)
    {
        try {
            $client = $request->user();
            if ($client == null || $client['login_type'] != 'client')
                return response()->json(['msg' => "Você precisa ser um cliente."], 401);
            $client_id = $client->client->id;
            $cashback = Cashback::with('business.parameters')
                ->join('clients', 'cashbacks.client_id', '=', 'clients.id')
                ->join('users', 'clients.user_id', '=', 'users.id')
                ->join('businesses', 'cashbacks.business_id', '=', 'businesses.id')
                ->where('clients.id', '=', $client_id)
                ->select('cashbacks.*',
                    'users.name as client_name',
                    'users.cpf as client_cpf',
                    'businesses.name as business_name'
                )
                ->findOrFail($id);
            return response()->json($cashback);
        } catch (ModelNotFoundException $ex) {
            return response()->json(['msg' => 'Cashback não encontrado.'], 404);
        }
    }

    public function getPendingClient(Request $request)
    {
        $request->validate([
            'search_type' => 'required|string|in:phone,cpf,email',
            'search_value' => 'required|string',
        ]);

        try {
            $employee = $request->user()->employee;
            if (!$employee || !$employee->business_selected_id) {
                return response()->json(['error' => 'Nenhum negócio selecionado para o funcionário.'], 403);
            }
            $businessId = $employee->business_selected_id;

            $searchType = $request->input('search_type');
            $searchValue = $request->input('search_value');

            $clientUserQuery = User::query();

            if ($searchType === 'phone') {
                $cleanedPhone = preg_replace('/\D/', '', $searchValue);
                $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9

                if (strlen($cleanedPhone) === 11) {
                    $codes = substr($cleanedPhone, 0, 2);
                    $numberDigits = substr($cleanedPhone, 2);
                    if (strlen($numberDigits) === 9) {
                        $phoneNumberV2 = $codes . substr($numberDigits, 1);
                    } else if (strlen($numberDigits) === 8) {
                        $phoneNumberV2 = $codes . '9' . $numberDigits;
                    }
                } elseif (strlen($cleanedPhone) === 10) {
                    $codes = substr($cleanedPhone, 0, 2);
                    $numberDigits = substr($cleanedPhone, 2);
                    if (strlen($numberDigits) === 9) {
                        $phoneNumberV2 = $codes . substr($numberDigits, 1);
                    } else if (strlen($numberDigits) === 8) {
                        $phoneNumberV2 = $codes . '9' . $numberDigits;
                    }
                }

                $clientUserQuery->where(function ($q) use ($cleanedPhone, $phoneNumberV2) {
                    $q->where('phone_number_1', $cleanedPhone);
                    if ($phoneNumberV2) {
                        $q->orWhere('phone_number_1', $phoneNumberV2);
                    }
                });
            } elseif ($searchType === 'cpf') {
                $cleanedCpf = preg_replace('/\D/', '', $searchValue);
                $clientUserQuery->where('cpf', $cleanedCpf);
            } elseif ($searchType === 'email') {
                $clientUserQuery->where('email', $searchValue);
            }

            $clientUser = $clientUserQuery->first();

            if (!$clientUser || !$clientUser->client) {
                return response()->json(['error' => 'Cliente não encontrado.'], 404);
            }

            $clientId = $clientUser->client->id;

            $cashbacks = Cashback::where('client_id', $clientId)
                ->where('business_id', $businessId)
                ->where('status', 'pending')
                ->whereNotNull('amount')
                ->get();

            $totalAmount = $cashbacks->sum('amount');

            $cashbacksComIdCriptografado = $cashbacks->map(function ($cashback) {
                $cashbackData = $cashback->only(['amount']);
                $cashbackData['encrypted_id'] = Crypt::encryptString($cashback->id);
                return $cashbackData;
            });

            return response()->json([
                'cashbacks' => $cashbacksComIdCriptografado,
                'totalAmount' => $totalAmount,
                'client_id' => $clientId,
                'client_name' => $clientUser->name,
                'client_phone' => $clientUser->phone_number_1,
                'client_email' => $clientUser->email
            ]);

        } catch (Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erro ao buscar cashbacks pendentes: ' . $e->getMessage() . ' Stack: ' . $e->getTraceAsString());
            return response()->json(['error' => 'Erro ao buscar cashbacks: ' . $e->getMessage()], 500);
        }
    }

    public function sendClientValidationCode(Request $request)
    {
        $request->validate([
            'client_id' => 'required|integer|exists:clients,id',
            'send_method' => 'required|string|in:whatsapp,email,both',
        ]);

        try {
            $employee = $request->user()->employee;
            if (!$employee || !$employee->business_selected_id) {
                return response()->json(['error' => 'Nenhum negócio selecionado para o funcionário.'], 403);
            }
            $businessId = $employee->business_selected_id;
            $clientId = $request->input('client_id');
            $sendMethod = $request->input('send_method');

            $client = Client::with('user')->find($clientId);
            if (!$client || !$client->user) {
                return response()->json(['error' => 'Cliente não encontrado.'], 404);
            }

            $validationCode = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            $cacheKey = 'cashback_validation_code_' . $clientId . '_' . $businessId;
            Cache::put($cacheKey, $validationCode, now()->addMinutes(10));

            $business = Business::find($businessId);
            if (!$business || !$business->parameters) { // Verifica se business e parameter existem
                \Illuminate\Support\Facades\Log::error("Negócio ou parâmetros não encontrados para business_id: {$businessId}");
                return response()->json(['error' => 'Configurações do negócio não encontradas para envio.'], 404);
            }

            $whatsappSent = false;
            $emailSent = false;
            $errors = [];
            $whatsappConnectionError = false;

            // Calcular o valor total dos cashbacks pendentes (usado tanto para WhatsApp quanto para Email)
            $totalAmount = Cashback::where('client_id', $clientId)
                ->where('business_id', $businessId)
                ->where('status', 'pending')
                ->whereNotNull('amount')
                ->sum('amount');

            if ($sendMethod === 'whatsapp' || $sendMethod === 'both') {
                if ($client->user->phone_number_1 && $business->parameters->whatsapp_instance_key) {
                    try {
                        ApiWhatsapp::sendCashbackValidationCode(
                            $business->parameters->whatsapp_instance_key,
                            $client->user->phone_number_1,
                            $validationCode,
                            $business->name,
                            $totalAmount
                        );
                        $whatsappSent = true;
                    } catch (Exception $e) {
                        \Illuminate\Support\Facades\Log::error('Falha ao enviar WhatsApp: ' . $e->getMessage());
                        if ($e->getMessage() === 'Whatsapp não conectado.') {
                             $whatsappConnectionError = true;
                             $business->parameters->whatsapp_instance_key = null;
                             $business->parameters->save();
                             $errors[] = 'Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado.';

                             // Se for 'both' e WhatsApp falhou por desconexão, retornar erro imediatamente
                             if ($sendMethod === 'both') {
                                 return response()->json([
                                     'error' => 'Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado.',
                                     'whatsapp_sent' => false,
                                     'email_sent' => false,
                                     'whatsapp_connection_error' => true
                                 ], 500);
                             }
                        } else {
                            $errors[] = 'Falha ao enviar código por WhatsApp.';
                        }
                    }
                } else {
                    if (!$client->user->phone_number_1) {
                        $errors[] = 'Número de telefone do cliente não configurado.';
                    }
                    if (!$business->parameters->whatsapp_instance_key) {
                        $errors[] = 'Chave de instância do WhatsApp não configurada para o negócio.';
                    }
                     \Illuminate\Support\Facades\Log::warning("WhatsApp não pode ser enviado para client_id {$clientId} para business_id {$businessId}. Telefone: {$client->user->phone_number_1}, Instância: {$business->parameters->whatsapp_instance_key}");
                }
            }

            if ($sendMethod === 'email' || $sendMethod === 'both') {
                if ($client->user->email) {
                    try {

                        $logo = $business->logo != null ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
                        Mail::to($client->user->email)->send(new CashbackValidationCodeMail(
                            $validationCode,
                            $totalAmount,
                            $business->name,
                            $logo
                        ));
                        $emailSent = true;
                    } catch (Exception $e) {
                        \Illuminate\Support\Facades\Log::error('Falha ao enviar Email: ' . $e->getMessage() . ' Stack: ' . $e->getTraceAsString());
                        $errors[] = 'Falha ao enviar código por Email.';
                    }
                } else {
                    $errors[] = 'Email do cliente não configurado.';
                }
            }

            // Verifica se algum método de envio teve sucesso
            $anyMethodSucceeded = $whatsappSent || $emailSent;
            // Verifica se todos os métodos solicitados falharam
            $allRequestedMethodsFailed = true;
            if ($sendMethod === 'whatsapp' && $whatsappSent) $allRequestedMethodsFailed = false;
            if ($sendMethod === 'email' && $emailSent) $allRequestedMethodsFailed = false;
            if ($sendMethod === 'both' && $anyMethodSucceeded) $allRequestedMethodsFailed = false;


            if ($whatsappConnectionError) {
                return response()->json([
                    'error' => 'Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado.',
                    'whatsapp_sent' => false,
                    'email_sent' => $emailSent,
                    'whatsapp_connection_error' => true
                ], 500);
            }

            if (!$anyMethodSucceeded && !empty($errors)) {
                 return response()->json(['error' => implode(' ', $errors), 'whatsapp_sent' => $whatsappSent, 'email_sent' => $emailSent], 500);
            }

            if ($sendMethod === 'both') {
                if ($whatsappSent && !$emailSent) {
                     return response()->json(['message' => 'Código enviado por WhatsApp, mas falhou ao enviar por Email.', 'whatsapp_sent' => true, 'email_sent' => false, 'partial_error' => implode(' ', $errors)], 207);
                }
                if (!$whatsappSent && $emailSent) {
                     return response()->json(['message' => 'Código enviado por Email, mas falhou ao enviar por WhatsApp.', 'whatsapp_sent' => false, 'email_sent' => true, 'partial_error' => implode(' ', $errors)], 207);
                }
                 if (!$whatsappSent && !$emailSent && !empty($errors)) {
                    return response()->json(['error' => implode(' ', $errors), 'whatsapp_sent' => false, 'email_sent' => false], 500);
                }
            } else {
                 if (($sendMethod === 'whatsapp' && !$whatsappSent && !empty($errors)) || ($sendMethod === 'email' && !$emailSent && !empty($errors))) {
                    return response()->json(['error' => implode(' ', $errors), 'whatsapp_sent' => $whatsappSent, 'email_sent' => $emailSent], 500);
                 }
            }

            if (!$anyMethodSucceeded && empty($errors)) {
                 return response()->json(['error' => 'Não foi possível enviar o código. Verifique os dados do cliente e configurações do negócio.', 'whatsapp_sent' => false, 'email_sent' => false], 400);
            }

            return response()->json([
                'message' => 'Tentativa de envio de código de validação processada.',
                'whatsapp_sent' => $whatsappSent,
                'email_sent' => $emailSent
            ]);

        } catch (Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erro ao enviar código de validação: ' . $e->getMessage() . ' Stack: ' . $e->getTraceAsString());
            return response()->json(['error' => 'Erro crítico ao enviar código de validação: ' . $e->getMessage()], 500);
        }
    }

    public function validateAndCompleteClient(Request $request)
    {
        $request->validate([
            'client_id' => 'required|integer|exists:clients,id',
            'validation_code' => 'required|string|digits:6',
            'encrypted_cashback_ids' => 'required|array',
            'encrypted_cashback_ids.*' => 'required|string',
        ]);

        try {
            $employee = $request->user()->employee;
            if (!$employee || !$employee->business_selected_id) {
                return response()->json(['error' => 'Nenhum negócio selecionado para o funcionário.'], 403);
            }
            $businessId = $employee->business_selected_id;
            $clientId = $request->input('client_id');
            $validationCode = $request->input('validation_code');
            $encryptedCashbackIds = $request->input('encrypted_cashback_ids');

            $cacheKey = 'cashback_validation_code_' . $clientId . '_' . $businessId;
            $cachedCode = Cache::get($cacheKey);

            if (!$cachedCode) {
                return response()->json(['error' => 'Código de validação expirado. Solicite um novo código.'], 400);
            }

            if ($cachedCode !== $validationCode) {
                return response()->json(['error' => 'Código de validação inválido.'], 400);
            }

            $decryptedCashbackIds = [];
            foreach ($encryptedCashbackIds as $encryptedId) {
                try {
                    $decryptedCashbackIds[] = Crypt::decryptString($encryptedId);
                } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
                    \Illuminate\Support\Facades\Log::warning("Falha ao descriptografar ID de cashback: {$encryptedId} para client_id {$clientId}, business_id {$businessId}");
                    return response()->json(['error' => 'Um ou mais IDs de cashback são inválidos ou foram corrompidos.'], 400);
                }
            }

            if (empty($decryptedCashbackIds)) {
                 return response()->json(['error' => 'Nenhum cashback selecionado para conclusão.'], 400);
            }

            $cashbacksParaConcluir = Cashback::whereIn('id', $decryptedCashbackIds)
                ->where('client_id', $clientId)
                ->where('business_id', $businessId)
                ->where('status', 'pending')
                ->get();

            if ($cashbacksParaConcluir->count() !== count($decryptedCashbackIds)) {
                 \Illuminate\Support\Facades\Log::warning("Tentativa de concluir cashbacks com IDs não correspondentes. Esperado: " . count($decryptedCashbackIds) . ", Encontrado pendente: " . $cashbacksParaConcluir->count() . ". IDs: " . implode(',', $decryptedCashbackIds) . " para client_id {$clientId}, business_id {$businessId}");
                 return response()->json(['error' => 'Alguns cashbacks selecionados não puderam ser processados, podem já ter sido concluídos ou não pertencem a este cliente/negócio.'], 400);
            }

            if ($cashbacksParaConcluir->isEmpty()){ // Deve ser redundante devido à verificação acima, mas seguro.
                 return response()->json(['error' => 'Nenhum cashback pendente encontrado para os IDs fornecidos.'], 404);
            }

            $saved_count = DB::transaction(function () use ($cashbacksParaConcluir, $cacheKey) {
                $saved_count = 0;
                foreach ($cashbacksParaConcluir as $cashback) {
                    $cashback->status = 'completed';
                    $cashback->used_at = Carbon::now();
                    $cashback->save();
                    $saved_count++;
                }

                Cache::forget($cacheKey);

                return $saved_count;
            });

            return response()->json(['message' => $saved_count . ' cashback(s) concluído(s) com sucesso!']);

        } catch (Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erro ao validar e concluir cashbacks: ' . $e->getMessage() . ' Stack: ' . $e->getTraceAsString());
            return response()->json(['error' => 'Erro crítico ao validar e concluir cashbacks: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Cria e envia cashback direto
     */
    public function storeDirect(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sending_type' => 'required|in:EMAIL,WHATSAPP',
            'email' => 'required_if:sending_type,EMAIL|email|nullable',
            'number_phone' => 'required_if:sending_type,WHATSAPP|string|nullable',
            'name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0.01',
            'percentage' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => true,
                'msg' => 'Dados inválidos.',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();
            $selected_business_id = Auth::user()->employee->business_selected_id;
            $business = Business::findOrFail($selected_business_id);
            $parameters = $business->parameters;

            // Usar método do EvaluationController para verificar limites
            $evaluationController = new EvaluationController();
            $shipmentsResponse = $evaluationController->validateBusinessShipments();
            $shipmentsData = $shipmentsResponse->getData();

            if ($shipmentsData->error) {
                throw new Exception($shipmentsData->msg);
            }

            $sendingType = $data['sending_type'];

            // Verificar se o tipo de envio está permitido e se há limite disponível
            if ($sendingType === 'EMAIL') {
                if ($shipmentsData->data->remaining_email_shipments <= 0) {
                    throw new Exception('Limite de envios por email atingido para o período atual.');
                }
            } elseif ($sendingType === 'WHATSAPP') {
                if ($shipmentsData->data->remaining_whatsapp_shipments <= 0) {
                    throw new Exception('Limite de envios por WhatsApp atingido para o período atual.');
                }

                if (!$parameters->whatsapp_instance_key) {
                    throw new Exception('WhatsApp não está configurado para este negócio.');
                }
            }

            // Buscar ou criar cliente
            $clientController = new ClientController();
            $contactData = [
                'email' => $data['email'] ?? null,
                'number_phone' => $data['number_phone'] ?? null,
                'name' => $data['name'],
            ];
            $client = $clientController->findOrCreateClientForEvaluation($contactData, $selected_business_id);

            // Calcular valor do cashback
            $cashbackAmount = floatval($data['amount']) * (floatval($data['percentage']) / 100);

            // Definir data de expiração do cashback
            $cashback_expiration_hours = $parameters->cashback_expiration_hours;
            $expiration_date = Carbon::now()->addHours($cashback_expiration_hours);

            // Criar cashback
            $cashback = Cashback::create([
                'status' => 'pending',
                'expiration_date' => $expiration_date,
                'percentage' => $data['percentage'],
                'amount' => $cashbackAmount,
                'business_id' => $selected_business_id,
                'client_id' => $client->id,
                'sending_type' => $data['sending_type'],
                'creation_type' => 'direct',
            ]);

            // Enviar notificação de forma síncrona
            $notificationJob = new SendCashbackDirectNotification($cashback);
            $notificationJob->processCashbackDirectNotification($cashback);

            DB::commit();

            return response()->json([
                'error' => false,
                'msg' => 'Cashback criado e enviado com sucesso.',
                'cashback' => $cashback,
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => true,
                'msg' => $e->getMessage()
            ], 500);
        }
    }
}
