<?php

namespace App\Http\Controllers\Api;

use App\Helpers\SystemHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UpdateSystemContact;
use App\Models\System;
use App\Http\Requests\Api\BaseRequest as Request;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class SystemController extends Controller
{
    /**
     * Retorna todos os parametros do sistema visíveis
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $systemParameters = SystemHelper::get_visibles();
        foreach ($systemParameters as $parameter) {
            if ($parameter->type == 'image' && $parameter->value != null && $parameter->value != '' && Storage::disk('s3')->exists($parameter->value)) {
                $parameter->value = Storage::disk('s3')->temporaryUrl($parameter->value, Carbon::now()->addMinutes(60));
            }
        }
        return response()->json($systemParameters);
    }

    /**
     * Atualiza os parâmetros do sistema
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $data = $request->all();

            unset($data['_token']);

            foreach ($data as $key => $value) {
                $parameter = System::where('code_name', $key)->first();

                if (!$parameter) {
                    continue;
                }
                if ($parameter->type == 'image') {
                    $isRemovingImage = $request->input($key.'_remove', false);

                    if($value != null && $value != ''){
                        $type = $key . '_type';
                        $type = $request->input($type);
                        $value = SystemHelper::uploadImage($value, $key, $type, $parameter->value);
                        $parameter->update(['value' => $value]);
                        continue;
                    } elseif ($isRemovingImage) {
                        $parameter->update(['value' => null]);
                    }
                    continue;
                }
                $parameter->update(['value' => $value]);
            }
        }catch (\Exception $e){
            return response()->json(['error' => $e->getMessage()],400);
        }

        return response()->json(['success' => 'Parâmetros do Sistema atualizados com sucesso!']);
    }

    public function updateContact(UpdateSystemContact $request)
    {
        try {
            $phone_number = preg_replace('/\D/', '', $request->phone_number);
            $email = $request->email;

            System::where('code_name', 'PHONE_NUMBER')
                ->update(['value' => $phone_number]);
            System::where('code_name', 'EMAIL')
                ->update(['value' => $email]);

            return $this->getContact($request);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Retorna um parâmetro global específico
     *
     * @param Request $request
     * @param string $code_name
     * @return JsonResponse
     */
    public function getOne(Request $request, $code_name): JsonResponse
    {
        return SystemHelper::getOne($code_name);
    }

    /**
     * Retorna os parâmetros básicos do sistema
     *
     * @return JsonResponse
     */
    public function getBasicSystemParameters(): JsonResponse
    {
        $systemParameters = SystemHelper::get();
        return response()->json(
            [
                'AUTH_CODE_RESEND_TIME_MINUTES' => SystemHelper::findInArrayByCodeName($systemParameters, 'AUTH_CODE_RESEND_TIME_MINUTES'),
                'LINK_APP_ANDROID' => SystemHelper::findInArrayByCodeName($systemParameters, 'LINK_APP_ANDROID'),
                'LINK_APP_IOS' => SystemHelper::findInArrayByCodeName($systemParameters, 'LINK_APP_IOS'),
                'MAIN_LOGO' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'MAIN_LOGO'),
                'MAIN_LOGO_THEME' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'MAIN_LOGO_THEME'),
                'APP_LOGO' =>   SystemHelper::findInArrayByCodeName($systemParameters, 'APP_LOGO'),
                'TAB_LOGO' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'TAB_LOGO'),
                'MAIN_COLOR' => SystemHelper::findInArrayByCodeName($systemParameters, 'MAIN_COLOR'),
                'PHONE_NUMBER' => SystemHelper::findInArrayByCodeName($systemParameters, 'PHONE_NUMBER'),
                'EMAIL' => SystemHelper::findInArrayByCodeName($systemParameters, 'EMAIL'),
                'BACKGROUND_IMAGE' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'BACKGROUND_IMAGE'),
                'BACKGROUND_COLOR' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'BACKGROUND_COLOR'),
                'BUSINESS_EXPIRATION_DAYS' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'BUSINESS_EXPIRATION_DAYS'),
                'EVALUATION_AVG_STAR_RATING' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'EVALUATION_AVG_STAR_RATING'),
                'EMPLOYEE_FIRST_LINK' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'EMPLOYEE_FIRST_LINK'),
                'EMPLOYEE_SECOND_LINK' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'EMPLOYEE_SECOND_LINK'),
                'FLOATING_BUTTON_LINK' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'FLOATING_BUTTON_LINK'),
                'FLOATING_BUTTON_ICON' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'FLOATING_BUTTON_ICON'),
                'CLIENT_FIRST_LINK' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'CLIENT_FIRST_LINK'),
                'CLIENT_SECOND_LINK' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'CLIENT_SECOND_LINK'),
                'FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB'),
                'SUPPORT_TAB_LINK' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'SUPPORT_TAB_LINK'),
                'ADMIN_BUSINESS_ID' =>  SystemHelper::findInArrayByCodeName($systemParameters, 'ADMIN_BUSINESS_ID'),
            ]
        );
    }

    /**
     * Retorna os parâmetros de contato
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getContact(): JsonResponse
    {
        $system = SystemHelper::getContact();
        return response()->json($system);
    }
}
