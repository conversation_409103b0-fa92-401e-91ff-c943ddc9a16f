<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ApiUser;
use App\Helpers\SystemHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Business\PublicRegisterBusinessRequest;
use App\Http\Requests\Api\Business\StoreBusinessRequest;
use App\Http\Requests\Api\Business\UpdateBusinessParametersRequest;
use App\Http\Requests\Api\Business\UpdateBusinessRequest;
use App\Http\Requests\Api\Business\ValidateCnpjRequest;
use App\Http\Requests\Api\StoreEmployee;
use App\Http\Requests\Api\UpdateEmployee;
use App\Http\Resources\Api\BusinessResource;
use App\Models\Business;
use App\Models\BusinessParameters;
use App\Models\Employee;
use App\Models\EmployeeBusiness;
use App\Models\Seller;
use App\Models\Topic;
use App\SiteNotifications\AdminBusinessApprovalNotification;
use App\SiteNotifications\AdminBusinessCancelNotification;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Helpers\ApiWhatsapp;

class BusinessController extends Controller
{
    public function getUserBusiness(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $per_page = $request->per_page ?? 5;
            $page = $request->page ?? 1;
            $search = $request->search ?? "";
            $filter_disabled = $request->filter_disabled;
            $status_filter = $request->status ?? 'all';
            $topics_selected_ids = $request->topics_selected_ids ?? [];
            $seller_id = $request->seller_id ?? null;
            $priorize_admin = $request->input('priorize_admin', false);
            $admin_business_id = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'ADMIN_BUSINESS_ID') ?? null;
            $adminBusiness = null;

            $query = match ($filter_disabled) {
                "disabled" => Business::withTrashed()->where(
                    "businesses.deleted_at",
                    "!=",
                    null
                ),
                "active" => Business::query(),
                "all" => Business::withTrashed(),
                null => Business::query(),
                default => Business::query(),
            };

            $isAuthorizedAllBusiness =
                $user->employee->authorizedFunction->release_all_business;

            if ($user->login_type != "admin" && !$isAuthorizedAllBusiness) {
                $query
                    ->join(
                        "employee_business",
                        "businesses.id",
                        "=",
                        "employee_business.business_id"
                    )
                    ->where(
                        "employee_business.employee_id",
                        $user->employee->id
                    );
            }

            if ($search) {
                $searchTerm = strtolower($search);
                $isCnpj = $this->isCnpjFormat($search);
                $query->where(function ($q) use ($searchTerm, $search, $isCnpj) {
                    $q->whereRaw('LOWER(businesses.name) LIKE ?', ['%' . $searchTerm . '%']);
                    if ($isCnpj) {
                        $cleanCnpj = preg_replace('/\D/', '', $search);
                        $q->orWhere('businesses.cnpj', 'LIKE', '%' . $cleanCnpj . '%');
                    }
                    $q->orWhereHas('plan', function ($subQuery) use ($searchTerm) {
                        $subQuery->whereRaw('LOWER(plans.name) LIKE ?', ['%' . $searchTerm . '%']);
                    });
                });
            }

            if (!empty($topics_selected_ids)) {
                $topics_selected_ids = array_filter(
                    $topics_selected_ids,
                    function ($topic_id) {
                        $topic = Topic::find($topic_id);
                        return $topic && !$topic->default;
                    }
                );

                foreach ($topics_selected_ids as $topic_id) {
                    $query->whereHas("topics", function ($query) use (
                        $topic_id
                    ) {
                        $query->where("topics.id", $topic_id);
                    });
                }
            }

            if ($status_filter !== 'all') {
                $query->where("businesses.status", $status_filter);
            }

            if ($seller_id !== null) {
                if ($seller_id === 'no_seller') {
                    $query->whereNull('businesses.seller_id');
                } else {
                    $query->where('businesses.seller_id', $seller_id);
                }
            }

            $query->orderBy("businesses.name");

            $isCrudEmployee = $request->is_crud_employee === "true" ?? false;
            $isCrudTopic = $request->is_crud_topic === "true" ?? false;
            $isCrudForm = $request->is_crud_form === "true" ?? false;
            $isCrudClients = $request->is_crud_clients === "true" ?? false;

            //No crud de funcionários, na listagem de negócios, só lista os negócios que estão vinculados a um plano que tenha a permissão de funcionários ativo.
            if ($isCrudEmployee) {
                $query->whereHas("plan", function ($query) {
                    $query->whereHas("authorizedFunctions", function ($query) {
                        $query->where("employees", true);
                    });
                });
            }
            //No crud de tópicos, na listagem de negócios, só lista os negócios que estão vinculados a um plano que tenha a permissão de tópicos ativo.
            if ($isCrudTopic) {
                $query->whereHas("plan", function ($query) {
                    $query->whereHas("authorizedFunctions", function ($query) {
                        $query
                            ->where("update_topics", true)
                            ->where("topics", true);
                    });
                });
            }
            //No crud de formulários, na listagem de negócios, só lista os negócios que estão vinculados a um plano que tenha a permissão de formulários ativo.
            if ($isCrudForm) {
                $query->whereHas("plan", function ($query) {
                    $query->whereHas("authorizedFunctions", function ($query) {
                        $query
                            ->where("update_forms", true)
                            ->where("forms", true);
                    });
                });
            }
            //No crud de clientes, na listagem de negócios, só lista os negócios que estão vinculados a um plano que tenha a permissão de formulários ativo.
            if ($isCrudClients) {
                $query->whereHas("plan", function ($query) {
                    $query->whereHas("authorizedFunctions", function ($query) {
                        $query
                            ->where("clients", true)
                            ->where("update_clients", true);
                    });
                });
            }

            $query->join(
                "plans",
                "businesses.plan_id",
                "=",
                "plans.id"
            )->select('businesses.*', 'plans.name as plan_name');

            if ($priorize_admin && isset($admin_business_id)) {
                // clona a query com todos os filtros pra verificar se, o negócio admin tá nela
                $adminQuery = clone $query;
                $adminBusiness = $adminQuery->with(['plan', 'seller.user'])->find($admin_business_id);
                // Remove o negócio admin da lista principal para não duplicar
                $query->where('businesses.id', '<>', $admin_business_id);
                // remove um da exibição (se houver o negócio admin)
                $per_page = $per_page <= 0 ? $per_page : $per_page - 1;
            }

            $total_value = null;
            if ($request->boolean('with_sum')) {
                $queryForSum = clone $query;
                $total_value = $queryForSum->sum('plans.value');
            }

            $businesses =
                $per_page <= 0
                    ? $query->get()
                    : $query->paginate($per_page, ["*"], "page", $page);
            $businesses->load(['plan', 'seller.user']);

            $response = [
                "adminBusiness" => $adminBusiness,
                "businesses" => $businesses,
            ];

            if (!is_null($total_value)) {
                $response["total_value"] = $total_value;
            }

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function getSellerBusiness(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (!$user->seller) {
                return response()->json(
                    ["msg" => "Vendedor não encontrado"],
                    404
                );
            }
            $per_page = $request->per_page ?? 5;
            $page = $request->page ?? 1;
            $search = $request->search ?? "";

            $query = Business::query();

            $filter_disabled = $request->filter_disabled ?? 'active';

            if ($filter_disabled === 'disabled') {
                $query->withTrashed()->whereNotNull('businesses.deleted_at');
            } elseif ($filter_disabled === 'all') {
                $query->withTrashed();
            }

            $registeredSellerIds = Seller::where('registered_by', $user->seller->id)
                ->pluck('id')
                ->toArray();

            $allSellerIds = array_merge([$user->seller->id], $registeredSellerIds);

            if ($request->filled('seller_id')) {
                if (!in_array($request->seller_id, $allSellerIds)) {
                    return response()->json(
                        ["msg" => "Vendedor não tem permissão para acessar o negócio"],
                        403
                    );
                }
                $query->where('seller_id', $request->seller_id);
            } else {
                $query->whereIn('seller_id', $allSellerIds);
            }

            if ($search) {
                // Prepara o termo de busca em minúsculas
                $searchTerm = strtolower($search);
                $isCnpj = $this->isCnpjFormat($search);
                $query->where(function ($q) use ($searchTerm, $isCnpj) {
                    // Busca case-insensitive no nome do negócio
                    $q->whereRaw('LOWER(businesses.name) LIKE ?', ['%' . strtolower($searchTerm) . '%']);

                    // Busca no CNPJ apenas se houver números no termo de busca
                    if ($isCnpj) {
                        $cleanCnpj = preg_replace('/\D/', '', $searchTerm);
                        $q->orWhere('cnpj', 'LIKE', '%' . $cleanCnpj . '%');
                    }

                    // Busca case-insensitive no nome do plano associado
                    $q->orWhereHas('plan', function ($subQuery) use ($searchTerm) {
                        $subQuery->whereRaw('LOWER(plans.name) LIKE ?', ['%' . strtolower($searchTerm) . '%']);
                    });
                });
            }

            $query->join(
                "plans",
                "businesses.plan_id",
                "=",
                "plans.id"
            )->select('businesses.*', 'plans.name as plan_name');
            $total_value = null;
            if ($request->boolean('with_sum')) {
                $queryForSum = clone $query;
                $total_value = $queryForSum->sum('plans.value');
            }

            $query->orderBy("businesses.name");

            $businesses = $per_page <= 0
                ? $query->get()
                : $query->paginate($per_page, ["*"], "page", $page);

            $businesses->load(['plan', 'seller']);

            $childSellers = Seller::where('registered_by', $user->seller->id)
                ->join('users', 'sellers.user_id', '=', 'users.id')
                ->orderBy('users.name')
                ->select('sellers.*')
                ->get();
            $childSellers->prepend($user->seller);

            $response = [
                "businesses" => $businesses,
                "child_sellers" => $childSellers,
            ];
            if (!is_null($total_value)) {
                $response["total_value"] = $total_value;
            }
            return response()->json($response);

        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function selectBusiness(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $business = Business::find($request->business_id);
            if (!$business) {
                return response()->json(
                    ["msg" => "Negócio não encontrado"],
                    404
                );
            }
            $employee = $user->employee;
            $employee->business_selected_id = $business->id;
            $employee->save();

            return response()->json([
                "msg" => "Negócio selecionado com sucesso",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function selectAllBusinesses(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $employee = $user->employee;
            $employee->business_selected_id = null;
            $employee->save();

            return response()->json([
                "msg" => "Todos os negócios selecionados com sucesso",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        try {
            $user = $request->user();
            switch ($user->login_type) {
                case "admin":
                    $business = Business::withTrashed()->find($id);
                    break;
                case "employee":
                    $employee = $user->employee;
                    if ($employee->isAdmin()) {
                        $business = Business::withTrashed()->find($id);
                    } else {
                        $business = $employee->businesses()->withTrashed()->find($id);
                    }
                    break;
                case "seller":
                    $registeredSellerIds = Seller::where('registered_by', $user->seller->id)
                        ->pluck('id')
                        ->toArray();
                    $allSellerIds = array_merge([$user->seller->id], $registeredSellerIds);

                    $business = Business::whereIn('seller_id', $allSellerIds)->withTrashed()->find($id);
                    break;
                default:
                    $business = null;
            }
            if (!$business) {
                return response()->json(
                    ["msg" => "Negócio não encontrado"],
                    404
                );
            }
            return response()->json([
                "business" => BusinessResource::make($business),
            ]);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function update(UpdateBusinessRequest $request, $id): JsonResponse
    {
        try {
            $user = $request->user();
            $cnpj = $request->input('cnpj', null);
            switch ($user->login_type) {
                case "admin":
                    $business = Business::find($id);
                    break;
                case "employee":
                    $employee = $user->employee;
                    if ($employee->isAdmin()) {
                        $business = Business::find($id);
                    } else {
                        $business = $employee->businesses->find($id);
                    }
                    break;
                default:
                    $business = null;
            }
            if (!$business) {
                return response()->json(
                    ["msg" => "Negócio não encontrado"],
                    404
                );
            }

            $data = $request->except('logo');
            $is_cnpj_optional = $request->boolean('cnpj_optional');
            if ($is_cnpj_optional) {
                $data['cnpj'] = null;
            } else if ($cnpj != $business->cnpj) {
                $businessWithCnpj = Business::withTrashed()->where('cnpj', $cnpj)->first();
                if ($businessWithCnpj) {
                    throw new Exception("Esse CNPJ já está cadastrado!");
                }
            }

            $business->fill($data);
            $removeImgFlag = $request->input('logo_remove', false);
            if ($request->logo) {
                //remove old logo if exists
                if ($business->logo) {
                    Storage::disk("s3")->delete($business->logo);
                }

                $logobase64 = $request->logo;
                $image = base64_decode(
                    preg_replace("#^data:image/\w+;base64,#i", "", $logobase64)
                );
                $file_name = $request->logo_file;
                $file_path = "Visao_Negocio/images/business/$business->id/$file_name";
                Storage::disk("s3")->put($file_path, $image);
                $business->logo = $file_path;
            } elseif ($removeImgFlag) {
                $business->logo = null;
            }
            $business->save();
            $employeesController = new EmployeesController();
            $employeesController->updatePermissionsFromPlan($business->plan_id, null);
            return response()->json([
                "msg" => "Negócio atualizado com sucesso",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function saveBusinessAndAdministrador(StoreBusinessRequest $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $data = $request->all();
            $employeeData = $data["employee"] ?? [];
            $employeeData["id"] = $data["employee_id"] ?? null;

            $isCreateNewEmployeeAndUser = $data["isCreateNewEmployeeAndUser"] ?? false;
            $isEditEmployee = $data["isEditEmployee"] ?? false;
            $isCreateNewEmployee = $data["isCreateNewEmployee"] ?? false;
            $is_cnpj_optional = $data["cnpj_optional"] ?? false;
            $businessWithCnpj = null;
            // Verifica se o CNPJ é opcional e se o campo está vazio
            if (!$is_cnpj_optional || !empty($data["cnpj"])) {
                $businessWithCnpj = Business::withTrashed()->where('cnpj', $data["cnpj"])->first();
                if ($businessWithCnpj) {
                    throw new Exception("Esse CNPJ já está cadastrado!");
                }
            }

            // Criação de um novo negócio
            $business = Business::create($data);
            BusinessParameters::create([
                'business_id' => $business->id,
            ]);

            $employeeData["businessesIds"] = [$business->id];

            $employeeController = new EmployeesController();

            if ($isCreateNewEmployeeAndUser || $isCreateNewEmployee) {
                $responseEmployee = $employeeController->store(
                    new StoreEmployee($employeeData)
                );
                if ($responseEmployee->status() != 200) {
                    DB::rollBack();

                    return response()->json(
                        ["message" => "Erro ao salvar o administrador."],
                        $responseEmployee->status()
                    );
                }
                $responseEmployee = $responseEmployee->getData()->employee;
            } elseif ($isEditEmployee) {
                // Edita o funcionário existente
                $responseEditEmployee = $employeeController->updateWithoutPermissionsAndAddBussinesses(
                    new UpdateEmployee($employeeData),
                    $employeeData["id"]
                );
                if ($responseEditEmployee->status() != 200) {
                    DB::rollBack();

                    return response()->json(
                        ["message" => "Erro ao salvar o administrador."],
                        $responseEditEmployee->status()
                    );
                }
                $responseEmployee = $responseEditEmployee->getData()->employee;
            }

            if ($isCreateNewEmployee || $isCreateNewEmployeeAndUser) {
                $authorizedFunctions = $business->plan->authorizedFunctions;

                // Filtra apenas as permissões que são true no plano
                $authorizedFunctionsTrue = collect($authorizedFunctions->toArray())
                    ->filter(function ($value) {
                        return $value === true;
                    });

                $employee = Employee::find($responseEmployee->id);

                $employee
                    ->authorizedFunction()
                    ->updateOrCreate([], $authorizedFunctionsTrue->toArray());
            }

            if ($isEditEmployee) {
                $planId = $data["plan_id"];
                $employeeController->updatePermissionsFromPlan($planId, [$responseEmployee->id]);
            }

            EmployeeBusiness::where("employee_id", $responseEmployee->id)
                ->where("business_id", $business->id)
                ->update(["admin" => true]);

            // Commit the transaction
            DB::commit();

            return response()->json([
                "message" => "Negócio e administrador salvos com sucesso.",
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction on any error
            DB::rollBack();

            if (isset($file_path) && Storage::disk("s3")->exists($file_path)) {
                Storage::disk("s3")->delete($file_path);
            }

            return response()->json([
                "msg" => $e->getMessage(),
                "error" => $e->getMessage(),
            ], 500);
        }
    }

    public function validateExistingName(Request $request): JsonResponse
    {
        try {
            $name = $request->name;

            $exists_active = Business::whereRaw(
                "LOWER(name) COLLATE utf8mb4_general_ci = ?",
                [strtolower($name)]
            )
                ->whereNull("deleted_at")
                ->exists();

            $exists_deleted = false;
            if (!$exists_active) {
                $exists_deleted = Business::withTrashed()
                    ->whereRaw("LOWER(name) COLLATE utf8mb4_general_ci = ?", [
                        strtolower($name),
                    ])
                    ->exists();
            }

            return response()->json([
                "exists_active" => $exists_active,
                "exists_deleted" => $exists_deleted,
            ]);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function validateCnpj(ValidateCnpjRequest $request)
    {
        return response()->json(['msg' => 'ok']);
    }

    public function reactivate(Request $request, $id)
    {
        try {
            $business = Business::withTrashed()->find($id);
            $business->restore();
            $business->status = Business::STATUS_APPROVED_REGISTRATION;
            $business->save();
            return response()->json([
                "msg" => "Negócio ativado com sucesso",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function disable(Request $request, $id)
    {
        try {
            $business = Business::find($id);
            $business->delete();
            return response()->json([
                "msg" => "Negócio desativado com sucesso",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                ["msg" => $e->getMessage(), "exception" => get_class($e)],
                400
            );
        }
    }

    public function updateParameters(UpdateBusinessParametersRequest $request, $id): JsonResponse
    {
        try {
            $business = Business::findOrFail($id);

            $parameters = $business->parameters;

            $parameters->evaluation_expiration_hours = $request->input('evaluation_expiration_hours');
            $parameters->cashback_expiration_hours = $request->input('cashback_expiration_hours');
            $parameters->cashback_percentage = $request->input('cashback_percentage');
//            $parameters->evaluation_interval_day = $request->input('evaluation_interval_day');
            $parameters->form_id = $request->input('form_id');
            //$parameters->can_set_cashback_value = $request->boolean('can_set_cashback_value');

            $parameters->save();

            return response()->json(['msg' => 'Parâmetros atualizados com sucesso.']);
        } catch (Exception $e) {
            return response()->json(['msg' => $e->getMessage()], 400);
        }
    }

    public function updateBusinessAdmin(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();

        try {
            $business = Business::with('plan.authorizedFunctions', 'employees')->findOrFail($id);

            $employeeData = $request->input('employee', []);
            $isCreateNewEmployeeAndUser = $request->boolean('isCreateNewEmployeeAndUser', false);
            $isEditEmployee = $request->boolean('isEditEmployee', false);
            $isCreateNewEmployee = $request->boolean('isCreateNewEmployee', false);

            $currentAdmin = $business->employees()->wherePivot('admin', true)->first();
            if ($currentAdmin) {
                $business->employees()->updateExistingPivot($currentAdmin->id, ['admin' => false]);
                $business->employees()->detach($currentAdmin->id);
            }

            $employeeController = new EmployeesController();

            if ($isCreateNewEmployeeAndUser || $isCreateNewEmployee) {
                $storeEmployeeRequest = new StoreEmployee($employeeData);
                $responseEmployee = $employeeController->store($storeEmployeeRequest);

                if ($responseEmployee->status() != 200) {
                    DB::rollBack();
                    return response()->json(["message" => "Erro ao salvar o novo administrador."], $responseEmployee->status());
                }

                $newEmployee = $responseEmployee->getData()->employee;
                $employeeId = $newEmployee->id;
            } elseif ($isEditEmployee) {
                if (!isset($employeeData['id'])) {
                    throw new \Exception("ID do funcionário para edição não fornecido.");
                }

                $updateEmployeeRequest = new UpdateEmployee($employeeData);
                $responseEditEmployee = $employeeController->updateWithoutPermissionsAndAddBussinesses($updateEmployeeRequest, $employeeData['id']);

                if ($responseEditEmployee->status() != 200) {
                    DB::rollBack();
                    return response()->json(["message" => "Erro ao atualizar o administrador."], $responseEditEmployee->status());
                }

                $updatedEmployee = $responseEditEmployee->getData()->employee;
                $employeeId = $updatedEmployee->id;
            } else {
                throw new \Exception("Nenhuma ação especificada para atualizar o administrador.");
            }

            $exists = DB::table('employee_business')
                ->where('business_id', $business->id)
                ->where('employee_id', $employeeId)
                ->exists();

            if (!$exists) {
                DB::table('employee_business')->insert([
                    'employee_id' => $employeeId,
                    'business_id' => $business->id,
                    'admin' => true
                ]);
            } else {
                DB::table('employee_business')
                    ->where('business_id', $business->id)
                    ->where('employee_id', $employeeId)
                    ->update(['admin' => true]);
            }

            if ($isCreateNewEmployee || $isCreateNewEmployeeAndUser) {
                $authorizedFunctions = $business->plan->authorizedFunctions->toArray();
                $authorizedFunctionsTrue = array_filter($authorizedFunctions, fn($value) => $value === true);

                $employeeModel = Employee::findOrFail($employeeId);
                $employeeModel->authorizedFunction()->updateOrCreate([], $authorizedFunctionsTrue);
            }

            if ($isEditEmployee) {
                $planId = $business->plan_id;
                $employeeController->updatePermissionsFromPlan($planId, [$employeeId]);
            }

            // Confirmar a transação
            DB::commit();

            return response()->json(["msg" => "Administrador atualizado com sucesso"], 200);

        } catch (\Exception $e) {
            // Reverter a transação em caso de erro
            DB::rollBack();
            return response()->json(["msg" => $e->getMessage()], 400);
        }
    }

    public function validateCnpjInUse(Request $request): JsonResponse
    {
        $cnpj = preg_replace('/\D/', '', $request->cnpj);

        $business = Business::where('cnpj', $cnpj)->withTrashed()->first();
        $exist = false;
        $isDeleted = false;

        if ($business) {
            $exist = true;
            $isDeleted = $business->deleted_at !== null;
        }

        return response()->json([
            'business' => $business,
            'exist' => $exist,
            'is_deleted' => $isDeleted
        ]);
    }

    public function publicRegister(PublicRegisterBusinessRequest $request): JsonResponse
    {
        DB::beginTransaction();

        try {
            $data = $request->validated();

            $isCreateNewEmployeeAndUser = $data["isCreateNewEmployeeAndUser"];
            $isEditEmployee = $data["isEditEmployee"];
            $isCreateNewEmployee = $data["isCreateNewEmployee"];
            $employeeData = [];

            // ->(A) Extrair dados do admin
            $employeeData["id"] = $data["employee_id"] ?? null;         // Se houver um ID (caso isEditEmployee)
            $employeeData["name"] = $data["name"] ?? null;              // Nome do admin
            $employeeData["email"] = $data["email"] ?? null;            // E-mail do admin
            $employeeData["cpf"] = $data["cpf"] ?? null;                // CPF do admin
            $employeeData["email_validated"] = $data["email_validated"]; // Data de verificação do e-mail
            $employeeData["phone_number_1"] = $data["phone_number"];      // Número de telefone do admin

            $cnpj = $data["cnpj"];
            $planId = $data["plan_id"];
            $businessName = $data["business_name"];
            $isCnpjOptional = $data["cnpj_optional"] ?? false;

            $businessWithCnpj = null;
            // 2) Verificar se já existe um negócio com esse CNPJ (mesmo deletado) apenas se o CNPJ não for opcional e estiver presente
            if (!$isCnpjOptional && $cnpj) {
                $businessWithCnpj = Business::withTrashed()->where('cnpj', $cnpj)->first();
            }

            if ($businessWithCnpj) {
                // Se existe e está deletado, reativar
                if ($businessWithCnpj->deleted_at) {
                    $businessWithCnpj->restore();

                    $businessWithCnpj->plan_id = $planId;
                    $businessWithCnpj->seller_id = $data["seller_id"] ?? null;
                    $businessWithCnpj->corporate_reason = null;

                    $businessWithCnpj->neighborhood = null;
                    $businessWithCnpj->street = null;
                    $businessWithCnpj->number = null;
                    $businessWithCnpj->city = null;
                    $businessWithCnpj->state = null;
                    $businessWithCnpj->cep = null;
                    $businessWithCnpj->complement = null;

                    $businessWithCnpj->name = $businessName;
                    $businessWithCnpj->description = null;
                    $businessWithCnpj->latitude = null;
                    $businessWithCnpj->longitude = null;
                    $businessWithCnpj->logo = null;

                    $businessWithCnpj->status = Business::STATUS_PENDING_APPROVAL;
                    $businessWithCnpj->save();

                    (new AdminBusinessApprovalNotification(null, $businessName))->sendToAdmins();

                    $business = $businessWithCnpj;
                } else {
                    throw new Exception("Esse CNPJ já está em uso!");
                }
            } else {
                // Se não existir, criar um novo em pending_approval
                $business = new Business();
                $business->cnpj = $cnpj;
                $business->plan_id = $planId;
                $business->seller_id = $data["seller_id"] ?? null;
                $business->name = $businessName ?: 'Negócio sem nome';
                $business->status = Business::STATUS_PENDING_APPROVAL;
                $business->save();
                (new AdminBusinessApprovalNotification(null, $business->name))->sendToAdmins();

                BusinessParameters::create([
                    'business_id' => $business->id,
                ]);
            }

            // 3) Agora gerenciar/criar/editar o funcionário (admin)
            $employeeController = new EmployeesController();

            if ($isCreateNewEmployeeAndUser || $isCreateNewEmployee) {
                // Cria um novo funcionário
                $storeEmployeeRequest = new StoreEmployee($employeeData);
                $responseEmployee = $employeeController->store($storeEmployeeRequest);

                if ($responseEmployee->status() != 200) {
                    DB::rollBack();
                    return response()->json(["message" => "Erro ao criar o administrador."], $responseEmployee->status());
                }

                $newEmployee = $responseEmployee->getData()->employee;
                $employeeId = $newEmployee->id;

            } else {
                // Edita o funcionário existente
                if (!isset($employeeData['id'])) {
                    throw new \Exception("ID do funcionário para edição não fornecido.");
                }

                $updateEmployeeRequest = new UpdateEmployee($employeeData);
                $responseEditEmployee = $employeeController->updateWithoutPermissionsAndAddBussinesses($updateEmployeeRequest, $employeeData['id']);

                if ($responseEditEmployee->status() != 200) {
                    DB::rollBack();
                    return response()->json(["message" => "Erro ao atualizar o administrador."], $responseEditEmployee->status());
                }

                $updatedEmployee = $responseEditEmployee->getData()->employee;
                $employeeId = $updatedEmployee->id;
            }

            //Vincular o funcionário ao negócio como admin
            $existsPivot = EmployeeBusiness::withTrashed()
                ->where('employee_id', $employeeId)
                ->where('business_id', $business->id)
                ->first();

            if (!$existsPivot) {
                EmployeeBusiness::create([
                    'employee_id' => $employeeId,
                    'business_id' => $business->id,
                    'admin' => true
                ]);
            } else {
                EmployeeBusiness::where('employee_id', $employeeId)
                    ->where('business_id', $business->id)
                    ->update(['admin' => true, 'deleted_at' => null]);
            }

            $authorizedFunctions = $business->plan->authorizedFunctions;
            $authorizedFunctionsTrue = collect($authorizedFunctions->toArray())
                ->filter(function ($val) {
                    return $val === true;
                });

            $employeeModel = Employee::findOrFail($employeeId);
            $employeeModel->authorizedFunction()->updateOrCreate([], $authorizedFunctionsTrue->toArray());

            DB::commit();

            return response()->json([
                "msg" => "Negócio cadastrado com sucesso!",
                "business" => [
                    "id" => $business->id,
                    "name" => $business->name,
                    "status" => $business->status,
                ],
                "employee_id" => $employeeId
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(["msg" => $e->getMessage()], 400);
        }
    }

    public function cancelBusiness(Request $request, $id)
    {
        try {
            $user = $request->user();
            $business = Business::findOrFail($id);

            $isAdmin = EmployeeBusiness::where('business_id', $business->id)
                ->where('employee_id', $user->employee->id)
                ->where('admin', true)
                ->exists();

            if (!$isAdmin) {
                return response()->json(['msg' => 'Você não é administrador deste negócio.'], 403);
            }

            // 2) Alterar o status para pending_cancellation
            $business->status = Business::STATUS_PENDING_CANCELLATION;
            $business->save();

            // 3) Inativar o negócio (soft delete)
            $business->delete();

            // 4) Descobrir se o usuário tem outro negócio ativo (não deletado)
            //    e que não esteja em pending_cancellation ou completed_cancellation.
            $otherActiveBusiness = $user->employee->businesses()
                ->whereNull('businesses.deleted_at')
                ->first();

            // 5) Notificar admins (aplicação) que é necessário confirmar cancelamento
            (new AdminBusinessCancelNotification($business->logo, $business->name))->sendToAdmins();

            if ($otherActiveBusiness) {
                // 6) Seleciona outro negócio automaticamente
                $user->employee->business_selected_id = $otherActiveBusiness->id;
                $user->employee->save();

                return response()->json([
                    'msg' => 'Negócio cancelado com sucesso. Outro negócio ativo selecionado.',
                    'logout' => false
                ], 200);

            } else {
                // 7) Caso não tenha nenhum outro negócio ativo, desloga o usuário
                auth('api')->logout();

                // 8) Retorna a resposta com uma mensagem de logout
                return response()->json([
                    'msg' => 'Negócio movido para cancelamento pendente. Você não possui outro negócio ativo, portanto foi deslogado automaticamente.',
                    'logout' => true
                ], 200);
            }


        } catch (\Exception $e) {
            return response()->json([
                'msg' => $e->getMessage(),
                'exception' => get_class($e)
            ], 400);
        }
    }

    public function approveBusiness(Request $request, $id)
    {
        try {
            $business = Business::findOrFail($id);
            $user = $request->user();
            if ($user->login_type !== 'admin')
                throw new Exception("Você não tem permissão para aprovar.");
            $business->status = Business::STATUS_APPROVED_REGISTRATION;
            $business->save();
            return response()->json(["msg" => 'Negócio aprovado!']);
        } catch (ModelNotFoundException $e) {
            return response()->json(["msg" => 'Negócio não encontrado!'], 404);
        } catch (\Exception $e) {
            return response()->json(["msg" => $e->getMessage()], 400);
        }
    }

    public function rejectBusiness(Request $request, $id)
    {
        try {
            $business = Business::findOrFail($id);
            $user = $request->user();
            if ($user->login_type !== 'admin')
                throw new Exception("Você não tem permissão para rejeitar.");
            $business->status = Business::STATUS_REJECTED_REGISTRATION;
            $business->save();
            $business->delete();
            return response()->json(["msg" => 'Negócio rejeitado!']);
        } catch (ModelNotFoundException $e) {
            return response()->json(["msg" => 'Negócio não encontrado!'], 404);
        } catch (\Exception $e) {
            return response()->json(["msg" => $e->getMessage()], 400);
        }
    }

    public function cancelPendingBusiness(Request $request, $id)
    {
        try {
            $business = Business::onlyTrashed()->findOrFail($id);
            $user = $request->user();
            if ($user->login_type !== 'admin')
                throw new Exception("Você não tem permissão para cancelar.");
            if ($business->status != Business::STATUS_PENDING_CANCELLATION)
                throw new Exception("O negócio escolhido não espera um cancelamento.");
            $business->status = Business::STATUS_COMPLETED_CANCELLATION;
            $business->save();
            return response()->json(["msg" => 'Negócio revertido!']);
        } catch (ModelNotFoundException $e) {
            return response()->json(["msg" => 'Negócio não encontrado!'], 404);
        } catch (\Exception $e) {
            return response()->json(["msg" => $e->getMessage()], 400);
        }
    }

    public function reverseCancelBusiness(Request $request, $id)
    {
        try {
            $business = Business::onlyTrashed()->findOrFail($id);
            $user = $request->user();
            if ($user->login_type !== 'admin')
                throw new Exception("Você não tem permissão para reverter.");
            if ($business->status != Business::STATUS_PENDING_CANCELLATION)
                throw new Exception("O negócio escolhido não espera um cancelamento.");
            $business->status = Business::STATUS_REVERSED_CANCELLATION;
            $business->save();
            $business->restore();
            return response()->json(["msg" => 'Negócio cancelado!']);
        } catch (ModelNotFoundException $e) {
            return response()->json(["msg" => 'Negócio não encontrado!'], 404);
        } catch (\Exception $e) {
            return response()->json(["msg" => $e->getMessage()], 400);
        }
    }

    /**
     * Verifica se uma string tem formato de CNPJ (com ou sem pontuação)
     * @param string $str String a ser verificada
     * @return bool True se a string tem formato de CNPJ, false caso contrário
     */
    private function isCnpjFormat(string $str): bool
    {
        // Remove tudo que não é dígito
        $numbers = preg_replace('/\D/', '', $str);

        // Verifica se tem 14 dígitos após remover pontuação
        if (strlen($numbers) !== 14) {
            return false;
        }

        // Verifica se tem formato de CNPJ com ou sem pontuação
        $pattern = '/^\d{2}\.?\d{3}\.?\d{3}\/?\d{4}\-?\d{2}$/';
        return preg_match($pattern, $str) === 1;
    }

    public function checkWhatsappInstanceInfo(Request $request): JsonResponse
    {
        try {
            $business_id = $request->business_id;
            $instanceKey = $request->key;

            if (empty($instanceKey) || empty($business_id)) {
                return response()->json([
                    'error' => true,
                    'message' => 'instanceKey ou business_id não fornecidos.'
                ], 400);
            }

            $result = ApiWhatsapp::checkInstanceStatus($instanceKey);
            if (
                (isset($result['error']) && $result['error'] === false && $result['message'] === 'Chave inválida fornecida.')||
                (isset($result['error']) && $result['error'] === false && isset($result['instance_data']['phone_connected']) && $result['instance_data']['phone_connected'] === false)||
                (isset($result['success']) && $result['success'] === false && isset($result['message']) && $result['message'] === 'Chave inválida fornecida.')
            ) {
                // Se a chave for inválida ou o status for desconectado, remove do banco de dados
                $business = Business::find($business_id);
                if ($business) {
                    $parameters = $business->parameters;
                    if ($parameters) {
                        $parameters->whatsapp_instance_key = null;
                        $parameters->save();
                    }
                }
                ApiWhatsapp::deleteInstance($instanceKey);
                return response()->json([
                    'error' => true,
                    'message' => 'Chave inválida fornecida, chave removida do banco de dados.'
                ], 400);
            }

            return response()->json($result);
        } catch (Exception $e) {
            // Verifica se a exceção é relacionada a uma chave de instância inválida
            if ($e->getMessage() == 'Chave inválida fornecida.') {
                return response()->json([
                    'error' => true,
                    'message' => 'Chave inválida fornecida.'
                ], 400);
            }

            return response()->json([
                'error' => true,
                'message' => 'Falha ao verificar informações da instância: ' . $e->getMessage()
            ], 500);
        }
    }
}
