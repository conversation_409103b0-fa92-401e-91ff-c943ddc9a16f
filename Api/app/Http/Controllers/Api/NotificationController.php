<?php

namespace App\Http\Controllers\Api;

use App\Enums\DashboardPeriods;
use App\Exceptions\MaisLuzExceptions\NoRequestFound;
use App\Helpers\ApiNotificationPush;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\StoreNotificationRequest;
use App\Http\Requests\Api\UpdateNotificationRequest;
use App\Models\Client;
use App\Models\Notification;
use App\Models\NotificationClient;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class NotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $sendingType = $request->input('sending_type', 'all');
        $status = $request->input('status', 'all');
        $periodSelected = $request->input('period', DashboardPeriods::all->value);
        $now = Carbon::now();
        $periods = [
            DashboardPeriods::today->value => [
                'startDate' => $now->copy()->startOfDay(),
                'endDate' => $now->copy()->endOfDay(),
            ],
            DashboardPeriods::last_30days->value => [
                'startDate' => $now->copy()->sub('30 days')->startOfMonth(),
                'endDate' => $now->copy()->endOfDay(),
            ],
        ];
        try {
            $user = $request->user();
            $canReleaseAllBusiness = $user->employee->authorizedFunction->release_all_business ?? false;
            $selectedBusinessId = $user->employee->business_selected_id ?? null;
            $query = Notification::query();
            $authorizedPlanFunctions = null;
            if ($selectedBusinessId) {
                $query->whereHas('business', function($q) use($selectedBusinessId) {
                    $q->where('businesses.id', '=', $selectedBusinessId);
                });
                $authorizedPlanFunctions = $user->employee->businessSelected->plan->authorizedFunctions ?? [];
            } else {
                if (!$canReleaseAllBusiness)
                    throw new Exception("Você precisa selecionar um negócio!");
            }
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%$search%")
                      ->orWhere('description', 'like', "%$search%");
                });
            }
            if ($periodSelected !== DashboardPeriods::all->value) {
                $startDate = $periods[$periodSelected]['startDate'] ?? null;
                $endDate = $periods[$periodSelected]['endDate'] ?? null;
                if ($periodSelected === DashboardPeriods::especific->value){
                    $startDate = Carbon::parse($request->input('startDate', null))->startOfDay();
                    $endDate = Carbon::parse($request->input('endDate', null))->endOfDay();
                    if (!$startDate || !$endDate)
                        throw new Exception("Você precisa especificar as datas de início e fim!");
                } 
                $query->whereBetween('created_at', [$startDate, $endDate]);    
            }

            if ($sendingType != 'all') {
                if ($authorizedPlanFunctions) {
                    $hasEmailPermission = $authorizedPlanFunctions->email_sending || false;
                    $hasWhatsappPermission = $authorizedPlanFunctions->whatsapp_sending || false;

                    if (($sendingType == 'email' && !$hasEmailPermission) || ($sendingType == 'whatsapp' && !$hasWhatsappPermission))
                        throw new Exception('Você não pode usar esse filtro');
                }
                $query->where('sending_type', $sendingType);
            } else {
                if ($authorizedPlanFunctions) {
                    $hasEmailPermission = $authorizedPlanFunctions->email_sending || false;
                    $hasWhatsappPermission = $authorizedPlanFunctions->whatsapp_sending || false;

                    if (!$hasEmailPermission) {
                        $query->whereNot('sending_type', 'email');
                    }

                    if (!$hasWhatsappPermission) {
                        $query->whereNot('sending_type', 'whatsapp');
                    }
                }
            }
            if ($status !== 'all') {
                switch ($status) {
                    case 'waiting':
                        $query->whereNotNull('created_at')
                                ->whereNull('started_at')
                                ->whereNull('finished_at');
                    break;
                    case 'sending':
                        $query->whereNotNull('created_at')
                              ->whereNotNull('started_at')
                              ->whereNull('finished_at');
                    break;
                    case 'finished':
                        $query->whereNotNull('created_at')
                              ->whereNotNull('started_at')
                              ->whereNotNull('finished_at');
                    break;
                    case 'canceled':
                        $query->whereNotNull('canceled_at');
                    break;
                }
            }

            $query->orderByDesc('created_at');
            $notifications = $perPage <= 0 ? $query->get() : $query->paginate($perPage);
            $notifications->load([
                'business' => function ($q) use ($canReleaseAllBusiness, $selectedBusinessId) {
                    if (!$canReleaseAllBusiness) {
                        $q->where('businesses.id', $selectedBusinessId);
                    }
                }
            ]);

            $nextProcessingTime = \Carbon\Carbon::now()->addHour()->startOfHour();

            return response()->json([
                'notifications' => $notifications,
                'next_processing_time' => $nextProcessingTime->format('Y-m-d H:i:s')
            ]);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNotificationRequest $request)
    {
        $data = $request->all();
        unset($data['number_clients']);
        $user = $request->user();
        $filtersInput = $request->input('filters', null);
        try {
            $selectedBusinessId = $user->employee->business_selected_id ?? null;
            $canReleaseAllBusiness = $user->employee->authorizedFunction->release_all_business || false;
            if (!$selectedBusinessId && !$canReleaseAllBusiness) {
                throw new Exception("Você deve selecionar um negócio!");
            }
            if ($filtersInput) {
                $filters = json_decode($filtersInput);
                $client_ids = Client::getClientsIdsFiltered($filters);
                if (count($client_ids) == 0) {
                    throw new Exception("Pelo menos um cliente deve ser listado!");
                }
            } else throw new Exception("Houve um erro ao encontrar os clientes!");
            $data['business_id'] = $selectedBusinessId;
            $data['number_clients'] = count($client_ids);
            $notification = Notification::create($data);
            $notification->clients()->sync($client_ids);
            return response()->json(['notification' => $notification], 201);
        } catch(ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 400);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 422);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $selectedBusinessId = $user->employee->business_selected_id ?? null;
            $notification = Notification::findOrFail($id);

            if ($notification->notification_push_id != null && $notification->sending_type === 'notification_push') {
                try {
                    $notificationPushPayload = ApiNotificationPush::getNotification($notification->notification_push_id);
                    $notification->sending_number_success = $notificationPushPayload['successful'];
                    $notification->sending_number_failed = $notificationPushPayload['failed'];
                } catch(Exception $e) {}
            }

            if (!$selectedBusinessId)
                $notification->load('business');

            return response()->json(['notification' => $notification]);
        } catch(ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 400);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateNotificationRequest $request, string $id)
    {
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            Notification::destroy($id);
            return response()->json(['msg' => 'Notificação deletada com sucesso!']);
        } catch (ModelNotFoundException $ex) {
            return response()->json(['msg' => 'Notificação não encontrado.'], 404);
        }
    }

    public function validateStore(StoreNotificationRequest $request) {
        return response()->json(['success' => true]);
    }

    public function validateUpdate(UpdateNotificationRequest $request) {
        return response()->json(['success' => true]);
    }

    public function getClientsByNotification(Request $request, $id) {
        $perPage = $request->input("per_page", 5);
        $search = $request->input("search", '');
        $shippingStatus = $request->input("shipping_status", 'all');
        try {
            $notification = Notification::findOrFail($id);
            $clients = $notification->notificationClients()
                    ->join('clients', 'notification_clients.client_id', '=', 'clients.id')
                    ->join('users', 'clients.user_id', '=', 'users.id')
                    ->select('notification_clients.*', 'users.name', 'users.cpf');

            if ($search) {
                $cpf = preg_replace('/[^0-9]/', '', $search);
                if (preg_match('/^\d{1,11}$/', $cpf))
                    $clients->where('users.cpf', 'like', "$cpf%");
                else
                    $clients->where('users.name', 'like', "%$search%");
            }

            if ($shippingStatus !== 'all') {
                switch($shippingStatus) {
                    case 'waiting':
                        $clients->where('notification_clients.shipping_status', 'waiting');
                    break;
                    case 'sent':
                        $clients->where('notification_clients.shipping_status', 'sent');
                    break;
                    case 'failed':
                        $clients->where('notification_clients.shipping_status', 'failed');
                    break;
                    case 'canceled':
                        $clients->where('notification_clients.shipping_status', 'canceled');
                    break;
                }
            }

            $clients = $perPage <= 0 ? $clients->get() : $clients->paginate($perPage);
            return response()->json(['clients' => $clients]);
        } catch (ModelNotFoundException $ex) {
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 404);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function indexClient(Request $request) {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', default: 1);
        $sendingType = $request->input('sending_type', 'all');
        $status = $request->input('status', 'all');
        $user = $request->user();
        $periodSelected = $request->input('period', DashboardPeriods::all->value);
        $now = Carbon::now();
        $periods = [
            DashboardPeriods::today->value => [
                'startDate' => $now->copy()->startOfDay(),
                'endDate' => $now->copy()->endOfDay(),
            ],
            DashboardPeriods::last_30days->value => [
                'startDate' => $now->copy()->sub('30 days')->startOfMonth(),
                'endDate' => $now->copy()->endOfDay(),
            ],
        ];
        try {
            if ($user->login_type !== 'client') {
                throw new Exception("Você deve entrar como cliente!");
            }
            $clientLoggedId = $user->client->id;
            $query = NotificationClient::query()
                    ->join('notifications', 'notification_clients.notification_id', '=', 'notifications.id')
                    ->where(['notification_clients.client_id' => $clientLoggedId, 'notification_clients.shipping_status' => 'sent'])
                    ->select('notifications.*', 'notification_clients.*');

            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('notifications.title', 'like', "%$search%")
                      ->orWhere('notifications.description', 'like', "%$search%");
                });
            }
            
            if ($periodSelected !== DashboardPeriods::all->value) {
                $startDate = $periods[$periodSelected]['startDate'] ?? null;
                $endDate = $periods[$periodSelected]['endDate'] ?? null;
                if ($periodSelected === DashboardPeriods::especific->value){
                    $startDate = Carbon::parse($request->input('startDate', null))->startOfDay();
                    $endDate = Carbon::parse($request->input('endDate', null))->endOfDay();
    
                    if (!$startDate || !$endDate)
                        throw new Exception("Você precisa especificar as datas de início e fim!");
                } 
                $query->whereBetween('shipping_date', [$startDate, $endDate]);    
            }

            if ($sendingType != 'all') {
                $query->where('notifications.sending_type', $sendingType);
            }

            if ($status !== 'all') {
                switch ($status) {
                    case 'waiting':
                        $query->whereNotNull('notifications.created_at')
                                ->whereNull('notifications.started_at')
                                ->whereNull('notifications.finished_at');
                    break;
                    case 'sending':
                        $query->whereNotNull('notifications.created_at')
                              ->whereNotNull('notifications.started_at')
                              ->whereNull('notifications.finished_at');
                    break;
                    case 'finished':
                        $query->whereNotNull('notifications.created_at')
                              ->whereNotNull('notifications.started_at')
                              ->whereNotNull('notifications.finished_at');
                    break;
                }
            }
            $query->orderByDesc('notifications.created_at');
            $notifications = $perPage <= 0 ? $query->get() : $query->paginate($perPage);
            return response()->json(['notifications' => $notifications]);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function showNotificationClient(string $id)
    {
        try {
            $notification = NotificationClient::findOrFail($id);
            return response()->json(['notification' => $notification]);
        } catch(ModelNotFoundException $e) {
            return response()->json($this->getResponseExceptionBody(new NoRequestFound()), 400);
        } catch(Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function cancel($id)
    {
        try {
            $notification = Notification::findOrFail($id);

            if ($notification->started_at !== null) {
                return response()->json(['msg' => 'Não é possível cancelar, notificação já iniciada.'], 422);
            }

            if ($notification->canceled_at !== null) {
                return response()->json(['msg' => 'Notificação já está cancelada.'], 422);
            }

            $notification->canceled_at = now();
            $notification->save();

            $notification->notificationClients()
                ->where('shipping_status', 'waiting')
                ->update(['shipping_status' => 'canceled']);

            return response()->json(['msg' => 'Notificação cancelada com sucesso!']);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Notificação não encontrada.', 'exception' => get_class($e)], 404);
        } catch (Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }
}
