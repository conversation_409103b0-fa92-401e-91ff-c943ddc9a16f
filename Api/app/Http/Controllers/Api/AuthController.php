<?php

namespace App\Http\Controllers\Api;


use App\Http\Requests\LoginRequest;
use App\Http\Controllers\Controller;
use App\Exceptions\MaisLuzExceptions\InvalidEmailCpfException;
use App\Exceptions\MaisLuzExceptions\InvalidEmailException;
use App\Exceptions\MaisLuzExceptions\UserNotFoundException;
use App\Exceptions\MaisLuzExceptions\UnauthorizedLoginException;
use App\Http\Requests\Api\BaseRequest as Request;
use App\Models\ClientCellDevice;
use App\Models\User;


class AuthController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['login', 'refresh']]);
    }

    public function login(LoginRequest $request)
    {
        try {
            $hasDevice = false;
            $deviceId = $request->input('id_device', null);
            $user = User::where('email', $request->email)->whereNotNull('cpf')->first();
            if(!$user){
                $cpf = preg_replace('/[^0-9]/', '', $request->email);
                $user = User::where('cpf', $cpf)->first();
            }

            if(!isset($user)) { throw new InvalidEmailCpfException(); }

            $credentials = [
                'cpf' => $user->cpf,
                'email' => $user->email,
                'password' => $request->password,
                'deleted_at' => null,
            ];

            if (! $token = auth('api')->attempt($credentials)) { throw new UnauthorizedLoginException(); }

            if($user->typesCount() == 1 &&
                $user->hasEmployee() &&
                $user->employee->businesses()->count() == 0 &&
                !$user->employee->authorizedFunction->release_all_business
            ){
                return response()->json(['msg' => 'Não existe nenhum negócio ativo vinculado a esse funcionário'], 401);
            }

            if ($user->hasClient() && $deviceId) {
                $client = $user->client;
                $device = ClientCellDevice::where(['id_client' => $client->id, 'id_device' => $deviceId])->first();
                if(!$device) {
                    ClientCellDevice::create(['id_client' => $client->id, 'id_device' => $deviceId]);
                }
                $hasDevice = true;
            }

            UserController::selectInitialBusinessForLogin($user);
            UserController::updateLastLogin($user);

            return $this->respondWithToken($token, $hasDevice);
        } catch(InvalidEmailException|UserNotFoundException|UnauthorizedLoginException $e) {
            return response()->json($this->getResponseExceptionBody($e), 401);
        } catch(\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 401);
        }
    }

    public function me(Request $r)
    {
        return response()->json(auth('api')->user());
    }


    public function logout(Request $r)
    {
        $deviceId = $r->input('id_device', null);
        $user = $r->user();
        if ($deviceId && $user->hasClient()) {
            ClientCellDevice::where(['id_client' => $user->client->id, 'id_device' => $deviceId])->delete();
        }
        auth('api')->logout();

        return response()->json(['message' => 'Successfully logged out']);
    }

    public function refresh(Request $r)
    {
        return $this->respondWithToken(auth('api')->refresh());
    }

    protected function respondWithToken($token, $isDevice = false)
    {
        if ($isDevice) {
            auth('api')->factory()->setTTL(0);
        } else {
            auth('api')->factory()->setTTL(env('JWT_TTL', 60));
        }

        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => $isDevice ? null : auth('api')->factory()->getTTL() * 60
        ]);
    }
}
