<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ApiUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Forms\StoreFormRequest;
use App\Http\Requests\Api\Forms\UpdateFormRequest;
use App\Models\Form;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FormController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $isPaginate = filter_var($request->input('is_paginate', true), FILTER_VALIDATE_BOOLEAN);
            $employee = $user->employee;
            $business_selected_id = $employee->business_selected_id;
            $per_page = $request->per_page ?? 5;
            $query = Form::query();
            $hasDefaultPermission = ApiUser::hasUserFunction('default_forms');

            if (!$employee->authorizedFunction->release_all_business) {
                $query->where(function ($query) use ($business_selected_id, $hasDefaultPermission) {
                    $query->where(function ($query) use ($hasDefaultPermission) {
                        $query->where('default', true);
                        if (!$hasDefaultPermission) {
                            $query->where('active', true);
                        }
                    })->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                        $query->where('business_id', $business_selected_id);
                    });
                });
            } else if ($employee->authorizedFunction->release_all_business) {
                $query->where(function ($query) use ($business_selected_id, $hasDefaultPermission) {
                    $query->where(function ($query) use ($hasDefaultPermission) {
                        $query->where('default', true);
                        if (!$hasDefaultPermission) {
                            $query->where('active', true);
                        }
                    })->orWhereHas('businesses', function ($query) use ($business_selected_id) {
                        $query->where('business_id', $business_selected_id);
                    });
                });
            }
            if ($business_selected_id) {
                $filter_default = $request->filter_default;
            } elseif ($employee->authorizedFunction->release_all_business) {
                $filter_default = "default";
            }else{
                $filter_default = null;
            }

            if ($filter_default != null && $filter_default != "") {
                if ($filter_default == 'default') {
                    $query->where('default', true);
                } elseif ($filter_default == 'not_default') {
                    $query->where('default', false);
                }
            }

            if ($request->search) {
                $query->where('name', 'like', "%{$request->search}%");
            }

            $query->orderBy('name', 'asc');
            if ($isPaginate)
                $forms = $per_page <= 0 ? $query->get() : $query->paginate($per_page);
            else $forms = $query->get();
            $forms->load('topics', 'businesses');

            return response()->json(['forms' => $forms]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function getFormsToList(Request $request): JsonResponse
    {
        return $this->index($request->merge(['per_page' => 0]));
    }


    public function store(StoreFormRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $user = $request->user();
            $business_selected_id = $user->employee->business_selected_id ?? false;
            if (!$business_selected_id){
                if (!$data['default'])
                    throw new \Exception("Você precisa selecionar um negócio para cadastrar.");
            }
            if (!$data['default']) {
                $data['active'] = true;
            }
            $form = Form::create($data);
            $form->topics()->sync($this->getTopicsWithOrder($data['topics']));
            if (!$data['default']) {
                $form->businesses()->sync($data['businesses']);
            }

            return response()->json(['form' => $form->load('topics', 'businesses')], 201);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function show(string $id): JsonResponse
    {
        try {
            $form = Form::with('topics', 'businesses')->findOrFail($id);
            return response()->json(['form' => $form]);
        } catch (ModelNotFoundException $e) {
            return response()->json(['msg' => 'Formulário não existe!'], 400);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function update(UpdateFormRequest $request, string $id): JsonResponse
    {
        try {
            $data = $request->validated();
            $employee = $request->user()->employee ?? null;
            $business_selected_id = $employee->business_selected_id ?? false;
            if (!$business_selected_id){
                if (!$data['default'])
                    throw new \Exception("Você precisa selecionar um negócio para cadastrar.");
            }
            if (!$data['default']) {
                $data['active'] = true;
            }
            $form = Form::findOrFail($id);
            $form->update($data);
            $form->topics()->sync($this->getTopicsWithOrder($data['topics']));
            if (!$data['default']) {
                $form->businesses()->sync($data['businesses']);
            } else {
                $form->businesses()->detach();
            }

            return response()->json(['form' => $form->load('topics', 'businesses')]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function destroy(string $id): JsonResponse
    {
        try {
            $form = Form::findOrFail($id);
            $form->delete();
            return response()->json(['msg' => 'Formulário deletado']);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function restore(string $id): JsonResponse
    {
        try {
            $form = Form::withTrashed()->findOrFail($id);
            $form->restore();
            return response()->json(['form' => $form]);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    public function forceDelete(string $id): JsonResponse
    {
        try {
            $form = Form::withTrashed()->findOrFail($id);
            $form->forceDelete();
            return response()->json(['msg' => 'Formulário deletado permanentemente']);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }

    private function getTopicsWithOrder(array $topics): array
    {
        $topicsWithOrder = [];
        foreach ($topics as $topic) {
            $topicsWithOrder[$topic['id']] = ['order' => $topic['order']];
        }
        return $topicsWithOrder;
    }
}
