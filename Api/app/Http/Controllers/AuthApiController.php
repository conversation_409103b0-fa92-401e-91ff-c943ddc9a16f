<?php

namespace App\Http\Controllers;

use App\Http\Requests\Api\BaseRequest as Request;
use Illuminate\Support\Facades\Http;
use App\Helpers\ApiUser;

class AuthApiController extends Controller
{
    public function loginPage()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        try {
            $url = $this->getApiUrl();
            $response = Http::post("$url/api/auth/login", $request->all());
            if($response->successful()) {
                $this->handleLoginRemender($request);
                session()->put('API_TOKEN', $response->json('access_token'));
                return redirect()->route('home')->with('success', 'Logado com sucesso!');
            }
            if($response->status() == 422) {
                return back()->withErrors($response->json('errors'))->withInput();
            }
            if($response->status() == 400) {
                //verificar se é cpf ou email
                $login = $request->email;
                $cpf = preg_replace('/[^0-9]/', '', $login);
                if (filter_var($login, FILTER_VALIDATE_EMAIL)) {
                    return back()->with('error', 'Email ou senha inválidos')->withInput();
                } elseif (preg_match('/^\d{11}$/', $cpf)) {
                    return back()->with('error', 'CPF ou senha inválidos')->withInput();
                } else {
                    return back()->with('error', 'Email, CPF ou senha inválidos')->withInput();
                }

            }
            $msg = $response->json('msg') ?? 'Senha inválida';
            return back()->with('error', $msg)->withInput();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function update_user_profile(Request $request)
    {
        try {
            $data = $this->removeMasks($request->except(['_method', '_token']));
            $this->put('api/user/update', $data);

            ApiUser::update();
            return back()->with('success', 'Usuário atualizado!');
        } catch(\Illuminate\Http\Exceptions\HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function update_user_password(Request $request)
    {
        try {
            $data = $request->except(['_method', '_token']);
            $this->put('api/user/password/update', $data);

            return back()->with('success', 'Senha atualizada!');
        } catch(\Illuminate\Http\Exceptions\HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function logout(Request $request)
    {
        try {
            session()->forget('API_TOKEN');
            session()->forget('AUTH_USER');
            session()->flash('success', 'Sessão encerrada!');

            return redirect()->route('home');
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function user(Request $request)
    {
        try {
            $url = $this->getApiUrl();
            $response = Http::withToken(session('API_TOKEN'))->get("$url/api/user");
            if($response->successful()) { return $response->json(); }
            else { return null; }
        } catch(\Exception $e) {
            return null;
        }
    }

    private function handleLoginRemender(Request $request)
    {
        session()->forget(['remember_email', 'remember_password']);
        $remember = $request->boolean('remember');

        if($remember) {
            session()->put('remember_email', $request->email);
            session()->put('remember_password', $request->password);
            session()->put('remember_login', $remember);
        }
    }
}
