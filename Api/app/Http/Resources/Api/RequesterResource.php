<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class RequesterResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'cpf' => $this->user->cpf,
            'name' => $this->user->name,
            'gender' => $this->user->gender,
            'birth_date' => $this->user->birth_date,
            'phone_number_1' => $this->user->phone_number_1,
            'phone_number_2' => $this->user->phone_number_2,
            'address' => AddressResource::make($this->user),
            'type' => $this->user->type,
        ];
    }
}
