<?php

namespace App\Http\Resources\Api;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class UserResource extends JsonResource
{
    public function toArray($request)
    {
        $avatar = null;
        if($this->avatar != null && Storage::disk('s3')->exists($this->avatar)) {
            $avatar = Storage::disk('s3')->temporaryUrl($this->avatar, Carbon::now()->addWeek());
        }

        $businesses = collect();
        // Se a relação (EMPLOYEE) for carregada, adiciona os negócios à coleção
        if ($this->relationLoaded('employee') && $this->employee && $this->employee->relationLoaded('businesses')) {
            $businesses = $businesses->merge($this->employee->businesses);
        }
        // Se a relação (CLIENT) for carregada, adiciona os negócios à coleção
        if ($this->relationLoaded('client') && $this->client && $this->client->relationLoaded('businesses')) {
            $businesses = $businesses->merge($this->client->businesses);
        }
        // remover as duplicastas
        $businesses = $businesses->unique('id')->values();   

        $isFromGetUsers = $this->isFromGetUsers ?? false;

        return [
            'id' => $this->id,
            'avatar' => $avatar,
            'name' => $this->name,
            'email' => $this->email,
            'birth_date' => $this->birth_date,
            'cpf' => $this->cpf,
            'gender' => $this->gender,
            'phone_number_1' => $this->phone_number_1,
            'phone_number_2' => $this->phone_number_2,
            'address' => AddressResource::make($this),
            'email_verified_at' => $this->email_verified_at,
            'login_type' => $this->login_type,
            'last_login_at' => $this->last_login_at,
            'is_admin_of_business' => $this->is_admin_of_business ?? false,
            'is_entrepreneur' => $this->is_entrepreneur ?? false,
            'client' => $this->relationLoaded('client') && $this->hasClient() ? ['id' => $this->client->id] : null,
            'seller' => $this->relationLoaded('seller') && $this->hasSeller() ? 
            [
                'id' => $this->seller->id,
                'percentage_per_sale' => $this->seller->percentage_per_sale,
                'register_sellers' => $this->seller->register_sellers,
                'registered_by' => $this->seller->registeredBy ? $this->seller->registeredBy->toArray() : null,
            ] : null,
            'employee' => $this->relationLoaded('employee') && $this->hasEmployee() ? 
            [
                'id' => $this->employee->id,
                'authorizedFunction' => [
                    'employees' => (bool) $this->employee->authorizedFunction->employees,
                    'settings' => (bool) $this->employee->authorizedFunction->settings,
                    'release_all_business' => (bool) $this->employee->authorizedFunction->release_all_business,
                    'manage_business_profile' => (bool) $this->employee->authorizedFunction->manage_business_profile,
                    'plans' => (bool) $this->employee->authorizedFunction->plans,
                    'sellers' => (bool) $this->employee->authorizedFunction->sellers,
                    'topics' => (bool) $this->employee->authorizedFunction->topics,
                    'default_topics' => (bool) $this->employee->authorizedFunction->default_topics,
                    'update_topics' => (bool) $this->employee->authorizedFunction->update_topics,
                    'forms' => (bool) $this->employee->authorizedFunction->forms,
                    'default_forms' => (bool) $this->employee->authorizedFunction->default_forms,
                    'update_forms' => (bool) $this->employee->authorizedFunction->update_forms,
                    'update_business' => (bool) $this->employee->authorizedFunction->update_business,
                    'cashback' => (bool) $this->employee->authorizedFunction->cashback,
                    'update_cashback' => (bool) $this->employee->authorizedFunction->update_cashback,
                    'clients' => (bool) $this->employee->authorizedFunction->clients,
                    'update_clients' => (bool) $this->employee->authorizedFunction->update_clients,
                    'evaluations' => (bool) $this->employee->authorizedFunction->evaluations,
                    'notifications' => (bool) $this->employee->authorizedFunction->notifications,
                    'update_notifications' => (bool) $this->employee->authorizedFunction->update_notifications,
                    'dashboard' => (bool) $this->employee->authorizedFunction->dashboard,
                    'parameters' => (bool) $this->employee->authorizedFunction->parameters,
                    'site_notifications' => (bool) $this->employee->authorizedFunction->site_notifications,
                    'update_site_notifications' => (bool) $this->employee->authorizedFunction->update_site_notifications,
                ],
                'authorizedSettingsFunction' => [
                    'contact' => (bool) $this->employee->authorizedSettingsFunction->contact,
                    'system_parameters' => (bool) $this->employee->authorizedSettingsFunction->system_parameters
                ]
            ] : null,
            'types' => [
                'admin' => [
                    'name' => "Administrador",
                    'hasType' => $this->hasAdmin()
                ],
                'employee' => [
                    'name' => "Negócio",
                    'hasType' => $isFromGetUsers ? $this->hasEmployee() : $this->hasEmployee() && !$this->hasAdmin() && ($this->employee->businesses()->withTrashed()->count() > 0 || $this->employee->authorizedFunction->release_all_business)
                ],
                'seller' => [
                    'name' => "Vendedor",
                    'hasType' => $this->hasSeller()
                ],
                'client' => [
                    'name' => "Cliente",
                    'hasType' => $this->hasClient()
                ]
            ],
            'businesses' => BusinessResource::collection($businesses),
        ];
    }
}