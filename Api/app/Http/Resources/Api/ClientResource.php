<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class ClientResource extends JsonResource
{
    public function toArray($request)
    {
        $array = UserResource::make($this->user)->toArray($request);
        $array['businesses'] = [];
        $array['id'] = $this->id;
        $array['user_id'] = $this->user_id;
        
        $array['is_entrepreneur'] = $this->is_entrepreneur;
        $array['is_informative_read'] = $this->is_informative_read;

        foreach($this->businesses as $business) {
            $array['businesses'][] = BusinessResource::make($business);
        }

        return $array;
    }
}
