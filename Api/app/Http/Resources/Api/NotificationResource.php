<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    public function toArray($request)
    {
        $array = [];
        $array['id'] = $this->id;
        $array['sending_type'] = $this->sending_type;
        $array['title'] = $this->title;
        $array['description'] = $this->description;
        $array['url_click'] = $this->url_click;
        $array['number_clients'] = $this->number_clients;
        $array['sending_number_success'] = $this->sending_number_success;
        $array['sending_number_failed'] = $this->sending_number_failed;
        $array['created_at'] = $this->created_at;
        $array['started_at'] = $this->started_at;
        $array['finished_at'] = $this->finished_at;
        $array['updated_at'] = $this->updated_at;
        $array['business_id'] = $this->business_id;

        if ($this->business) {
            $array['business'] = [
                'id' => $this->business->id,
                'name' => $this->business->name,
            ];
        }
        return $array;
    }
}
