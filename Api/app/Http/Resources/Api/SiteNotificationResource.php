<?php

namespace App\Http\Resources\Api;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class SiteNotificationResource extends JsonResource
{
    public function toArray($request)
    {
        $icon = null;
        if($this->icon && Storage::disk('s3')->exists($this->icon)) {
            $icon = Storage::disk('s3')->temporaryUrl($this->icon, Carbon::now()->addDay());
        }
        $array = [];
        $array['id'] = $this->id;
        $array['title'] = $this->title;
        $array['description'] = $this->description;
        $array['url_click'] = $this->url_click;
        $array['created_at'] = $this->created_at;
        $array['updated_at'] = $this->updated_at;
        $array['business_id'] = $this->business_id;
        $array['icon'] = $icon;
        if ($this->business) {
            $array['business'] = [
                'id' => $this->business->id,
                'name' => $this->business->name,
            ];
        }
        return $array;
    }
}
