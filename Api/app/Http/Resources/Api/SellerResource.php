<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class SellerResource extends JsonResource
{
    public function toArray($request)
    {
        $array = UserResource::make($this->user)->toArray($request);
        $array['id'] = $this->id;
        $array['percentage_per_sale'] = $this->percentage_per_sale;
        $array['register_sellers'] = $this->register_sellers;
        $array['registered_by'] = $this->registered_by;
        $array['businesses'] = $this->businesses();

        unset($array['created_at']);
        unset($array['updated_at']);
        unset($array['deleted_at']);

        if ($this->registeredBy !== null) {
            $array['registered_by'] = $this->registeredBy->toArray();
        }

        return $array;
    }
}
