<?php

namespace App\Http\Resources\Api;
use Illuminate\Http\Resources\Json\JsonResource;

class UserSiteNotificationResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user' => [
                'name' => $this->user->name,
                'cpf' => $this->user->cpf,
                'type' => $this->type,
                'email' => $this->user->email,
                'phone_number_1' =>  $this->user->phone_number_1,
            ],
            'notification' => new SiteNotificationResource($this->siteNotification),
            'viewed' => $this->viewed,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'viewed_at' => $this->viewed_at
        ];
    }
}
