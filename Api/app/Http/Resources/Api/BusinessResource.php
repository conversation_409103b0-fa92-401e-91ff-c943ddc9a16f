<?php

namespace App\Http\Resources\Api;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class BusinessResource extends JsonResource
{

    public function toArray($request): array
    {
        $logo = null;
        if($this->logo && Storage::disk('s3')->exists($this->logo)) {
            $logo = Storage::disk('s3')->temporaryUrl($this->logo, Carbon::now()->addWeek());
        }

        $admin = $this->employees()
            ->wherePivot('admin', true)
            ->first();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'logo' => $logo,
            'plan' => $this->plan,
            'seller' => $this->seller,
            'deleted_at' => $this->deleted_at,
            'cnpj' => $this->cnpj,
            'corporate_reason' => $this->corporate_reason,
            'neighborhood' => $this->neighborhood,
            'street' => $this->street,
            'number' => $this->number,
            'city' => $this->city,
            'state' => $this->state,
            'cep' => $this->cep,
            'complement' => $this->complement,
            'parameters' => $this->parameters,
            'status' => $this->status,
            'url_facebook' => $this->url_facebook,
            'url_google' => $this->url_google,
            'url_instagram' => $this->url_instagram,
            'url_linkedin' => $this->url_linkedin,
            'admin' => [
                'id' => $admin?->id,
                'name' => $admin?->user->name,
                'cpf' => $admin?->user->cpf,
                'phone_number_1' => $admin?->user->phone_number_1,
            ],
        ];

    }
}
