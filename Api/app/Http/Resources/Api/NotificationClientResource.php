<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationClientResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'client' => [
                'name' => $this->client->user->name,
                'cpf' => $this->client->user->cpf,
            ],
            'notification' => new NotificationResource($this->notification),
            'notification_client_id' => $this->id,
            'shipping_date' => $this->shipping_date,
            'shipping_status' => $this->shipping_status,
        ];
    }
}
