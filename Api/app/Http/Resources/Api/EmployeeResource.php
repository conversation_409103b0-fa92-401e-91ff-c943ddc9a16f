<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeResource extends JsonResource
{
    public function toArray($request)
    {
        $array = UserResource::make($this->user)->toArray($request);
        $array['id'] = $this->id;
        $array['user_id'] = $this->user_id;
        $array['authorizedFunction'] = [];
        $array['authorizedFunction']['employees'] = boolval($this->authorizedFunction->employees);
        $array['authorizedFunction']['settings'] = boolval($this->authorizedFunction->settings);
        $array['authorizedFunction']['release_all_business'] = boolval($this->authorizedFunction->release_all_business);
        $array['authorizedFunction']['manage_business_profile'] = boolval($this->authorizedFunction->manage_business_profile);
        $array['authorizedFunction']['plans'] = boolval($this->authorizedFunction->plans);
        $array['authorizedFunction']['sellers'] = boolval($this->authorizedFunction->sellers);
        $array['authorizedFunction']['topics'] = boolval($this->authorizedFunction->topics);
        $array['authorizedFunction']['default_topics'] = boolval($this->authorizedFunction->default_topics);
        $array['authorizedFunction']['update_topics'] = boolval($this->authorizedFunction->update_topics);
        $array['authorizedFunction']['forms'] = boolval($this->authorizedFunction->forms);
        $array['authorizedFunction']['default_forms'] = boolval($this->authorizedFunction->default_forms);
        $array['authorizedFunction']['update_forms'] = boolval($this->authorizedFunction->update_forms);
        $array['authorizedFunction']['update_business'] = boolval($this->authorizedFunction->update_business);
        $array['authorizedFunction']['cashback'] = boolval($this->authorizedFunction->cashback);
        $array['authorizedFunction']['update_cashback'] = boolval($this->authorizedFunction->update_cashback);
        $array['authorizedFunction']['clients'] = boolval($this->authorizedFunction->clients);
        $array['authorizedFunction']['update_clients'] = boolval($this->authorizedFunction->update_clients);
        $array['authorizedFunction']['evaluations'] = boolval($this->authorizedFunction->evaluations);
        $array['authorizedFunction']['notifications'] = boolval($this->authorizedFunction->notifications);
        $array['authorizedFunction']['update_notifications'] = boolval($this->authorizedFunction->update_notifications);
        $array['authorizedFunction']['dashboard'] = boolval($this->authorizedFunction->dashboard);
        $array['authorizedFunction']['parameters'] = boolval($this->authorizedFunction->parameters);
        $array['authorizedFunction']['site_notifications'] = boolval($this->authorizedFunction->site_notifications);
        $array['authorizedFunction']['update_site_notifications'] = boolval($this->authorizedFunction->update_site_notifications);

        $array['authorizedSettingsFunction'] = [];
        $array['authorizedSettingsFunction']['contact'] = boolval($this->authorizedSettingsFunction->contact);
        $array['authorizedSettingsFunction']['system_parameters'] = boolval($this->authorizedSettingsFunction->system_parameters);

        $array['business_selected'] = BusinessResource::make($this->businessSelected);
        $array['admin'] = $this->admin;
        $array['initial_screen'] = $this->initial_screen;
        $array['is_informative_read'] = $this->is_informative_read;

        $array['businesses'] = [];
        foreach ($this->businesses as $business) {
            $array['businesses'][] = BusinessResource::make($business);
        }

        $array['businesses_canceled'] = [];

        if (empty($array['businesses'])) {
            foreach ($this->businesses_canceled as $business) {
                $array['businesses_canceled'][] = BusinessResource::make($business);
            }
        }

        $array['is_admin_of_business'] = $this->is_admin_of_business ?? false;

        unset($array['created_at']);
        unset($array['updated_at']);
        unset($array['deleted_at']);

        return $array;
    }
}
