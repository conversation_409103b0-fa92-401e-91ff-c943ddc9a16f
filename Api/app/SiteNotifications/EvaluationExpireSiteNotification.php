<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class EvaluationExpireSiteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName, string $url_click)
    {
        parent::__construct(
            $icon, 
            $business_id, 
            "Avaliação expirando!",
            "A sua avaliação de $businessName está prestes a expirar. Clique para responder",
            $url_click,
            SiteNotificationTypes::client
        );
    }
}
