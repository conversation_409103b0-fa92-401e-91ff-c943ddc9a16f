<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class AdminBusinessReverseCancelNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, string $businessName, int $businessId)
    {
        parent::__construct(
            $icon, 
            null, 
            "Cancelamento Revertido!",
            "O negócio $businessName teve o cancelamento revertido!",
            config('app.frontend_url')."/businesses/$businessId",
            SiteNotificationTypes::employee
        );
    }
}
