<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class AdminCashbackCompleteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName, string $clientName, string $cashbackDescription)
    {
        parent::__construct(
            $icon, 
            $business_id, 
            "Cashback utilizado!",
            "O cliente $clientName usou um cashback de $cashbackDescription do negócio $businessName!",
            config('app.frontend_url')."/cashbacks",
            SiteNotificationTypes::employee
        );
    }
}
