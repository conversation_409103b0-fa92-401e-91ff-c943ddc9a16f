<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class CashbackCreateSiteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName, string $cashbackDescription)
    {
        parent::__construct(
            $icon, 
            $business_id, 
            "Cashback criado!",
            "Você recebeu um cashback de $cashbackDescription do negócio $businessName!",
            config('app.frontend_url')."/cashbacks/client",
            SiteNotificationTypes::client
        );
    }
}
