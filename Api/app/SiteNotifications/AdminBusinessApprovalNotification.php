<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class AdminBusinessApprovalNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, string $businessName)
    {
        $queryParams = http_build_query([
            'search' => $businessName,
            'filter_disabled' => 'all',
            'seller_id' => '',
            'status' => 'pending_approval',
        ]);

        parent::__construct(
            $icon, 
            null, 
            "Aprovação Pendente",
            "O negócio $businessName está com a aprovação pendente! Clique para aprovar",
            config('app.frontend_url')."/businesses?$queryParams",
            SiteNotificationTypes::employee
        );
    }
}
