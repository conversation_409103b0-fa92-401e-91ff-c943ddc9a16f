<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class CashbackCompleteSiteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName, string $cashbackDescription)
    {
        parent::__construct(
            $icon, 
            $business_id, 
            "Cashback utilizado!",
            "Você utilizou um cashback de $cashbackDescription do negócio $businessName!",
            config('app.frontend_url')."/cashbacks/client",
            SiteNotificationTypes::client
        );
    }
}
