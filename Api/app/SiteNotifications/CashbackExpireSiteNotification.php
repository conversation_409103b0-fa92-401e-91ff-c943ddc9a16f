<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class CashbackExpireSiteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName, string $cashbackDescription)
    {
        parent::__construct(
            $icon, 
            $business_id, 
            "Cashback expirando!",
            "O seu cashback de $cashbackDescription do negócio $businessName está prestes a expirar!",
            config('app.frontend_url')."/cashbacks/client",
            SiteNotificationTypes::client
        );
    }
}
