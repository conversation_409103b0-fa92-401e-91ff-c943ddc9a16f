<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class AdminBusinessExpireNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, string $businessName)
    {
        $queryParams = http_build_query([
            'search' => $businessName,
            'filter_disabled' => 'all',
            'seller_id' => '',
            'status' => 'expired_registration',
        ]);

        parent::__construct(
            $icon, 
            null, 
            "Negócio expirado!",
            "O cadastro do negócio $businessName expirou! Clique para ver",
            config('app.frontend_url')."/businesses?$queryParams",
            SiteNotificationTypes::employee
        );
    }
}
