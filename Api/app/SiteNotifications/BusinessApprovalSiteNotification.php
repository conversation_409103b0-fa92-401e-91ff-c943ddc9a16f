<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class BusinessApprovalSiteNotification extends BaseSiteNotification
{
    public function __construct(string $businessName, int $businessId, bool $isApproved)
    {
        parent::__construct(
            null,
            null, 
            "Cadastro ".($isApproved ? 'aprovado!' : 'rejeitado!'),
            "O seu negócio $businessName foi ".($isApproved ? 'aprovado!' : 'rejeitado!'),
            config('app.frontend_url')."/businesses/$businessId",
            SiteNotificationTypes::employee
        );
    }
}
