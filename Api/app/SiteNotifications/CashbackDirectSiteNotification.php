<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class CashbackDirectSiteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName, string $cashbackDescription)
    {
        parent::__construct(
            $icon,
            $business_id,
            "Você Ganhou um Cashback! 🎉",
            "Parabéns! Você ganhou um cashback de $cashbackDescription na $businessName!",
            config('app.frontend_url') . '/cashbacks/client',
            SiteNotificationTypes::client
        );
    }
}