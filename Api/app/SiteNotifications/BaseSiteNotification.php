<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\Models\Employee;
use App\Models\SiteNotification;
use App\Models\User;
use Exception;

abstract class BaseSiteNotification
{
    protected ?string $icon;
    protected ?int $business_id;
    protected string $title;
    protected string $description;
    protected ?string $url_click;
    protected SiteNotificationTypes $type;

    public function __construct(?string $icon, ?int $business_id, string $title, string $description, ?string $url_click = null, SiteNotificationTypes $type)
    {
        $this->icon = $icon;
        $this->business_id = $business_id;
        $this->title = $title;
        $this->description = $description;
        $this->url_click = $url_click;
        $this->type = $type;
    }

    /**
     * Envia a notificação pra cada usuário do array
     */
    public function send(array $userIds): ?SiteNotification {
        $validUserIds = User::whereIn('id', $userIds)->pluck('id')->toArray();

        if (empty($validUserIds)) {
            throw new Exception('Nenhum usuário válido encontrado para notificação.');
        }

        return $this->createNotification($validUserIds);
    }
    
    /**
     * Envia a notificação pra todos admins (da aplicação)
    */

    public function sendToAdmins(): ?SiteNotification {
        $adminIds = Employee::whereAdmin(true)->pluck('user_id')->toArray();

        if (empty($adminIds)) {
            throw new Exception('Nenhum administrador encontrado.');
        }

        return $this->createNotification($adminIds);
    }

    private function createNotification(array $userIds): SiteNotification
    {
        $notification = SiteNotification::create([
            'business_id' => $this->business_id,
            'title' => $this->title,
            'description' => $this->description,
            'icon' => $this->icon,
            'url_click' => $this->url_click,
        ]); 

        $notification->users()->syncWithPivotValues($userIds, [
            'type' => $this->type->value,
            'created_at' => now(),
            'updated_at' => null,
            'deleted_at' => null,
        ]);

        return $notification;
    }
}
