<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class AdminEvaluationRespondedSiteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName)
    {
        parent::__construct(
            $icon, 
            $business_id, 
            "Avaliação Respondida!",
            "Uma avalição do negócio $businessName foi respondida. Clique para ver todas avaliações",
            config('app.url') . "/evaluations",
            SiteNotificationTypes::employee
        );
    }
}
