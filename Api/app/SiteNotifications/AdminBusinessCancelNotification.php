<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class AdminBusinessCancelNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, string $businessName)
    {
        $queryParams = http_build_query([
            'search' => $businessName,
            'filter_disabled' => 'all',
            'seller_id' => '',
            'status' => 'pending_cancellation',
        ]);

        parent::__construct(
            $icon, 
            null, 
            "Cancelamento Pendente",
            "O negócio $businessName está com o cancelamento pendente! Clique para confirmar",
            config('app.frontend_url')."/businesses?$queryParams",
            SiteNotificationTypes::employee
        );
    }
}
