<?php

namespace App\SiteNotifications;

use App\Enums\SiteNotificationTypes;
use App\SiteNotifications\BaseSiteNotification;

class SendEvaluationSiteNotification extends BaseSiteNotification
{
    public function __construct(?string $icon, int $business_id, string $businessName, string $url_click)
    {
        parent::__construct(
            $icon, 
            $business_id, 
            "Avaliação disponível!",
            "A sua avaliação de $businessName já está disponível. Clique para responder",
            $url_click,
            SiteNotificationTypes::client
        );
    }
}
