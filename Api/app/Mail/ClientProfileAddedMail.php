<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use App\Models\User;
use App\Models\Business;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ClientProfileAddedMail extends Mailable
{
    use Queueable, SerializesModels;

    protected $user;
    protected $business;
    private ?string $systemLogo;
    private ?string $mainColor;
    private string $loginUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, Business $business)
    {
        $this->user = $user;
        $this->business = $business;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
        $this->loginUrl = config('app.frontend_url') . '/login';
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Seu perfil foi associado a um novo negócio')
            ->view('emails.client-profile-added', [
                'user' => $this->user,
                'business' => $this->business,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
                'loginUrl' => $this->loginUrl,
            ]);
    }
}
