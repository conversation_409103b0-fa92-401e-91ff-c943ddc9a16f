<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CashbackValidationCodeMail extends Mailable
{
    use Queueable, SerializesModels;

    private string $validationCode;
    private ?string $businessName;
    private ?string $businessLogo;
    private ?string $systemLogo;
    private ?string $mainColor;
    private float $totalAmount;

    public function __construct(
        string $validationCode,
        float $totalAmount,
        ?string $businessName = null,
        ?string $businessLogo = null
    )
    {
        $this->validationCode = $validationCode;
        $this->totalAmount = $totalAmount;
        $this->businessName = $businessName ?? config('app.name');
        $this->businessLogo = $businessLogo;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    public function build(): Mailable
    {
        if($this->systemLogo == null){
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }

        return $this->subject('Código de Validação - Cashback')
            ->view('emails.cashback_validation_code')
            ->with([
                'validationCode' => $this->validationCode,
                'totalAmount' => $this->totalAmount,
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
            ]);
    }
}