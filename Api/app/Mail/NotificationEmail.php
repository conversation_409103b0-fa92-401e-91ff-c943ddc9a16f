<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class NotificationEmail extends Mailable
{
    use Queueable, SerializesModels;

    private ?string $businessLogo;
    private ?string $businessName;
    private string $notificationDescription;
    private string $notificationTitle;
    private ?string $systemLogo;
    private ?string $mainColor;
    private ?string $url_facebook;
    private ?string $url_google;
    private ?string $url_instagram;
    private ?string $url_linkedin;

    /**
     * Create a new message instance.
     */
    public function __construct(
        string $notificationTitle,
        string $notificationDescription,
        ?string $businessName,
        ?string $businessLogo,
        ?string $url_facebook = null,
        ?string $url_google = null,
        ?string $url_instagram = null,
        ?string $url_linkedin = null
    ) {
        $this->businessLogo = $businessLogo;
        $this->businessName = $businessName;
        $this->notificationTitle = $notificationTitle;
        $this->notificationDescription = $notificationDescription;
        $this->url_facebook = $url_facebook;
        $this->url_google = $url_google;
        $this->url_instagram = $url_instagram;
        $this->url_linkedin = $url_linkedin;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    /**
     * Build the message.
     */
    public function build(): Mailable
    {
        if ($this->systemLogo === null) {
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }

        return $this->subject(Str::limit($this->notificationTitle, 50, '...'))
            ->view('emails.notification')
            ->with([
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
                'businessLogo' => $this->businessLogo,
                'businessName' => $this->businessName,
                'notificationTitle' => $this->notificationTitle,
                'notificationDescription' => $this->notificationDescription,
                'url_facebook' => $this->url_facebook,
                'url_google' => $this->url_google,
                'url_instagram' => $this->url_instagram,
                'url_linkedin' => $this->url_linkedin,
            ]);
    }
}
