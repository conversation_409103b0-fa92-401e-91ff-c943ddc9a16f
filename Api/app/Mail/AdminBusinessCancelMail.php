<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use App\Models\Business;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AdminBusinessCancelMail extends Mailable
{
    use Queueable, SerializesModels;
    private string $businessStatus;
    private ?string $businessLogo;
    private string $businessName;
    private Carbon $lastMonth;
    private float $planValue;
    private ?string $systemLogo;
    private ?string $mainColor;

    /**
     * Create a new message instance.
     */
    public function __construct(?string $businessLogo, string $businessName, Carbon $lastMonth, float $planValue)
    {
        $this->businessLogo = $businessLogo;
        $this->businessName = $businessName;
        $this->lastMonth = $lastMonth;
        $this->planValue = $planValue;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    public function build(): Mailable
    {
        if($this->systemLogo == null){
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }
        
        return $this->subject("O cadastro do seu negócio foi cancelado!")
            ->view('emails.admin_business_cancel')
            ->with([
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'lastMonth' => $this->lastMonth,
                'planValue' => $this->planValue,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
            ]);
    }
}
