<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use App\Models\Business;
use Carbon\Carbon;
use Date;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AdminBusinessExpireMail extends Mailable
{
    use Queueable, SerializesModels;
    private Carbon $created_at;
    private Carbon $expired_at;
    private ?string $businessLogo;
    private string $businessName;
    private ?string $systemLogo;
    private ?string $mainColor;

    /**
     * Create a new message instance.
     */
    public function __construct(?string $businessLogo, string $businessName, Carbon $created_at, Carbon $expired_at)
    {
        $this->businessLogo = $businessLogo;
        $this->businessName = $businessName;
        $this->created_at = $created_at;
        $this->expired_at = $expired_at;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    public function build(): Mailable
    {
        if($this->systemLogo == null){
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }
        
        return $this->subject("O registro de seu negócio expirou!")
            ->view('emails.admin_business_expire')
            ->with([
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'created_at' => $this->created_at,
                'expired_at' => $this->expired_at,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
            ]);
    }
}
