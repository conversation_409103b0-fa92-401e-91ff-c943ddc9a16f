<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ClientCashbackDirectMail extends Mailable
{
    use Queueable, SerializesModels;

    public $cashbackDescription;
    public $businessName;
    public $logo;
    public $systemLogo;
    public $mainColor;

    /**
     * Create a new message instance.
     */
    public function __construct($cashbackDescription, $businessName, $logo = null)
    {
        $this->cashbackDescription = $cashbackDescription;
        $this->businessName = $businessName;
        $this->logo = $logo;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');

        return $this->subject('Você <PERSON> um Cashback! 🎉 - ' . $this->businessName)
                    ->view('emails.cashback_direct')
                    ->with([
                        'cashbackDescription' => $this->cashbackDescription,
                        'businessName' => $this->businessName,
                        'logo' => $this->logo,
                        'cashbackClientsLink' => config('app.frontend_url') . '/cashbacks/client',
                        'systemLogo' => $this->systemLogo,
                        'mainColor' => $this->mainColor,
                    ]);
    }
}
