<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AdminEvaluationRespondedMail extends Mailable
{
    use Queueable, SerializesModels;

    private string $businessName;
    private ?string $businessLogo;
    private ?string $systemLogo;
    private ?string $mainColor;

    /**
     * Create a new message instance.
     */
    public function __construct(
        string $businessName,
        ?string $businessLogo,
    ) {
        $this->businessName = $businessName;
        $this->businessLogo = $businessLogo;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    /**
     * Build the message.
     */
    public function build(): Mailable
    {
        if ($this->systemLogo === null) {
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }

        return $this->subject('Uma avaliação foi respondida!')
            ->view('emails.admin_evaluation_responded')
            ->with([
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
            ]);
    }
}
