<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class CLientWelcomeMail extends Mailable
{
    use Queueable, SerializesModels;

    private User $user;
    private string $password;
    private ?string $systemLogo;
    private ?string $mainColor;
    private string $loginUrl;

    public function __construct(User $user, string $password)
    {
        $this->user = $user;
        $this->password = $password;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
        $this->loginUrl = config('app.frontend_url') . '/login';
    }

    public function build(): Mailable
    {
        if ($this->systemLogo == null) {
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }

        return $this->subject('Bem-vindo ao ' . config('app.name'))
            ->view('emails.client_welcome')
            ->with([
                'user' => $this->user,
                'password' => $this->password,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
                'loginUrl' => $this->loginUrl,
            ]);
    }
}
