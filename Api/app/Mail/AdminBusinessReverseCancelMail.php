<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use App\Models\Business;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AdminBusinessReverseCancelMail extends Mailable
{
    use Queueable, SerializesModels;
    private ?string $businessLogo;
    private string $businessName;
    private ?string $systemLogo;
    private ?string $mainColor;

    /**
     * Create a new message instance.
     */
    public function __construct(?string $businessLogo, string $businessName)
    {
        $this->businessLogo = $businessLogo;
        $this->businessName = $businessName;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    public function build(): Mailable
    {
        if($this->systemLogo == null){
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }
        
        return $this->subject("O cancelamento do seu negócio foi revertido!")
            ->view('emails.admin_business_reverse_cancel')
            ->with([
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
            ]);
    }
}
