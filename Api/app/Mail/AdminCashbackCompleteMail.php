<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AdminCashbackCompleteMail extends Mailable
{
    use Queueable, SerializesModels;
    private string $cashbackDescription;
    private string $businessName;
    private string $clientName;
    private ?string $businessLogo;
    private ?string $systemLogo;
    private ?string $mainColor;

    /**
     * Create a new message instance.
     */
    public function __construct(string $cashbackDescription, string $clientName, string $businessName, ?string $businessLogo)
    {
        $this->cashbackDescription = $cashbackDescription;
        $this->clientName = $clientName;
        $this->businessName = $businessName;
        $this->businessLogo = $businessLogo;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    public function build(): Mailable
    {
        if($this->systemLogo == null){
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }

        return $this->subject("Um cashback do seu negócio foi utilizado!")
            ->view('emails.admin_cashback_complete')
            ->with([
                'cashbackDescription' => $this->cashbackDescription,
                'clientName' => $this->clientName,
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
            ]);
    }
}
