<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EvaluationLinkMail extends Mailable
{
    use Queueable, SerializesModels;

    private string $evaluationLink;
    private string $businessName;
    private ?string $businessLogo;
    private ?string $systemLogo;
    private ?string $mainColor;
    private ?string $url_facebook;
    private ?string $url_google;
    private ?string $url_instagram;
    private ?string $url_linkedin;

    /**
     * Create a new message instance.
     */
    public function __construct(
        string $evaluationLink,
        string $businessName,
        ?string $businessLogo,
        ?string $url_facebook = null,
        ?string $url_google = null,
        ?string $url_instagram = null,
        ?string $url_linkedin = null
    ) {
        $this->evaluationLink = $evaluationLink;
        $this->businessName = $businessName;
        $this->businessLogo = $businessLogo;
        $this->url_facebook = $url_facebook;
        $this->url_google = $url_google;
        $this->url_instagram = $url_instagram;
        $this->url_linkedin = $url_linkedin;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    /**
     * Build the message.
     */
    public function build(): Mailable
    {
        if ($this->systemLogo === null) {
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }

        return $this->subject('Sua Avaliação está Pronta')
            ->view('emails.evaluation_link')
            ->with([
                'evaluationLink' => $this->evaluationLink,
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
                'url_facebook' => $this->url_facebook,
                'url_google' => $this->url_google,
                'url_instagram' => $this->url_instagram,
                'url_linkedin' => $this->url_linkedin,
            ]);
    }
}
