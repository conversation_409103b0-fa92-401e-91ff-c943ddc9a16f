<?php

namespace App\Mail;

use App\Helpers\SystemHelper;
use App\Models\Business;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AdminBusinessApprovalMail extends Mailable
{
    use Queueable, SerializesModels;
    private string $businessStatus;
    private ?string $businessLogo;
    private string $businessName;
    private ?string $systemLogo;
    private ?string $mainColor;

    /**
     * Create a new message instance.
     */
    public function __construct(?string $businessLogo, string $businessName, string $businessStatus)
    {
        $this->businessLogo = $businessLogo;
        $this->businessName = $businessName;
        $this->businessStatus = $businessStatus;
        $this->systemLogo = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_LOGO', 7);
        $this->mainColor = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'MAIN_COLOR');
    }

    public function build(): Mailable
    {
        if($this->systemLogo == null){
            $this->systemLogo = config('app.frontend_url') . '/icon/logo_visao_negocio.svg';
        }
        
        $isApproved = $this->businessStatus === Business::STATUS_APPROVED_REGISTRATION;
        return $this->subject("O registro do seu negócio foi ".($isApproved ? 'aprovado!' : 'rejeitado!'))
            ->view('emails.admin_business_approval')
            ->with([
                'businessName' => $this->businessName,
                'businessLogo' => $this->businessLogo,
                'isApproved' => $isApproved,
                'systemLogo' => $this->systemLogo,
                'mainColor' => $this->mainColor,
            ]);
    }
}
