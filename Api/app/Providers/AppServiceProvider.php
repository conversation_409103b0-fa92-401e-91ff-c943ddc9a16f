<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\ClientImportItem;
use App\Observers\ClientImportItemObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        ClientImportItem::observe(ClientImportItemObserver::class);
    }
}
