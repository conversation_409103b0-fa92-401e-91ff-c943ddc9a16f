<?php

namespace App\Rules;

use App\Models\Client;
use App\Models\Employee;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueClientUserEmail implements ValidationRule
{
    protected ?int $ignoreClientId;
    protected ?string $ignoreCpf;

    /**
     * Create a new rule instance.
     *
     * @param int|null $ignoreClientId Opcional: ID do cliente a ser ignorado na validação (para updates).
     * @param string|null $ignoreCpf Opcional: CPF do cliente a ser ignorado na validação (para updates).
     */
    public function __construct(?int $ignoreClientId = null, ?string $ignoreCpf = null)
    {
        $this->ignoreClientId = $ignoreClientId;
        $this->ignoreCpf = $ignoreCpf;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $email = $value;

        if (empty($email)) {
            return;
        }

        $query = User::where(function ($query) use ($email) {
            $query->where('email', $email);
        })->whereNotNull('cpf'); // Apenas usuários com CPF preenchido (cadastro completo)

        // Ignora o usuário atual na validação (para updates)
        if ($this->ignoreClientId !== null) {
            $client = Client::find($this->ignoreClientId);
            if ($client) {
                $query->where('id', '!=', $client->user_id);
            }
        }
        if ($this->ignoreCpf !== null) {
            $query->where('cpf', '!=', $this->ignoreCpf);
        }

        $existingUser = $query->first();

        if ($existingUser) {
            $fail('Este email já está associado a outro usuário.');
        }
    }
}
