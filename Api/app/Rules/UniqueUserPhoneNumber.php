<?php

namespace App\Rules;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueUserPhoneNumber implements ValidationRule
{
    protected ?int $ignoreUserId;
    protected ?string $ignoreCpf;

    /**
     * Create a new rule instance.
     *
     * @param int|null $ignoreUserId Opcional: ID do usuário a ser ignorado na validação (para updates).
     * @param string|null $ignoreCpf Opcional: CPF do usuário a ser ignorado na validação (para updates).
     */
    public function __construct(?int $ignoreUserId = null, ?string $ignoreCpf = null)
    {
        $this->ignoreUserId = $ignoreUserId;
        $this->ignoreCpf = $ignoreCpf;
    }

    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $phoneNumber = preg_replace('/\D/', '', $value);

        if (empty($phoneNumber)) {
            return;
        }

        $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9

        $codes = substr($phoneNumber, 0, 2);
        $numberDigits = substr($phoneNumber, 2);
        if (strlen($numberDigits) === 9) {
            $phoneNumberV2 = $codes . substr($numberDigits, 1);
        } else if (strlen($numberDigits) === 8) {
            $phoneNumberV2 = $codes . '9' . $numberDigits;
        }

        $query = User::where(function ($query) use ($phoneNumber, $phoneNumberV2) {
            $query->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
        })->whereNotNull('cpf'); // Apenas usuários com CPF preenchido (cadastro completo)

        // Ignora o usuário atual na validação (para updates)
        if ($this->ignoreUserId !== null) {
            $query->where('id', '!=', $this->ignoreUserId);
        }
        if ($this->ignoreCpf !== null) {
            $query->where('cpf', '!=', $this->ignoreCpf);
        }

        $existingUser = $query->first();

        if ($existingUser) {
            $fail('Este telefone já está associado a outro usuário.');
        }
    }
}
