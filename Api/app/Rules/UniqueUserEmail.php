<?php

namespace App\Rules;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueUserEmail implements ValidationRule
{
    protected ?int $ignoreUserId;
    protected ?string $ignoreCpf;

    /**
     * Create a new rule instance.
     *
     * @param int|null $ignoreUserId Opcional: ID do usuário a ser ignorado na validação (para updates).
     * @param string|null $ignoreCpf Opcional: CPF do usuário a ser ignorado na validação (para updates).
     */
    public function __construct(?int $ignoreUserId = null, ?string $ignoreCpf = null)
    {
        $this->ignoreUserId = $ignoreUserId;
        $this->ignoreCpf = $ignoreCpf;
    }

    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $email = $value;

        if (empty($email)) {
            return;
        }

        $query = User::where(function ($query) use ($email) {
            $query->where('email', $email);
        })->whereNotNull('cpf'); // Apenas usuários com CPF preenchido (cadastro completo)

        // Ignora o usuário atual na validação (para updates)
        if ($this->ignoreUserId !== null) {
            $query->where('id', '!=', $this->ignoreUserId);
        }
        if ($this->ignoreCpf !== null) {
            $query->where('cpf', '!=', $this->ignoreCpf);
        }

        $existingUser = $query->first();

        if ($existingUser) {
            $fail('Este email já está associado a outro usuário.');
        }
    }
}
