<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class UniqueField implements Rule
{
    private string $user_type;
    private ?int $ignore_id;
    private bool $replace_value;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(string $user_type, bool $replace_value = false, ?int $ignore_id = null)
    {
        $this->user_type = $user_type;
        $this->ignore_id = $ignore_id;
        $this->replace_value = $replace_value;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $value = $this->replace_value ? str_replace(['.', '-'], '', $value) : $value;
        $query = \App\Models\User::where('type', $this->user_type)->where($attribute, $value);

        if(isset($this->ignore_id)) {
            $model = $this->getClass($this->user_type);
            $id = $model::find($this->ignore_id)->user->id;
            $query = $query->where('id', '<>', $id);
        }

        return !$query->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.unique');
    }

    private function getClass(string $type)
    {
        switch($type) {
            case 'employee':
                return \App\Models\Employee::class;
            case 'requester':
                return \App\Models\Requester::class;
            default:
                return null;
        }
    }
}
