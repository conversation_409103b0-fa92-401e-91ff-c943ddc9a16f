<?php

namespace App\Rules;

use App\Models\Employee;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueEmployeeUserEmail implements ValidationRule
{
    protected ?int $ignoreEmployeeId;
    protected ?string $ignoreCpf;

    /**
     * Create a new rule instance.
     *
     * @param int|null $ignoreEmployeeId Opcional: ID do funcionário a ser ignorado na validação (para updates).
     * @param string|null $ignoreCpf Opcional: CPF do funcionário a ser ignorado na validação (para updates).
     */
    public function __construct(?int $ignoreEmployeeId = null, ?string $ignoreCpf = null)
    {
        $this->ignoreEmployeeId = $ignoreEmployeeId;
        $this->ignoreCpf = $ignoreCpf;
    }

    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $email = $value;

        if (empty($email)) {
            return;
        }

        $query = User::where(function ($query) use ($email) {
            $query->where('email', $email);
        })->whereNotNull('cpf'); // Apenas usuários com CPF preenchido (cadastro completo)

        if ($this->ignoreEmployeeId !== null) {
            $employee = Employee::find($this->ignoreEmployeeId);
            if ($employee) {
                $query->where('id', '!=', $employee->user_id);
            }
        }
        if ($this->ignoreCpf !== null) {
            $query->where('cpf', '!=', $this->ignoreCpf);
        }

        $existingUser = $query->first();

        if ($existingUser) {
            $fail('Este email já está associado a outro usuário.');
        }
    }
}
