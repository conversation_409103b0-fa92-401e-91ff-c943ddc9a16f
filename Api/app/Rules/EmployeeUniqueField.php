<?php

namespace App\Rules;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class EmployeeUniqueField implements ValidationRule
{
    protected $ignore_id;
    protected $ignore_user_id;

    /**
     * Create a new rule instance.
     *
     * @param int|null $ignore_id
     * @param int|null $ignore_user_id
     */
    public function __construct(int $ignore_id = null, int $ignore_user_id = null)
    {
        $this->ignore_id = $ignore_id;
        $this->ignore_user_id = $ignore_user_id;
    }

    /**
     * Run the validation rule.
     *
     * @param string $attribute
     * @param mixed $value
     * @param Closure $fail
     */
    public function validate(string $attribute, $value, Closure $fail): void
    {
        $query = User::whereIn('type', ['employee', 'admin'])
            ->where($attribute, $value);

        if (isset($this->ignore_id)) {
            $id = User::find($this->ignore_id)->user->id;
            $query = $query->where('id', '<>', $id);
        }

        if (isset($this->ignore_user_id)) {
            $query = $query->where('id', '<>', $this->ignore_user_id);
        }

        if ($query->exists()) {
            $fail("O $attribute já está em uso.");
        }
    }

}
