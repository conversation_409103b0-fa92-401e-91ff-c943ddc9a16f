<?php

namespace App\Rules;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueRule implements ValidationRule
{
    private array $userTypes;
    private ?int $ignoredId;

    public function __construct(array $userTypes = [], ?int $ignoredId = null)
    {
        $this->userTypes = $userTypes;
        $this->ignoredId = $ignoredId;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $query = User::where($attribute, $value);

        $existUserTypes = count($this->userTypes) > 0;

        if($existUserTypes) {
            $query = $query->whereIn('type', $this->userTypes);
        }
        if(isset($this->ignoredId)) {
            $query = $query->where('id', '<>', $this->ignoredId);
        }

        $exists = $query->exists();

        if($exists) {
            $fail('validation.unique')->translate();
        }
    }
}
