<?php

namespace App\Jobs;

use App\Enums\EvaluationSendingType;
use App\Helpers\ApiWhatsapp;
use App\Mail\EvaluationExpireMail;
use App\Models\Evaluation;
use App\SiteNotifications\EvaluationExpireSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendEvaluationLinkExpireNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         * Evaluations que faltam 24hrs para expirar, enviar uma notificação
         */

         $now = Carbon::now()->addHours(24);

         $evaluations = Evaluation::where('shipping_status', 'SENT')
            ->where('expiration_date', '<=', $now)
            ->whereNull('notified_expiration_at')
            ->get();

        foreach($evaluations as $evaluation) {
            $this->processEvaluationExpireNotification($evaluation);
        }
    }

    public function processEvaluationExpireNotification(Evaluation $evaluation) {
        $this->sendSiteNotification($evaluation);
        switch($evaluation->sending_type) {
            case EvaluationSendingType::email->value:
                $this->sendEmailNotification($evaluation);
            break;
            case EvaluationSendingType::whatsapp->value:
                $this->sendWhatsappNotification($evaluation);
            break;
        }
    }

    public function sendSiteNotification(Evaluation $evaluation): void {
        $client = $evaluation->client;
        $business = $evaluation->business;
        $evaluationLink = config('app.frontend_url') . '/evaluations/respond/' . $evaluation['token'];
        try {
            (new EvaluationExpireSiteNotification(
                $business->logo,
                $business->id,
                $business->name,
                $evaluationLink
            ))->send([$client->user_id]);
        } catch (Exception $e) {
            Log::info('Falha ao enviar notificação (SITE) de expiração da avaliação {$evaluation->id}. {$e->getMessage()}');
        }
    }

    public function sendEmailNotification(Evaluation $evaluation) {
        $clientEmail = optional($evaluation->client?->user)->email ?? $evaluation->email;
        $business = $evaluation->business;
        $evaluationLink = config('app.frontend_url') . '/evaluations/respond/' . $evaluation['token'];
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        try {
            if($clientEmail != null && $clientEmail != "") {
                Mail::to($clientEmail)->send(
                    new EvaluationExpireMail(
                        $evaluationLink,
                        $business->name,
                        $logo,
                        $business->url_facebook,
                        $business->url_google,
                        $business->url_instagram,
                        $business->url_linkedin
                    ));
            }
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de expiração da Avaliação {$evaluation->id} via EMAIL. {$e->getMessage()}");
        }
        $evaluation->notified_expiration_at = Carbon::now();
        $evaluation->save();
    }

    public function sendWhatsappNotification(Evaluation $evaluation){
        $this->sendEmailNotification($evaluation);
        try {
            $evaluationLink = config('app.frontend_url') . '/evaluations/respond/' . $evaluation['token'];
            $clientName = optional($evaluation->client?->user)->name;
            $businessName = $evaluation->business->name;
            $userPhone = $evaluation->number_phone;
            $greetings = isset($clientName) ? "Olá, $clientName!" : "Olá!";

            $title = "Eii.. Sua avaliação tá quase expirando… e a gente já tá aqui chorando! 😭";
            $description = "Ainda dá tempo de contar pra gente o que achou da sua experiência!\n" .
                "É rapidinho e faz toda a diferença pra gente continuar melhorando.\n\n".
                "💬 Avalie aqui:\n" .
                "$evaluationLink". "\n\n" .
                "É rapidinho, prometo 🤞🏻";

            ApiWhatsapp::sendMessageWithInstance(
                $evaluation->business->parameters->whatsapp_instance_key,
                $userPhone, $title, $description, $businessName);

            if (!$evaluation->notified_expiration_at) {
                $evaluation->notified_expiration_at = Carbon::now();
                $evaluation->save();
            }
        } catch (Exception $ex) {
            Log::info("Falha ao enviar notificação de expiração da Avaliação {$evaluation->id} via ZAP. {$ex->getMessage()}");
        }
    }
}
