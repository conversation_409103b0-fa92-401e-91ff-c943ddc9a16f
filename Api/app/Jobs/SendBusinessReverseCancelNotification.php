<?php

namespace App\Jobs;

use App\Mail\AdminBusinessApprovalMail;
use App\Mail\AdminBusinessCancelMail;
use App\Mail\AdminBusinessReverseCancelMail;
use App\Models\Business;
use App\SiteNotifications\AdminBusinessReverseCancelNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendBusinessReverseCancelNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         *  Negócios que estão com cancelamento pendente, mas não foram notificados ainda
         */
         $businesses = Business::withTrashed()
                    ->where('status', Business::STATUS_REVERSED_CANCELLATION)
                    ->whereNull('reverse_cancel_notified_at')
                    ->get();

        foreach($businesses as $business) {
            $this->sendSiteNotification($business);
            $this->sendEmailNotification($business);
        }
    }

        public function sendSiteNotification(Business $business): void {
        $admin = $business->admin();
        try {
            (new AdminBusinessReverseCancelNotification(null, $business->name, $business->id))
            ->send([$admin->user_id]);
        } catch (Exception $e) {
            Log::info('Falha ao enviar notificação (SITE) de reversão de cancelamento do negócio {$business->id}. {$e->getMessage()}');
        }
    }

    public function sendEmailNotification(Business $business) {
        $admin = $business->admin();
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        try {
            if ($admin && $admin->user) {
                Mail::to($admin->user->email)->send(new AdminBusinessReverseCancelMail($logo, $business->name));
            } else Log::info("O administrador desse negócio [{$business->id}, {$business->name}] não está ativo.");
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de cancelamento do negócio {$business->id} via EMAIL. {$e->getMessage()}");
        }
        $business->reverse_cancel_notified_at = Carbon::now();
        $business->save();
    }
}
