<?php

namespace App\Jobs;

use App\Models\ClientImportBatch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ProcessIncompleteClientImportBatch implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $batch;
    protected $config;

    /**
     * Especifica a conexão da fila a ser usada
     */
    public function __construct(ClientImportBatch $batch)
    {
        $this->batch = $batch;
        $this->config = $batch->config;
        $this->onQueue('imports'); // Define a fila específica
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [new WithoutOverlapping($this->batch->id)];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Iniciando processamento do lote', [
            'batch_id' => $this->batch->id,
            'file_name' => $this->batch->file_name
        ]);

        // Atualiza o status do lote para processing
        $this->batch->update(['status' => ClientImportBatch::STATUS_PROCESSING]);

        try {
            // Tenta ler o arquivo do S3
            try {
                Log::info('Tentando ler arquivo do S3', [
                    'batch_id' => $this->batch->id,
                    'path' => $this->batch->file_path
                ]);

                if (!Storage::disk('s3')->exists($this->batch->file_path)) {
                    throw new \Exception("Arquivo não encontrado no S3: {$this->batch->file_path}");
                }

                $content = Storage::disk('s3')->get($this->batch->file_path);
            } catch (\Exception $e) {
                Log::error('Erro ao ler arquivo do S3', [
                    'batch_id' => $this->batch->id,
                    'path' => $this->batch->file_path,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }

            // Determina o tipo de arquivo (csv ou xlsx)
            $extension = pathinfo($this->batch->file_name, PATHINFO_EXTENSION);

            Log::info('Arquivo carregado com sucesso', [
                'batch_id' => $this->batch->id,
                'extension' => $extension,
                'size' => strlen($content)
            ]);

            // Processa o arquivo baseado no tipo
            if ($extension === 'csv') {
                $this->processCSV($content);
            } else {
                $this->processExcel($content);
            }

            // Não atualizamos mais o status aqui pois será feito pelo observer
            // quando todos os items estiverem processados

        } catch (\Exception $e) {
            // Em caso de erro, marca o lote como falho
            $this->batch->update([
                'status' => ClientImportBatch::STATUS_FAILED,
                'processed_at' => now()
            ]);

            throw $e;
        }
    }

    protected function processCSV(string $content): void
    {
        $rows = explode("\n", $content);

        // Remove BOM if present
        if (isset($rows[0])) {
            $rows[0] = str_replace("", '', $rows[0]);
        }

        // Remove espaços no início e fim das strings de cada linha
        $rows = array_map(function($row) {
            return array_map(function ($value) {
                return is_string($value) ? trim($value) : $value;
            }, $row);
        }, $rows);

        // Get separator and has_headers from batch config
        $separator = $this->config['separator'] ?? ';';
        $hasHeaders = $this->config['has_headers'] ?? true;

        Log::info('Processando CSV', [
            'batch_id' => $this->batch->id,
            'separator' => $separator,
            'has_headers' => $hasHeaders
        ]);

        // Get header row if has_headers is true
        $header = [];
        if ($hasHeaders) {
            $header = str_getcsv(array_shift($rows), $separator);
        }

        // Update total records count - subtract 1 if has headers
        $totalRecords = count($rows);
        if ($hasHeaders) {
            $totalRecords--;
        }

        Log::info('Total de registros identificados', [
            'batch_id' => $this->batch->id,
            'total_records' => $totalRecords
        ]);

        $this->batch->update(['total_records' => $totalRecords]);

        foreach ($rows as $index => $row) {
            if (empty(trim($row))) continue;

            $values = str_getcsv($row, $separator);

            // Valida se o número de valores corresponde ao número de colunas
            if (!empty($header) && count($values) !== count($header)) {
                Log::error('Número incorreto de colunas na linha', [
                    'batch_id' => $this->batch->id,
                    'row_number' => $index + 1,
                    'expected' => count($header),
                    'received' => count($values),
                    'values' => $values
                ]);
                continue;
            }

            // If no headers, create numeric keys
            if (empty($header)) {
                $data = array_combine(
                    range(0, count($values) - 1),
                    $values
                );
            } else {
                $data = array_combine($header, $values);
            }

            // Map the data according to batch config
            $mappedData = $this->mapData($data);

            if (isset($mappedData['phone'])) {
                $mappedData['phone'] = str_replace('+55', '', $mappedData['phone']);
            }

            Log::debug('Criando item de importação', [
                'batch_id' => $this->batch->id,
                'row_number' => $index + 1,
                'mapped_data' => $mappedData
            ]);

            // Create import item
            $item = $this->batch->items()->create([
                'row_number' => $index + 1,
                'raw_data' => $mappedData,
                'status' => ClientImportBatch::STATUS_PENDING
            ]);

            ProcessIncompleteClientImportItem::dispatch($item);
        }
    }

    protected function processExcel(string $content): void
    {
        // Salva o conteúdo temporariamente para o PHPSpreadsheet ler
        $tempFile = tempnam(sys_get_temp_dir(), 'import');
        file_put_contents($tempFile, $content);

        $spreadsheet = IOFactory::load($tempFile);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        // Remove o arquivo temporário
        unlink($tempFile);

        // Remove espaços no início e fim das strings de cada linha
        $rows = array_map(function($row) {
            return array_map(function ($value) {
                return is_string($value) ? trim($value) : $value;
            }, $row);
        }, $rows);

        // Get header row if has_headers is true
        $header = [];
        if ($this->config['has_headers'] ?? true) {
            $header = array_shift($rows);
        }

        // Filtra linhas vazias e conta o total de registros
        $nonEmptyRows = array_filter($rows, function($row) {
            return !empty(array_filter($row));
        });
        $totalRecords = count($nonEmptyRows);

        Log::info('Total de registros identificados no Excel', [
            'batch_id' => $this->batch->id,
            'total_records' => $totalRecords
        ]);

        $this->batch->update(['total_records' => $totalRecords]);

        foreach ($rows as $index => $row) {
            if (empty(array_filter($row))) continue;

            // If no headers, create numeric keys
            if (empty($header)) {
                $data = array_combine(
                    range(0, count($row) - 1),
                    $row
                );
            } else {
                $data = array_combine($header, $row);
            }

            // Map the data according to batch config
            $mappedData = $this->mapData($data);

            if (isset($mappedData['phone'])) {
                $cleanedPhone = preg_replace('/[^0-9+]/', '', $mappedData['phone']);

                // verifica (87 0 0000-0000) 11 dígitos --> (87 0000-0000) 10 dígitos
                if (strlen($cleanedPhone) !== 11 && strlen($cleanedPhone) > 10) {
                    // remove o +55 ou 55 no começo 
                    $mappedData['phone'] = preg_replace('/^(\+55|55)/', '', $mappedData['phone']);
                }
            }

            Log::debug('Criando item de importação do Excel', [
                'batch_id' => $this->batch->id,
                'row_number' => $index + 1,
                'mapped_data' => $mappedData
            ]);

            $item = $this->batch->items()->create([
                'row_number' => $index + 1,
                'raw_data' => $mappedData,
                'status' => ClientImportBatch::STATUS_PENDING
            ]);

            ProcessIncompleteClientImportItem::dispatch($item);
        }
    }

    protected function mapData(array $data): array
    {
        $mapping = $this->config['mapping'] ?? [];
        $hasHeaders = $this->config['has_headers'] ?? true;
        $mappedData = [];

        Log::debug('Iniciando mapeamento de dados', [
            'has_headers' => $hasHeaders,
            'mapping' => $mapping,
            'data_keys' => array_keys($data)
        ]);

        foreach ($mapping as $targetField => $sourceField) {
            $value = null;
            
            if ($hasHeaders) {
                // Quando tem headers, usa o nome da coluna
                if (isset($data[$sourceField])) {
                    $value = $data[$sourceField];
                }
            } else {
                // Quando não tem headers, aplica o mapeamento numérico corretamente
                $columnIndex = (int)$sourceField;
                if (isset($data[$columnIndex])) {
                    $value = $data[$columnIndex];
                }
            }

            $mappedData[$targetField] = $value;
            
            Log::debug('Campo mapeado', [
                'target' => $targetField,
                'source' => $sourceField,
                'value' => $value,
                'has_headers' => $hasHeaders,
                'data_keys' => array_keys($data)
            ]);
        }

        return $mappedData;
    }
}
