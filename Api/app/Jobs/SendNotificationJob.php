<?php

namespace App\Jobs;

use App\Helpers\ApiNotificationPush;
use App\Helpers\ApiWhatsapp;
use App\Helpers\NotificationHelper;
use App\Helpers\SystemHelper;
use App\Mail\NotificationEmail;
use App\Models\Notification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SendNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Encontrar as notificações que não foram
        $notifications = Notification::whereNull('finished_at')->get();

        foreach ($notifications as $notification) {
            try {
                $notification->started_at = Carbon::now();
                $notificationClients = $notification->notificationClients()
                                        ->where('shipping_status', 'waiting')
                                        ->get();
                $number_failed = $notification->sending_number_success;
                $number_success = $notification->sending_number_failed;
                $business = $notification->business;
                $businessName = $business->name ?? null;

                if ($business && $business->logo != null) {
                    $logo = Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7));
                } else {
                    $logo = null;
                }

                if ($notification->sending_type === 'email') {
                    // Enviar cada notificação para os clientes
                    foreach ($notificationClients as $notificationClient) {
                        $client = $notificationClient->client;
                        $user = $client->user;
                        try {
                            // Tentativa de enviar email
                            if ($business) {
                                Mail::to($user->email)->send(new NotificationEmail(
                                    $notification->title,
                                    NotificationHelper::applyMasksValues($notification->description, [
                                        'clientName' => $user->name,
                                        'clientCpf' => $user->cpf,
                                    ]),
                                    $businessName,
                                    $logo,
                                    $business->url_facebook,
                                    $business->url_google,
                                    $business->url_instagram,
                                    $business->url_linkedin
                                ));
                            } else Mail::to($user->email)->send(new NotificationEmail(
                                $notification->title,
                                NotificationHelper::applyMasksValues($notification->description, [
                                    'clientName' => $user->name,
                                    'clientCpf' => $user->cpf,
                                ]),
                                null,
                                $logo,
                            ));
                            // atualizar status
                            $notificationClient->shipping_status = 'sent';
                            $notificationClient->shipping_date = Carbon::now();
                            $notificationClient->save();
                            $number_success++;
                        } catch (Exception $e) {
                            Log::error("Erro ao enviar a Notificação para o Cliente ID {$client->id}: {$e->getMessage()}");
                            // Atualizar status
                            $notificationClient->shipping_status = 'failed';
                            $notificationClient->save(); // Salvar as alterações
                            $number_failed++;
                            continue;
                        }
                    }
                } elseif ($notification->sending_type === 'notification_push') {
                    // Lógica enviar notificação (APP)
                    $devicesIds = [];
                    foreach($notificationClients as $notificationClient) {
                        $client = $notificationClient->client;
                        $devicesIds = $client->devices->pluck('id_device');
                    }
                    try {
                        $icon = SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'TAB_LOGO') ?? null;
                        $notificationPushPayload = ApiNotificationPush::createNotifications(
                            $devicesIds,
                            $notification->title,
                            $notification->description,
                            $notification->url_click,
                            $icon,
                            $icon,
                        );

                        $json = json_encode($notificationPushPayload);

                        Log::info("Info {$json}");

                        if (!isset($notificationPushPayload['errors'])) {
                            $notification->notification_push_id = $notificationPushPayload['id'];
                            $number_success += count($devicesIds);
                        } else {
                            $number_failed += count($devicesIds);
                        }

                        foreach($notificationClients as $notificationClient) {
                            $notificationClient->shipping_status = 'sent';
                            $notificationClient->shipping_date = Carbon::now();
                            $notificationClient->save();
                        }
                    } catch(Exception $e) {
                        Log::error("Erro ao criar notification_push notificationId {$notification->id}: {$e->getMessage()}");
                        continue;
                    }
                } elseif ($notification->sending_type === 'whatsapp') {
                    // Lógica enviar notificação (ZAP)
                    foreach ($notificationClients as $notificationClient) {
                        $client = $notificationClient->client;
                        $user = $client->user;
                        try {
                            if ($business) {
                                ApiWhatsapp::sendMessageWithInstance(
                                    $business->parameters->whatsapp_instance_key,
                                    $user->phone_number_1,
                                    $notification->title,
                                    NotificationHelper::applyMasksValues($notification->description, [
                                        'clientName' => $user->name,
                                        'clientCpf' => $user->cpf,
                                    ]),
                                    $business->name
                                );
                            } else {
                                ApiWhatsapp::sendMessage(
                                    $user->phone_number_1,
                                    $notification->title,
                                    NotificationHelper::applyMasksValues($notification->description, [
                                        'clientName' => $user->name,
                                        'clientCpf' => $user->cpf,
                                    ]),
                                );
                            }
                            // atualizar status
                            $notificationClient->shipping_status = 'sent';
                            $notificationClient->shipping_date = Carbon::now();
                            $notificationClient->save();
                            $number_success++;
                        } catch (Exception $e) {
                            Log::error("Erro ao enviar a Notificação para o Cliente ID {$client->id} VIA ZAP: {$e->getMessage()}");
                            // Atualizar status
                            $notificationClient->shipping_status = 'failed';
                            $notificationClient->save(); // Salvar as alterações
                            $number_failed++;
                            continue;
                        }
                    }
                }

                $notification->sending_number_failed = $number_failed;
                $notification->sending_number_success = $number_success;
                $notification->finished_at = Carbon::now();
                $notification->save();
            } catch(Exception $e) {
                Log::error("Erro ao validar a Notificação de ID {$notification->id}: {$e->getMessage()}");
                continue;
            }
        }
    }
}
