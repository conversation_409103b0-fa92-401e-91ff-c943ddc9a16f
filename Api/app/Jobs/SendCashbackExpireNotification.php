<?php

namespace App\Jobs;

use App\Enums\CashbackStatus;
use App\Enums\EvaluationSendingType;
use App\Helpers\ApiWhatsapp;
use App\Mail\CashbackExpireNotificationMail;
use App\Models\Cashback;
use App\SiteNotifications\CashbackExpireSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendCashbackExpireNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         * Cashbacks que faltam 24hrs para expirar, enviar uma notificação
         */

         $now = Carbon::now()->addHours(24);

         $cashbacks = Cashback::where('status', CashbackStatus::Pending->value)
            ->where('expiration_date', '<=', $now)
            ->whereNull('notification_expiration_date')
            ->get();

        foreach($cashbacks as $cashback) {
            $this->processCashbackNotification($cashback);
        }
    }

    public function processCashbackNotification(Cashback $cashback) {
        $this->sendSiteNotification($cashback);
        switch($cashback->sending_type) {
            case EvaluationSendingType::email->value:
                $this->sendEmailNotification($cashback);
            break;
            case EvaluationSendingType::whatsapp->value:
                $this->sendWhatsappNotification($cashback);
            break;
        }
    }

    public function sendSiteNotification(Cashback $cashback): void {
        $client = $cashback->client;
        $business = $cashback->business;
        $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
        $cashbackDescription = $canSetCashbackValue ? 
                                "R$ " . number_format($cashback->amount, 2, ',', '') :
                                number_format($cashback->percentage, 2, ',', ''). '%';
        try {
            (new CashbackExpireSiteNotification($business->logo, $business->id, $business->name, $cashbackDescription))
            ->send([$client->user_id]);
        } catch (Exception $e) {
            Log::info('Falha ao enviar notificação (SITE) de expiração do cashback {$cashback->id}. {$e->getMessage()}');
        }
    }

    public function sendEmailNotification(Cashback $cashback) {
        $client = $cashback->client;
        $business = $cashback->business;
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
        $cashbackDescription = $canSetCashbackValue ? 
                                "R$ " . number_format($cashback->amount, 2, ',', '') :
                                number_format($cashback->percentage, 2, ',', ''). '%';
        try {
            if($client->user->email != null && $client->user->email != "") {
                Mail::to($client->user->email)->send(new CashbackExpireNotificationMail(
                    $cashback->percentage,
                    $business->name,
                    $logo,
                    $business->url_facebook,
                    $business->url_google,
                    $business->url_instagram,
                    $business->url_linkedin
                ));
            }
        } catch (Exception $e) {
            Log::info('Falha ao enviar notificação de expiração do cashback {$cashback->id} via EMAIL. {$e->getMessage()}');
        }
        $cashback->notification_expiration_date = Carbon::now();
        $cashback->save();
    }

    public function sendWhatsappNotification(Cashback $cashback){
        $this->sendEmailNotification($cashback);
        try {
            $business = $cashback->business;
            $client = $cashback->client;
            $user = $client->user;
            $userPhone = $user->phone_number_1;
            $cashbackLink = config('app.frontend_url') . '/cashbacks/client';
            $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
            $cashbackDescription = $canSetCashbackValue ?
                                    "R$ " . number_format($cashback->amount, 2, ',', '') :
                                    number_format($cashback->percentage, 2, ',', ''). '%';

            $title = "Seu Cashback Está Expirando! ⏱️";
            $description = "Olá, " . explode(" ", $user->name)[0] . "!\n\n" .
                "Faltam menos de *24 horas* para seu cashback de $cashbackDescription expirar!\n\n".
                "Para ver seus cashbacks, acesse o link abaixo:\n" .
                "$cashbackLink\n\n" .
                "Atenciosamente, *$business->name*.";

            ApiWhatsapp::sendMessageWithInstance(
                $business->parameters->whatsapp_instance_key,
                $userPhone, $title, $description, $business->name);
            if (!$cashback->notification_created_date) {
                $cashback->notification_created_date = Carbon::now();
                $cashback->save();
            }
        } catch (Exception $ex) {
            Log::info("Falha ao enviar notificação do cashback {$cashback->id} via ZAP. {$ex->getMessage()}");
        }
    }

}
