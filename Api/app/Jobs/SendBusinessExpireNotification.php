<?php

namespace App\Jobs;

use App\Helpers\SystemHelper;
use App\Mail\AdminBusinessApprovalMail;
use App\Mail\AdminBusinessExpireMail;
use App\Models\Business;
use App\SiteNotifications\AdminBusinessExpireNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendBusinessExpireNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         * Negócios que expiraram
         */
        $now = Carbon::now()->subDays(SystemHelper::findInArrayByCodeName(SystemHelper::get(), 'BUSINESS_EXPIRATION_DAYS') ?? 30);

         $businesses = Business::where('status', Business::STATUS_PENDING_APPROVAL)
                        ->where('created_at', '<=', $now)
                        ->get();

        foreach($businesses as $business) {
            $this->sendSiteNotification($business);
            $this->sendEmailNotification($business);
        }
    }

    public function sendSiteNotification(Business $business): void {
        try {
            (new AdminBusinessExpireNotification($business->logo, $business->name))
            ->sendToAdmins();
        } catch (Exception $e) {
            Log::info('Falha ao enviar notificação (SITE) de expiração do cadastro do negócio {$business->id}. {$e->getMessage()}');
        }
    }

    public function sendEmailNotification(Business $business) {
        $admin = $business->admin();
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        try {
            if ($admin && $admin->user) {
                Mail::to($admin->user->email)->send(
                    new AdminBusinessExpireMail(
                    $logo,
                    $business->name,
                    Carbon::parse($business->created_at),
                    Carbon::now()
                ));
            } else Log::info("O administrador desse negócio [{$business->id}, {$business->name}] não está ativo.");
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de expiração do negócio {$business->id} via EMAIL. {$e->getMessage()}");
        }
        $business->status = Business::STATUS_EXPIRED_REGISTRATION;
        $business->save();
        $business->delete();
    }
}
