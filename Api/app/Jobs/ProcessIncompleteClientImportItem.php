<?php

namespace App\Jobs;

use App\Helpers\ApiWhatsapp;
use App\Mail\CLientWelcomeMail;
use App\Mail\ClientProfileAddedMail;
use App\Models\Business;
use App\Models\ClientImportItem;
use App\Models\SystemLog;
use App\Models\User;
use App\Models\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ProcessIncompleteClientImportItem implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $item;

    protected $userCreated = false;
    protected $userRestored = false;

    protected $clientCreated = false;
    protected $clientRestored = false;

    protected $clientExistAndAssociatedNow = false;

    protected $userEmailHasConflict = false;

    /**
     * Create a new job instance.
     */
    public function __construct(ClientImportItem $item)
    {
        $this->item = $item;
        $this->onQueue('imports-items');
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [new WithoutOverlapping($this->item->id)];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception)
    {
        Log::error('Falha no processamento do item de importação', [
            'item_id' => $this->item->id,
            'batch_id' => $this->item->batch_id,
            'error' => $exception->getMessage()
        ]);

        $this->item->update([
            'status' => ClientImportItem::STATUS_FAILED,
            'error_message' => $exception->getMessage(),
            'processed_at' => now()
        ]);

        $this->updateBatchCounters();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Iniciando processamento do item de importação', [
            'item_id' => $this->item->id,
            'batch_id' => $this->item->batch_id,
            'row_number' => $this->item->row_number
        ]);

        // Marca o item como em processamento
        $this->item->update(['status' => ClientImportItem::STATUS_PROCESSING]);

        try {
            DB::beginTransaction();

            $data = $this->item->raw_data;

            if (isset($data['phone'])) {
                $data['phone'] = preg_replace('/[^0-9]/', '', $data['phone']);
            }

            $requiredFieldsRules = [
                'name' => ['required', 'min:5', 'max:255'],
                'email' => ['required_without:phone', 'email'],
                'phone' => ['required_without:email', 'numeric', 'digits_between:10,11'],
            ];


            // 1. Validar campos obrigatórios primeiro
            $validatorRequired = Validator::make($data, $requiredFieldsRules, [
                'name.required' => 'O nome é obrigatório',
                'name.min' => 'O nome deve ter no mínimo 5 caracteres',
                'name.max' => 'O nome deve ter no máximo 255 caracteres',
                'email.required_without' => 'O e-mail é obrigatório quando o telefone não é fornecido',
                'email.email' => 'O e-mail informado não é válido',
                'phone.required_without' => 'O telefone é obrigatório quando o e-mail não é fornecido',
                'phone.numeric' => 'O telefone deve conter apenas números',
                'phone.digits_between' => 'O telefone deve ter entre 10 e 11 dígitos'
            ]);

            if ($validatorRequired->fails()) {
                $errors = $validatorRequired->errors()->all();
                $errorMessage = "Falha na validação dos campos obrigatórios:\n- " . implode("\n- ", $errors);
                throw new \Exception($errorMessage);
            }

            Log::info('Preparando dados do usuário', [
                'item_id' => $this->item->id,
                'data_fields' => array_keys($data)
            ]);

            // Processa os dados do cliente
            $userData = $this->prepareUserData($data);

            Log::info('Buscando/criando usuário', [
                'item_id' => $this->item->id,
                'email' => $userData['email'],
                'telefone' => $userData['phone_number_1']
            ]);

            // Busca ou cria o usuário
            $user = $this->findOrCreateUser($userData);

            Log::info('Buscando/criando cliente', [
                'item_id' => $this->item->id,
                'user_id' => $user->id
            ]);

            // Cria ou atualiza o cliente
            $client = $this->createOrUpdateClient($user, $data);

            Log::info('informações do processamento', [
                'userCreated' => $this->userCreated,
                'userRestored' => $this->userRestored,
                'clientCreated' => $this->clientCreated,
                'clientRestored' => $this->clientRestored,
                'clientExistAndAssociatedNow' => $this->clientExistAndAssociatedNow,
                'userEmailHasConflict' => $this->userEmailHasConflict
            ]);

            // Envia email de boas vindas apenas para novos usuários ou usuários que estavam deletados
            // if (
            //   // Se o cliente já existia e foi associado a um novo negócio
            //   ($this->clientExistAndAssociatedNow && !$this->userCreated && !$this->userRestored) ||
            //   // Se o cliente foi criado
            //   ($this->clientCreated && !$this->userCreated && !$this->userRestored) ||
            //   // Se o cliente foi restaurado e o usuário já existia
            //   ($this->clientRestored && !$this->userCreated && !$this->userRestored)
            //   ){
            //     $business = Business::find($this->item->batch->business_id);
            //     Mail::to($user->email)->queue(new ClientProfileAddedMail($user, $business));
            // }

            Log::info('Importação do item concluída com sucesso', [
                'item_id' => $this->item->id,
                'client_id' => $client->id,
                'user_id' => $user->id
            ]);

            // Atualiza o item com sucesso
            $this->item->update([
                'status' => ClientImportItem::STATUS_COMPLETED,
                'client_id' => $client->id,
                'processed_at' => now()
            ]);

            // Atualiza contadores do lote
            $this->updateBatchCounters();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            // Registra o erro e marca como falho
            $this->item->update([
                'status' => ClientImportItem::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'processed_at' => now()
            ]);

            // Atualiza contadores do lote
            $this->updateBatchCounters();
        }
    }

    protected function prepareUserData(array $data): array
    {
        return [
            'name' => $data['name'],
            'email' => $data['email'] ?? null,
            'phone_number_1' => $data['phone'] ?? null,
        ];
    }

    /**
     * Manipula a restauração e atualização de um usuário existente
     */
    protected function handleRestoreExistingUser(User $user, array $userData, bool $hasEmail, bool $hasPhone): void
    {
        // Verifica se o email já está sendo usado por outro usuário
        $emailInUse = $hasEmail 
            ? User::where('email', $userData['email'])
                ->where('id', '<>', $user->id)
                ->exists()
            : false;

        // Retorna os dados que vão ser utilizados no cadastro
        $updateData = $this->prepareUserUpdateData($userData, $hasEmail, $hasPhone, $emailInUse, $user);
        
        $user->update($updateData);
        $user->restore();

        // Registra log se o email estava em uso
        if ($emailInUse) {
            SystemLog::warning(
                description: sprintf(
                    "Cliente importado: %s (ID: %d), mas o email %s já estava cadastrado para outro usuário",
                    $userData['name'],
                    $user->id,
                    $userData['email']
                ),
                origin: 'Import Client Registration',
            );
        }
    }

    /**
     * Prepara os dados para atualização do usuário
     */
    protected function prepareUserUpdateData(
        array $userData, 
        bool $hasEmail, 
        bool $hasPhone,
        bool $emailInUse,
        User $existingUser
    ): array {
        $baseData = [
            'name' => $userData['name'] ?? $existingUser->name,
            'phone_number_2' => null,
            'email_verified_at' => null,
            'birth_date' => null,
            'cpf' => null,
            'neighborhood' => null,
            'street' => null,
            'number' => null,
            'city' => null,
            'state' => null,
            'cep' => null,
            'complement' => null,
            'login_type' => 'client'
        ];

        // Tratamento especial para email se já estiver em uso
        $emailValue = $hasEmail 
            ? ($emailInUse ? null : $userData['email'])
            : null;

        return array_merge($baseData, [
            'email' => $emailValue,
            'phone_number_1' => $hasPhone ? $userData['phone_number_1'] : null
        ]);
    }

    protected function findOrCreateUser(array $userData): User
    {
        
        $hasEmail = !empty($userData['email']);
        $hasPhone = !empty($userData['phone_number_1']);
        $existingUser = null;
        
        // Busca usuário existente pelo número
        if ($hasPhone) {
            $phoneNumber = $userData['phone_number_1'];
            $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
            $codes = substr($phoneNumber, 0, 2);
            $numberDigits = substr($phoneNumber, 2);

            if (strlen($numberDigits) === 9) {
                $phoneNumberV2 = $codes . substr($numberDigits, 1);
            } else if (strlen($numberDigits) === 8) {
                $phoneNumberV2 = $codes . '9' . $numberDigits;
            }

            // busca nos ativos usuarios com telefone igual (com digito 9 ou sem)
            $existingUser = User::where(function ($query) use ($phoneNumber, $phoneNumberV2) {
                $query->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
            })->first();

            // busca nos usuarios deletados com telefone igual (com digito 9 ou sem)
            if (!$existingUser) {
                $existingUser = User::withTrashed()->where(function ($query) use ($phoneNumber, $phoneNumberV2) {
                    $query->whereIn('phone_number_1', [$phoneNumber, $phoneNumberV2]);
                })->first();
            }

            // Verifica se o email passado é igual ao email cadastrado no banco
            if ($hasEmail && $existingUser && isset($existingUser->email)) {
                SystemLog::warning(
                    description: "Cliente importado: {$userData['name']}, encontrado pelo telefone, mas o email é diferente. {$userData['email']}",
                    origin: 'Import Client Registration',
                );
            }
        }
        
        // Busca usuário existente pelo email
        if (!$existingUser && $hasEmail) {
            // busca primeiro nos ativos
            $existingUser = User::where('email', $userData['email'])->first();

            // se não encontrou nos ativos, busca nos deletados
            if (!$existingUser) {
                $existingUser = User::withTrashed()
                                ->where('email', $userData['email'])
                                ->first();
            }
        }

        if ($existingUser) {
            if ($existingUser->trashed()) {
                $this->handleRestoreExistingUser($existingUser, $userData, $hasEmail, $hasPhone);
                $this->userRestored = true;
            }
        
            return $existingUser;
        }

        $password = Str::random(10);
        $userData['password'] = bcrypt($password);
        $user = User::create($userData);

        $this->userCreated = true;

        // Envia email com a senha apenas para usuários novos ou que estavam deletados
//        if (!$existingUser || ($existingUser && $existingUser->trashed())) {
//            Mail::to($user->email)->queue(new CLientWelcomeMail($user, $password));
//
//            // Verifica se o plano do negócio permite envio por WhatsApp
//            $business = Business::with(['plan.authorizedFunctions'])->find($this->item->batch->business_id);
//            if ($business && $business->plan && $business->plan->authorizedFunctions && $business->plan->authorizedFunctions->whatsapp_sending) {
//                // Formata a mensagem de boas vindas
//                $title = 'Bem-vindo ao ' . $business->name;
//                $description = "Olá {$user->name}!\n\n" .
//                    "Sua conta foi criada com sucesso.\n\n" .
//                    "Aqui estão suas credenciais de acesso:\n" .
//                    "E-mail: {$user->email}\n" .
//                    "Senha: {$password}\n\n" .
//                    "Recomendamos que você altere sua senha após o primeiro login para garantir a segurança da sua conta.\n\n" .
//                    "Para acessar o sistema, visite: " . config('app.frontend_url') . "/login";
//
//                try {
//                    // Envia a mensagem pelo WhatsApp
//                    ApiWhatsapp::sendMessageWithInstance(
//                        $business->parameters->whatsapp_instance_key,
//                        $user->phone_number_1,
//                        $title,
//                        $description,
//                        $business->name
//                    );
//                } catch (\Exception $e) {
//                    // Loga o erro mas permite que o processo continue
//                    Log::warning('Falha ao enviar mensagem de WhatsApp', [
//                        'error' => $e->getMessage(),
//                        'user_id' => $user->id,
//                        'business_id' => $business->id
//                    ]);
//                }
//            }
//        }

        return $user;
    }

    protected function createOrUpdateClient(User $user, array $data): Client
    {
        // Verifica se já existe um cliente para este usuário
        $client = $user->client()->withTrashed()->first();

        if ($client) {
            if ($client->trashed()) {
                $client->restore();
                $this->clientRestored = true;
            }
            
            // Associa o cliente ao negócio do batch se não estiver já associado
            if (!$client->businesses()->where('business_id', $this->item->batch->business_id)->exists()) {
                // Verifica se o relacionamento já existe deletado (soft-deleted)
                $existingRelationship = DB::table('client_business')
                    ->where('client_id', $client->id)
                    ->where('business_id', $this->item->batch->business_id)
                    ->whereNotNull('deleted_at')
                    ->first();
                
                if ($existingRelationship) {
                    // Restaura o relacionamento soft-deleted usando update direto
                    DB::table('client_business')
                        ->where('client_id', $client->id)
                        ->where('business_id', $this->item->batch->business_id)
                        ->update([
                            'deleted_at' => null,
                            'updated_at' => now()
                        ]);
                } else {
                    // Cria o relacionamento somente se não existir (nem mesmo deletado)
                    $client->businesses()->attach($this->item->batch->business_id, [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                
                $this->clientExistAndAssociatedNow = true;
            }

            return $client;
        }
        
        $this->clientCreated = true;
        // Cria novo cliente
        $client = $user->client()->create([
            'is_entrepreneur' => $data['is_entrepreneur'] ?? false
        ]);

        // Associa o cliente ao negócio do batch
        $client->businesses()->attach($this->item->batch->business_id, [
            'created_at' => now(),
            'updated_at' => now()
        ]);


        return $client;
    }

    protected function updateBatchCounters(): void
    {
        $batch = $this->item->batch;

        // Atualiza contadores
        $completed = $batch->items()->where('status', ClientImportItem::STATUS_COMPLETED)->count();
        $failed = $batch->items()->where('status', ClientImportItem::STATUS_FAILED)->count();

        $batch->update([
            'processed_records' => $completed,
            'failed_records' => $failed
        ]);
    }
}
