<?php

namespace App\Jobs;

use App\Enums\EvaluationSendingType;
use App\Helpers\ApiWhatsapp;
use App\Mail\AdminEvaluationRespondedMail;
use App\Mail\ClientEvaluationRespondedMail;
use App\Mail\EvaluationExpireMail;
use App\Models\Evaluation;
use App\SiteNotifications\AdminEvaluationRespondedSiteNotification;
use App\SiteNotifications\EvaluationExpireSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use Mail;
use Storage;

class SendEvaluationRespondedNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         * Evaluations que foram respondidas, mas não notificadas
         */

         $evaluations = Evaluation::where('shipping_status', 'RESPONDED')
            ->whereNull('notified_responded_at')
            ->get();

        foreach($evaluations as $evaluation) {
            $this->processNotification($evaluation);
        }
    }

    public function processNotification(Evaluation $evaluation) {
        $this->sendSiteNotification($evaluation);
        switch($evaluation->sending_type) {
            case EvaluationSendingType::email->value:
                $this->sendEmailNotification($evaluation);
            break;
            case EvaluationSendingType::whatsapp->value:
                $this->sendWhatsappNotification($evaluation);
            break;
        }
    }

    public function sendSiteNotification(Evaluation $evaluation): void {
        try {
            $business = $evaluation->business;

            if(!$business){
                throw new Exception("Negócio da avaliação {$evaluation->id} não está ativo");
            }

            $admin = $business->admin() ?? null;

            if (!$admin || !$admin->user)
                throw new Exception("Negócio: {$business->name} ID: {$business->id}  não possui administrador");

            (new AdminEvaluationRespondedSiteNotification(
                $business->logo,
                $business->id,
                $business->name
            ))->send([$admin->user_id]);
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação (SITE) de respondida da avaliação {$evaluation->id}. {$e->getMessage()}");
        }
    }

    public function sendEmailNotification(Evaluation $evaluation) {
        try {
            $business = $evaluation->business ?? null;

            if(!$business){
                throw new Exception("Negócio da avaliação {$evaluation->id} não está ativo");
            }

            $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
            if (!$evaluation->anonymous) {
                if($evaluation->client->user->email != null && $evaluation->client->user->email != "") {
                    $clientEmail = $evaluation->client->user->email;
                    Mail::to($clientEmail)->send(
                        new ClientEvaluationRespondedMail(
                            $business->name,
                            $logo,
                            $business->url_facebook,
                            $business->url_google,
                            $business->url_instagram,
                            $business->url_linkedin
                        ));
                }
            }
            $admin = $business->admin() ?? null;
            
            if (!$admin || !$admin->user)
                throw new Exception("Negócio: {$business->name} ID: {$business->id}  não possui administrador");

            Mail::to($admin->user->email)->send(
                new AdminEvaluationRespondedMail(
                    $business->name,
                    $logo,
                ));
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de respondida da Avaliação {$evaluation->id} via EMAIL. {$e->getMessage()}");
        }
        $evaluation->notified_responded_at = Carbon::now();
        $evaluation->save();
    }

    public function sendWhatsappNotification(Evaluation $evaluation){
        $this->sendEmailNotification($evaluation);
        try {
            $business = $evaluation->business;
            $businessName = $business->name;

            if (!$evaluation->anonymous) {
                $clientName = $evaluation->client->user->name;
                $greetings = isset($clientName) ? "Olá, " . explode(" ", $clientName)[0] : "Olá!";
                $userPhone = $evaluation->number_phone;
                $title = "Obrigado por sua avaliação! 😊";
                $description = "$greetings\n\n" .
                    "Vimos que você respondeu nossa avaliação!\n".
                    "Sua opinião é essencial para melhorarmos nossos serviços.\n\n" .
                    "Atenciosamente, *$businessName*.";

                ApiWhatsapp::sendMessageWithInstance(
                    $business->parameters->whatsapp_instance_key,
                    $userPhone, $title, $description, $businessName);
            }

            $admin = $business->admin();
            if (!$admin)
                throw new Exception("Não há admin em $businessName");

            $adminUser = $admin->user;
            $titleAdmin = "Uma avaliação foi respondida! 😊";
            $descriptionAdmin = "Olá, " . explode(" ", $adminUser->name)[0] .
                "Uma avaliação do negócio *$businessName* foi respondida!\n\n".
                "Para acessar as avaliações, clique no link abaixo\n".
                config('app.url') . "/evaluations\n\n" . 
                "Atenciosamente, *" . config('app.name') . "*";

            ApiWhatsapp::sendMessageWithInstance(
                $business->parameters->whatsapp_instance_key,
                $adminUser->phone_number_1, $titleAdmin, $descriptionAdmin, $businessName);

            if (!$evaluation->notified_responded_at) {
                $evaluation->notified_responded_at = Carbon::now();
                $evaluation->save();
            }
        } catch (Exception $ex) {
            Log::info("Falha ao enviar notificação de respondida da Avaliação {$evaluation->id} via ZAP. {$ex->getMessage()}");
        } 
    }
}
