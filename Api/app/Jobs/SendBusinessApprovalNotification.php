<?php

namespace App\Jobs;

use App\Enums\SiteNotificationTypes;
use App\Mail\AdminBusinessApprovalMail;
use App\Models\Business;
use App\Models\SiteNotification;
use App\SiteNotifications\BusinessApprovalSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendBusinessApprovalNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         *  Negócios que foram aprovados ou rejeitados, mas não foram notificados ainda
         */
         $businesses = Business::withTrashed()->where(function ($query) {
                        $query->where('status', Business::STATUS_APPROVED_REGISTRATION)
                            ->orWhere('status', Business::STATUS_REJECTED_REGISTRATION);
                    })->whereNull('approval_notified_at')->get();

        foreach($businesses as $business) {
            $this->sendSiteNotification($business);
            $this->sendEmailNotification($business);
        }
    }

    public function sendSiteNotification(Business $business): void {
        $admin = $business->admin();
        try {
            $isApproved = $business->status === Business::STATUS_APPROVED_REGISTRATION;
            (new BusinessApprovalSiteNotification($business->name, $business->id, $isApproved))
            ->send([$admin->user_id]);
        } catch (Exception $e) {
            Log::info('Falha ao enviar notificação (SITE) de expiração do cadastro do negócio {$business->id}. {$e->getMessage()}');
        }
    }

    public function sendEmailNotification(Business $business) {
        $admin = $business->admin();
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        try {
            if ($admin && $admin->user) {
                Mail::to($admin->user->email)->send(new AdminBusinessApprovalMail($logo, $business->name, $business->status));
            } else Log::info("O administrador desse negócio [{$business->id}, {$business->name}] não está ativo.");
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de julgamento do negócio {$business->id} via EMAIL. {$e->getMessage()}");
        }
        $business->approval_notified_at = Carbon::now();
        $business->save();
    }
}
