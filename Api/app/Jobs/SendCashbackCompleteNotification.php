<?php

namespace App\Jobs;

use App\Enums\CashbackStatus;
use App\Enums\EvaluationSendingType;
use App\Helpers\ApiWhatsapp;
use App\Mail\AdminCashbackCompleteMail;
use App\Mail\ClientCashbackCompleteMail;
use App\Models\Cashback;
use App\SiteNotifications\AdminCashbackCompleteNotification;
use App\SiteNotifications\CashbackCompleteSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendCashbackCompleteNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         * Cashbacks que foram concluídos
         */
         $cashbacks = Cashback::where('status', CashbackStatus::Completed->value)
                    ->whereNull('notification_complete_date')
                    ->get();

        foreach($cashbacks as $cashback) {
            $this->processCashbackNotification($cashback);
        }
    }

    public function processCashbackNotification(Cashback $cashback) {
        $this->sendSiteNotification($cashback);
        switch($cashback->sending_type) {
            case EvaluationSendingType::email->value:
                $this->sendEmailNotification($cashback);
            break;
            case EvaluationSendingType::whatsapp->value:
                $this->sendWhatsappNotification($cashback);
            break;
        }
    }

    public function sendSiteNotification(Cashback $cashback): void {
        $client = $cashback->client;
        $business = $cashback->business;
        $admin = $business->admin();
        $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
        $cashbackDescription = $canSetCashbackValue ? 
                                "R$ " . number_format($cashback->amount, 2, ',', '') :
                                number_format($cashback->percentage, 2, ',', ''). '%';
        try {
            (new CashbackCompleteSiteNotification(
                $business->logo, 
                $business->id, 
                $business->name, 
                $cashbackDescription
            ))->send([$client->user_id]);
            (new AdminCashbackCompleteNotification(
                null, 
                $business->id, 
                $business->name, 
                $client->user->name, 
                $cashbackDescription
            ))->send([$admin->user_id]);
        } catch (Exception $e) {
            Log::info('Falha ao enviar notificação (SITE) de criação do cashback {$cashback->id}. {$e->getMessage()}');
        }
    }

    public function sendEmailNotification(Cashback $cashback) {
        $client = $cashback->client;
        $business = $cashback->business;
        $admin = $business->admin();
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
        $cashbackDescription = $canSetCashbackValue ? 
                                "R$ " . number_format($cashback->amount, 2, ',', '') :
                                number_format($cashback->percentage, 2, ',', ''). '%';
        try {
            if ($admin && $admin->user) {
                Mail::to($admin->user->email)->send(new AdminCashbackCompleteMail(
                    $cashbackDescription, 
                    $client->user->name, 
                    $business->name, 
                    $logo,
                ));
            } else Log::info("O administrador desse negócio não está ativo.");
            if($client->user->email != null && $client->user->email != "") {
                Mail::to($client->user->email)->send(new ClientCashbackCompleteMail(
                    $cashback->percentage,
                    $business->name,
                    $logo
                ));
            }
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de conclusão do cashback {$cashback->id} via EMAIL. {$e->getMessage()}");
        }
        $cashback->notification_complete_date = Carbon::now();
        $cashback->save();
    }

    public function sendWhatsappNotification(Cashback $cashback){
        $this->sendEmailNotification($cashback);
        try {
            $business = $cashback->business;
            $client = $cashback->client;
            $user = $client->user;
            $adminUser = $business->admin()->user;
            $userPhone = $user->phone_number_1;
            $cashbackClientsLink = config('app.frontend_url') . '/cashbacks/client';
            $cashbackAdminLink = config('app.frontend_url') . '/cashbacks';
            $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
            $cashbackDescription = $canSetCashbackValue ?
                                    "R$ " . number_format($cashback->amount, 2, ',', '') :
                                    number_format($cashback->percentage, 2, ',', ''). '%';

            $title = "Seu Cashback Foi Utilizado! ✅";
            $description = "Olá, " . explode(" ", $user->name)[0] . "!\n\n" .
                "Informamos que o seu cashback de cashback de $cashbackDescription foi utilizado com sucesso em nossa plataforma.\n\n".
                "Esperamos que tenha aproveitado essa oportunidade para economizar!\n" .
                "Continue nos ajudando para ganhar mais benefícios e cashbacks exclusivos.\n\n" .
                "Para ver seus cashbacks, acesse o link abaixo:\n" .
                "$cashbackClientsLink\n\n" .
                "Atenciosamente, *$business->name*.";

            $titleAdmin = "Um cashback do seu negócio foi utilizado!";
            $descriptionAdmin = "Olá, $adminUser->name!\n\n".
                    "O cliente *$user->name* utilizou um cashback de *$cashbackDescription* do negócio *$business->name*.\n\n" . 
                    "Para mais informações, acesse o link abaixo:\n" . 
                    "$cashbackAdminLink\n\n" .
                    "Atenciosamente, *". config('app.name') . '*.';

            ApiWhatsapp::sendMessageWithInstance(
                $business->parameters->whatsapp_instance_key,
                $userPhone, $title, $description, $business->name);
            ApiWhatsapp::sendMessageWithInstance(
                $business->parameters->whatsapp_instance_key,
                $adminUser->phone_number_1, $titleAdmin, $descriptionAdmin);

            if (!$cashback->notification_created_date) {
                $cashback->notification_created_date = Carbon::now();
                $cashback->save();
            }
        } catch (Exception $ex) {
            Log::info("Falha ao enviar notificação do cashback {$cashback->id} via ZAP. {$ex->getMessage()}");
        }
    }
}
