<?php

namespace App\Jobs;

use App\Mail\AdminBusinessApprovalMail;
use App\Mail\AdminBusinessCancelMail;
use App\Models\Business;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendBusinessCancelNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         *  Negócios que estão com cancelamento pendente, mas não foram notificados ainda
         */
         $businesses = Business::withTrashed()
                    ->where('status', Business::STATUS_PENDING_CANCELLATION)
                    ->whereNull('cancel_notified_at')
                    ->get();

        foreach($businesses as $business) {
            $this->sendEmailNotification($business);
        }
    }

    public function sendEmailNotification(Business $business) {
        $admin = $business->admin();
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        $deleted_at = $business->deleted_at;
        $lastMonth = Carbon::parse($deleted_at)->subMonth();
        $planValue = $business->plan->value ?? -1;
        try {
            if ($admin && $admin->user) {
                Mail::to($admin->user->email)->send(new AdminBusinessCancelMail($logo, $business->name, $lastMonth, $planValue));
            } else Log::info("O administrador desse negócio [{$business->id}, {$business->name}] não está ativo.");
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de cancelamento do negócio {$business->id} via EMAIL. {$e->getMessage()}");
        }
        $business->cancel_notified_at = Carbon::now();
        $business->save();
    }
}
