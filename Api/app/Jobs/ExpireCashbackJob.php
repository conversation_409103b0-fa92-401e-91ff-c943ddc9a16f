<?php

namespace App\Jobs;

use App\Models\Cashback;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;

class ExpireCashbackJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Obter a data e hora atual
        $now = Carbon::now();

        // Buscar cashbacks que foram enviadas e expiraram
        $expiredCashbacks = Cashback::where('status', 'pending')
            ->where('expiration_date', '<', $now)
            ->get();

        // Atualizar o status de cada cashback expirado
        foreach ($expiredCashbacks as $cashback) {
            try {
                $cashback->status = 'expired';
                $cashback->save();
            } catch (\Exception $e) {
                // Log de erro caso a atualização falhe
                Log::error("Erro ao expirar Cashback ID {$cashback->id}: {$e->getMessage()}");
            }
        }
    }
}
