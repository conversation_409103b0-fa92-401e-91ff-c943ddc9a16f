<?php

namespace App\Jobs;

use App\Models\Evaluation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ExpireEvaluationsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(){
        //
    }

    public function handle(): void
    {
        // Obter a data e hora atual
        $now = Carbon::now();

        // Buscar avaliações que foram enviadas e expiraram
        $expiredEvaluations = Evaluation::where('shipping_status', 'SENT')
            ->where('expiration_date', '<', $now)
            ->get();

        // Atualizar o status de cada avaliação expirada
        foreach ($expiredEvaluations as $evaluation) {
            try {
                $evaluation->shipping_status = 'EXPIRED';
                $evaluation->save();
            } catch (\Exception $e) {
                // Log de erro caso a atualização falhe
                Log::error("Erro ao expirar a Avaliação ID {$evaluation->id}: {$e->getMessage()}");
            }
        }
    }
}
