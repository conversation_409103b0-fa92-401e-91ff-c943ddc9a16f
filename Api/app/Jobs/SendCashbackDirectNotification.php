<?php

namespace App\Jobs;

use App\Enums\EvaluationSendingType;
use App\Helpers\ApiWhatsapp;
use App\Mail\ClientCashbackDirectMail;
use App\Models\Cashback;
use App\SiteNotifications\CashbackDirectSiteNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class SendCashbackDirectNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $cashback;

    /**
     * Create a new job instance.
     */
    public function __construct(Cashback $cashback)
    {
        $this->cashback = $cashback;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->processCashbackDirectNotification($this->cashback);
    }

    public function processCashbackDirectNotification(Cashback $cashback)
    {
        $this->sendSiteNotification($cashback);
        switch ($cashback->sending_type) {
            case EvaluationSendingType::email->value:
                $this->sendEmailNotification($cashback);
                break;
            case EvaluationSendingType::whatsapp->value:
                $this->sendWhatsappNotification($cashback);
                break;
        }
    }

    public function sendSiteNotification(Cashback $cashback): void
    {
        $client = $cashback->client;
        $business = $cashback->business;
        $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
        $cashbackDescription = $canSetCashbackValue ?
            "R$ " . number_format($cashback->amount, 2, ',', '') :
            number_format($cashback->percentage, 2, ',', '') . '%';
        try {
            (new CashbackDirectSiteNotification(
                $business->logo,
                $business->id,
                $business->name,
                $cashbackDescription
            ))->send([$client->user_id]);
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação (SITE) de criação do cashback direto {$cashback->id}. {$e->getMessage()}");
        }
    }

    public function sendEmailNotification(Cashback $cashback)
    {
        $client = $cashback->client;
        $business = $cashback->business;
        $logo = $business->logo ? Storage::disk('s3')->temporaryUrl($business->logo, Carbon::now()->addDays(7)) : null;
        $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
        $cashbackDescription = $canSetCashbackValue ?
            "R$ " . number_format($cashback->amount, 2, ',', '') :
            number_format($cashback->percentage, 2, ',', '') . '%';
        try {
            if ($client->user->email != null && $client->user->email != "") {
                Mail::to($client->user->email)->send(new ClientCashbackDirectMail(
                    $cashbackDescription,
                    $business->name,
                    $logo
                ));
            }
        } catch (Exception $e) {
            Log::info("Falha ao enviar notificação de cashback direto {$cashback->id} via EMAIL. {$e->getMessage()}");
        }
        $cashback->notification_created_date = Carbon::now();
        $cashback->save();
    }

    public function sendWhatsappNotification(Cashback $cashback)
    {
        try {
            $business = $cashback->business;
            $client = $cashback->client;
            $user = $client->user;
            $userPhone = $user->phone_number_1;
            $cashbackClientsLink = config('app.frontend_url') . '/cashbacks/client';
            $canSetCashbackValue = $business->parameters->can_set_cashback_value || false;
            $cashbackDescription = $canSetCashbackValue ?
                "R$ " . number_format($cashback->amount, 2, ',', '') :
                number_format($cashback->percentage, 2, ',', '') . '%';

            $title = "Eii " . explode(" ", $user->name)[0] . ", olha quem tem cashback novo na conta! 💸😎";
            $description = "Você garantiu *$cashbackDescription* de cashback com a gente.\n" .
                "Um presentinho nosso pra você economizar e voltar ainda mais feliz!\n\n" .
                "👉 Acesse aqui:\n" .
                "$cashbackClientsLink\n\n" .
                "Obrigado por confiar e fazer parte da nossa história!\n" .
                "Com carinho, *$business->name*.";

            ApiWhatsapp::sendMessageWithInstance(
                $business->parameters->whatsapp_instance_key,
                $userPhone, $title, $description, $business->name);

            if (!$cashback->notification_created_date) {
                $cashback->notification_created_date = Carbon::now();
                $cashback->save();
            }
        } catch (Exception $ex) {
            Log::info("Falha ao enviar notificação do cashback direto {$cashback->id} via WhatsApp. {$ex->getMessage()}");
            if ($ex->getMessage() == 'WhatsApp não conectado') {
                $parameters = $business->parameters;
                $parameters->whatsapp_instance_key = null;
                $parameters->save();
            }
            throw $ex;
        }
    }
}
