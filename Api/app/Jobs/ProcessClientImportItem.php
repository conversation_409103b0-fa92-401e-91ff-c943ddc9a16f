<?php

namespace App\Jobs;

use App\Helpers\ApiWhatsapp;
use App\Mail\CLientWelcomeMail;
use App\Mail\ClientProfileAddedMail;
use App\Models\Business;
use App\Models\ClientImportItem;
use App\Models\User;
use App\Models\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ProcessClientImportItem implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $item;

    protected $userCreated = false;
    protected $userRestored = false;

    protected $clientCreated = false;
    protected $clientRestored = false;

    protected $clientExistAndAssociatedNow = false;

    /**
     * Valida se um CPF é válido usando o algoritmo de validação
     */
    protected function cpfIsValid(string $cpf): bool
    {
        // Remove caracteres não numéricos
        $cpf = preg_replace('/[^0-9]/', '', $cpf);

        // Verifica se tem 11 dígitos
        if (strlen($cpf) != 11) {
            return false;
        }

        // Verifica se todos os dígitos são iguais
        if (preg_match('/^(\d)\1+$/', $cpf)) {
            return false;
        }

        // Calcula primeiro dígito verificador
        $soma = 0;
        for ($i = 1; $i <= 9; $i++) {
            $soma += intval(substr($cpf, $i - 1, 1)) * (11 - $i);
        }
        $resto = ($soma * 10) % 11;
        if ($resto == 10 || $resto == 11) {
            $resto = 0;
        }
        if ($resto != intval(substr($cpf, 9, 1))) {
            return false;
        }

        // Calcula segundo dígito verificador
        $soma = 0;
        for ($i = 1; $i <= 10; $i++) {
            $soma += intval(substr($cpf, $i - 1, 1)) * (12 - $i);
        }
        $resto = ($soma * 10) % 11;
        if ($resto == 10 || $resto == 11) {
            $resto = 0;
        }
        if ($resto != intval(substr($cpf, 10, 1))) {
            return false;
        }

        return true;
    }

    /**
     * Create a new job instance.
     */
    public function __construct(ClientImportItem $item)
    {
        $this->item = $item;
        $this->onQueue('imports-items');
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [new WithoutOverlapping($this->item->id)];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception)
    {
        Log::error('Falha no processamento do item de importação', [
            'item_id' => $this->item->id,
            'batch_id' => $this->item->batch_id,
            'error' => $exception->getMessage()
        ]);

        $this->item->update([
            'status' => ClientImportItem::STATUS_FAILED,
            'error_message' => $exception->getMessage(),
            'processed_at' => now()
        ]);

        $this->updateBatchCounters();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Iniciando processamento do item de importação', [
            'item_id' => $this->item->id,
            'batch_id' => $this->item->batch_id,
            'row_number' => $this->item->row_number
        ]);

        // Marca o item como em processamento
        $this->item->update(['status' => ClientImportItem::STATUS_PROCESSING]);

        try {
            DB::beginTransaction();

            $data = $this->item->raw_data;

            // Limpar e validar dados antes da validação dos campos
            if (isset($data['cpf'])) {
                $data['cpf'] = preg_replace('/[^0-9]/', '', $data['cpf']);

                // Validar CPF usando o algoritmo
                if (!$this->cpfIsValid($data['cpf'])) {
                    throw new \Exception('O número de CPF informado é inválido. Por favor, verifique se os dígitos estão corretos.');
                }
            }

            if (isset($data['phone'])) {
                $data['phone'] = preg_replace('/[^0-9]/', '', $data['phone']);
            }


            // 1. Validar campos obrigatórios primeiro
            $validatorRequired = Validator::make($data, [
                'name' => ['required', 'min:5', 'max:255'],
                'email' => ['required', 'email'],
                'cpf' => ['required', 'numeric', 'digits:11'],
                'phone' => ['required', 'numeric', 'digits_between:10,11'],
            ], [
                'name.required' => 'O nome é obrigatório',
                'name.min' => 'O nome deve ter no mínimo 5 caracteres',
                'name.max' => 'O nome deve ter no máximo 255 caracteres',
                'email.required' => 'O e-mail é obrigatório',
                'email.email' => 'O e-mail informado não é válido',
                'cpf.required' => 'O CPF é obrigatório',
                'cpf.numeric' => 'O CPF deve conter apenas números',
                'cpf.digits' => 'O CPF deve ter exatamente 11 dígitos',
                'phone.required' => 'O telefone é obrigatório',
                'phone.numeric' => 'O telefone deve conter apenas números',
                'phone.digits_between' => 'O telefone deve ter entre 10 e 11 dígitos'
            ]);

            if ($validatorRequired->fails()) {
                $errors = $validatorRequired->errors()->all();
                $errorMessage = "Falha na validação dos campos obrigatórios:\n- " . implode("\n- ", $errors);
                throw new \Exception($errorMessage);
            }

            // 2. Validar campos opcionais
            $validatorOptional = Validator::make($data, [
                'birth_date' => ['nullable', 'date', 'before:today'],
                'neighborhood' => ['nullable', 'min:2'],
                'street' => ['nullable', 'min:2'],
                'number' => ['nullable'],
                'city' => ['nullable'],
                'state' => ['nullable', 'size:2'],
                'cep' => ['nullable', 'numeric', 'digits:8'],
                'complement' => ['nullable', 'min:2'],
            ], [
                'birth_date.date' => 'A data de nascimento deve ser uma data válida',
                'birth_date.before' => 'A data de nascimento deve ser anterior à data atual',
                'neighborhood.min' => 'O bairro deve ter no mínimo 2 caracteres',
                'street.min' => 'A rua deve ter no mínimo 2 caracteres',
                'state.size' => 'O estado deve ter exatamente 2 caracteres (sigla)',
                'cep.numeric' => 'O CEP deve conter apenas números',
                'cep.digits' => 'O CEP deve ter exatamente 8 dígitos',
                'complement.min' => 'O complemento deve ter no mínimo 2 caracteres'
            ]);

            // Se houver erros nos campos opcionais, registra no log mas continua o processo
            if ($validatorOptional->fails()) {
                $errors = $validatorOptional->errors()->all();
                Log::warning('Alguns campos opcionais foram ignorados por serem inválidos:', [
                    'item_id' => $this->item->id,
                    'linha' => $this->item->row_number,
                    'erros' => $errors
                ]);

                // Remove os campos opcionais inválidos dos dados
                foreach ($validatorOptional->errors()->keys() as $key) {
                    unset($data[$key]);
                }
            }

            Log::info('Preparando dados do usuário', [
                'item_id' => $this->item->id,
                'data_fields' => array_keys($data)
            ]);

            // Processa os dados do cliente
            $userData = $this->prepareUserData($data);
            $cpf = preg_replace('/[^0-9]/', '', $data['cpf']);

            Log::info('Buscando/criando usuário', [
                'item_id' => $this->item->id,
                'cpf' => $cpf,
                'email' => $userData['email']
            ]);

            // Busca ou cria o usuário
            $user = $this->findOrCreateUser($userData, $cpf);

            Log::info('Buscando/criando cliente', [
                'item_id' => $this->item->id,
                'user_id' => $user->id
            ]);

            // Cria ou atualiza o cliente
            $client = $this->createOrUpdateClient($user, $data);

            Log::info('informações do processamento', [
                'userCreated' => $this->userCreated,
                'userRestored' => $this->userRestored,
                'clientCreated' => $this->clientCreated,
                'clientRestored' => $this->clientRestored,
                'clientExistAndAssociatedNow' => $this->clientExistAndAssociatedNow
            ]);

            // Envia email de boas vindas apenas para novos usuários ou usuários que estavam deletados
            if (
              // Se o cliente já existia e foi associado a um novo negócio
              ($this->clientExistAndAssociatedNow && !$this->userCreated && !$this->userRestored) ||
              // Se o cliente foi criado
              ($this->clientCreated && !$this->userCreated && !$this->userRestored) ||
              // Se o cliente foi restaurado e o usuário já existia
              ($this->clientRestored && !$this->userCreated && !$this->userRestored)
              ){
                $business = Business::find($this->item->batch->business_id);
                Mail::to($user->email)->queue(new ClientProfileAddedMail($user, $business));
            }

            Log::info('Importação do item concluída com sucesso', [
                'item_id' => $this->item->id,
                'client_id' => $client->id,
                'user_id' => $user->id
            ]);

            // Atualiza o item com sucesso
            $this->item->update([
                'status' => ClientImportItem::STATUS_COMPLETED,
                'client_id' => $client->id,
                'processed_at' => now()
            ]);

            // Atualiza contadores do lote
            $this->updateBatchCounters();

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();

            // Registra o erro e marca como falho
            $this->item->update([
                'status' => ClientImportItem::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'processed_at' => now()
            ]);

            // Atualiza contadores do lote
            $this->updateBatchCounters();
        }
    }

    protected function prepareUserData(array $data): array
    {
        $birth_date = null;

        // Processar a data de nascimento se existir
        if (isset($data['birth_date']) && !empty($data['birth_date'])) {
            // Log para debug
            Log::info('Processando data de nascimento', [
                'valor_original' => $data['birth_date'],
                'item_id' => $this->item->id
            ]);

            // Se já estiver no formato YYYY-MM-DD
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $data['birth_date'])) {
                $birth_date = $data['birth_date'];
            }
            // Se estiver em formato com separador /
            elseif (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $data['birth_date'], $matches)) {
                $first = (int)$matches[1];
                $second = (int)$matches[2];
                $year = (int)$matches[3];
                
                // Primeiro tenta como formato brasileiro (DD/MM/YYYY)
                if (checkdate($second, $first, $year)) {
                    $day = $first;
                    $month = $second;
                }
                // Se falhar, tenta como formato americano (MM/DD/YYYY)
                elseif (checkdate($first, $second, $year)) {
                    $day = $second;
                    $month = $first;
                }
                // Se ambos falharem, marca como data inválida
                else {
                    Log::warning('Data inválida detectada', [
                        'data' => $data['birth_date'],
                        'primeiro_numero' => $first,
                        'segundo_numero' => $second,
                        'ano' => $year,
                        'item_id' => $this->item->id
                    ]);
                    $day = null;
                    $month = null;
                }
                
                // Se conseguiu determinar dia e mês válidos, formata a data
                if ($day !== null && $month !== null) {
                    $birth_date = sprintf('%04d-%02d-%02d', $year, $month, $day);
                    Log::info('Data validada e convertida', [
                        'formato_detectado' => checkdate($second, $first, $year) ? 'brasileiro' : 'americano',
                        'original' => $data['birth_date'],
                        'convertida' => $birth_date,
                        'item_id' => $this->item->id
                    ]);
                }
            }

            // Log do resultado
            Log::info('Data processada', [
                'resultado' => $birth_date,
                'item_id' => $this->item->id
            ]);
        }

        return [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone_number_1' => $data['phone'] ?? null,
            'cpf' => preg_replace('/[^0-9]/', '', $data['cpf']),
            'birth_date' => $birth_date,
            'zipcode' => $data['zipCode'] ?? null,
            'neighborhood' => $data['neighborhood'] ?? null,
            'street' => $data['street'] ?? null,
            'number' => $data['number'] ?? null,
            'complement' => $data['complement'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'login_type' => 'client',
            'gender' => isset($data['gender']) && in_array($data['gender'], ['f', 'm']) ? $data['gender'] : null,
        ];
    }

    /**
     * Mescla dados do usuário mantendo valores existentes não vazios
     */
    protected function mergeUserData(User $existingUser, array $newData): array
    {
        $mergedData = [];

        foreach ($newData as $key => $value) {
            // Se o campo existente estiver vazio e o novo valor não estiver vazio, usa o novo valor
            if (empty($existingUser->{$key}) && !empty($value)) {
                $mergedData[$key] = $value;
            }
        }

        return $mergedData;
    }

    protected function findOrCreateUser(array $userData, string $cpf): User
    {
        // Busca usuário existente pelo CPF, incluindo deletados
        $existingUser = User::withTrashed()->where('cpf', $cpf)->first();

        if ($existingUser) {
            if ($existingUser->trashed()) {
                $existingUser->restore();
                $this->userRestored = true;
            }

            // Mescla dados mantendo valores existentes não vazios
            $mergedData = $this->mergeUserData($existingUser, $userData);

            // Só atualiza se houver novos dados para adicionar
            if (!empty($mergedData)) {
                $existingUser->update($mergedData);
            }

            return $existingUser;
        }

        // Verifica se já existe um usuário com o mesmo email
        $emailUser = User::where('email', $userData['email'])->first();
        if ($emailUser) {
            throw new \Exception(sprintf(
                'O e-mail %s já está cadastrado para outro usuário com CPF diferente. Por favor, utilize outro e-mail ou verifique se o CPF está correto.',
                $userData['email']
            ));
        }

        // Cria novo usuário
        $password = Str::random(10);
        $userData['password'] = bcrypt($password);
        $user = User::create($userData);

        $userCreated = true;

        // Envia email com a senha apenas para usuários novos ou que estavam deletados
        if (!$existingUser || ($existingUser && $existingUser->trashed())) {
            Mail::to($user->email)->queue(new CLientWelcomeMail($user, $password));

//            // Verifica se o plano do negócio permite envio por WhatsApp
//            $business = Business::with(['plan.authorizedFunctions'])->find($this->item->batch->business_id);
//            if ($business && $business->plan && $business->plan->authorizedFunctions && $business->plan->authorizedFunctions->whatsapp_sending) {
//                // Formata a mensagem de boas vindas
//                $title = 'Bem-vindo ao ' . $business->name;
//                $description = "Olá {$user->name}!\n\n" .
//                    "Sua conta foi criada com sucesso.\n\n" .
//                    "Aqui estão suas credenciais de acesso:\n" .
//                    "E-mail: {$user->email}\n" .
//                    "Senha: {$password}\n\n" .
//                    "Recomendamos que você altere sua senha após o primeiro login para garantir a segurança da sua conta.\n\n" .
//                    "Para acessar o sistema, visite: " . config('app.frontend_url') . "/login";
//
//                try {
//                    // Envia a mensagem pelo WhatsApp
//                    ApiWhatsapp::sendMessageWithInstance(
//                        $business->parameters->whatsapp_instance_key,
//                        $user->phone_number_1,
//                        $title,
//                        $description,
//                        $business->name
//                    );
//                } catch (\Exception $e) {
//                    // Loga o erro mas permite que o processo continue
//                    Log::warning('Falha ao enviar mensagem de WhatsApp', [
//                        'error' => $e->getMessage(),
//                        'user_id' => $user->id,
//                        'business_id' => $business->id
//                    ]);
//                }
//            }
        }

        return $user;
    }

    protected function createOrUpdateClient(User $user, array $data): Client
    {
        // Verifica se já existe um cliente para este usuário
        $client = $user->client()->withTrashed()->first();

        if ($client) {
            if ($client->trashed()) {
                $client->restore();
                $this->clientRestored = true;
            }
            
            // Associa o cliente ao negócio do batch se não estiver já associado
            if (!$client->businesses()->where('business_id', $this->item->batch->business_id)->exists()) {
                // Verifica se o relacionamento já existe deletado (soft-deleted)
                $existingRelationship = DB::table('client_business')
                    ->where('client_id', $client->id)
                    ->where('business_id', $this->item->batch->business_id)
                    ->whereNotNull('deleted_at')
                    ->first();
                
                if ($existingRelationship) {
                    // Restaura o relacionamento soft-deleted usando update direto
                    DB::table('client_business')
                        ->where('client_id', $client->id)
                        ->where('business_id', $this->item->batch->business_id)
                        ->update([
                            'deleted_at' => null,
                            'updated_at' => now()
                        ]);
                } else {
                    // Cria o relacionamento somente se não existir (nem mesmo deletado)
                    $client->businesses()->attach($this->item->batch->business_id, [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                
                $this->clientExistAndAssociatedNow = true;
            }

            return $client;
        }
        
        $this->clientCreated = true;
        // Cria novo cliente
        $client = $user->client()->create([
            'is_entrepreneur' => $data['is_entrepreneur'] ?? false
        ]);

        // Associa o cliente ao negócio do batch
        $client->businesses()->attach($this->item->batch->business_id, [
            'created_at' => now(),
            'updated_at' => now()
        ]);


        return $client;
    }

    protected function updateBatchCounters(): void
    {
        $batch = $this->item->batch;

        // Atualiza contadores
        $completed = $batch->items()->where('status', ClientImportItem::STATUS_COMPLETED)->count();
        $failed = $batch->items()->where('status', ClientImportItem::STATUS_FAILED)->count();

        $batch->update([
            'processed_records' => $completed,
            'failed_records' => $failed
        ]);
    }
}
