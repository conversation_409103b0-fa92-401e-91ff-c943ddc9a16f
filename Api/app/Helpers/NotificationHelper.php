<?php
namespace App\Helpers;

class NotificationHelper
{
    public static function applyMasksValues(string $text, array $values = []) {
        if (isset($values['clientName'])){
            $text = str_replace('{{NOME_CLIENTE}}', $values['clientName'], $text);
        }

        if (isset($values['clientCpf'])){
            $cpf = $values['clientCpf'];
            $maskedCpf = substr($cpf, 0, 3) . '.XXX.XXX-' . substr($cpf, -2);
            $text = str_replace('{{CPF_CLIENTE}}', $maskedCpf, $text);
        }

        return $text;
    }
}