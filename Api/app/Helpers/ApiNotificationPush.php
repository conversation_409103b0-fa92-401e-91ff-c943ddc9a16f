<?php

namespace App\Helpers;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Http;
use Log;
use Storage;

class ApiNotificationPush
{
    public static function createNotifications(
        $subscription_ids, 
        $title, 
        $description, 
        $urlClick = null, 
        $largeIcon = null, 
        $smallIcon = null
    ) {
        $baseUrl = env('NOTIFICATION_PUSH_API_URL');

        $response = Http::withToken(env('NOTIFICATION_PUSH_API_TOKEN'))->post($baseUrl . 'v1/notifications', [
            "app_id" => env('NOTIFICATION_PUSH_APP_ID'),
            "contents" => ["en" => $description],
            "headings" => ["en" => $title],
            "url" => $urlClick,
            "large_icon" => $largeIcon,
            "small_icon" => $smallIcon,
            "include_subscription_ids" => $subscription_ids
        ]);
        return $response->json();
    }

    public static function getNotification($notificationPushId) {
        $baseUrl = env('NOTIFICATION_PUSH_API_URL');
        $response = Http::withToken(env('NOTIFICATION_PUSH_API_TOKEN'))
                    ->get($baseUrl . 'v1/notifications/'. $notificationPushId .'?app_id='.env('NOTIFICATION_PUSH_APP_ID'));
        return $response->json();
    }
}

