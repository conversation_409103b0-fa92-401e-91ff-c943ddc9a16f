<?php

namespace App\Helpers;

use App\Models\System;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class SystemHelper
{
    public static function get()
    {
        return System::all();
    }

    public static function get_visibles()
    {
        return System::where('visible_screen', true)->get();
    }

    public static function getOne($code_name)
    {
        $parameter = System::where('code_name', $code_name)->first();
        if (!$parameter) {
            return response()->json(['error' => 'Parâmetro global não encontrado'], 404);
        }
        return response()->json($parameter);
    }

    public static function getContact()
    {
        $system = self::get();

        return [
            'email' => SystemHelper::findInArrayByCodeName($system, 'EMAIL'),
            'phone_number' => SystemHelper::findInArrayByCodeName($system, 'PHONE_NUMBER'),
        ];
    }

    public static function findInArrayByCodeName($array, $code_name, ?int $expirationDaysForImagens = null)
    {
        foreach ($array as $item) {
            if ($item->code_name == $code_name) {
                if ($item->type == 'image' && $item->value != null && $item->value != '' && Storage::disk('s3')->exists($item->value)) {
                    return Storage::disk('s3')->temporaryUrl($item->value, $expirationDaysForImagens ? Carbon::now()->addDays($expirationDaysForImagens) 
                                                                            : Carbon::now()->addMinutes(60));
                }
                return $item->value;
            }
        }
        return null;
    }

    public static function getAppLinks()
    {
        $system = self::get();

        return [
            'link_app_android' => $system['LINK_APP_ANDROID'],
            'link_app_ios' => $system['LINK_APP_IOS'],
        ];
    }

    public static function uploadImage($imageBase64, $key, $imageType, $oldPath)
    {

        if ($oldPath != null && $oldPath != '' && Storage::disk('s3')->exists($oldPath)) {
            Storage::disk('s3')->delete($oldPath);
        }

        if ($imageBase64 != null && $imageBase64 != '') {
            $image = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $imageBase64));
            $file_name = $key . '.' . $imageType;
            $file_path = "Visao_Negocio/images/system/parameters/$file_name";
            Storage::disk('s3')->put($file_path, $image);
            return $file_path;
        }
        return null;
    }
}
