<?php

namespace App\Helpers;

use App\Mail\WhatsappFallbackMail;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ApiWhatsapp
{
    /**
     * Inicializa uma nova instância do WhatsApp para um negócio
     */
    public static function initBusinessInstance(string $businessId): array
    {
        try {
            $instanceKey = "VisaoNegocio_" . Str::uuid() . "_{$businessId}";
            $baseUrl = env('WHATSAPP_API_URL');

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->get("{$baseUrl}/instance/init", [
                    'key' => $instanceKey
                ]);

            if ($response->failed()) {
                throw new Exception('Erro ao inicializar instância do WhatsApp, status: ' . $response->status());
            }

            return $response->json();
        } catch (Exception $e) {
            Log::error('Erro ao inicializar instância do WhatsApp: ' . $e->getMessage());
            throw new Exception("Falha ao inicializar instância do WhatsApp: " . $e->getMessage());
        }
    }

    /**
     * Obtém o QR Code em base64 para uma instância
     */
    public static function getInstanceQrCode(string $instanceKey): string
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->get("{$baseUrl}/instance/qrbase64", [
                    'key' => $instanceKey
                ]);

            if ($response->failed()) {
                throw new Exception('Erro ao obter QR Code, status: ' . $response->status());
            }

            $data = $response->json();
            if (isset($data['error']) && $data['error']) {
                throw new Exception($data['message'] ?? 'Erro ao obter QR Code');
            }

            return $data['qrcode'];
        } catch (Exception $e) {
            Log::error('Erro ao obter QR Code: ' . $e->getMessage());
            throw new Exception("Falha ao obter QR Code: " . $e->getMessage());
        }
    }

    /**
     * Verifica o status de uma instância
     */
    public static function checkInstanceStatus(string $instanceKey): array
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->get("{$baseUrl}/instance/info", [
                    'key' => $instanceKey
                ]);

            if ($response->failed()) {
                if($response->status() === 403 && $response->json()['error'] && $response->json()['message'] === 'invalid key supplied') {
                    self::logoutInstance($instanceKey);
                    self::deleteInstance($instanceKey);
                    throw new Exception('Chave inválida fornecida.');
                }
                throw new Exception('Erro ao verificar status da instância, status: ' . $response->status());
            }

            return $response->json();
        } catch (Exception $e) {
            if ($e->getMessage() === 'Chave inválida fornecida.') {
                return ['success' => false, 'message' => 'Chave inválida fornecida.'];
            }
            Log::error('Erro ao verificar status da instância: ' . $e->getMessage());
            throw new Exception("Falha ao verificar status da instância: " . $e->getMessage());
        }
    }

    /**
     * Desconecta uma instância
     */
    public static function logoutInstance(string $instanceKey): array
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');
            $queryParams = http_build_query(['key' => $instanceKey]);
            $url = "{$baseUrl}/instance/logout?{$queryParams}";

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->delete($url);

            if ($response->failed()) {
                $payload = $response->json();
                // Verifica se o erro é 403 com a mensagem específica de chave inválida
                if ($response->status() === 403 && isset($payload['error']) && $payload['error'] === true &&
                    ($payload['message'] === 'invalid key supplied' || $payload['message'] === "phone isn't connected")
                ) {
                    // Retorna sucesso se a chave for inválida ao deslogar
                    return ['success' => true, 'message' => 'Instância já desconectada ou chave inválida.'];
                }
                if ($response->status() === 401 && isset($payload['error']) && $payload['error'] === true && $payload['message'] === "phone isn't connected") {
                    // Retorna sucesso se a chave for inválida ao deslogar
                    return ['success' => true, 'message' => 'Instância já desconectada ou chave inválida.'];
                }
                throw new Exception('Erro ao desconectar instância, status: ' . $response->status());
            }

            return $response->json();
        } catch (Exception $e) {
            Log::error('Erro ao desconectar instância: ' . $e->getMessage());
            throw new Exception("Falha ao desconectar instância: " . $e->getMessage());
        }
    }

    /**
     * Exclui uma instância do WhatsApp
     */
    public static function deleteInstance(string $instanceKey): array
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');
            $url = "{$baseUrl}/instance/delete";
            $queryParams = http_build_query(['key' => $instanceKey]);

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->delete($url . '?' . $queryParams);

            if ($response->failed()) {
                $payload = $response->json();
                 // Verifica se o erro é 403 com a mensagem específica de chave inválida
                if ($response->status() === 403 && isset($payload['error']) && $payload['error'] === true && $payload['message'] === 'invalid key supplied') {
                    // Retorna sucesso se a chave for inválida ao deletar
                    return ['success' => true, 'message' => 'Instância já excluída ou chave inválida.'];
                }
                throw new Exception('Erro ao excluir instância, status: ' . $response->status());
            }

            return $response->json();
        } catch (Exception $e) {
            Log::error('Erro ao excluir instância: ' . $e->getMessage());
            throw new Exception("Falha ao excluir instância: " . $e->getMessage());
        }
    }

    /**
     * Lista todas as instâncias ativas
     */
    public static function listInstances(): array
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->get("{$baseUrl}/instance/list", [
                    'active' => true
                ]);

            if ($response->failed()) {
                throw new Exception('Erro ao listar instâncias, status: ' . $response->status());
            }

            return $response->json();
        } catch (Exception $e) {
            Log::error('Erro ao listar instâncias: ' . $e->getMessage());
            throw new Exception("Falha ao listar instâncias: " . $e->getMessage());
        }
    }

/**
     * Envia um código de validação genérico por WhatsApp.
     *
     * @param string $instanceKey A chave da instância do WhatsApp.
     * @param string $userPhone O número de telefone do usuário.
     * @param string $code O código de validação.
     * @param string|null $businessName O nome do negócio (opcional, para o cabeçalho da mensagem).
     * @return array Resposta da API de envio.
     * @throws Exception Se houver falha no envio ou configuração.
     */
    public static function sendValidationCode(string $instanceKey, string $userPhone, string $code, ?string $businessName = null): array
    {
        if (empty($instanceKey)) {
            Log::error('instanceKey não foi fornecida para sendValidationCode.');
            throw new Exception('Configuração da API do WhatsApp incompleta para enviar código de validação: instanceKey ausente.');
        }

        $title = "Código de Validação";
        // A tarefa especifica "Visão do Negócio" na mensagem.
        // O $businessName (se fornecido) será usado no cabeçalho da mensagem pelo sendMessageWithInstance.
        // Se $businessName for null, config('app.name') será usado pelo sendMessageWithInstance.
        $description = "Seu código de validação do Visão do Negócio é: {$code}";

        // Opções para buildMessage, se necessário. Por exemplo, para não ter assinaturas.
        // A tarefa não especifica, então usaremos o padrão.
        $options = [];

        try {
            // O método sendMessageWithInstance já lida com a formatação do telefone e tentativas.
            return self::sendMessageWithInstance(
                $instanceKey,
                $userPhone,
                $title,
                $description,
                $businessName,
                $options
            );
        } catch (Exception $e) {
            // sendMessageWithInstance já faz log, mas podemos adicionar um contexto específico aqui.
            Log::error("Erro específico ao tentar enviar código de validação para o número {$userPhone} usando a instância {$instanceKey}: " . $e->getMessage());
            // Re-throw a exceção para que o chamador possa lidar com ela.
            throw $e;
        }
    }

    /**
     * Envia um código de validação de cashback por WhatsApp.
     *
     * @param string $instanceKey A chave da instância do WhatsApp.
     * @param string $userPhone O número de telefone do usuário.
     * @param string $code O código de validação.
     * @param string $businessName O nome do negócio.
     * @param float $totalAmount O valor total do cashback.
     * @return array Resposta da API de envio.
     * @throws Exception Se houver falha no envio ou configuração.
     */
    public static function sendCashbackValidationCode(string $instanceKey, string $userPhone, string $code, string $businessName, float $totalAmount): array
    {
        if (empty($instanceKey)) {
            Log::error('instanceKey não foi fornecida para sendCashbackValidationCode.');
            throw new Exception('Configuração da API do WhatsApp incompleta para enviar código de validação de cashback: instanceKey ausente.');
        }

        $title = "Código de Validação de Cashback";
        
        // Monta a descrição incluindo o valor total formatado
        $description = "O negócio *{$businessName}* verificou que você possui *R$ " . number_format($totalAmount, 2, ',', '.') . "* em cashback disponível e está solicitando a validação para utilizá-lo.\n\n";
        $description .= "Seu código de validação é: *{$code}*\n\n";
        $description .= "⚠️ *Importante:*\n";
        $description .= "• Este código é válido por apenas *10 minutos*\n";
        $description .= "• Deve dar baixa exclusivamente no estabelecimento onde foi solicitado\n";
        $description .= "• Após informar o código, seu cashback será automaticamente concluído";

        // Opções para buildMessage
        $options = [];

        try {
            return self::sendMessageWithInstance(
                $instanceKey,
                $userPhone,
                $title,
                $description,
                $businessName,
                $options
            );
        } catch (Exception $e) {
            Log::error("Erro ao enviar código de validação de cashback para o número {$userPhone} usando a instância {$instanceKey}: " . $e->getMessage());
            throw $e;
        }
    }
    /**
     * Envia mensagem usando uma instância específica
     */
    public static function sendMessageWithInstance(
        string  $instanceKey,
        string  $userPhone,
        string  $title,
        string  $description,
        ?string $businessName = null,
        array   $options = [],
        int     $attempts = 0
    ): array
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');

            if (!isset($businessName)) {
                $businessName = config('app.name');
            }

            $userPhone = self::formatPhone($userPhone, $attempts > 0);
            $message = self::buildMessage($title, $description, $businessName, $options);

            $url = "{$baseUrl}/message/text";
            $queryParams = http_build_query([
                'key' => $instanceKey
            ]);

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->post($url . '?' . $queryParams, [
                    "id" => $userPhone,
                    "message" => $message
                ]);

            if ($response->failed()) {
                if ($response->status() === 400 && $response->json()['error'] && $response->json()['message'] === 'DDD inválido.') {
                    throw new Exception('Número de telefone inválido, verifique o DDD.');
                }
                if ($response->status() === 400 && $response->json()['error'] && $response->json()['message'] === 'no account exists') {
                    throw new Exception('Nenhum whatsapp encontrado para o número informado.');
                }
                if ($response->status() === 401 && $response->json()['error'] && $response->json()['message'] === "phone isn't connected") {
                    //logout
                    self::logoutInstance($instanceKey);
                    self::deleteInstance($instanceKey);
                    throw new Exception('Whatsapp não conectado.');
                }
                if ($response->status() === 403 && $response->json()['error'] && $response->json()['message'] === "invalid key supplied") {
                    //logout
                    self::logoutInstance($instanceKey);
                    self::deleteInstance($instanceKey);
                    throw new Exception('Whatsapp não conectado.');
                }
                throw new Exception('Erro ao enviar mensagem via WhatsApp, status: ' . $response->status());
            }

            $payload = $response->json();

            // Se houver erro, tentar reenviar com o 9 antes
            if (isset($payload['error']) && $payload['error'] === true) {
                if ($payload['message'] === 'Error: no account exists' && $attempts === 0) {
                    return self::sendMessageWithInstance(
                        $instanceKey,
                        $userPhone,
                        $title,
                        $description,
                        $businessName,
                        $options,
                        $attempts + 1
                    );
                } else throw new Exception('Falha no reenvio da mensagem via ZAP: ' . $payload['message']);
            }

            return $payload;
        } catch (Exception $e) {
            Log::error('Erro ao enviar mensagem via WhatsApp: ' . $e->getMessage());
            if ($e->getMessage() === 'Número de telefone inválido, verifique o DDD.') {
                throw $e;
            }
            if ($e->getMessage() === 'Nenhum whatsapp encontrado para o número informado.') {
                throw $e;
            }
            if ($e->getMessage() === 'Whatsapp não conectado.') {
                throw $e;
            }
            throw new Exception("Falha no envio da mensagem via WhatsApp: " . $e->getMessage());
        }
    }

    /**
     * Envia mensagem usando a API global do .env (método legado)
     * @deprecated Use sendMessageWithInstance() instead
     */
    public static function sendMessage(
        string  $userPhone,
        string  $title,
        string  $description,
        ?string $businessName = null,
        ?string $fallBackEmail = null,
        array   $options = [],
        int     $attempts = 0)
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');
            $queryParams = http_build_query([
                'key' => env('WHATSAPP_API_KEY')
            ]);

            if (!isset($businessName))
                $businessName = config('app.name');

            $userPhone = self::formatPhone($userPhone, $attempts > 0);
            $message = self::buildMessage($title, $description, $businessName, $options);

            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->post($baseUrl . "/message/text?$queryParams", [
                    "id" => $userPhone,
                    "message" => $message,
                ]);

            if ($response->failed()) {
                throw new Exception('Erro ao enviar mensagem via WhatsApp, status: ' . $response->status());
            }

            $payload = $response->json();

            // Se houver erro, tentar reenviar com o 9 antes
            if (isset($payload['error']) && $payload['error'] === true) {
                if ($payload['message'] === 'Error: no account exists' && $attempts === 0) {
                    return self::sendMessage(
                        $userPhone,
                        $title,
                        $description,
                        $businessName,
                        $fallBackEmail,
                        $options,
                        $attempts + 1
                    );
                } else {
                    if ($fallBackEmail) {
                        $message = self::buildMessage($title, $description, $businessName, [
                            'noSignatures' => true,
                            'noHeader' => true,
                            'noTitle' => true
                        ]);
                        Mail::to($fallBackEmail)->send(new WhatsappFallbackMail(
                            $title,
                            $message,
                            $businessName,
                        ));
                        return;
                    }
                }
            }

            return $response->json();
        } catch (Exception $e) {
            Log::error('Erro ao enviar mensagem via WhatsApp: ' . $e->getMessage());
            throw new Exception("Falha no envio da mensagem via Whatsapp: " . $e->getMessage());
        }
    }

    public static function formatPhone(string $userPhone, bool $addNineDigit = false): string
    {
        $cleanUserPhone = preg_replace('/[^0-9+]/', '', $userPhone);

        if (empty($cleanUserPhone)) {
            throw new Exception('Número de telefone inválido!');
        }

        if (strpos($cleanUserPhone, '+55') === 0) {
            $cleanUserPhone = substr($cleanUserPhone, 1);
        }

        if (strlen($cleanUserPhone) < 12) {
            $cleanUserPhone = "55$cleanUserPhone";
        }

        $codes = substr($cleanUserPhone, 0, 4); // +55 DDD
        $numberDigits = substr($cleanUserPhone, 4);

        if (strlen($numberDigits) === 9) {
            $numberDigits = substr($numberDigits, 1);
        }

        if ($addNineDigit && strlen($numberDigits) === 8) {
            $numberDigits = '9' . $numberDigits;
        }

        return $codes . $numberDigits;
    }

    public static function buildMessage(
        string $title,
        string $description,
        string $businessName,
        array  $options = []): string
    {
        if (empty(trim($title)) && empty($options['noHeader']))
            throw new Exception('O título não pode ser vazio.');

        if (empty(trim($description))) {
            throw new Exception('A descrição não pode ser vazia.');
        }

        $businessHeader = "*$businessName*";
        $formattedTitle = "*" . ucwords($title) . "*";

        $msgParts = [];

        /*if (empty($options['noHeader'])) {
            $msgParts[] = $businessHeader;
        }*/

        if (empty($options['noTitle'])) {
            $msgParts[] = $formattedTitle;
        }

        $msgParts[] = $description;

        if (empty($options['noSignatures'])) {
            $msgParts[] = "Essa é uma mensagem automática, mas a gente aqui tá atento a cada palavra sua!";
        }

        return implode("\n\n", $msgParts);
    }

    /**
     * Valida se o WhatsApp está conectado para uma instância específica
     *
     * @param string $instanceKey A chave da instância
     * @return array Array com 'connected' (bool) e 'message' (string)
     */
    public static function validateInstanceConnection(string $instanceKey): array
    {
        try {
            $baseUrl = env('WHATSAPP_API_URL');

            // Tenta verificar o status da instância para validar a conexão
            $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
                ->get("{$baseUrl}/instance/info", [
                    'key' => $instanceKey
                ]);

            // Verifica os mesmos códigos de erro que indicam desconexão no sendMessageWithInstance
            if ($response->failed()) {
                $payload = $response->json();
                
                // Status 401 com mensagem "phone isn't connected"
                if ($response->status() === 401 &&
                    isset($payload['error']) && $payload['error'] === true &&
                    isset($payload['message']) && $payload['message'] === "phone isn't connected") {
                    
                    // Executa as mesmas ações do sendMessageWithInstance
                    self::logoutInstance($instanceKey);
                    self::deleteInstance($instanceKey);
                    
                    return [
                        'connected' => false,
                        'message' => 'WhatsApp não conectado.',
                        'error_code' => 401,
                        'error_type' => 'phone_not_connected'
                    ];
                }

                // Status 403 com mensagem "invalid key supplied"
                if ($response->status() === 403 &&
                    isset($payload['error']) && $payload['error'] === true &&
                    isset($payload['message']) && $payload['message'] === 'invalid key supplied') {
                    
                    // Executa as mesmas ações do sendMessageWithInstance
                    self::logoutInstance($instanceKey);
                    self::deleteInstance($instanceKey);
                    
                    return [
                        'connected' => false,
                        'message' => 'WhatsApp não conectado.',
                        'error_code' => 403,
                        'error_type' => 'invalid_key'
                    ];
                }

                // Outros erros de falha na requisição
                return [
                    'connected' => false,
                    'message' => 'Erro ao verificar conexão do WhatsApp.',
                    'error_code' => $response->status(),
                    'error_type' => 'request_failed'
                ];
            }

            // Se chegou até aqui, a requisição foi bem sucedida
            $payload = $response->json();
            
            // Verifica se há erro na resposta mesmo com status 200
            if (isset($payload['error']) && $payload['error'] === true) {
                return [
                    'connected' => false,
                    'message' => $payload['message'] ?? 'Erro desconhecido ao verificar conexão.',
                    'error_code' => null,
                    'error_type' => 'api_error'
                ];
            }

            // WhatsApp está conectado
            return [
                'connected' => true,
                'message' => 'WhatsApp conectado com sucesso.',
                'instance_info' => $payload
            ];

        } catch (Exception $e) {
            Log::error('Erro ao validar conexão da instância do WhatsApp: ' . $e->getMessage());
            
            return [
                'connected' => false,
                'message' => 'Falha ao verificar conexão do WhatsApp: ' . $e->getMessage(),
                'error_code' => null,
                'error_type' => 'exception'
            ];
        }
    }

}
