<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class ApiUser
{
    public static function get()
    {
        try {
            $user = session('AUTH_USER');

            if(!is_null($user)) { return $user; }
            if(is_null(session('API_TOKEN'))) { return null; }

            return self::getUser();
        } catch(\Exception $e) {
            return null;
        }
    }

    public static function update()
    {
        return self::getUser();
    }

    private static function getUser()
    {
        $url = env('API_URL');

        $response = Http::withToken(session('API_TOKEN'))->get("$url/api/user");

        if($response->successful()) {
            session()->forget('AUTH_USER');
            session()->put('AUTH_USER', $response->json());
            return $response->json();
        } else {
            return null;
        }
    }

    public static function logged()
    {
        return self::get() != null;
    }

    public static function notLogged()
    {
        return !self::logged();
    }

    public static function hasPermission($permission)
    {
        $user = Auth::user();

        $authorizedPlanFunctions = $user->employee->businessSelected->plan->authorizedFunctions ?? [];
        $authorizedUserFunctions = $user->employee->authorizedFunction ?? [];

        $hasPlanFunction = ($authorizedPlanFunctions[$permission] ?? false);
        $hasUserFunction = ($authorizedUserFunctions[$permission] ?? false);

        return $hasPlanFunction && $hasUserFunction;
    }

    public static function hasPlanFunction($permission)
    {
        $user = Auth::user();
        $authorizedPlanFunctions = $user->employee->businessSelected->plan->authorizedFunctions ?? [];

        return ($authorizedPlanFunctions[$permission] ?? false);
    }

    public static function hasUserFunction($permission)
    {
        $user = Auth::user();
        $authorizedUserFunctions = $user->employee->authorizedFunction ?? [];

        return ($authorizedUserFunctions[$permission] ?? false);
    }
}
