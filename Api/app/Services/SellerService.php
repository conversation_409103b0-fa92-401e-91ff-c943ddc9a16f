<?php

namespace App\Services;

use App\Mail\SellerResetPasswordMail;
use App\Mail\SellerWelcomeMail;
use App\Models\Seller;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SellerService
{
    public function __construct()
    {

    }

    public function getAllPaginated(int $perPage, ?string $search = '', int $page = 1, string $sellerType = 'all')
    {
        $query = Seller::query();
        $query->join('users', 'sellers.user_id', '=', 'users.id')
            ->select('users.name as name', 'sellers.*');

        if ($search) {
            $query->where('name', 'like', "%$search%");
        }

        switch ($sellerType) {
            case 'main':
                $query->whereNull('sellers.registered_by');
                break;
            case 'child':
                $query->whereNotNull('sellers.registered_by');
                break;
        }

        $query->orderBy('name');

        return $perPage <= 0 ?
            $query->get() :
            $query->paginate(
                $perPage,
                ['*'],
                'page',
                $page
            );
    }

    public function getAllSellers(int $perPage, ?string $search = '', int $page = 1)
    {
        return $perPage <= 0 ?
            Seller::query()->join('users', 'sellers.user_id', '=', 'users.id')
                ->select('users.name as name', 'sellers.*')
                ->where('users.name', 'like', "%$search%")
                ->orderBy('name')
                ->get() :
            Seller::query()->join('users', 'sellers.user_id', '=', 'users.id')
                ->select('users.name as name', 'sellers.*')
                ->where('users.name', 'like', "%$search%")
                ->orderBy('name')
                ->paginate(
                    $perPage,
                    ['*'],
                    'page',
                    $page
                );
    }

    //getAllSellersForSeller
    public function getAllSellersForSeller(int $perPage, ?string $search = '', int $page = 1)
    {
        $user = auth()->user();
        $seller = $user->seller;
        return $perPage <= 0 ?
            Seller::query()->join('users', 'sellers.user_id', '=', 'users.id')
                ->select('users.name as name', 'sellers.*')
                ->where('users.name', 'like', "%$search%")
                ->where('sellers.registered_by', $seller->id)
                ->orderBy('name')
                ->get() :
            Seller::query()->join('users', 'sellers.user_id', '=', 'users.id')
                ->select('users.name as name', 'sellers.*')
                ->where('users.name', 'like', "%$search%")
                ->where('sellers.registered_by', $seller->id)
                ->orderBy('name')
                ->paginate(
                    $perPage,
                    ['*'],
                    'page',
                    $page
                );
    }

    public function store(
        string  $name,
        string  $email,
        Carbon  $birthDate,
        string  $cpf,
        ?string $gender,
        string  $phoneNumber1,
        ?string $phoneNumber2,
        string  $neighborhood,
        string  $street,
        string  $number,
        string  $city,
        string  $state,
        string  $cep,
        ?string $complement,
        bool    $registerSellers,
        float   $percentagePerSale,
        bool    $emailVerified,
        ?int    $registeredBy = null,
    ): User
    {
        $userData = [
            'name' => $name,
            'email' => $email,
            'login_type' => 'seller',
            'birth_date' => $birthDate->toDate(),
            'cpf' => $cpf,
            'gender' => $gender == 'other' ? null : $gender,
            'phone_number_1' => $phoneNumber1,
            'phone_number_2' => $phoneNumber2,
            'neighborhood' => $neighborhood,
            'street' => $street,
            'number' => $number,
            'city' => $city,
            'state' => $state,
            'cep' => $cep,
            'complement' => $complement,
        ];

        if ($emailVerified) {
            $userData['email_verified_at'] = now();
        }

        $sellerData = [
            'register_sellers' => $registerSellers,
            'percentage_per_sale' => $percentagePerSale,
        ];

        if ($registeredBy) {
            $sellerData['registered_by'] = $registeredBy;
        }

        $user = User::withTrashed()->where('cpf', $cpf)->first();

        $existingUser = $user != null;
        $oldEmail = $existingUser ? $user->email : null;

        $shouldGeneratePassword = false;
        $randomPassword = null;

        if ($user) {
            if ($user->trashed()) {
                $user->restore();
                $shouldGeneratePassword = true;
            }

            $user->update($userData);

            $seller = $user->seller()->withTrashed()->first();
            if ($seller) {
                if ($seller->trashed()) {
                    $seller->restore();
                    $seller->update($sellerData);
                } else {
                    $seller->update($sellerData);
                }
            } else {
                $user->seller()->create($sellerData);
            }
        } else {
            $shouldGeneratePassword = true;
            $randomPassword = Str::random(10);
            $userData['password'] = bcrypt($randomPassword);

            $user = User::create($userData);
            $user->seller()->create($sellerData);
        }

        $emailChanged = $existingUser && $oldEmail != $email;

        if ($emailVerified) {
            $user->update(['email_verified_at' => now()]);
        } elseif ($emailChanged) {
            $user->update(['email_verified_at' => null]);
        }

        if ($shouldGeneratePassword) {
            if (!$randomPassword) {
                $randomPassword = Str::random(10);
                $user->update(['password' => bcrypt($randomPassword)]);
            }
            // Envia o e-mail de boas-vindas com a senha
            Mail::to($user->email)->send(new SellerWelcomeMail($user, $randomPassword));
        }

        return $user;
    }

    public function update(
        Seller  $seller,
        string  $name,
        string  $email,
        Carbon  $birthDate,
        string  $cpf,
        ?string $gender,
        string  $phoneNumber1,
        ?string $phoneNumber2,
        string  $neighborhood,
        string  $street,
        string  $number,
        string  $city,
        string  $state,
        string  $cep,
        ?string $complement,
        bool    $registerSellers,
        float   $percentagePerSale,
        bool    $emailVerified,
        string  $oldEmail,
        bool $resetPassword = false,
    ): User
    {
        $userData = [
            'name' => $name,
            'email' => $email,
            'login_type' => 'seller',
            'birth_date' => $birthDate->toDate(),
            'cpf' => $cpf,
            'gender' => $gender == 'other' ? null : $gender,
            'phone_number_1' => $phoneNumber1,
            'phone_number_2' => $phoneNumber2,
            'neighborhood' => $neighborhood,
            'street' => $street,
            'number' => $number,
            'city' => $city,
            'state' => $state,
            'cep' => $cep,
            'complement' => $complement,
        ];

        if ($email !== $oldEmail) {
            $userData['email_verified_at'] = $emailVerified ? now() : null;
        }

        $sellerData = [
            'register_sellers' => $registerSellers,
            'percentage_per_sale' => $percentagePerSale,
        ];

        $seller->user()->update($userData);
        $seller->update($sellerData);

        if ($resetPassword) {
            $randomPassword = Str::random(10);
            $seller->user()->update(['password' => bcrypt($randomPassword)]);

            Mail::to($seller->user->email)->send(new SellerResetPasswordMail($seller->user, $randomPassword));
        }

        return $seller->user;
    }

    public function delete($id): JsonResponse
    {
        try {
            $seller = Seller::findOrFail($id);

            $user = $seller->user;

            if ($user->typesCount() == 1 && $user->hasSeller()) {
                $user->delete();
            }

            $seller->delete();

            return response()->json(['msg' => 'Seller deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
        }
    }


    public function findByCpf(string $cpf): ?Seller
    {
        return Seller::whereHas('user', function ($query) use ($cpf) {
            $query->where('cpf', $cpf);
        })->first();
    }
}
