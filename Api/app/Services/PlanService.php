<?php

namespace App\Services;

use App\Http\Controllers\Api\EmployeesController;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Builder;

class PlanService
{
    public function __construct()
    {

    }

    public function getAll(int $perPage, ?string $search)
    {
        return ($perPage > 0
            ? Plan::when($search, function (Builder $query, string $search) {
                $query->orWhere('name', 'like', "%$search%")
                    ->orWhere('description', 'like', "%$search%")
                    ->orWhere('value', 'like', "%$search%");
            })->orderBy('name')->paginate($perPage)
            : Plan::orderBy('name')->get()
        );
    }

    public function getActivePlans(int $perPage, ?string $search = '', int $page = 1)
    {
        return $perPage <= 0 ?
            Plan::when($search, function (Builder $query, string $search) {
                $query->where('name', 'like', "%$search%");
            })->orderBy('name')->get() :
            Plan::when($search, function (Builder $query, string $search) {
                $query->where('name', 'like', "%$search%");
            })->orderBy('name')->paginate(
                $perPage,
                ['*'],
                'page',
                $page
            );
    }

    function store(
        string $name,
        ?string $description,
        float $value,
        bool $active,
        bool $publiclyVisible,
        ?string $paymentSystemTag,
        array $authorizedFunctions
    ): Plan
    {
        $plan = Plan::create([
            'name' => $name,
            'description' => $description,
            'value' => $value,
            'active' => $active,
            'publicly_visible' => $publiclyVisible,
            'payment_system_tag' => $paymentSystemTag,
        ]);

        $plan->authorizedFunctions()->create($authorizedFunctions);

        return $plan;
    }

    public function update(
        Plan $plan,
        string $name,
        ?string $description,
        float $value,
        bool $active,
        bool $publiclyVisible,
        ?string $paymentSystemTag,
        array $authorizedFunctions
    ): Plan
    {
        $plan->update([
            'name' => $name,
            'description' => $description,
            'value' => $value,
            'active' => $active,
            'publicly_visible' => $publiclyVisible,
            'payment_system_tag' => $paymentSystemTag,
        ]);

        $plan->authorizedFunctions()->update($authorizedFunctions);
        $employeesController = new EmployeesController();
        $employeesController->updatePermissionsFromPlan($plan->id, null, null);
        return $plan;
    }

    public
    function delete(Plan $plan): Plan
    {
        $plan->delete();
        $plan->authorizedFunctions()->delete();
        return $plan;
    }


    public function getByTag(string $tag): Plan
    {
        return Plan::where('payment_system_tag', $tag)->first();
    }
}
