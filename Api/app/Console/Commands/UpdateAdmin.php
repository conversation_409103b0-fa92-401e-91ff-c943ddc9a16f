<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\Seller;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class UpdateAdmin extends Command
{
    /**
     * O nome e a assinatura do comando.
     *
     * @var string
     */
    protected $signature = 'admin:update-permissions';

    /**
     * A descrição do comando.
     *
     * @var string
     */
    protected $description = 'Atualiza as permissões do usuário admin';

    /**
     * Executa o comando.
     *
     * @return int
     */
    public function handle(): void
    {
        $admins = Employee::where('admin', 1)->get();

        if ($admins->isEmpty()) {
            $this->error('Nenhum admin encontrado');
            return;
        }

        foreach ($admins as $admin) {
            // Atualiza as permissões do admin
            $admin->authorizedFunction()->updateOrCreate(
                ['employee_id' => $admin->id],
                [
                    'employees' => true,
                    'settings' => true,
                    'release_all_business' => true,
                    'manage_business_profile' => true,
                    'plans' => true,
                    'sellers' => true,
                    'topics' => true,
                    'default_topics' => true,
                    'update_topics' => true,
                    'forms' => true,
                    'default_forms' => true,
                    'update_forms' => true,
                    'update_business' => true,
                    'cashback' => true,
                    'update_cashback' => true,
                    'clients' => true,
                    'update_clients' => true,
                    'evaluations' => true,
                    'notifications' => true,
                    'update_notifications' => true,
                    'dashboard' => true,
                    'parameters' => true,
                    'site_notifications' => true,
                    'update_site_notifications' => true,
                ]
            );

            // Atualiza as permissões de configuração do admin
            $admin->authorizedSettingsFunction()->updateOrCreate(
                ['employee_id' => $admin->id],
                [
                    'contact' => true,
                    'system_parameters' => true,
                ]
            );
        }

        $this->info('Permissões dos admins atualizadas com sucesso!');
        return;
    }
}