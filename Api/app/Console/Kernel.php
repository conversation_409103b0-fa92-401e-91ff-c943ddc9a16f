<?php

namespace App\Console;

use App\Jobs\ExpireCashbackJob;
use App\Jobs\ExpireEvaluationsJob;
use App\Jobs\SendBusinessApprovalNotification;
use App\Jobs\SendBusinessCancelNotification;
use App\Jobs\SendBusinessExpireNotification;
use App\Jobs\SendBusinessReverseCancelNotification;
use App\Jobs\SendCashbackCompleteNotification;
use App\Jobs\SendCashbackCreateNotification;
use App\Jobs\SendCashbackExpireNotification;
use App\Jobs\SendEvaluationLinkExpireNotification;
use App\Jobs\SendEvaluationRespondedNotification;
use App\Jobs\SendNotificationJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->job(new ExpireEvaluationsJob())->everyFiveMinutes();
        $schedule->job(new ExpireCashbackJob())->everyFiveMinutes();
        $schedule->job(new SendNotificationJob())->hourly();
        $schedule->job(new SendCashbackExpireNotification())->everyFiveMinutes();
        $schedule->job(new SendEvaluationLinkExpireNotification())->everyFiveMinutes();
        $schedule->job(new SendCashbackCreateNotification())->everyFiveMinutes();
        $schedule->job(new SendCashbackCompleteNotification())->everyFiveMinutes();
        $schedule->job(new SendBusinessApprovalNotification())->everyFiveMinutes();
        $schedule->job(new SendBusinessExpireNotification())->hourly();
        $schedule->job(new SendBusinessCancelNotification())->everyFiveMinutes();
        $schedule->job(new SendBusinessReverseCancelNotification())->everyFiveMinutes();
        $schedule->job(new SendEvaluationRespondedNotification())->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
