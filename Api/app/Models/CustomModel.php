<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CustomModel extends Model
{
    public function getCreatedAtAttribute($value): string
    {
        return Carbon::parse($value)
                     ->setTimezone('America/Sao_Paulo')
                     ->format('Y-m-d H:i:s');   
    }
    
    public function getUpdatedAtAttribute($value): string
    {
        return Carbon::parse($value)
                     ->setTimezone('America/Sao_Paulo')
                     ->format('Y-m-d H:i:s');
    }

    public function getDeletedAtAttribute($value): string|null
    {
        return $value ? Carbon::parse($value)
                            ->setTimezone('America/Sao_Paulo')
                            ->format('Y-m-d H:i:s')    
                      : null;
    }
}