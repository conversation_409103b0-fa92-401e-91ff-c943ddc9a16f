<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Plan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'value',
        'active',
        'publicly_visible',
        'payment_system_tag',
    ];

    public function authorizedFunctions()
    {
        return $this->hasOne(AuthorizedFunctionsPlans::class);
    }

    public function businesses()
    {
        return $this->hasMany(Business::class);
    }

    public function toArray()
    {
        $array = parent::toArray();

        return [
            ...$array,
            'authorizedFunctions' => $this->authorizedFunctions,
        ];
    }
}
