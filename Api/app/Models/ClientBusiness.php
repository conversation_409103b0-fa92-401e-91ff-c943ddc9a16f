<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClientBusiness extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'client_business';

    protected $primaryKey = ['client_id', 'business_id'];

    public $incrementing = false;

    protected $fillable = [
        'client_id',
        'business_id',
        'created_at',
        'updated_at'
    ];

    public function client()
    {
        return $this->belongsTo(client::class);
    }

    public function business()
    {
        return $this->belongsTo(Business::class);
    }
}
