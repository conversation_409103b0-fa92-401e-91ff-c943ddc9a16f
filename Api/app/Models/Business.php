<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Business extends Model
{
    use HasFactory, SoftDeletes;

    const STATUS_PENDING_APPROVAL = 'pending_approval';
    const STATUS_APPROVED_REGISTRATION = 'approved_registration';
    const STATUS_REJECTED_REGISTRATION = 'rejected_registration';
    const STATUS_PENDING_CANCELLATION = 'pending_cancellation';
    const STATUS_COMPLETED_CANCELLATION = 'completed_cancellation';
    const STATUS_REVERSED_CANCELLATION = 'reversed_cancellation';
    const STATUS_EXPIRED_REGISTRATION = 'expired_registration';

    protected $fillable = [
        'name',
        'description',
        'latitude',
        'longitude',
        'logo',
        'plan_id',
        'seller_id',
        'cnpj',
        'corporate_reason',
        'neighborhood',
        'street',
        'number',
        'city',
        'state',
        'cep',
        'complement',
        'status',
        'url_facebook',
        'url_instagram',
        'url_google',
        'url_linkedin',
    ];

    public function employees(): BelongsToMany
    {
        return $this->belongsToMany(Employee::class, 'employee_business', 'business_id', 'employee_id');
    }

    public function admin(): ?Employee
    {
        return $this->belongsToMany(Employee::class, 'employee_business', 'business_id', 'employee_id')
                    ->wherePivot('admin', true)
                    ->first();
    }

    public function topics(): BelongsToMany
    {
        return $this->belongsToMany(Topic::class, 'business_topic', 'business_id', 'topic_id');
    }

    public function forms(): BelongsToMany
    {
        return $this->belongsToMany(Form::class, 'business_form', 'business_id', 'form_id');
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(Seller::class);
    }

    public function parameters(): HasOne
    {
        return $this->hasOne(BusinessParameters::class);
    }
}
