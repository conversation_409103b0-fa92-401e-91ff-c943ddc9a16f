<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClientImportItem extends Model
{
    protected $fillable = [
        'batch_id',
        'row_number',
        'raw_data',
        'status',
        'error_message',
        'processed_at',
        'client_id'
    ];

    protected $casts = [
        'raw_data' => 'array',
        'processed_at' => 'datetime'
    ];

    // Status possíveis
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    public function batch()
    {
        return $this->belongsTo(ClientImportBatch::class, 'batch_id');
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }
}