<?php

namespace App\Models;

use App\Helpers\SystemHelper;
use App\Http\Resources\Api\UserResource;
use Exception;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Validation\UnauthorizedException;
use Ty<PERSON>\JWTAuth\Contracts\JWTSubject;

#use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'password',
        'birth_date',
        'cpf',
        'gender',
        'phone_number_1',
        'phone_number_2',
        'neighborhood',
        'street',
        'number',
        'city',
        'state',
        'cep',
        'complement',
        'login_type',
        'email_verified_at',
        'last_login_at',
    ];

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function hasEmployee(): bool
    {
        return $this->employee()->exists();
    }

    public function hasAdmin(): bool
    {
        return $this->employee()->exists() && $this->employee->admin;
    }

    public function employee()
    {
        return $this->hasOne(Employee::class, 'user_id', 'id');
    }

    public function hasSeller(): bool
    {
        return $this->seller()->exists();
    }

    public function seller(): HasOne
    {
        return $this->hasOne(Seller::class, 'user_id', 'id');
    }

    public function hasClient(): bool
    {
        return $this->client()->exists();
    }

    public function client(): HasOne
    {
        return $this->hasOne(Client::class, 'user_id', 'id');
    }

    public function typesCount(): int
    {
        return $this->employee()->count() + $this->seller()->count() + $this->client()->count();
    }

    public function toArray(): UserResource
    {
        return UserResource::make($this);
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    public function getUserPriorityType() 
    {
        // Cliente tem prioridade a todos
        if ($this->hasClient())
            return "client";

        // Funcionário tem prioridade a vendedor
        if ($this->hasEmployee())
            return 'employee';

        if ($this->hasSeller())
            return 'seller';
    }

    /**
     * Retorna uma lista de IDs de usuários filtrados conforme os filtros especificados.
     *
     * Essa função aplica uma série de filtros para restringir os usuários com base em critérios como:
     * - Termos de busca (nome, e-mail, CPF, telefone)
     * - Perfil do usuário (cliente, funcionário, vendedor)
     * - Associação a negócios (por ID, nome ou CNPJ)
     * - Permissões específicas do usuário logado (ex: pode ver vendedores, pode liberar todos os negócios)
     * - Admin/Empreendedor (funcionário administrador de negócio ou cliente empreendedor)
     *
     * O usuário logado não é incluído
     *
     * @param array $filters         Array de filtros a serem aplicados. Pode incluir:
     *                               - 'search': string (nome, e-mail, CPF, telefone)
     *                               - 'searchBusiness': string (nome ou CNPJ do negócio)
     *                               - 'profile': string ('client', 'employee', 'seller')
     *                               - 'isAdmin': string ('yes', 'no', 'all')
     *                               - 'isEntrepreneur': string ('yes', 'no', 'all')
     * @param array|null $exceptIds IDs de usuários a serem explicitamente excluídos da busca. 
     *                   Utilizado na lógica de store de SiteNotificationController
     *
     * @return array Lista de IDs de usuários.
     *
     * @throws UnauthorizedException Caso o usuário logado não possua as permissões necessárias
     *                               para aplicar filtros sensíveis como 'searchBusiness',
     *                               'isAdmin' ou 'isEntrepreneur'.
     *
     */

    public static function getUserIdsFiltered(array $filters, ?array $exceptIds = [])
    {
        $userLogged = Auth::user();
        $userLoggedEmployee = $userLogged->employee;
        $exceptIds[] = $userLogged->id; 
        $canSeeSellers = $userLoggedEmployee->authorizedFunction->sellers ?? false;
        $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
        $userLoggedSelectedBusinessId = $userLogged->employee->business_selected_id;
        $adminBusiness = (int) SystemHelper::findInArrayByCodeName(SystemHelper::get(),'ADMIN_BUSINESS_ID');
        $isAdminBusinessSelected = $userLoggedSelectedBusinessId === $adminBusiness;
        $query = User::query()
                ->whereNotIn('id', $exceptIds);

        if (!empty($filters['search'])) {
            $searchTerm = $filters['search'];
            $digitsOnly = preg_replace('/[^0-9]/', '', $searchTerm);

            // Tenta pesquisar por email, telefone (com ou sem o dígito 9), CPF ou Nome
            $query->where(function ($q) use ($searchTerm, $digitsOnly) {
                if (filter_var($searchTerm, FILTER_VALIDATE_EMAIL)) {
                    $q->where('users.email', 'like', "%$searchTerm%");
                } elseif (strlen($digitsOnly) === 11) {
                    $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                    $codes = substr($digitsOnly, 0, 2);
                    $numberDigits = substr($digitsOnly, 2);
                    if (strlen($numberDigits) === 9) {
                        $phoneNumberV2 = $codes . substr($numberDigits, 1);
                    } else if (strlen($numberDigits) === 8) {
                        $phoneNumberV2 = $codes . '9' . $numberDigits;
                    }

                    // Pesquisa primeiro pelo CPF, depois pelo Telefone
                    $q->where('users.cpf', 'like', "$digitsOnly%")
                        ->orWhere('users.phone_number_1', 'like', "%$digitsOnly%")
                        ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                } elseif (strlen($digitsOnly) >= 8 && strlen($digitsOnly) <= 10) {
                    $phoneNumberV2 = ''; // versão do número removendo ou adicionando digito 9
                    $codes = substr($digitsOnly, 0, 2);
                    $numberDigits = substr($digitsOnly, 2);
                    if (strlen($numberDigits) === 9) {
                        $phoneNumberV2 = $codes . substr($numberDigits, 1);
                    } else if (strlen($numberDigits) === 8) {
                        $phoneNumberV2 = $codes . '9' . $numberDigits;
                    }

                    // Pesquisa por número de 8 a 10 dígitos
                    $q->where('users.phone_number_1', 'like', "%$digitsOnly%")
                        ->orWhere('users.phone_number_1', 'like', "%$phoneNumberV2%");
                } else {
                    // Pesquisa por nome
                    $q->where('users.name', 'like', "%$searchTerm%");
                }
            });
        }

        if ($userLoggedEmployee->admin || $userLoggedEmployee->authorizedFunction->release_all_business) {
            if ($userLoggedSelectedBusinessId && !$isAdminBusinessSelected) {
                $query->where(function ($q) use ($userLoggedSelectedBusinessId, $canSeeSellers) {
                    $q->whereHas('employee.businesses', function ($query) use ($userLoggedSelectedBusinessId) {
                        $query->where('businesses.id', $userLoggedSelectedBusinessId);
                    })->orWhereHas('client.businesses', function ($query) use ($userLoggedSelectedBusinessId) {
                        $query->where('businesses.id', $userLoggedSelectedBusinessId);
                    });

                    if ($canSeeSellers)
                        $q->orWhereHas('seller');
                });
            }
        } else {
            $query->where(function ($q) use ($userLoggedSelectedBusinessId, $canSeeSellers) {
                $q->whereHas('employee.businesses', function ($query) use ($userLoggedSelectedBusinessId) {
                    $query->where('businesses.id', $userLoggedSelectedBusinessId);
                })->orWhereHas('client.businesses', function ($query) use ($userLoggedSelectedBusinessId) {
                    $query->where('businesses.id', $userLoggedSelectedBusinessId);
                });

                if ($canSeeSellers)
                    $q->orWhereHas('seller');
            });
        }

        if (!empty($filters['searchBusiness'])) {
            $searchBusiness = $filters['searchBusiness'];
            if (!$canReleaseAllBusinesses || !$isAdminBusinessSelected)
                throw new UnauthorizedException('Você não possui permissão');

            $cnpj = preg_replace('/[^0-9]/', '', $searchBusiness);
            if (preg_match('/^\d{1,14}$/', $cnpj))
                $query->where(function ($q) use ($cnpj) {
                    $q->whereHas('employee.businesses', function ($query) use($cnpj) {
                        $query->where('businesses.cnpj', 'like', "$cnpj%");
                    })->orWhereHas('client.businesses', function ($query) use($cnpj) {
                        $query->where('businesses.cnpj', 'like', "$cnpj%");
                    });
                });
            else $query->where(function ($q) use ($searchBusiness) {
                $q->whereHas('employee.businesses', function ($query) use($searchBusiness) {
                    $query->where('businesses.name','like', "%$searchBusiness%");
                })->orWhereHas('client.businesses', function ($query) use($searchBusiness) {
                    $query->where('businesses.name', 'like', "%$searchBusiness%");
                });
            });
        }

        if (!empty($filters['profile'])) {
            $userProfile = $filters['profile'];
            switch($userProfile) {
                case 'client':
                    $query->whereHas('client');
                    break;
                case 'employee':
                    $query->whereHas('employee');
                    break;
                case 'seller':
                    if (!$canSeeSellers)
                        throw new UnauthorizedException('Você não possui permissão.');

                    $query->whereHas('seller');
                    break;
                default:
                    $query->where(function ($q) {
                        $q->whereHas('client')
                            ->orWhereHas('employee')
                            ->orWhereHas('seller');
                    });
                break;
            }
        }

        if (!empty($filters['isAdmin']) || !empty($filters['isEntrepreneur'])) {
            $isAdmin = $filters['isAdmin'] ?? 'all';
            $isEntrepreneur = $filters['isEntrepreneur'] ?? 'all';
            if (!$canReleaseAllBusinesses)
                throw new UnauthorizedException('Você não tem permissão.');

            if ($isAdmin === 'yes') {
                // Verifica se existe pelo menos um relacionamento que satisfaça
                $query->whereHas('employee.businesses', function($q) use($userLoggedSelectedBusinessId, $isAdminBusinessSelected, $isAdmin) {
                    if (!$isAdminBusinessSelected && $userLoggedSelectedBusinessId) {
                        $q->where('businesses.id', $userLoggedSelectedBusinessId);
                    }
            
                    $q->where('employee_business.admin', true);
                });
            } elseif ($isAdmin === 'no') {
                // Verifica se o funcionário não é admin de NENHUM negócio
                $query->whereDoesntHave('employee.businesses', function($q) use($userLoggedSelectedBusinessId, $isAdminBusinessSelected, $isAdmin) {
                    if (!$isAdminBusinessSelected && $userLoggedSelectedBusinessId) {
                        $q->where('businesses.id', $userLoggedSelectedBusinessId);
                    }
                    $q->where('employee_business.admin', true);
                });
            }

            if ($isEntrepreneur === 'yes') {
                $query->where(function($q) use($userLoggedSelectedBusinessId, $isAdminBusinessSelected) {
                    // se o cliente for também funcionário e for administrador de algum negócio, ele também deve ser considerado Empreendedor
                    $q->whereHas('employee.businesses', function($q) use($userLoggedSelectedBusinessId, $isAdminBusinessSelected) {
                        if(!$isAdminBusinessSelected && $userLoggedSelectedBusinessId)
                            $q->where('businesses.id', $userLoggedSelectedBusinessId);
                        $q->where('employee_business.admin', true);
                    })->orWhereHas('client', function($q) {
                        $q->where('is_entrepreneur', true);
                    });
                });
            } elseif ($isEntrepreneur === 'no') {
                // Usuário não pode ser admin e nem empreendedor na tabela de cliente
                $query->where(function($q) use($userLoggedSelectedBusinessId, $isAdminBusinessSelected) {
                    $q->whereDoesntHave('employee.businesses', function($q) use($userLoggedSelectedBusinessId, $isAdminBusinessSelected) {
                        if (!$isAdminBusinessSelected && $userLoggedSelectedBusinessId) {
                            $q->where('businesses.id', $userLoggedSelectedBusinessId);
                        }
                        $q->where('employee_business.admin', true);
                    })->whereDoesntHave('client', function ($q) {
                        $q->where('is_entrepreneur', true);
                    });
                });
            }
        }

        $userIds = $query->get()->pluck('id')->toArray();
        return $userIds;
    }

    public static function isEmployeeAdminOfBusiness($employee, $business_selected_id, $isAdminBusinessSelected)
    {
        if (!$employee) {
            return false;
        }

        $query = $employee->businesses();
        if ($business_selected_id && !$isAdminBusinessSelected) {
            $query->where('businesses.id', $business_selected_id);
        }
        return $query->wherePivot('admin', true)->exists();
    }
}
