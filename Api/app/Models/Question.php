<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Question extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['evaluation_type', 'topic_id', 'description', 'order', 'business_id'];

    public function topic() : BelongsTo
    {
        return $this->belongsTo(Topic::class)->withTrashed();
    }

    public function business() : BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function evaluationRespones() {
        return $this->hasMany(EvaluationResponse::class);
    }
}
