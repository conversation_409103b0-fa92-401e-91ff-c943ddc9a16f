<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Topic extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['name', 'description', 'default', 'active', 'required'];

    public function questions() : HasMany
    {
        return $this->hasMany(Question::class);
    }

    public function businesses() : BelongsToMany
    {
        return $this->belongsToMany(Business::class, 'business_topic', 'topic_id', 'business_id');
    }

    public function forms() : BelongsToMany
    {
        return $this->belongsToMany(Form::class, 'form_topic', 'topic_id', 'form_id');
    }
}
