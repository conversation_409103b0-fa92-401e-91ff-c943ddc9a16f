<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BusinessForm extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'business_form';

    protected $fillable = [
        'business_id',
        'form_id',
    ];

    public function business() : BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function form() : BelongsTo
    {
        return $this->belongsTo(Form::class);
    }

}
