<?php

namespace App\Models;

use App\Observers\CashbackObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

#[ObservedBy([CashbackObserver::class])]
class Cashback extends Model
{
    use HasFactory;

    protected $table = 'cashbacks';

    protected $fillable = [
        'status',
        'expiration_date',
        'percentage',
        'amount',
        'business_id',
        'client_id',
        'sending_type',
        'creation_type',
        'used_at',
        'notification_expiration_date',
        'notification_created_date',
        'notification_complete_date',
    ];

    public function business() {
        return $this->belongsTo(Business::class);
    }

    public function client() {
        return $this->belongsTo(Client::class);
    }
}
