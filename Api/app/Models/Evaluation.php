<?php

namespace App\Models;

use App\Observers\EvaluationObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[ObservedBy([EvaluationObserver::class])]
class Evaluation extends Model
{
    use HasFactory;

    protected $fillable = [
        'token',
        'anonymous',
        'sending_type',
        'email',
        'number_phone',
        'shipping_date',
        'form_id',
        'shipping_status',
        'expiration_date',
        'business_id',
        'cashback_percentage',
        'amount',
        'cashback_expiration_date',
        'client_id',
        'notified_expiration_at',
        'notified_responded_at',
        'cashback_created_at_send',
        'with_cashback',
    ];

    protected $casts = [
        'shipping_date' => 'datetime:Y-m-d H:i:s',
        'expiration_date' => 'datetime:Y-m-d H:i:s',
        'cashback_expiration_date' => 'datetime:Y-m-d H:i:s',
        'cashback_created_at_send' => 'boolean',
        'with_cashback' => 'boolean',
    ];

    public function responses(): HasMany
    {
        return $this->hasMany(EvaluationResponse::class);
    }

    public function form(): BelongsTo
    {
        return $this->belongsTo(Form::class)->withTrashed();
    }

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
