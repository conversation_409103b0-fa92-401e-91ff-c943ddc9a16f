<?php

namespace App\Models;

use App\Http\Resources\Api\NotificationClientResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationClient extends Model
{
    use HasFactory;

    protected $table = 'notification_clients';

    protected $fillable = [
        'notification_id',
        'client_id',
        'shipping_date',
        'shipping_status'
    ];

    protected $casts = [
        'shipping_date' => 'datetime',
    ];

    public $timestamps = false;

    public function client() {
        return $this->belongsTo(Client::class);
    }

    public function notification() {
        return $this->belongsTo(Notification::class);
    }

    public function toArray(): NotificationClientResource
    {
        return NotificationClientResource::make($this);
    }

}
