<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BusinessTopic extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'business_topic';

    protected $fillable = [
        'business_id',
        'topic_id',
    ];

    public function business() : BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function topic() : BelongsTo
    {
        return $this->belongsTo(Topic::class);
    }

}
