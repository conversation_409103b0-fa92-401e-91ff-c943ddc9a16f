<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class AuthorizedSettingsFunction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['contact', 'employee_id', 'system_parameters'];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id')->withTrashed();
    }

    public function havePermission(Request $request)
    {
        if($request->is('api/settings/contact/*') || $request->is('api/settings/contact')) {
            return $this->contact;
        } else {
            return true;
        }
    }
}
