<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AuthorizedFunctionsPlans extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'employees',
        'manage_business_profile',
        'topics',
        'update_topics',
        'forms',
        'update_forms',
        'cashback',
        'update_cashback',
        'clients',
        'update_clients',
        'evaluations',
        'notifications',
        'update_notifications',
        'dashboard',
        'parameters',
        'site_notifications',
        'update_site_notifications',
        'total_employees',
        'total_email_sends',
        'email_sends_type',
        'email_sends_days',
        'total_whatsapp_sends',
        'whatsapp_sends_type',
        'whatsapp_sends_days',
        'email_sending',
        'whatsapp_sending',
    ];

    protected $casts = [
        'employees' => 'boolean',
        'manage_business_profile' => 'boolean',
        'topics' => 'boolean',
        'update_topics' => 'boolean',
        'forms' => 'boolean',
        'update_forms' => 'boolean',
        'cashback' => 'boolean',
        'update_cashback' => 'boolean',
        'clients' => 'boolean',
        'update_clients' => 'boolean',
        'evaluations' => 'boolean',
        'notifications' => 'boolean',
        'update_notifications' => 'boolean',
        'dashboard' => 'boolean',
        'parameters' => 'boolean',
        'site_notifications' => 'boolean',
        'update_site_notifications' => 'boolean',
        'total_employees' => 'integer',
        'total_email_sends' => 'integer',
        'email_sends_days' => 'integer',
        'total_whatsapp_sends' => 'integer',
        'whatsapp_sends_days' => 'integer',
        'email_sending' => 'boolean',
        'whatsapp_sending' => 'boolean',
    ];

    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }
}
