<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemLog extends Model
{
    use HasFactory;

    const LOG_LEVEL_CRITICAL = 'critical';
    const LOG_LEVEL_DEBUG = 'debug';
    const LOG_LEVEL_ERROR = 'error';
    const LOG_LEVEL_INFO = 'INFO';
    const LOG_LEVEL_WARNING = 'warning';

    protected $table = 'system_logs';

    protected $fillable = [
        'level',
        'origin',
        'description',
        'hasError',
    ];

    public static function log(
        string $level,
        string $description,
        string $origin,
        ?bool $hasError = null
    ): SystemLog {
        // Determina se é erro baseado no nível
        $isError = $hasError ?? in_array($level, [
            SystemLog::LOG_LEVEL_ERROR, 
            SystemLog::LOG_LEVEL_CRITICAL
        ]);

        return SystemLog::create([
            'level' => $level,
            'description' => $description,
            'origin' => $origin,
            'hasError' => $isError,
        ]);
    }

    public static function warning(string $description, string $origin): SystemLog
    {
        return self::log(SystemLog::LOG_LEVEL_WARNING, $description, $origin, false);
    }

    public static function debug(string $description, string $origin): SystemLog
    {
        return self::log(SystemLog::LOG_LEVEL_DEBUG, $description, $origin, false);
    }

    public static function error(string $description, string $origin): SystemLog
    {
        return self::log(SystemLog::LOG_LEVEL_ERROR, $description, $origin, true);
    }

    public static function info(string $description, string $origin): SystemLog
    {
        return self::log(SystemLog::LOG_LEVEL_INFO, $description, $origin, false);
    }

    public static function critical(string $description, string $origin): SystemLog
    {
        return self::log(SystemLog::LOG_LEVEL_CRITICAL, $description, $origin, true);
    }
}
