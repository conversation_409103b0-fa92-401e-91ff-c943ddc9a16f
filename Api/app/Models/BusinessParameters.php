<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessParameters extends Model
{
    use HasFactory;

    protected $table = 'business_parameters';

    protected $primaryKey = 'business_id';

    protected $casts = [
        'can_set_cashback_value' => 'boolean',
    ];

    protected $fillable = [
        'business_id',
        'form_id',
        'evaluation_expiration_hours',
        'cashback_expiration_hours',
        'cashback_percentage',
        'evaluation_interval_day',
        'can_set_cashback_value',
        // Campos do WhatsApp
        'whatsapp_instance_key',
        'whatsapp_phone_number',
    ];

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }
}
