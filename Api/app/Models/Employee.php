<?php

namespace App\Models;

use App\Http\Resources\Api\EmployeeResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Employee extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'admin',
        'user_id',
        'business_selected_id',
        'initial_screen',
        'is_informative_read',
    ];

    public function isAdmin(): bool
    {
        return $this->admin;
    }

    /**
     * Verifica se o funcionário ainda tem acesso ao negócio selecionado
     *
     * @return bool
     */
    public function hasAccessToSelectedBusiness(): bool
    {
        // Se não tiver negócio selecionado, retorna true
        if (!$this->business_selected_id) {
            return true;
        }

        // Verifica se o negócio selecionado ainda existe na relação de negócios do funcionário
        return $this->businesses()->where('businesses.id', $this->business_selected_id)->exists();
    }

    public function businesses(): BelongsToMany
    {
        return $this->belongsToMany(Business::class, 'employee_business', 'employee_id', 'business_id')
            ->withPivot('admin');
    }

    public function businesses_canceled(): BelongsToMany
    {
        return $this->belongsToMany(Business::class, 'employee_business', 'employee_id', 'business_id')->withTrashed()->where('businesses.deleted_at', '!=', null);
    }

    public function businessSelected(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_selected_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function authorizedFunction()
    {
        return $this->hasOne(AuthorizedFunction::class, 'employee_id', 'id')->withTrashed();
    }

    public function authorizedSettingsFunction()
    {
        return $this->hasOne(AuthorizedSettingsFunction::class, 'employee_id', 'id')->withTrashed();
    }

    public static function withUser()
    {
        return Employee::join('users', 'users.id', '=', 'employees.user_id')
            ->select('employees.*');
    }

    public function toArray(): EmployeeResource
    {
        return EmployeeResource::make($this);
    }

    public static function getEmployeesIdsFiltered(object $filters): array {
        $query = Employee::query()
            ->join('users', 'employees.user_id', '=', 'users.id')
            ->select('users.id as id', 'users.name', 'users.cpf');
        $userLogged = Auth::user();
        $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
        $userLoggedSelectedBusinessId = $userLogged->employee->business_selected_id;
        $query->where('users.id', '<>', $userLogged->id);
        if (!$canReleaseAllBusinesses) {
            if (!$userLoggedSelectedBusinessId) {
                throw new \Exception("Você deve selecionar um negócio.");
            }
            $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                $q->where('employee_business.business_id', $userLoggedSelectedBusinessId);
            });
        } else if($userLoggedSelectedBusinessId) {
            $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                $q->where('employee_business.business_id', $userLoggedSelectedBusinessId);
            });
        }
        if ($filters->search) {
            $cpf = preg_replace('/[^0-9]/', '', $filters->search);
            if (preg_match('/^\d{1,11}$/', $cpf))
                $query->where('users.cpf', 'like', "$cpf%");
            else
                $query->where('users.name', 'like', "%$filters->search%");
        }
        if ($filters->employeeType && $canReleaseAllBusinesses) {
            $employeeType = $filters->employeeType;

            if($employeeType == 'admin') {
                $query->whereHas('businesses', function($query) use($userLoggedSelectedBusinessId) {
                    $query->where('employee_business.admin', true)
                        ->when($userLoggedSelectedBusinessId, function($q) use($userLoggedSelectedBusinessId) {
                            $q->where('businesses.id', $userLoggedSelectedBusinessId);
                    });
                });
            }

            if($employeeType == 'commom') {
                $query->whereHas('businesses', function($query) use($userLoggedSelectedBusinessId) {
                    $query->where('employee_business.admin', false)
                        ->when($userLoggedSelectedBusinessId, function($q) use($userLoggedSelectedBusinessId) {
                            $q->where('businesses.id', $userLoggedSelectedBusinessId);
                    });
                });
            }
        }
        $employeeIds = $query->get()->pluck('id')->toArray();
        return $employeeIds;
    }
}
