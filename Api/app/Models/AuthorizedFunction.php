<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Request;

class AuthorizedFunction extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'employees',
        'settings',
        'employee_id',
        'release_all_business',
        'manage_business_profile',
        'plans',
        'sellers',
        //Topics
        'topics',
        'default_topics',
        'update_topics',
        //Forms
        'forms',
        'default_forms',
        'update_forms',
        //Business
        'update_business',
        'parameters',
        //Cashback
        'cashback',
        'update_cashback',
        //Clients
        'clients',
        'update_clients',
        //Evaluations
        'evaluations',
        //Notifications
        'notifications',
        'update_notifications',
        //Dashboard
        'dashboard',
        //Site notification
        'site_notifications',
        'update_site_notifications'
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id')->withTrashed();
    }

    public function havePermission(Request $request)
    {
        if($request->is('api/employees/*') || $request->is('api/employees')) {
            return $this->employees;
        }
        if($request->is('api/clients/*') || $request->is('api/clients')) {
            return $this->clients;
        }
        if($request->is('api/settings/*') || $request->is('api/settings')) {
            return $this->settings;
        }
        if($request->is('api/topics/*') || $request->is('api/topics')) {
            return $this->topics;
        }
        if($request->is('api/forms/*') || $request->is('api/forms')) {
            return $this->forms;
        }
        if($request->is('api/notifications/*') || $request->is('api/notifications')) {
            return $this->notifications;
        }
        if($request->is('api/dashboard/*') || $request->is('api/dashboard')) {
            return $this->dashboard;
        }
        if($request->is('api/siteNotifications/*') || $request->is('api/siteNotifications')) {
            return $this->site_notifications;
        }
        return false;
    }
}
