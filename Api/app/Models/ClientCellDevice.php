<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClientCellDevice extends Model
{
    use HasFactory;

    protected $fillable = [
        'id_client',
        'id_device'
    ];

    protected $table = 'client_cell_devices';

    protected $primaryKey = ['id_client', 'id_device'];

    public $incrementing = false;

    public function client(): BelongsTo {
        return $this->belongsTo(Client::class);
    }

}
