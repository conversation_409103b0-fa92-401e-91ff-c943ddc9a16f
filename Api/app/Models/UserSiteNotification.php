<?php

namespace App\Models;

use App\Http\Resources\Api\UserSiteNotificationResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserSiteNotification extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'site_notification_id',
        'type',
        'viewed',
        'viewed_at'
    ];

    public function user() {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function siteNotification() {
        return $this->belongsTo(SiteNotification::class);
    }

    public function toArray(): UserSiteNotificationResource
    {
        return UserSiteNotificationResource::make($this);
    }
}
