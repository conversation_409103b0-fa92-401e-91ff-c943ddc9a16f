<?php

namespace App\Models;

use App\Http\Resources\Api\SellerResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Seller extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'register_sellers',
        'percentage_per_sale',
        'registered_by',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function registeredBy(): BelongsTo
    {
        return $this->belongsTo(self::class, 'registered_by');
    }

    public function toArray(): SellerResource
    {
        return SellerResource::make($this);
    }

    public function businesses()
    {
        return Business::where('seller_id', $this->id)->get();
    }
    
    static public function getSellersIdsFiltered(object $filters): array {
        $query = Seller::query()
            ->join('users', 'sellers.user_id', 'users.id')
            ->select('users.id as id', 'users.name', 'users.cpf');
        $userLogged = Auth::user();
        $canSeeSellers = $userLogged->employee->authorizedFunction->sellers || false;
        if (!$canSeeSellers) 
            return [];
        if (isset($filters->search)) {
            $cpf = preg_replace('/[^0-9]/', '', $filters->search);
            if (preg_match('/^\d{1,11}$/', $cpf))
                $query->where('users.cpf', 'like', "$cpf%");
            else
                $query->where('users.name', 'like', "%$filters->search%");
        }
        $sellersIds = $query->get()->pluck('id')->toArray();
        return $sellersIds;
    }
}
