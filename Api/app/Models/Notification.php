<?php

namespace App\Models;

use App\Http\Resources\Api\NotificationResource;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Notification extends CustomModel
{
    use HasFactory;

    protected $fillable = [
        'notification_push_id',
        'sending_type',
        'title',
        'description',
        'url_click',
        'number_clients',
        'business_id',
        'started_at',
        'finished_at',
        'canceled_at'
    ];

    public function business() {
        return $this->belongsTo(Business::class);
    }

    public function notificationClients() {
        return $this->hasMany(NotificationClient::class, 'notification_id', 'id');
    }

    public function clients()
    {
        return $this->belongsToMany(Client::class, 'notification_clients', 'notification_id', 'client_id');
    }
}
