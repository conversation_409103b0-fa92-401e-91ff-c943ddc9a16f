<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeBusiness extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'employee_business';

    protected $primaryKey = ['employee_id', 'business_id'];

    public $incrementing = false;


    protected $fillable = [
        'employee_id',
        'business_id',
        'admin',
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function business()
    {
        return $this->belongsTo(Business::class);
    }
}
