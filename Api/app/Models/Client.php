<?php

namespace App\Models;

use App\Http\Resources\Api\ClientResource;
use Auth;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Client extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'is_entrepreneur',
        'is_informative_read'
    ];

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function cashback(): BelongsToMany
    {
        return $this->BelongsToMany(Cashback::class);
    }

    public function businesses(): BelongsToMany
    {
        return $this->belongsToMany(Business::class, 'client_business', 'client_id', 'business_id')
            ->whereNull('client_business.deleted_at');
    }

    public function devices(): HasMany {
        return $this->hasMany(ClientCellDevice::class, 'id_client', 'id');
    }

    public function toArray(): ClientResource
    {
        return ClientResource::make($this);
    }

    static public function getClientsIdsFiltered(object $filters): array {
        $query = Client::query()
            ->join('users', 'clients.user_id', '=', 'users.id')
            ->select('clients.id', 'users.name', 'users.cpf');
        $userLogged = Auth::user();
        $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
        $userLoggedSelectedBusinessId = $userLogged->employee->business_selected_id;

        if (!$canReleaseAllBusinesses) {
            if (!$userLoggedSelectedBusinessId) {
                throw new \Exception("Você deve selecionar um negócio.");
            }
            $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
            });
        } else if($userLoggedSelectedBusinessId) {
            $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
            });
        }
        if (isset($filters->has_devices)) {
            $hasDevices = filter_var($filters->has_devices, FILTER_VALIDATE_BOOLEAN);
            if ($hasDevices) {
                $query->whereHas('devices');
            }
        }

        if (isset($filters->search)) {
            $cpf = preg_replace('/[^0-9]/', '', $filters->search);
            if (preg_match('/^\d{1,11}$/', $cpf))
                $query->where('users.cpf', 'like', "$cpf%");
            else
                $query->where('users.name', 'like', "%$filters->search%");
        }

        if (isset($filters->is_entrepreneur) && $filters->is_entrepreneur !== 'all') {
            $filtered = filter_var($filters->is_entrepreneur, FILTER_VALIDATE_BOOLEAN);
            if ($filtered) {
                $query->where('clients.is_entrepreneur', true);
            } else $query->where('clients.is_entrepreneur', false);
        }
        $clientsIds = $query->get()->pluck('id')->toArray();
        return $clientsIds;
    }

    static public function getClientsUserIdsFiltered(object $filters): array {
        $query = Client::query()
            ->join('users', 'clients.user_id', '=', 'users.id')
            ->select('users.id as id', 'users.name', 'users.cpf');
        $userLogged = Auth::user();
        $canReleaseAllBusinesses = $userLogged->employee->authorizedFunction->release_all_business || false;
        $userLoggedSelectedBusinessId = $userLogged->employee->business_selected_id;

        if (!$canReleaseAllBusinesses) {
            if (!$userLoggedSelectedBusinessId) {
                throw new \Exception("Você deve selecionar um negócio.");
            }
            $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
            });
        } else if($userLoggedSelectedBusinessId) {
            $query->whereHas('businesses', function ($q) use ($userLoggedSelectedBusinessId) {
                $q->where('client_business.business_id', $userLoggedSelectedBusinessId);
            });
        }
        if (isset($filters->has_devices)) {
            $hasDevices = filter_var($filters->has_devices, FILTER_VALIDATE_BOOLEAN);
            if ($hasDevices) {
                $query->whereHas('devices');
            }
        }

        if (isset($filters->search)) {
            $cpf = preg_replace('/[^0-9]/', '', $filters->search);
            if (preg_match('/^\d{1,11}$/', $cpf))
                $query->where('users.cpf', 'like', "$cpf%");
            else
                $query->where('users.name', 'like', "%$filters->search%");
        }

        if (isset($filters->is_entrepreneur) && $filters->is_entrepreneur !== 'all') {
            $filtered = filter_var($filters->is_entrepreneur, FILTER_VALIDATE_BOOLEAN);
            if ($filtered) {
                $query->where('clients.is_entrepreneur', true);
            } else $query->where('clients.is_entrepreneur', false);
        }
        $clientsIds = $query->get()->pluck('id')->toArray();
        return $clientsIds;
    }

    public static function withUser()
    {
        return Client::join('users', 'users.id', '=', 'clients.user_id')
            ->select('clients.*');
    }
}
