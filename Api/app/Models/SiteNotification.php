<?php

namespace App\Models;

use App\Enums\SiteNotificationTypes;
use App\Http\Resources\Api\SiteNotificationResource;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SiteNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'title',
        'description',
        'icon',
        'url_click'
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_site_notifications');
    }

    public function userSiteNotifications()
    {
        return $this->hasMany(UserSiteNotification::class, 'site_notification_id', 'id');
    }

    public function business() {
        return $this->belongsTo(Business::class);
    }

    public function toArray(): SiteNotificationResource
    {
        return SiteNotificationResource::make($this);
    }
}
