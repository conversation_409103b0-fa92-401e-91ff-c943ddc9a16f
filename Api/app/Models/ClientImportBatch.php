<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClientImportBatch extends Model
{
    protected $fillable = [
        'file_path',
        'file_name',
        'status',
        'processed_at',
        'total_records',
        'processed_records',
        'failed_records',
        'created_by_id',
        'business_id',
        'config',
    ];

    protected $casts = [
        'config' => 'array',
        'processed_at' => 'datetime',
    ];

    // Status possíveis
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    public function items(): HasMany
    {
        return $this->hasMany(ClientImportItem::class, 'batch_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(Employee::class, 'created_by_id');
    }

    public function business()
    {
        return $this->belongsTo(Business::class);
    }
}
