<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Hand<PERSON> extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
        
        });
        $this->renderable(function(Throwable $e, $request) {
            if($request->is('api/*')) {
                $exception = get_class($e);

                if(in_array($exception, ['Illuminate\Auth\AuthenticationException', 'Symfony\Component\Routing\Exception\RouteNotFoundException'])) {
                    return response()->json(['msg' => 'Usuário não autenticado.', 'exception' => get_class($e)], 401);
                } 
                if ($exception == 'Illuminate\Validation\ValidationException'){
                    return response()->json(['msg' => 'Os dados fornecidos são inválidos.', 'errors' => $e->errors()], 422);
                }

                $httpCode = 400;
                $exceptionCode = $exception;
                
                if(method_exists($e, 'getHttpCode') && !is_null($e->getHttpCode())) {
                    $httpCode = $e->getHttpCode();
                }
                if(method_exists($e, 'getCodeException') && !is_null($e->getCodeException())) {
                    $exceptionCode = $e->getCodeException();
                }

                return response()->json(['msg' => $e->getMessage(), 'code' => $exceptionCode, 'exception' => get_class($e)], $httpCode);
            }
        });
    }
}
