<?php

namespace App\Exceptions\MaisLuzExceptions;

use Exception;

class BaseException extends Exception
{
    protected string $codeException;
    protected ?int $httpCode;

    public function __construct(string $msg, ?int $httpCode = null)
    {
        parent::__construct($msg, 1);
        $this->codeException = 'BaseException';
        $this->httpCode = $httpCode;
    }

    public function getCodeException()
    {
        return $this->codeException;
    }

    public function getHttpCode()
    {
        return $this->httpCode;
    }
}
