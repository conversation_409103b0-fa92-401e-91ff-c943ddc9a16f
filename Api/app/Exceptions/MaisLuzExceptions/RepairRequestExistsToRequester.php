<?php

namespace App\Exceptions\MaisLuzExceptions;

use App\Models\RepairRequestRequester;

class RepairRequestExistsToRequester extends BaseException
{
    private $repair;

    public function __construct(RepairRequestRequester $repair, string $msg = 'Já existe uma solicitação de reparo em andamento para o poste selecionado.')
    {
        parent::__construct($msg);
        $this->codeException = 'RepairRequestExistsToRequester';
        $this->repair = $repair;
    }

    public function getRepair()
    {
        return $this->repair;
    }
}
