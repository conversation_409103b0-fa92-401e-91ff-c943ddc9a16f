<?php

namespace App\Observers;

use App\Models\Evaluation;
use Carbon\Carbon;

class EvaluationObserver
{
    /**
     * Handle the Evaluation "created" event.
     */
    public function created(Evaluation $evaluation): void
    {
        //
    }

    public function creating(Evaluation $evaluation): void
    {
        /*
         * <PERSON><PERSON>r, na criação, se falta menos de 24 horas para expirar a Avaliação
         * (Evitar envio de notificação)
        */

        $dateToExpire = Carbon::now()->addHours(24);
        if ($evaluation->expiration_date <= $dateToExpire) {
            $evaluation->notified_expiration_at = Carbon::now();
        }
    }

    /**
     * Handle the Evaluation "updated" event.
     */
    public function updated(Evaluation $evaluation): void
    {
        //
    }

    /**
     * Handle the Evaluation "deleted" event.
     */
    public function deleted(Evaluation $evaluation): void
    {
        //
    }

    /**
     * Handle the Evaluation "restored" event.
     */
    public function restored(Evaluation $evaluation): void
    {
        //
    }

    /**
     * Handle the Evaluation "force deleted" event.
     */
    public function forceDeleted(Evaluation $evaluation): void
    {
        //
    }
}
