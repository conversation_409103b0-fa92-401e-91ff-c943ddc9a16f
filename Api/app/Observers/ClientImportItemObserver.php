<?php

namespace App\Observers;

use App\Models\ClientImportItem;
use App\Models\ClientImportBatch;
use Illuminate\Support\Facades\Log;

class ClientImportItemObserver
{
    /**
     * Evento disparado após uma atualização em ClientImportItem.
     * Verifica se todos os itens do batch estão finalizados e, em caso afirmativo, atualiza o batch para COMPLETED.
     */
    public function updated(ClientImportItem $item)
    {
        Log::info('ClientImportItem atualizado', [
            'item_id' => $item->id,
            'batch_id' => $item->batch_id,
            'status' => $item->status
        ]);

        // Supondo que o relacionamento 'batch' esteja definido no modelo ClientImportItem
        $batch = $item->batch;
        if (!$batch) {
            Log::warning('Batch não encontrado para o item', ['item_id' => $item->id]);
            return;
        }

        // Considere que os status finais sejam COMPLETED ou FAILED.
        // Verifica se ainda existem itens com status PENDING.
        $pendingItems = $batch->items()
            ->where('status', ClientImportBatch::STATUS_PENDING)
            ->count();

        Log::info('Verificando itens pendentes', [
            'batch_id' => $batch->id,
            'pending_items' => $pendingItems
        ]);

        if ($pendingItems === 0) {
            Log::info('Todos os itens processados, atualizando batch para COMPLETED', [
                'batch_id' => $batch->id
            ]);

            // Todos os itens foram processados; atualiza o status do batch
            $batch->update([
                'status' => ClientImportBatch::STATUS_COMPLETED,
                'processed_at' => now(),
            ]);
        }
    }
}