<?php

namespace App\Observers;

use App\Enums\CashbackStatus;
use App\Models\Cashback;
use Carbon\Carbon;

class CashbackObserver
{
    /**
     * Handle the Cashback "creating" event.
     */
    public function creating(Cashback $cashback): void
    {
        /*
         * <PERSON><PERSON><PERSON>, na criação, se falta menos de 24 horas para expirar o cashback
         * (Evitar envio de notificação)
        */

        $dateToExpire = Carbon::now()->addHours(24);
        if ($cashback->expiration_date <= $dateToExpire) {
            $cashback->notification_expiration_date = Carbon::now();
        }
    }

    /**
     * Handle the Cashback "created" event.
     */
    public function created(Cashback $cashback): void
    {
        //
    }

    public function updating(Cashback $cashback): void
    {
        if ($cashback->status === CashbackStatus::Completed->value){
            $cashback->used_at = Carbon::now();
        }
    }

    /**
     * Handle the Cashback "updated" event.
     */
    public function updated(Cashback $cashback): void
    {
    }

    /**
     * Handle the Cashback "deleted" event.
     */
    public function deleted(Cashback $cashback): void
    {
        //
    }

    /**
     * Handle the Cashback "restored" event.
     */
    public function restored(Cashback $cashback): void
    {
        //
    }

    /**
     * Handle the Cashback "force deleted" event.
     */
    public function forceDeleted(Cashback $cashback): void
    {
        //
    }
}
