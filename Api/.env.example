APP_NAME='Vis<PERSON> Negócio'
APP_ENV=homologation
APP_KEY=base64:iaVxJPOEJ8MbRXA/hRs9h8ZUOv6TVprvUKIlJs5jlwM=
APP_DEBUG=true

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=visao-negocio
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=73pghv5ofPIxpBWHLF0LsXMnJsOig1fUP04ThQIu
AWS_DEFAULT_REGION=sa-east-1
AWS_BUCKET=api.maisluz.homolog

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=b09igvR8gXz7YfYm45sbDiuA9pDbkvBSEZKqA73whZXDpwB4isHhRWNScLnjQhTB

JWT_TTL=1440

FRONTEND_URL=http://localhost:8000

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mippxltjgvwmrbkq
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

NOTIFICATION_PUSH_API_TOKEN=ZmI4NGNkNTItMDAwMC00OWYyLTk4ZmYtZGE5Y2UzM2UwZDI2
NOTIFICATION_PUSH_API_URL=https://onesignal.com/api/
NOTIFICATION_PUSH_APP_ID=************************************

WHATSAPP_API_URL=
WHATSAPP_API_KEY=
WHATSAPP_API_TOKEN=