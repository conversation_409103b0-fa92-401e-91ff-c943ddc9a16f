#!/bin/bash

# Atualizar os Pacotes do Sistema
apt update

# Configurar o <PERSON><PERSON> (America/Recife)
export DEBIAN_FRONTEND=noninteractive
ln -fs /usr/share/zoneinfo/America/Recife /etc/localtime
apt install -y tzdata
dpkg-reconfigure --frontend noninteractive tzdata

# Instalar Dependências Necessárias
apt install -y software-properties-common curl

# Adicionar o Repositório do PHP
add-apt-repository ppa:ondrej/php -y

# Atualizar Novamente os Pacotes
apt update

# Instalar o PHP 8.1
apt install -y php8.1

# Instalar Extensões do PHP Necessárias para o Laravel
apt install -y php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip

# Baixar o Instalador do Composer
curl -sS https://getcomposer.org/installer -o composer-setup.php

# Instalar o Composer
php composer-setup.php --install-dir=/usr/local/bin --filename=composer

# Verificar a Instalação do PHP
php -v

# Verificar a Instalação do Composer
composer -V

# Limpar o Instalador do Composer
rm composer-setup.php

echo "Instalação concluída com sucesso!"