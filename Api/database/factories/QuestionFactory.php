<?php

namespace Database\Factories;

use App\Models\Question;
use App\Models\Topic;
use Illuminate\Database\Eloquent\Factories\Factory;

class QuestionFactory extends Factory
{
    protected $model = Question::class;
    public function definition(): array
    {
        return [
            'topic_id' => Topic::factory(),
            'description' => $this->faker->sentence,
            'evaluation_type' => $this->faker->randomElement(['yes_no', 'five_star']),
            'order' => $this->faker->numberBetween(1, 10),
        ];
    }
}
