<?php

namespace Database\Factories;

use App\Models\Business;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Business>
 */
class BusinessFactory extends Factory
{

    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'description' => $this->faker->paragraph,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'logo' => null,
            'cnpj' => $this->faker->unique()->numerify('##############'),
            'corporate_reason' => $this->faker->company,
            'neighborhood' => $this->faker->city,
            'city' => $this->faker->city,
            'state' => $this->faker->stateAbbr,
            'cep' => $this->faker->numerify('########'),
            'street' => $this->faker->streetName,
            'number' => $this->faker->buildingNumber,
            'complement' => $this->faker->secondaryAddress,
            'status' => Business::STATUS_APPROVED_REGISTRATION,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
