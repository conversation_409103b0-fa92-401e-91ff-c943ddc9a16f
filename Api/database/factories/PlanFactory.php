<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Plan>
 */
class PlanFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => 'Plan '.$this->faker->numberBetween(1, 1000),
            'description' => $this->faker->text(),
            'value' => $this->faker->numberBetween(1, 1000),
            'active' => $this->faker->boolean(),
            'publicly_visible' => $this->faker->boolean(),
        ];
    }
}
