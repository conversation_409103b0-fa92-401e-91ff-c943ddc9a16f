<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class FormFactory extends Factory
{
    public function definition(): array
    {
        $default = $this->faker->boolean;
        return [
            'name' => $this->faker->word . ' Form',
            'description' => $this->faker->text,
            'default' => $default,
            'active' => !$default ? true : $this->faker->boolean,
        ];
    }
}
