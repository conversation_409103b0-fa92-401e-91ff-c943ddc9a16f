<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->safeEmail(),
            'password' => Hash::make('123456'),
            'birth_date' => $this->faker->date(),
            'cpf' => "{$this->faker->numberBetween(100000, 999999)}{$this->faker->numberBetween(10000, 99999)}",
            'gender' => ['f', 'm', null][$this->faker->numberBetween(0,2)],
            'phone_number_1' => $this->faker->numberBetween(100000, 999999).''.$this->faker->numberBetween(10000, 99999),
            'phone_number_2' => $this->faker->numberBetween(100000, 999999).''.$this->faker->numberBetween(10000, 99999),
            'neighborhood' => $this->faker->streetName(),
            'street'=> $this->faker->streetName(),
            'number' => $this->faker->numberBetween(100, 1000),
            'city' => $this->faker->city(),
            'state' => 'Pernambuco',
            'cep' => "{$this->faker->numberBetween(10000000, 99999999)}",
            'complement' => $this->faker->text(10),
            'login_type' => ['employee','seller'][$this->faker->numberBetween(0,1)],
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function unverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }
}
