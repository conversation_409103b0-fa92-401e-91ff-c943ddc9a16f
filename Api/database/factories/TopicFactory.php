<?php

namespace Database\Factories;

use App\Models\Topic;
use Illuminate\Database\Eloquent\Factories\Factory;

class TopicFactory extends Factory
{

    protected $model = Topic::class;

    public function definition(): array
    {
        $default = $this->faker->boolean(20);
        return [
            'name' => $this->faker->unique()->word,
            'description' => $this->faker->boolean(20) ? null : $this->faker->sentence,
            'default' => $default,
            'active' => !$default ? true : $this->faker->boolean,
            'required' => false,
        ];
    }
}
