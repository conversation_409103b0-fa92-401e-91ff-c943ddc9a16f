<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MoveFieldsToAuthorizedFunctionsPlanstable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->integer('total_employees')->after('update_site_notifications')->default(0);
            $table->integer('total_sends')->after('total_employees')->default(0);
            $table->enum('total_sends_type', ['monthly', 'daily'])->after('total_sends')->default('monthly');
            $table->integer('total_sends_days')->after('total_sends_type')->nullable();
            $table->boolean('email_sending')->after('total_sends_days')->default(false);
            $table->boolean('whatsapp_sending')->after('email_sending')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->dropColumn([
                'total_employees',
                'total_sends', 
                'total_sends_type',
                'total_sends_days',
                'email_sending',
                'whatsapp_sending'
            ]);
        });
    }
}