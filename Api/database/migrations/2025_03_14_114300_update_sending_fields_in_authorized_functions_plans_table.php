<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Primeiro: Adiciona novos campos
        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->integer('total_email_sends')->after('total_sends')->default(0);
            $table->enum('email_sends_type', ['monthly', 'daily'])->after('total_email_sends')->default('monthly');
            $table->integer('email_sends_days')->after('email_sends_type')->nullable();
            
            $table->integer('total_whatsapp_sends')->after('email_sends_days')->default(0);
            $table->enum('whatsapp_sends_type', ['monthly', 'daily'])->after('total_whatsapp_sends')->default('monthly');
            $table->integer('whatsapp_sends_days')->after('whatsapp_sends_type')->nullable();
        });

        // Segundo: Copia os dados existentes
        DB::statement('UPDATE authorized_functions_plans SET 
            total_email_sends = total_sends,
            email_sends_type = total_sends_type,
            email_sends_days = total_sends_days,
            total_whatsapp_sends = total_sends,
            whatsapp_sends_type = total_sends_type,
            whatsapp_sends_days = total_sends_days
            WHERE email_sending = 1 OR whatsapp_sending = 1');

        // Terceiro: Remove campos antigos
        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->dropColumn(['total_sends', 'total_sends_type', 'total_sends_days']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Primeiro: Restaura campos antigos
        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->integer('total_sends')->after('total_employees')->default(0);
            $table->enum('total_sends_type', ['monthly', 'daily'])->after('total_sends')->default('monthly');
            $table->integer('total_sends_days')->after('total_sends_type')->nullable();
        });

        // Segundo: Copia dados de volta
        DB::statement('UPDATE authorized_functions_plans SET 
            total_sends = GREATEST(total_email_sends, total_whatsapp_sends),
            total_sends_type = CASE 
                WHEN total_email_sends >= total_whatsapp_sends THEN email_sends_type 
                ELSE whatsapp_sends_type 
            END,
            total_sends_days = CASE 
                WHEN total_email_sends >= total_whatsapp_sends THEN email_sends_days 
                ELSE whatsapp_sends_days 
            END');

        // Terceiro: Remove novos campos
        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->dropColumn([
                'total_email_sends',
                'email_sends_type',
                'email_sends_days',
                'total_whatsapp_sends',
                'whatsapp_sends_type',
                'whatsapp_sends_days'
            ]);
        });
    }
};
