<?php

use App\Models\System;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('system', function (Blueprint $table) {
            if (System::where('code_name', 'DAYS_TO_TRANSFER_CASHBACK_AND_EVALUATION')->doesntExist()) {
                System::create([
                    'code_name' => 'DAYS_TO_TRANSFER_CASHBACK_AND_EVALUATION',
                    'name' => 'Quantidade de dias para transferir cashback e avaliação',
                    'description' => 'Parâmetro com a quantidade de dias para transferir o cashback e avaliação do cliente',
                    'value' => 30,
                    'type' => 'number',
                    'visible_screen' => 1,
                    'required' => 1,
                ]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system', function (Blueprint $table) {
            System::where('code_name', 'DAYS_TO_TRANSFER_CASHBACK_AND_EVALUATION')->delete();
        });
    }
};
