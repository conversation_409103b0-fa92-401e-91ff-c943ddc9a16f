<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    private $statuses = [
        'pending_approval',      // Pendente Aprovação
        'approved_registration', // Cadastro Aprovado
        'rejected_registration', // Cadastro Rejeitado
        'pending_cancellation',  // Cancelamento Pendente
        'completed_cancellation',// Cancelamento Concluído
        'reversed_cancellation', // Cancelamento Revertido
    ];

    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->enum('status', $this->statuses)
                ->default('approved_registration')
                ->after('cnpj');
        });

        // Atualiza os registros existentes para o status padrão 'approved_registration'
        DB::table('businesses')->update(['status' => 'approved_registration']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
