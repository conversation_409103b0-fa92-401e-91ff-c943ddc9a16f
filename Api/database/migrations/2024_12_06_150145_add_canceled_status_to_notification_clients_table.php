<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('notification_clients', function (Blueprint $table) {
            $table->enum('shipping_status', ['waiting', 'sent', 'failed', 'canceled'])
                ->default('waiting')
                ->change();
        });
    }

    public function down(): void
    {
        Schema::table('notification_clients', function (Blueprint $table) {
            $table->enum('shipping_status', ['waiting', 'sent', 'failed'])
                ->default('waiting')
                ->change();
        });
    }
};
