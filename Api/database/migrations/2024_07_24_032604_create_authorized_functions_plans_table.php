<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('authorized_functions_plans', function (Blueprint $table) {
            $table->id();
            $table->boolean('employees')->default(false);
            $table->boolean('manage_business_profile')->default(false);
            $table->boolean('topics')->default(false);
            $table->boolean('update_topics')->default(false);
            $table->boolean('forms')->default(false);
            $table->boolean('update_forms')->default(false);
            $table->boolean('cashback')->default(false);
            $table->boolean('update_cashback')->default(false);
            $table->boolean('clients')->default(false);
            $table->boolean('update_clients')->default(false);
            $table->boolean('evaluations')->default(false);
            $table->boolean('notifications')->default(false);
            $table->boolean('update_notifications')->default(false);
            $table->boolean('dashboard')->default(false);
            
            $table->unsignedBigInteger('plan_id');
            $table->foreign('plan_id')->references('id')->on('plans')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('authorized_functions_plans');
    }
};
