<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create("evaluations", function (Blueprint $table) {
            $table->id();
            $table->string("token")->unique();
            $table->boolean("anonymous")->default(true);
            $table->string("client_cpf", 11);
            $table->enum("sending_type", [
                "QR_CODE",
                "EMAIL",
                "WHATSAPP",
                "SMS",
            ]);
            $table->string("email")->nullable();
            $table->string("number_phone")->nullable();
            $table->dateTime("shipping_date");
            $table->foreignId("form_id")->constrained();
            $table->enum("shipping_status", ["SENT", "RESPONDED", "EXPIRED"]);
            $table->dateTime("expiration_date");
            $table->foreignId("business_id")->constrained();
            $table->decimal("cashback_percentage", 5, 2);
            $table->dateTime("cashback_expiration_date");
            $table->foreignId("client_id")->nullable()->constrained();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists("evaluations");
    }
};
