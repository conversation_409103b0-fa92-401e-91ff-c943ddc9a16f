<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            //
            $table->string('cnpj', 14)->unique();
            $table->string('corporate_reason');
            $table->string('neighborhood');// Referente ao endereço
            $table->string('city');// Referente ao endereço
            $table->string('state');// Referente ao endereço
            $table->string('cep');// Referente ao endereço
            $table->string('street')->nullable();// Referente ao endereço
            $table->string('number')->nullable();// Referente ao endereço
            $table->string('complement')->nullable();// Referente ao endereço
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropColumn([
                'cnpj',
                'corporate_reason',
                'neighborhood',
                'street',
                'number',
                'city',
                'state',
                'cep',
                'complement',
            ]);
        });
    }
};
