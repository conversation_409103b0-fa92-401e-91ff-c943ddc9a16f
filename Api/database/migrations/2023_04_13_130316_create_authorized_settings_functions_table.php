<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAuthorizedSettingsFunctionsTable extends Migration
{
    public function up()
    {
        Schema::create('authorized_settings_functions', function (Blueprint $table) {
            $table->id();
            $table->boolean('contact')->default(false);
            $table->boolean('system_parameters')->default(false);
            $table->unsignedBigInteger('employee_id');
            $table->foreign('employee_id')->references('id')->on('employees')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('authorized_settings_functions');
    }
}
