<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('system', function (Blueprint $table) {
            DB::table('system')->insert([
                [
                    'code_name' => 'EMPLOYEE_FIRST_LINK',
                    'name' => 'Primeiro link da tela inicial de funcionários',
                    'description' => 'Parâmetro com o primeiro link para renderizar na página inicial dos funcionários. (A exibição desse link pode ser pulada)',
                    'value' => "https://visaonegocio.com/primeiros-passos/",
                    'type' => 'string',
                    'visible_screen' => 1,
                    'required' => 1,
                ],
                [
                    'code_name' => 'EMPLOYEE_SECOND_LINK',
                    'name' => 'Segundo link da tela inicial de funcionários',
                    'description' => 'Parâmetro com o segundo link para renderizar na página inicial dos funcionários.',
                    'value' => "https://visaonegocio.super.site/",
                    'type' => 'string',
                    'visible_screen' => 1,
                    'required' => 1,
                ],
                [
                    'code_name' => 'FLOATING_BUTTON_LINK',
                    'name' => 'Link da página que vai ser exibida no botão flutuante',
                    'description' => 'Parâmetro com link para renderizar o conteúdo do botão flutuante',
                    'value' => "https://visaonegocio.super.site/",
                    'type' => 'string',
                    'visible_screen' => 1,
                    'required' => 0,
                ],
                [
                    'code_name' => 'FLOATING_BUTTON_ICON',
                    'name' => 'Ícone do botão flutuante',
                    'description' => 'Ícone do botão flutuante',
                    'value' => "",
                    'type' => 'image',
                    'visible_screen' => 1,
                    'required' => 0,
                ],
                [
                    'code_name' => 'CLIENT_FIRST_LINK',
                    'name' => 'Primeiro link da tela inicial de clientes',
                    'description' => 'Parâmetro com o primeiro link para renderizar na página inicial dos clientes. (A exibição desse link pode ser pulada)',
                    'value' => "https://visaonegocio.com/primeiros-passos/",
                    'type' => 'string',
                    'visible_screen' => 1,
                    'required' => 1,
                ],
                [
                    'code_name' => 'CLIENT_SECOND_LINK',
                    'name' => 'Segundo link da tela inicial de clientes',
                    'description' => 'Parâmetro com o segundo link para renderizar na página inicial dos clientes.',
                    'value' => "https://visaonegocio.super.site/",
                    'type' => 'string',
                    'visible_screen' => 1,
                    'required' => 1,
                ],
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system', function (Blueprint $table) {
            DB::table('system')->whereIn('code_name', [
                'EMPLOYEE_FIRST_LINK',
                'EMPLOYEE_SECOND_LINK',
                'FLOATING_BUTTON_LINK',
                'CLIENT_FIRST_LINK',
                'CLIENT_SECOND_LINK',
                'FLOATING_BUTTON_ICON'
            ])->delete();
        });
    }
};
