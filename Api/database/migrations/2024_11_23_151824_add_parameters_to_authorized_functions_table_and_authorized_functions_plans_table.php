<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void
    {
        Schema::table('authorized_functions', function (Blueprint $table) {
            $table->boolean('parameters')->default(false);
        });

        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->boolean('parameters')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('authorized_functions', function (Blueprint $table) {
            $table->dropColumn('parameters');
        });

        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->dropColumn('parameters');
        });
    }
};
