<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->integer('evaluation_interval_day')->default(0)->change();
        });
    }

    public function down(): void
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->integer('evaluation_interval_day')->default(30)->change();
        });
    }
};
