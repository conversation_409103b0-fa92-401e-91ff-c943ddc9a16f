<?php

use App\Models\System;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('system', function (Blueprint $table) {
            $adminBusiness = DB::table('businesses')->where('cnpj', '37219139000130')->first();

            if (System::where('code_name', 'ADMIN_BUSINESS_ID')->doesntExist()) {
                System::create([
                    'code_name' => 'ADMIN_BUSINESS_ID',
                    'name' => 'ID do negócio ADMIN do sistema',
                    'description' => 'Parâmetro com o ID do negócio que vai ser o ADMIN do sistema',
                    'value' => isset($adminBusiness) ? $adminBusiness->id : -1,
                    'type' => 'number',
                    'visible_screen' => 0,
                    'required' => 1
                ]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system', function (Blueprint $table) {
            System::where('code_name', 'ADMIN_BUSINESS_ID')->delete();
        });
    }
};
