<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('authorized_functions', function (Blueprint $table) {
            $table->boolean('site_notifications')->default(false);
            $table->boolean('update_site_notifications')->default(false);
        });

        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->boolean('site_notifications')->default(false);
            $table->boolean('update_site_notifications')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('authorized_functions', function (Blueprint $table) {
            $table->dropColumn('site_notifications');
            $table->dropColumn('update_site_notifications');
        });
        
        Schema::table('authorized_functions_plans', function (Blueprint $table) {
            $table->dropColumn('site_notifications');
            $table->dropColumn('update_site_notifications');
        });
    }
};
