<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->text('url_facebook')->nullable();
            $table->text('url_instagram')->nullable();
            $table->text('url_google')->nullable();
            $table->text('url_linkedin')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropColumn('url_facebook');
            $table->dropColumn('url_instagram');
            $table->dropColumn('url_google');
            $table->dropColumn('url_linkedin');
        });
    }
};
