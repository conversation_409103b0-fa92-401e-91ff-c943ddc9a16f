<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('business_parameters', function (Blueprint $table) {
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->primary('business_id');

            $table->unsignedBigInteger('form_id')->nullable();
            $table->foreign('form_id')->references('id')->on('forms')->onDelete('cascade');

            $table->unsignedInteger('evaluation_expiration_hours')->default(48);
            $table->unsignedInteger('cashback_expiration_hours')->default(720);
            $table->decimal('cashback_percentage', 5, 2)->unsigned()->default(5.00);
            $table->integer('evaluation_interval_day')->default(30);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('business_parameters');
    }
};
