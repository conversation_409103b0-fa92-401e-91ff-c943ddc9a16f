<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_cell_devices', function (Blueprint $table) {
            $table->foreignId('id_client')->references('id')->on('clients')->onDelete('cascade');
            $table->string('id_device')->unique()->default(null);
            $table->primary(['id_client', 'id_device']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_cell_devices');
    }
};
