<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->string('whatsapp_instance_key')->nullable()->comment('Chave da instância do WhatsApp no formato VisaoNegocio_{idDoNegocio}');
            $table->string('whatsapp_phone_number')->nullable()->comment('Número de telefone conectado ao WhatsApp');
        });
    }

    public function down()
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->dropColumn([
                'whatsapp_instance_key',
                'whatsapp_phone_number',
            ]);
        });
    }
};
