<?php

use App\Enums\EvaluationSendingType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cashbacks', function (Blueprint $table) {
            $table->id();
            $table->enum('status', ['pending', 'completed', 'expired'])->default('pending');
            $table->decimal('percentage', 5, 2)->unsigned();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->enum('sending_type', EvaluationSendingType::values())
                    ->default(EvaluationSendingType::email->value);
            $table->dateTime('expiration_date');
            $table->dateTime('notification_expiration_date')->nullable();
            $table->dateTime('notification_created_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cashbacks');
    }
};
