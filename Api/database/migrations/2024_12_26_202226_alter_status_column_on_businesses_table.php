<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private $statuses = [
        'pending_approval',      // Pendente Aprovação
        'approved_registration', // Cadastro Aprovado
        'rejected_registration', // Cadastro Rejeitado
        'pending_cancellation',  // Cancelamento Pendente
        'completed_cancellation',// Cancelamento Concluído
        'reversed_cancellation', // Cancelamento Revertido
        'expired_registration', // Cadastro Expirado
    ];

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $businesses = DB::table('businesses')->select(['id', 'status'])->get();

        Schema::table('businesses', function(Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('businesses', function(Blueprint $table) {
            $table->enum('status', $this->statuses)
                ->default('approved_registration')
                ->after('cnpj');
        });

        foreach ($businesses as $business) {
            DB::table('businesses')
                ->where('id', $business->id)
                ->update(['status' => $business->status]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
