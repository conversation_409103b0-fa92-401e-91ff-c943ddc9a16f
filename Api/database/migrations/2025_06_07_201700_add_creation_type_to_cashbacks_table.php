<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cashbacks', function (Blueprint $table) {
            $table->enum('creation_type', ['direct', 'evaluation'])->default('evaluation')->after('sending_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cashbacks', function (Blueprint $table) {
            $table->dropColumn('creation_type');
        });
    }
};
