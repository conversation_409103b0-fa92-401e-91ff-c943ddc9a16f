<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSystemTable extends Migration
{
    public function up()
    {
        Schema::create('system', function (Blueprint $table) {
            $table->string('code_name')->primary();

            $table->string('name');
            $table->text('description')->nullable();
            $table->text('value')->nullable();

            $table->enum('type', ['string', 'number', 'float', 'boolean', 'yes_not', 'image', 'color']);
            $table->boolean('visible_screen');
            $table->boolean('required');

            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('system');
    }
}
