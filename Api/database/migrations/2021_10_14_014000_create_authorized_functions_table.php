<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAuthorizedFunctionsTable extends Migration
{
    public function up()
    {
        Schema::create('authorized_functions', function (Blueprint $table) {
            $table->id();
            $table->boolean('employees')->default(false);
            $table->boolean('settings')->default(false);
            $table->boolean('release_all_business')->default(false);
            $table->boolean('manage_business_profile')->default(false);
            $table->boolean('plans')->default(false);
            $table->boolean('sellers')->default(false);
            $table->boolean('topics')->default(false);
            $table->boolean('default_topics')->default(false);
            $table->boolean('update_topics')->default(false);
            $table->boolean('forms')->default(false);
            $table->boolean('default_forms')->default(false);
            $table->boolean('update_forms')->default(false);
            $table->boolean('update_business')->default(false);
            $table->boolean('cashback')->default(false);
            $table->boolean('update_cashback')->default(false);
            $table->boolean('clients')->default(false);
            $table->boolean('update_clients')->default(false);
            $table->boolean('evaluations')->default(false);
            $table->boolean('notifications')->default(false);
            $table->boolean('update_notifications')->default(false);
            $table->boolean('dashboard')->default(false);

            $table->unsignedBigInteger('employee_id');
            $table->foreign('employee_id')->references('id')->on('employees')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('authorized_functions');
    }
}
