<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('notification_push_id')->unique()->nullable()->default(null);
            $table->enum('sending_type', ['email', 'whatsapp', 'notification_push']);
            $table->string('title', 255);
            $table->text('description');
            $table->string('url_click')->nullable();
            $table->unsignedInteger('number_clients');
            $table->unsignedInteger('sending_number_success')->default(0);
            $table->unsignedInteger('sending_number_failed')->default(0);
            $table->unsignedBigInteger('business_id')->nullable();
            $table->foreign('business_id')->references('id')->on('businesses')->onDelete('cascade');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('finished_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
