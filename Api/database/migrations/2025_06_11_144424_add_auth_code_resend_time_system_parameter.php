<?php

use App\Models\System;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('system', function (Blueprint $table) {
            if (System::where('code_name', 'AUTH_CODE_RESEND_TIME_MINUTES')->doesntExist()) {
                System::create([
                    'code_name' => 'AUTH_CODE_RESEND_TIME_MINUTES',
                    'name' => 'Tempo para reenvio de códigos de autenticação',
                    'description' => 'Tempo em minutos que o usuário deve aguardar para reenviar códigos de autenticação',
                    'value' => 3,
                    'type' => 'number',
                    'visible_screen' => 0,
                    'required' => 1,
                ]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system', function (Blueprint $table) {
            System::where('code_name', 'AUTH_CODE_RESEND_TIME_MINUTES')->delete();
        });
    }
};
