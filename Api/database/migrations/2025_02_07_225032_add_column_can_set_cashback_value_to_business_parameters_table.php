<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->boolean('can_set_cashback_value')->default(true)->after('evaluation_interval_day');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->dropColumn('can_set_cashback_value');
        });
    }
};
