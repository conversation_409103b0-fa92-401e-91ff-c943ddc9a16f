<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('evaluations', function (Blueprint $table) {
            DB::statement('ALTER TABLE evaluations ADD INDEX idx_evaluations_business_shipping_date (business_id, shipping_date DESC)');
        });
    }

    public function down(): void
    {
        Schema::table('evaluations', function (Blueprint $table) {
            $table->dropIndex('idx_evaluations_business_shipping_date');
        });
    }
};
