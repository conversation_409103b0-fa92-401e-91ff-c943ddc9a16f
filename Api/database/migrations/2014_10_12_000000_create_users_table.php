<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');

            $table->enum('login_type', [ 'admin','employee', 'seller', 'client'])
                ->comment('Indica o tipo de login do usuário')->nullable();

            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->string('password');

            $table->date('birth_date');
            $table->string('cpf', 11);
            $table->enum('gender', ['f', 'm'])->nullable();
            $table->string('phone_number_1')->nullable();
            $table->string('phone_number_2')->nullable();
            $table->string('neighborhood')->nullable();// Referente ao endereço
            $table->string('street')->nullable();// Referente ao endereço
            $table->string('number')->nullable();// Referente ao endereço
            $table->string('city')->nullable();// Referente ao endereço
            $table->string('state')->nullable();// Referente ao endereço
            $table->string('cep')->nullable();// Referente ao endereço
            $table->string('complement')->nullable();// Referente ao endereço

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
