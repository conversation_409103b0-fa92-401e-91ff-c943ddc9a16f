<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create("evaluation_responses", function (Blueprint $table) {
            $table->id();
            $table->foreignId("evaluation_id")->constrained()->onDelete('cascade');
            $table->foreignId("question_id")->constrained();
            $table->string("response", 255);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists("evaluation_responses");
    }
};
