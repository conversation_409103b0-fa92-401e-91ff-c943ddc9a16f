<?php

use App\Models\System;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('system', function (Blueprint $table) {
            if (System::where('code_name', 'FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB')->doesntExist()) {
                System::create([
                    'code_name' => 'FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB',
                    'name' => 'Define se o botão flutuante abrirá uma nova aba',
                    'description' => 'Parâmetro que define se o botão flutuante abrirá uma nova aba',
                    'value' => "false",
                    'type' => 'boolean',
                    'visible_screen' => 1,
                    'required' => 0,
                ]);
            }

            if (System::where('code_name', 'SUPPORT_TAB_LINK')->doesntExist()) {
                System::create([
                    'code_name' => 'SUPPORT_TAB_LINK',
                    'name' => 'Link da página de suporte',
                    'description' => 'Parâmetro com o link que abrirá na tab de Suporte',
                    'value' => "https://visaonegocio.super.site/",
                    'type' => 'string',
                    'visible_screen' => 1,
                    'required' => 0
                ]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system', function (Blueprint $table) {
            System::whereIn('code_name', [
                'FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB',
                'SUPPORT_TAB_LINK',
            ])->delete();
        });
    }
};
