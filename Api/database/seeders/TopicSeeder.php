<?php

namespace Database\Seeders;

use App\Models\Business;
use App\Models\Question;
use App\Models\Topic;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TopicSeeder extends Seeder
{
    public function run(): void
    {
        // Criar tópicos e questões associadas
        $topicsData = [
            [
                'name' => 'Atendimento ao Cliente',
                'description' => 'Avaliação do atendimento oferecido pela empresa.',
                'questions' => [
                    ['description' => 'Como você avalia o tempo de espera para ser atendido?', 'evaluation_type' => 'five_star'],
                    ['description' => 'O atendente foi educado e prestativo?', 'evaluation_type' => 'yes_no'],
                ],
            ],
            [
                'name' => 'Qualidade do Produto',
                'description' => 'Avaliação da qualidade do produto adquirido.',
                'questions' => [
                    ['description' => 'Como você avalia a qualidade do produto adquirido?', 'evaluation_type' => 'five_star'],
                    ['description' => 'O produto atendeu às suas expectativas?', 'evaluation_type' => 'yes_no'],
                ],
            ],
            [
                'name' => 'Experiência de Compra',
                'description' => 'Avaliação da experiência de compra no site.',
                'questions' => [
                    ['description' => 'Como você avalia a facilidade de navegação no site?', 'evaluation_type' => 'five_star'],
                    ['description' => 'Encontrou alguma dificuldade ao finalizar a compra?', 'evaluation_type' => 'yes_no'],
                ],
            ],
            [
                'name' => 'Entrega',
                'description' => 'Avaliação do processo de entrega.',
                'questions' => [
                    ['description' => 'Como você avalia o estado da embalagem na entrega?', 'evaluation_type' => 'five_star'],
                    ['description' => 'Você recebeu notificações sobre o status da entrega?', 'evaluation_type' => 'yes_no'],
                ],
            ],
            [
                'name' => 'Suporte Técnico',
                'description' => 'Avaliação do suporte técnico oferecido pela empresa.',
                'questions' => [
                    ['description' => 'O tempo de resposta do suporte foi satisfatório?', 'evaluation_type' => 'five_star'],
                    ['description' => 'Você conseguiu resolver o problema com a ajuda do suporte?', 'evaluation_type' => 'yes_no'],
                ],
            ],
        ];

        // Criar tópicos e questões no banco de dados
        foreach ($topicsData as $topicData) {
            $topic = Topic::create([
                'name' => $topicData['name'],
                'description' => $topicData['description'],
                'default' => rand(0, 1),
                'active' => true,
            ]);

            foreach ($topicData['questions'] as $questionData) {
                Question::create([
                    'topic_id' => $topic->id,
                    'description' => $questionData['description'],
                    'evaluation_type' => $questionData['evaluation_type'],
                ]);
            }
        }

    }
}
