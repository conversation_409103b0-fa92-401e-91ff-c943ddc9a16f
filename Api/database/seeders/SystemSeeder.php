<?php

namespace Database\Seeders;

use App\Models\System;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemSeeder extends Seeder
{
    public function run(): void
    {
        $dados = [
            ['code_name' => 'LINK_APP_ANDROID', 'name' => 'Link app Android', 'description' => 'Link para download do aplicativo Android', 'value' => null, 'type' => 'string', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'LINK_APP_IOS', 'name' => 'Link app Ios', 'description' => 'Link para download do aplicativo IOS', 'value' => null, 'type' => 'string', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'MAIN_LOGO', 'name' => 'Logo principal', 'description' => 'Logo principal do site', 'value' => null, 'type' => 'image', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'MAIN_LOGO_THEME', 'name' => 'Logo em qualidade maior', 'description' => 'Logo usada na tela de login e na tela de perfil e configurações', 'value' => null, 'type' => 'image', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'APP_LOGO', 'name' => 'Logo do app', 'description' => 'Logo do aplicativo', 'value' => null, 'type' => 'image', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'TAB_LOGO', 'name' => 'Logo tab', 'description' => 'Logo da aba do aplicativo', 'value' => null, 'type' => 'image', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'MAIN_COLOR', 'name' => 'Cor principal', 'description' => 'Cor principal do aplicativo', 'value' => '#FBBC05', 'type' => 'color', 'visible_screen' => 1, 'required' => 1],
            ['code_name' => 'PHONE_NUMBER', 'name' => 'Telefone', 'description' => 'Número de telefone para contato', 'value' => null, 'type' => 'string', 'visible_screen' => 0, 'required' => 0],
            ['code_name' => 'EMAIL', 'name' => 'Email', 'description' => 'E-mail para contato', 'value' => null, 'type' => 'string', 'visible_screen' => 0, 'required' => 0],
            ['code_name' => 'BACKGROUND_IMAGE', 'name' => 'Imagem de fundo', 'description' => 'Plano de fundo do sistema', 'value' => null, 'type' => 'image', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'BACKGROUND_COLOR', 'name' => 'Cor de fundo', 'description' => 'Cor de fundo do sistema', 'value' => '#FFFFFF', 'type' => 'color', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'BUSINESS_EXPIRATION_DAYS', 'name' => 'Qtd de dias para expirar cadastro do negócio', 'description' => 'Parâmetro com a quantidade de dias para expirar cadastro de um negócio com aprovação pendente', 'value' => 30, 'type' => 'number', 'visible_screen' => 1, 'required' => 1],
            ['code_name' => 'EVALUATION_AVG_STAR_RATING', 'name' => 'Média das respostas para redirecionar para a url google do negócio', 'description' => 'Parâmetro com a média necessária para redirecionar o cliente para o google', 'value' => 4.5, 'type' => 'number', 'visible_screen' => 1, 'required' => 1],
            ['code_name' => 'EMPLOYEE_FIRST_LINK', 'name' => 'Primeiro link da tela inicial de funcionários', 'description' => 'Parâmetro com o primeiro link para renderizar na página inicial dos funcionários. (A exibição desse link pode ser pulada)', 'value' => "https://visaonegocio.super.site/", 'type' => 'string', 'visible_screen' => 1, 'required' => 1],
            ['code_name' => 'EMPLOYEE_SECOND_LINK', 'name' => 'Segundo link da tela inicial de funcionários', 'description' => 'Parâmetro com o segundo link para renderizar na página inicial dos funcionários.', 'value' => "https://visaonegocio.com/primeiros-passos/", 'type' => 'string', 'visible_screen' => 1, 'required' => 1],
            ['code_name' => 'FLOATING_BUTTON_LINK', 'name' => 'Link da página que vai ser exibida no botão flutuante', 'description' => 'Parâmetro com link para renderizar o conteúdo do botão flutuante', 'value' => "https://visaonegocio.super.site/", 'type' => 'string', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'FLOATING_BUTTON_ICON', 'name' => 'Ícone do botão flutuante', 'description' => 'Ícone do botão flutuante', 'value' => "", 'type' => 'image', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'CLIENT_FIRST_LINK', 'name' => 'Primeiro link da tela inicial de clientes', 'description' => 'Parâmetro com o primeiro link para renderizar na página inicial dos clientes. (A exibição desse link pode ser pulada)', 'value' => "https://visaonegocio.super.site/", 'type' => 'string', 'visible_screen' => 1, 'required' => 1],
            ['code_name' => 'CLIENT_SECOND_LINK', 'name' => 'Segundo link da tela inicial de clientes', 'description' => 'Parâmetro com o segundo link para renderizar na página inicial dos clientes.', 'value' => "https://visaonegocio.com/primeiros-passos/", 'type' => 'string', 'visible_screen' => 1, 'required' => 1],
            ['code_name' => 'FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB', 'name' => 'Define se o botão flutuante abrirá uma nova aba', 'description' => 'Parâmetro que define se o botão flutuante abrirá uma nova aba', 'value' => "false", 'type' => 'boolean', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'SUPPORT_TAB_LINK', 'name' => 'Link da página de suporte', 'description' => 'Parâmetro com o link que abrirá na tab de Suporte', 'value' => "https://visaonegocio.super.site/", 'type' => 'string', 'visible_screen' => 1, 'required' => 0],
            ['code_name' => 'ADMIN_BUSINESS_ID', 'name' => 'ID do negócio ADMIN do sistema', 'description' => 'Parâmetro com o ID do negócio que vai ser o ADMIN do sistema', 'value' => -1, 'type' => 'number', 'visible_screen' => 0, 'required' => 1],
            ['code_name' => 'DAYS_TO_TRANSFER_CASHBACK_AND_EVALUATION', 'name' => 'Quantidade de dias para transferir cashback e avaliação', 'description' => 'Parâmetro com a quantidade de dias para transferir o cashback e avaliação do cliente', 'value' => 30, 'type' => 'number', 'visible_screen' => 1, 'required' => 1],
          ];

        $existingCodes = System::whereIn('code_name', array_column($dados, 'code_name'))->pluck('code_name')->toArray();

        $newData = array_filter($dados, fn($dado) => !in_array($dado['code_name'], $existingCodes));

        System::insert($newData);
    }
}
