<?php

namespace Database\Seeders;

use App\Helpers\Environment;
use App\Models\Plan;
use Faker\Factory;
use Faker\Generator;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar um plano gratuito
        $freePlan = Plan::create([
            'name' => 'Plano Gratuito',
            'description' => 'Plano básico com funcionalidades limitadas.',
            'value' => 0,
            'active' => true,
            'publicly_visible' => true,
        ]);

        $freePlan->authorizedFunctions()->create([
            'employees' => 1,
            'manage_business_profile' => rand(0, 1) < 0.8 ? 1 : 0,
            'topics' => 1,
            'update_topics' => 1,
            'forms' => 1,
            'update_forms' => 1,
            'cashback' => 1,
            'update_cashback' => rand(0, 1) < 0.8 ? 1 : 0,
            'clients' => 1,
            'update_clients' => 1,
            'evaluations' => 1,
            'notifications' => rand(0, 1) < 0.8 ? 1 : 0,
            'update_notifications' => rand(0, 1) < 0.8 ? 1 : 0,
            'dashboard' => rand(0, 1) < 0.8 ? 1 : 0,
            'parameters' => rand(0, 1) < 0.8 ? 1 : 0,
            'site_notifications' => rand(0, 1) < 0.8 ? 1 : 0,
            'update_site_notifications' => rand(0, 1) < 0.8 ? 1 : 0,
        ]);

        // Criar um plano pago completo
        $paidPlan = Plan::create([
            'name' => 'Plano Completo',
            'description' => 'Plano completo com todas as funcionalidades disponíveis.',
            'value' => 99.99,
            'active' => true,
            'publicly_visible' => true,
        ]);

        $paidPlan->authorizedFunctions()->create([
            'employees' => 1,
            'manage_business_profile' => 1,
            'topics' => 1,
            'update_topics' => 1,
            'forms' => 1,
            'update_forms' => 1,
            'cashback' => 1,
            'update_cashback' => 1,
            'clients' => 1,
            'update_clients' => 1,
            'evaluations' => 1,
            'notifications' => 1,
            'update_notifications' => 1,
            'dashboard' => 1,
            'site_notifications' => 1,
            'update_site_notifications' => 1,
        ]);

        $this->command->info('Planos gratuitos e pagos criados com sucesso!');
    }
}
