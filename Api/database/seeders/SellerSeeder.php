<?php

namespace Database\Seeders;


use App\Helpers\Environment;
use App\Models\Seller;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SellerSeeder extends Seeder
{

    public function run(): void
    {
        $users = User::factory(12)->create(['login_type' => 'seller']);
        $users->map(function($user) {
            Seller::factory()->create(['user_id' => $user->id]);
        });
    }
}
