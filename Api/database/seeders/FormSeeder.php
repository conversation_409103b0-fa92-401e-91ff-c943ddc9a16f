<?php

namespace Database\Seeders;

use App\Models\Business;
use App\Models\Form;
use App\Models\Topic;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FormSeeder extends Seeder
{
    public function run(): void
    {
        Form::factory()
            ->count(14)
            ->create();

        $forms = Form::all();
        $allTopics = Topic::all();
        $allBusinesses = Business::all();

        foreach ($forms as $form) {
            $topics = $allTopics->random(rand(1, 5));
            $form->topics()->attach($topics);
            if(!$form->default) {
                $businesses = $allBusinesses->random();
                $form->businesses()->attach($businesses);
            }
        }

        $forms->each(function (Form $form) {
            $topics = $form->topics;
            foreach ($topics as $index => $topic) {
                $form->topics()->updateExistingPivot($topic->id, ['order' => $index + 1]);
            }
        });
    }
}
