<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        if (app()->environment('development')) {
            $this->call([
                SystemSeeder::class,
                EmployeeSeeder::class,
                PlanSeeder::class,
                BusinessEmployeeSeeder::class,
                SellerSeeder::class,
                TopicSeeder::class,
                FormSeeder::class,
            ]);
        } else {
            $this->call([SystemSeeder::class]);
        }
    }
}
