<?php

namespace Database\Seeders;

use App\Models\Business;
use App\Models\Employee;
use App\Models\EmployeeBusiness;
use App\Models\Plan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BusinessEmployeeSeeder extends Seeder
{
    public function run(): void
    {
        $plans = Plan::all();

        foreach ($plans as $plan) {
            $random = rand(1, 5);
            for ($i = 0; $i < $random; $i++) {
                $business = Business::factory()->create([
                    'plan_id' => $plan->id,
                    'status' => Business::STATUS_APPROVED_REGISTRATION,
                ]);

                $business->parameters()->create([
                    'business_id' => $business->id,
                    'form_id' => null,
                    'evaluation_expiration_hours' => 48,
                    'cashback_expiration_hours' => 720,
                    'cashback_percentage' => 5.00,
                    'evaluation_interval_day' => rand(1, 30),
                ]);
            }
        }

        // Associa negócios a funcionários
        $employees = Employee::all();

        foreach ($employees as $employee) {
            if ($employee->authorizedFunction->release_all_business) {
                continue;
            }
            $businesses = Business::inRandomOrder()->take(rand(2, 7))->pluck('id');
            foreach ($businesses as $businessId) {
                EmployeeBusiness::create([
                    'employee_id' => $employee->id,
                    'business_id' => $businessId,
                ]);
            }
        }

        //adicionar para cada negocio um admin dos seus funcionarios vinculados
        // Atribui um administrador para cada negócio
        $businesses = Business::all();
        foreach ($businesses as $business) {
            // Verifica se o negócio tem funcionários vinculados
            if ($business->employees->isNotEmpty()) {
                // Seleciona um funcionário aleatório para ser o administrador
                $employeeBusiness = $business->employees->random();

                // Define o funcionário como administrador
                EmployeeBusiness::where('employee_id', $employeeBusiness->id)
                    ->where('business_id', $business->id)
                    ->update(['admin' => true]);
            }
        }

    }
}
