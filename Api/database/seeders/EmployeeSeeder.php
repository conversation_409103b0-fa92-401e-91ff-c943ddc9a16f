<?php

namespace Database\Seeders;

use App\Helpers\Environment;
use App\Models\Employee;
use App\Models\Seller;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class EmployeeSeeder extends Seeder
{
    public function run()
    {
        $user_admin = User::factory()->create([
            'name' => 'User Name',
            'login_type' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456')
        ]);
        $user_admin->update(['email_verified_at' => now()]);

        $admin = Employee::factory()->create([
            'user_id' => $user_admin->id,
            'admin' => true
        ]);

        Seller::factory()->create(['user_id' => $user_admin->id]);

        $admin->authorizedFunction()->create([
            'employees' => true,
            'settings' => true,
            'release_all_business' => true,
            'manage_business_profile' => true,
            'plans' => true,
            'sellers' => true,
            'topics' => true,
            'default_topics' => true,
            'update_topics' => true,
            'forms' => true,
            'default_forms' => true,
            'update_forms' => true,
            'update_business' => true,
            'cashback' => true,
            'update_cashback' => true,
            'clients' => true,
            'update_clients' => true,
            'evaluations' => true,
            'notifications' => true,
            'update_notifications' => true,
            'dashboard' => true,
            'parameters' => true,
            'site_notifications' => true,
            'update_site_notifications' => true,
        ]);
        $admin->authorizedSettingsFunction()->create(['contact' => true, 'system_parameters' => true]);

        if (app()->environment('development')) {
            $dados = [
                ['id' => 2, 'name' => 'Teste 1 ghsdfh dfg', 'cpf' => '***********', 'email' => '<EMAIL>'],
                ['id' => 3, 'name' => 'Teste 2 cbvnn', 'cpf' => '***********', 'email' => '<EMAIL>'],
                ['id' => 4, 'name' => 'Teste 3 345252', 'cpf' => '***********', 'email' => '<EMAIL>'],
                ['id' => 5, 'name' => 'Teste 4 bcfndfgn', 'cpf' => '64516517701', 'email' => '<EMAIL>'],
                ['id' => 6, 'name' => 'Teste 5 35y4u5h iogdf', 'cpf' => '77597591349', 'email' => '<EMAIL>'],
                ['id' => 7, 'name' => 'Teste 6 sdfghsfgdj h fghjfgh jfghmnvb', 'cpf' => '18363498157', 'email' => '<EMAIL>'],
                ['id' => 8, 'name' => 'Teste 7tr yhe', 'cpf' => '20837582423', 'email' => '<EMAIL>'],
                ['id' => 9, 'name' => 'Teste 8rth ', 'cpf' => '18458548372', 'email' => '<EMAIL>'],
                ['id' => 10, 'name' => 'Teste 9nbvmvbn mbn23423', 'cpf' => '58386134160', 'email' => '<EMAIL>'],
                ['id' => 11, 'name' => 'Teste 10 vhmbvnm sdz', 'cpf' => '18552522527', 'email' => '<EMAIL>'],
                ['id' => 12, 'name' => 'Teste 11 zxcvz', 'cpf' => '55612832869', 'email' => '<EMAIL>'],
                ['id' => 13, 'name' => 'Teste 12 bnvccbvnfg', 'cpf' => '65733148675', 'email' => '<EMAIL>'],
                ['id' => 14, 'name' => 'Teste 13 bmnvbnmtdr31', 'cpf' => '40555612708', 'email' => '<EMAIL>'],
                ['id' => 15, 'name' => 'Teste 14 çpoiipo', 'cpf' => '73357764652', 'email' => '<EMAIL>'],
            ];

            foreach ($dados as $dado) {
                DB::table('users')->updateOrInsert(
                    [
                        'id' => $dado['id'],
                    ],
                    [
                        'id' => $dado['id'],
                        'name' => $dado['name'],
                        'cpf' => $dado['cpf'],
                        'email' => $dado['email'],
                        'password' => Hash::make('123123123'),
                        'birth_date' => '1997-09-12',
                        'gender' => 'm',
                        'phone_number_1' => '87988888888',
                        'phone_number_2' => '8788888888',
                        'neighborhood' => 'Padre Cícero',
                        'street' => 'José Freitas',
                        'number' => '12',
                        'city' => 'Ibimirim',
                        'state' => 'PE',
                        'cep' => '56580000',
                        'complement' => 'Fim da rua',
                        'login_type' => 'employee',
                    ]
                );

                DB::table('employees')->updateOrInsert(
                    [
                        'id' => $dado['id'],
                    ],
                    [
                        'id' => $dado['id'],
                        'user_id' => $dado['id'],
                        'admin' => 0,
                    ]
                );

                DB::table('authorized_functions')->insertOrIgnore([
                    'employees' => 1,
                    'settings' => 1,
                    'manage_business_profile' => 0,
                    'release_all_business' => 0,
                    'plans' => 0,
                    'topics' => rand(0, 1),
                    'default_topics' => rand(0, 1),
                    'update_topics' => rand(0, 1),
                    'forms' => rand(0, 1),
                    'default_forms' => rand(0, 1),
                    'update_forms' => rand(0, 1),
                    'update_business' => rand(0, 1),
                    'cashback' => rand(0, 1),
                    'update_cashback' => rand(0, 1),
                    'clients' => rand(0, 1),
                    'update_clients' => rand(0, 1),
                    'evaluations' => rand(0, 1),
                    'notifications' => rand(0, 1),
                    'update_notifications' => rand(0, 1),
                    'dashboard' => rand(0, 1),
                    'parameters' => rand(0, 1),
                    'site_notifications' => rand(0, 1),
                    'update_site_notifications' => rand(0, 1),
                    'employee_id' => $dado['id'],
                ]);

                DB::table('authorized_settings_functions')->insertOrIgnore([
                    'id' => $dado['id'],
                    'system_parameters' => 1,
                    'contact' => 1,
                    'employee_id' => $dado['id'],
                ]);
            }
        }

    }
}
