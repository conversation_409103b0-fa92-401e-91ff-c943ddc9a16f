APP_NAME='Visão Negócio'
APP_ENV=docker
APP_KEY=base64:ecA9Ar5czodybTicjKnUF/iy4ZwhceTKHM2d9Cee8UU=
APP_DEBUG=true

FRONTEND_URL=http://frontend:8000

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=visao_negocio
DB_USERNAME=visao_negocio
DB_PASSWORD=123456

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=73pghv5ofPIxpBWHLF0LsXMnJsOig1fUP04ThQIu
AWS_DEFAULT_REGION=sa-east-1
AWS_BUCKET=api.maisluz.homolog

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=b09igvR8gXz7YfYm45sbDiuA9pDbkvBSEZKqA73whZXDpwB4isHhRWNScLnjQhTB

JWT_TTL=1440

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mippxltjgvwmrbkq
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"












