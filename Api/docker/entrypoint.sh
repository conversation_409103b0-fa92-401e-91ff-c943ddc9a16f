#!/bin/sh

# Esperar que o banco de dados esteja pronto
echo "Esperando o banco de dados estar pronto..."
while ! nc -z $DB_HOST $DB_PORT; do
  sleep 0.1
done
echo "Banco de dados está pronto!"

# Executar as migrations
echo "Executando migrations..."
php artisan migrate --force

echo "Executando 'php artisan optimize'"
php artisan optimize

echo "Executando 'php artisan route:clear'"
php artisan route:clear

echo "Executando 'php artisan config:clear'"
php artisan config:clear

echo "Executando 'php artisan route:cache'"
php artisan route:cache

# Executar o comando passado no CMD
exec "$@"
