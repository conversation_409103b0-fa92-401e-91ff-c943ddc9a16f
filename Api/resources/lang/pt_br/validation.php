<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'O :attribute deve ser aceito.',
    'accepted_if' => 'The :attribute must be accepted when :other is :value.',
    'active_url' => 'O :attribute não é uma URL valida.',
    'after' => 'A :attribute deve uma data depois de :date.',
    'after_or_equal' => 'The :attribute must be a date after or equal to :date.',
    'alpha' => 'The :attribute must only contain letters.',
    'alpha_dash' => 'The :attribute must only contain letters, numbers, dashes and underscores.',
    'alpha_num' => 'The :attribute must only contain letters and numbers.',
    'array' => ':attribute deve ser uma lista.',
    'before' => 'A :attribute deve ser antes de :date.',
    'before_or_equal' => 'A :attribute deve ser antes ou igual a :date.',
    'between' => [
        'numeric' => ':attribute deve estar entre :min e :max.',
        'file' => ':attribute deve estar entre :min e :max KB.',
        'string' => ':attribute deve ter entre :min e :max characteres.',
        'array' => ':attribute deve ter entre :min e :max itens.',
    ],
    'boolean' => ':attribute deve ser verdadeiro ou falso.',
    'confirmed' => ':attribute e confirmação de :attribute não são iguais.',
    'current_password' => 'A senha é incorrecta.',
    'date' => 'O campo :attribute não é uma data válida.',
    'date_equals' => 'A :attribute deve ser igual a :date',
    'date_format' => ':attribute não está no formato :format.',
    'different' => 'The :attribute and :other must be different.',
    'digits' => ':attribute deve ter :digits digitos.',
    'digits_between' => 'Deve ter de :min a :max digitos.',
    'dimensions' => 'The :attribute has invalid image dimensions.',
    'distinct' => 'The :attribute field has a duplicate value.',
    'email' => 'O :attribute deve ser um email válido.',
    'ends_with' => 'The :attribute must end with one of the following: :values.',
    'exists' => 'O :attribute selecionado é invalido.',
    'file' => 'The :attribute must be a file.',
    'filled' => 'The :attribute field must have a value.',
    'gt' => [
        'numeric' => 'The :attribute must be greater than :value.',
        'file' => 'The :attribute must be greater than :value kilobytes.',
        'string' => 'The :attribute must be greater than :value characters.',
        'array' => 'The :attribute must have more than :value items.',
    ],
    'gte' => [
        'numeric' => 'The :attribute must be greater than or equal to :value.',
        'file' => 'The :attribute must be greater than or equal to :value kilobytes.',
        'string' => 'The :attribute must be greater than or equal to :value characters.',
        'array' => 'The :attribute must have :value items or more.',
    ],
    'image' => ':attribute deve ser uma imagem.',
    'in' => ':attribute é invalido.',
    'in_array' => 'The :attribute field does not exist in :other.',
    'integer' => 'The :attribute must be an integer.',
    'ip' => 'The :attribute must be a valid IP address.',
    'ipv4' => 'The :attribute must be a valid IPv4 address.',
    'ipv6' => 'The :attribute must be a valid IPv6 address.',
    'json' => 'The :attribute must be a valid JSON string.',
    'lt' => [
        'numeric' => 'The :attribute must be less than :value.',
        'file' => 'The :attribute must be less than :value kilobytes.',
        'string' => 'The :attribute must be less than :value characters.',
        'array' => 'The :attribute must have less than :value items.',
    ],
    'lte' => [
        'numeric' => 'The :attribute must be less than or equal to :value.',
        'file' => 'The :attribute must be less than or equal to :value kilobytes.',
        'string' => 'The :attribute must be less than or equal to :value characters.',
        'array' => 'The :attribute must not have more than :value items.',
    ],
    'max' => [
        'numeric' => ':attribute não deve ser maior que :max.',
        'file' => ':attribute não deve ser maior que :max KB.',
        'string' => ':attribute deve ter no máximo :max characteres.',
        'array' => ':attribute não deve ter mais que :max itens.',
    ],
    'mimes' => ':attribute deve ser um arquivo do tipo: :values.',
    'mimetypes' => 'The :attribute must be a file of type: :values.',
    'min' => [
        'numeric' => ':attribute deve ser no mínimo :min.',
        'file' => 'The :attribute must be at least :min kilobytes.',
        'string' => ':attribute deve ter pelo menos :min characteres.',
        'array' => 'The :attribute must have at least :min items.',
    ],
    'multiple_of' => 'The :attribute must be a multiple of :value.',
    'not_in' => 'The selected :attribute is invalid.',
    'not_regex' => 'The :attribute format is invalid.',
    'numeric' => 'O campo :attribute deve ser um número.',
    'password' => 'A password é incorreta.',
    'present' => 'The :attribute field must be present.',
    'regex' => 'The :attribute format is invalid.',
    'required' => 'O campo :attribute deve ser informado.',
    'required_if' => 'O campo :attribute é necessário quanto :other é :value.',
    'required_unless' => 'The :attribute field is required unless :other is in :values.',
    'required_with' => 'The :attribute field is required when :values is present.',
    'required_with_all' => 'The :attribute field is required when :values are present.',
    'required_without' => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'prohibited' => 'The :attribute field is prohibited.',
    'prohibited_if' => 'The :attribute field is prohibited when :other is :value.',
    'prohibited_unless' => 'O campo :attribute é proibido a menos que :other seja selecionado.',
    'prohibits' => 'The :attribute field prohibits :other from being present.',
    'same' => 'The :attribute and :other must match.',
    'size' => [
        'numeric' => 'The :attribute must be :size.',
        'file' => 'The :attribute must be :size kilobytes.',
        'string' => 'The :attribute must be :size characters.',
        'array' => 'The :attribute must contain :size items.',
    ],
    'starts_with' => 'The :attribute must start with one of the following: :values.',
    'string' => 'The :attribute must be a string.',
    'timezone' => 'The :attribute must be a valid timezone.',
    'unique' => ':attribute informado já existe.',
    'uploaded' => 'The :attribute failed to upload.',
    'url' => 'The :attribute must be a valid URL.',
    'uuid' => 'The :attribute must be a valid UUID.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'installation_date' => [
            'before_or_equal' => ':attribute deve ser antes ou igual a hoje('.(now()->format('d-m-Y')).')',
        ],
        'birth_date' => [
            'before' => ':attribute inválida',
        ],
        'number' => [
            'required' => 'Número informado inválido. (Ex: 01 ou SN)',
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'description' => 'descrição',
        'value' => 'valor',
        'employees' => 'funcionários',
        'light_pole' => 'postes de luz',
        'adjustment_requests' => 'solicitação de ajuste',
        'name' => 'nome',
        'pole_number' => 'numero do poste',
        'installation_date' => 'data de instalação',
        'last_inspection_date' => 'última manutenção',
        'latitude' => 'latitude',
        'longitude' => 'longitude',
        'street' => 'rua',
        'district' => 'bairro',
        'cep' => 'CEP',
        'complement' => 'complemento',
        'date' => 'data',
        'email' => 'email',
        'password' => 'senha',
        'new_password' => 'nova senha',
        'new_password_confirmation' => 'confirmação nova senha',
        'birth_date' => 'data de nascimento',
        'cpf' => 'CPF',
        'gender' => 'sexo',
        'phone_number_1' => 'telefone 1',
        'phone_number_2' => 'telefone 2',
        'neighborhood' => 'bairro',
        'number' => 'número',
        'city' => 'cidade',
        'state' => 'estado',
        'type' => 'tipo',
        'start_date' => 'data inicial',
        'end_date' => 'data final',
        'lamp_type_light_pole_id' => 'tipo de lâmpada',
        'topics' => 'topicos',
        'default_topics' => 'topicos padrão',
        'update_topics' => 'atualizar topicos',
    ],
];
