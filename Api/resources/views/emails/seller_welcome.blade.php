<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Bem-vindo ao {{ config('app.name') }}</title>
    <style>
        :root {
            --main-color: {{ $mainColor ?? '#FBBC05' }};
        }
        /* Basic Reset and Styles */
        body, p, h1, h2, h3, h4, h5, h6, a {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            color: #333333;
        }
        body {
            background-color: #f4f4f4;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            background-color: #ffffff;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #FFFFFF;
            padding: 8px;
            text-align: center;
            border-bottom: 5px solid {{ $mainColor ?? '#FBBC05' }};
        }
        .header img.system-logo {
            max-width: 60px;
            height: auto;
        }
        .content {
            padding: 20px;
        }
        .content h2 {
            color: {{ $mainColor ?? '#FBBC05' }};
            margin-bottom: 15px;
            font-size: 24px;
            text-align: start;
        }
        .content p {
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 16px;
            text-align: justify;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            font-size: 16px;
            color: #ffffff;
            background-color: {{ $mainColor ?? '#FBBC05' }};
            text-decoration: none;
            border-radius: 25px;
            transition: background-color 0.3s ease;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            font-size: 14px;
            color: #777777;
        }
        .footer a {
            color: {{ $mainColor ?? '#FBBC05' }};
            text-decoration: none;
            margin: 0 5px;
            font-weight: bold;
        }
        /* Responsive Design */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
                border-radius: 0;
            }
            .header img.system-logo {
                max-width: 60px;
            }
            .btn {
                width: 100%;
                padding: 15px 0;
            }
            .content p {
                text-align: left;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <!-- Header with System Logo -->
    <div class="header">
        <!-- System Logo -->
        @if ($systemLogo)
            <img src="{{ $systemLogo }}" alt="{{ config('app.name') }} Logo" class="system-logo">
        @else
            <h2 style="color: black;">{{ config('app.name') }}</h2>
        @endif
    </div>

    <!-- Main Content -->
    <div class="content">
        <h2>Olá, {{ $user->name }}!</h2>
        <p>Seja bem-vindo ao {{ config('app.name') }}. Sua conta foi criada com sucesso.</p>
        <p>Aqui estão suas credenciais de acesso:</p>
        <p><strong>E-mail:</strong> {{ $user->email }}</p>
        <p><strong>Senha:</strong> {{ $password }}</p>
        <p>Recomendamos que você altere sua senha após o primeiro login para garantir a segurança da sua conta.</p>

        <!-- Call-to-Action Button -->
        <div class="button-container">
            <a href="{{ $loginUrl }}" class="btn" style="color: #fff">Fazer Login</a>
        </div>
    </div>

    <!-- Footer with Additional Information -->
    <div class="footer">
        <p><a href="{{ config('app.frontend_url') }}">Visite nosso site</a></p>
    </div>
</div>
</body>
</html>
