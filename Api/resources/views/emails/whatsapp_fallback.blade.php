@extends('layouts.email')

@section('content')
    @php
        $title = str_replace('*', '', $title); // retira os * do título
        // substitui palavras *algo* por negrito do HTML
        $description = preg_replace('/\*(.*?)\*/', '<strong>$1</strong>', $description);

        // converte \n pra tag br do HTML
        $description = nl2br($description);

        $description = preg_replace_callback(
            '/(https?:\/\/[^\s]+)/', 
            function ($matches) use ($mainColor) {
                $url = $matches[1];
                return '<div class="button-container" style="text-align: center; margin: 20px 0;">
                            <a href="' . $url . '" class="btn" style="color: #fff">
                                Acessar Link
                            </a>
                        </div>';
            },
            $description
        );

        $businessName = $businessName;
        $businessLogo = $businessLogo;
        $systemLogo = $systemLogo;
        $mainColor = $mainColor;
    @endphp

    <h2>{{ $title }}</h2>
    <p>{!! $description !!}</p>

    <p>Atenciosamente,<br><strong>{{ ucfirst($businessName) }}</strong></p>
@endsection