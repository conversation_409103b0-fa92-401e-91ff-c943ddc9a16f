@extends('layouts.email')

@section('content')
    @php
        $businessName = $businessName;
        $businessLogo = $businessLogo;
        $systemLogo = $systemLogo;
        $mainColor = $mainColor;
        $lastMonthName = $lastMonth->translatedFormat('F Y');
    @endphp

    <h2>Registro cancelado!</h2>
    <p>Notificamos que o registro de <b>{{ucfirst($businessName)}}</b> e o pagamento recorrente foram cancelados!</p>
    <p>
        Será cobrado apenas o pagamento pendente referente ao último mês corrente 
        (<b>{{ ucfirst($lastMonthName) }}</b>) no valor de 
        <b>R$ {{ number_format($planValue, 2, ',', '.') }}</b>.
    </p>

    <!-- Botão de Chamada para Ação -->
    <div class="button-container">
        <a href="{{ config('app.frontend_url') . '/support' }}" 
            class="btn" 
            style="color: #fff">
            Entrar em contato conosco
        </a>
    </div>

    <p>Atenciosamente,<br><strong>{{ config('app.name') }}</strong></p>
@endsection
