@extends('layouts.email')

@section('content')
    @php
        $businessName = $businessName;
        $businessLogo = $businessLogo;
        $systemLogo = $systemLogo;
        $mainColor = $mainColor;
    @endphp

    <h2>Código de Validação de Cashback</h2>

    <p>O<PERSON><PERSON>,</p>

    <p>O negócio <strong>{{ $businessName }}</strong> verificou que você possui <strong>R$ {{ number_format($totalAmount, 2, ',', '.') }}</strong> em cashback disponível e está solicitando a validação para utilizá-lo.</p>

    <p><strong>Para completar o processo:</strong></p>
    <ol style="margin-left: 20px; color: #333333;">
        <li style="color: #333333;">Informe o código abaixo para o funcionário</li>
        <li style="color: #333333;">Após a confirmação, seu cashback será aplicado</li>
    </ol>

    <div style="background-color: #f8f9fa; border: 2px solid {{ $mainColor ?? '#FBBC05' }}; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center;">
        <h3 style="margin: 0; font-size: 32px; font-weight: bold; color: {{ $mainColor ?? '#FBBC05' }}; letter-spacing: 4px;">
            {{ $validationCode }}
        </h3>
    </div>

    <p><strong>Importante:</strong></p>
    <ul style="margin-left: 20px; color: #333333;">
        <li style="color: #333333;">Este código é válido por apenas <strong>10 minutos</strong></li>
        <li style="color: #333333;">Deve dar baixa exclusivamente no estabelecimento onde foi solicitado</li>
        <li style="color: #333333;">Após informar o código, seu cashback será automaticamente concluído</li>
    </ul>

    <p>Se você não está no estabelecimento ou não solicitou este código, pode ignorar este email com segurança.</p>

    <p>Atenciosamente,<br><strong>{{ $businessName }}</strong></p>
@endsection