@extends('layouts.email')

@section('content')
    @php
        $businessName = $businessName;
        $businessLogo = $businessLogo;
        $systemLogo = $systemLogo;
        $mainColor = $mainColor;
    @endphp

    <h2>{{ "O registro do seu negócio foi ". ($isApproved ? 'aprovado!' : 'rejeitado!')}}</h2>

    
    @if ($isApproved)
        <p>Boas notícias!</p>
        <p><b>{{ucfirst($businessName)}}</b> acaba de ter seu registro aprovado!</p>
        <p>Você pode acessar nossa plataforma e utilizar todos os recursos disponíveis.</p>
        <p>Clique no botão abaixo para visitar a plataforma <b>{{ config('app.name') }}</b>.</p>
    @else
        <p>Infelizmente, o registro de <b>{{ucfirst($businessName)}}</b> não foi aprovado neste momento.</p>
        <p>Isso pode ter ocorrido pelos seguintes motivos:</p>
        <ul style="padding-left: 24px">
            <li style="font-size: 1rem; color: #000"><b>Informações inconsistentes</b></li>
            <li style="font-size: 1rem; color: #000"><b>Não atendimento aos requisitos mínimos</b></li>
        </ul>
    @endif

    <!-- Botão de Chamada para Ação -->
    <div class="button-container">
        <a href="{{ $isApproved ? config('app.frontend_url') : config('app.frontend_url') . '/support' }}" 
            class="btn" 
            style="color: #fff">
            {{$isApproved ? 'Ver plataforma' : 'Entrar em contato conosco'}}
        </a>
    </div>

    <p>Atenciosamente,<br><strong>{{ config('app.name') }}</strong></p>
@endsection
