{"swagger": "2.0", "info": {"title": "+Serviços Prestados", "description": "+Serviços Prestados API", "version": "1.0.0"}, "host": null, "basePath": "/", "schemes": ["http", "https"], "consumes": ["application/json", "multipart/form-data"], "produces": ["application/json"], "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "description": "Enter the token with the `Bearer `. Example: 'Bearer Xkjlkjsdljskdjf'"}}, "paths": {"/api/user": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}}}, "/api": {"get": {"summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}}}, "/api/city/by-url": {"get": {"summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "url", "type": "string", "required": true, "description": ""}]}}, "/api/city/get-contact": {"get": {"summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}}, "/api/app_latest_version": {"get": {"summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}}, "/api/get_app_link": {"get": {"summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}}, "/api/auth/login": {"post": {"summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["email", "password", "city_id"], "properties": {"email": {"type": "string"}, "password": {"type": "string"}, "city_id": {"type": "string"}}}}]}}, "/api/auth/logout": {"post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["city_id"], "properties": {"city_id": {"type": "string"}}}}]}}, "/api/auth/refresh": {"post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["city_id"], "properties": {"city_id": {"type": "string"}}}}]}}, "/api/auth/me": {"post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["city_id"], "properties": {"city_id": {"type": "string"}}}}]}}, "/api/settings/contact": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}, "put": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["city_id"], "properties": {"email": {"type": "string"}, "phone_number": {"type": "string"}, "city_id": {"type": "string"}}}}]}}, "/api/settings/global_parameters": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}, "put": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["city_id"], "properties": {"city_id": {"type": "string"}}}}]}}, "/api/global_parameters/{code_name}": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "path", "name": "code_name", "type": "string", "required": true, "description": ""}, {"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}}, "/api/user/update": {"put": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["name", "phone_number_1", "neighborhood", "street", "number", "city", "state", "cep", "city_id"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "birth_date": {"type": "string"}, "cpf": {"type": "number"}, "gender": {"type": "string", "enum": ["f", "m", "other"]}, "phone_number_1": {"type": "number"}, "phone_number_2": {"type": "number"}, "neighborhood": {"type": "string"}, "street": {"type": "string"}, "number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "cep": {"type": "number"}, "complement": {"type": "string"}, "admin": {"type": "boolean"}, "employees": {"type": "boolean"}, "light_pole": {"type": "boolean"}, "adjustment_requests": {"type": "boolean"}, "city_id": {"type": "string"}}}}]}}, "/api/user/validate/update": {"post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["name", "phone_number_1", "neighborhood", "street", "number", "city", "state", "cep", "city_id"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "birth_date": {"type": "string"}, "cpf": {"type": "number"}, "gender": {"type": "string", "enum": ["f", "m", "other"]}, "phone_number_1": {"type": "number"}, "phone_number_2": {"type": "number"}, "neighborhood": {"type": "string"}, "street": {"type": "string"}, "number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "cep": {"type": "number"}, "complement": {"type": "string"}, "admin": {"type": "boolean"}, "employees": {"type": "boolean"}, "light_pole": {"type": "boolean"}, "adjustment_requests": {"type": "boolean"}, "city_id": {"type": "string"}}}}]}}, "/api/user/password/update": {"put": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["password", "new_password", "city_id"], "properties": {"password": {"type": "string"}, "new_password": {"type": "string"}, "city_id": {"type": "string"}}}}]}}, "/api/check-permission": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}}, "/api/user/update-email-verified-at": {"post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}}}, "/api/employees": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}, "post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["name", "password", "birth_date", "cpf", "phone_number_1", "neighborhood", "street", "number", "city", "state", "cep", "city_id"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "birth_date": {"type": "string"}, "cpf": {"type": "number"}, "gender": {"type": "string", "enum": ["f", "m", "other"]}, "phone_number_1": {"type": "number"}, "phone_number_2": {"type": "number"}, "neighborhood": {"type": "string"}, "street": {"type": "string"}, "number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "cep": {"type": "number"}, "complement": {"type": "string"}, "admin": {"type": "boolean"}, "employees": {"type": "boolean"}, "light_pole": {"type": "boolean"}, "adjustment_requests": {"type": "boolean"}, "settings": {"type": "boolean"}, "city_id": {"type": "string"}}}}]}}, "/api/employees/{employee}": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "path", "name": "employee", "type": "string", "required": true, "description": ""}, {"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}, "put": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "path", "name": "employee", "type": "string", "required": true, "description": ""}, {"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["name", "cpf", "phone_number_1", "neighborhood", "street", "number", "city", "state", "cep", "city_id"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "birth_date": {"type": "string"}, "cpf": {"type": "number"}, "gender": {"type": "string", "enum": ["f", "m", "other"]}, "phone_number_1": {"type": "number"}, "phone_number_2": {"type": "number"}, "neighborhood": {"type": "string"}, "street": {"type": "string"}, "number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "cep": {"type": "number"}, "complement": {"type": "string"}, "admin": {"type": "boolean"}, "employees": {"type": "boolean"}, "light_pole": {"type": "boolean"}, "adjustment_requests": {"type": "boolean"}, "city_id": {"type": "string"}}}}]}, "patch": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "path", "name": "employee", "type": "string", "required": true, "description": ""}, {"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["name", "cpf", "phone_number_1", "neighborhood", "street", "number", "city", "state", "cep", "city_id"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "birth_date": {"type": "string"}, "cpf": {"type": "number"}, "gender": {"type": "string", "enum": ["f", "m", "other"]}, "phone_number_1": {"type": "number"}, "phone_number_2": {"type": "number"}, "neighborhood": {"type": "string"}, "street": {"type": "string"}, "number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "cep": {"type": "number"}, "complement": {"type": "string"}, "admin": {"type": "boolean"}, "employees": {"type": "boolean"}, "light_pole": {"type": "boolean"}, "adjustment_requests": {"type": "boolean"}, "city_id": {"type": "string"}}}}]}, "delete": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "path", "name": "employee", "type": "string", "required": true, "description": ""}, {"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}}, "/api/employees/validate/store": {"post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["name", "password", "birth_date", "cpf", "phone_number_1", "neighborhood", "street", "number", "city", "state", "cep", "city_id"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "birth_date": {"type": "string"}, "cpf": {"type": "number"}, "gender": {"type": "string", "enum": ["f", "m", "other"]}, "phone_number_1": {"type": "number"}, "phone_number_2": {"type": "number"}, "neighborhood": {"type": "string"}, "street": {"type": "string"}, "number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "cep": {"type": "number"}, "complement": {"type": "string"}, "admin": {"type": "boolean"}, "employees": {"type": "boolean"}, "light_pole": {"type": "boolean"}, "adjustment_requests": {"type": "boolean"}, "settings": {"type": "boolean"}, "city_id": {"type": "string"}}}}]}}, "/api/employees/validate/update/{id}": {"post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "path", "name": "id", "type": "string", "required": true, "description": ""}, {"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["name", "cpf", "phone_number_1", "neighborhood", "street", "number", "city", "state", "cep", "city_id"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "birth_date": {"type": "string"}, "cpf": {"type": "number"}, "gender": {"type": "string", "enum": ["f", "m", "other"]}, "phone_number_1": {"type": "number"}, "phone_number_2": {"type": "number"}, "neighborhood": {"type": "string"}, "street": {"type": "string"}, "number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "cep": {"type": "number"}, "complement": {"type": "string"}, "admin": {"type": "boolean"}, "employees": {"type": "boolean"}, "light_pole": {"type": "boolean"}, "adjustment_requests": {"type": "boolean"}, "city_id": {"type": "string"}}}}]}}, "/api/employees/validate/cpf_exist": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}}, "/api/employees/search": {"get": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "query", "name": "city_id", "type": "string", "required": true, "description": ""}]}, "post": {"security": [{"Bearer": []}], "summary": "", "description": "", "deprecated": false, "responses": {"200": {"description": "OK"}}, "parameters": [{"in": "body", "name": "body", "description": "", "schema": {"type": "object", "required": ["city_id"], "properties": {"city_id": {"type": "string"}}}}]}}}}