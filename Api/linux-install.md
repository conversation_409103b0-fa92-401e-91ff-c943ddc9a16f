# Instruções de Deploy para Aplicação Visão Negócio no Ubuntu

### Instalar o PHP 8.1 (admin e api)

1. **Atualizar os Pacotes do Sistema**:

   ```bash
   apt update
   ```

2. **Instalar Dependências Necessárias**:

   O PHP requer algumas dependências adicionais, como `software-properties-common` para adicionar repositórios externos:

   ```bash
   apt install -y software-properties-common
   ```

3. **Adicionar o Repositório do PHP**

   ```bash
   add-apt-repository ppa:ondrej/php
   ```

4. **Atualizar Novamente os Pacotes**: Depois de adicionar o repositório, execute o comando de atualização novamente:

   ```bash
   apt update
   ```

5. **Instalar o PHP 8.1**:

   ```bash
   apt install -y php8.1
   ```

6. **Instalar Extensões do PHP Necessárias para o Laravel**: Algumas extensões são essenciais para o Laravel funcionar corretamente:

   ```bash
   apt install -y php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip
   ```

7. **Ajustar o arquivo /etc/php/8.1/cli/php.ini**:

    ```bash
    sudo nano /etc/php/8.1/cli/php.ini
    ```
   Ajustar o tamanho máximo de upload de arquivos:
    ```bash
    upload_max_filesize = 5M
    ```

8. **Verificar a Instalação do PHP**:

   Para verificar se o PHP foi instalado corretamente, execute o comando abaixo:

   ```bash
   php -v
   ```

   Se tudo estiver correto, você verá a versão do PHP instalada, como:

   ```
   PHP 8.1.30 (cli) (built: Sep 27 2024 04:07:54) (NTS)
   Copyright (c) The PHP Group
   Zend Engine v4.1.30, Copyright (c) Zend Technologies
       with Zend OPcache v8.1.30, Copyright (c), by Zend Technologies
   ```

### Instalar o Composer (admin e api)

1. **Baixar o Instalador do Composer**:

   O Composer é um gerenciador de dependências para o PHP, necessário para projetos Laravel. Primeiro, baixe o instalador do Composer:

   ```bash
   curl -sS https://getcomposer.org/installer -o composer-setup.php
   ```

   Caso o `curl` não esteja instalado, use o comando:

   ```bash
   apt install curl
   ```

2. **Instalar o Composer**:

   Após a verificação, execute o instalador:

   ```bash
   php composer-setup.php --install-dir=/usr/local/bin --filename=composer
   ```

3. **Verificar a Instalação do Composer**:

   Verifique se o Composer foi instalado corretamente:

   ```bash
   composer -v
   ```

   Deve aparecer algo como:

   ```
   Composer version 2.8.2 2024-10-29 16:12:11
   PHP version 8.1.30 (/usr/bin/php8.1)
   ```

### Resumo dos Comandos Utilizados

```bash
# Atualizar os Pacotes do Sistema
apt update

# Instalar Dependências Necessárias
apt install -y software-properties-common

# Adicionar o Repositório do PHP
add-apt-repository ppa:ondrej/php

# Atualizar Novamente os Pacotes
apt update

# Instalar o PHP 8.1
apt install -y php8.1

# Instalar Extensões do PHP Necessárias para o Laravel
apt install -y php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip

# Verificar a Instalação do PHP
php -v

# Baixar o Instalador do Composer
curl -sS https://getcomposer.org/installer -o composer-setup.php

# Instalar o Composer
php composer-setup.php --install-dir=/usr/local/bin --filename=composer

# Verificar a Instalação do Composer
composer -v
```

### Script de Instalação Automática

Abaixo está um script que automatiza todo o processo descrito acima:

Crie um arquivo chamado `install_laravel_env.sh` com o conteúdo abaixo e execute-o em seu sistema.

```bash
#!/bin/bash

# Atualizar os Pacotes do Sistema
apt update

# Instalar Dependências Necessárias
apt install -y software-properties-common curl

# Adicionar o Repositório do PHP
add-apt-repository ppa:ondrej/php -y

# Atualizar Novamente os Pacotes
apt update

# Instalar o PHP 8.1
apt install -y php8.1

# Instalar Extensões do PHP Necessárias para o Laravel
apt install -y php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip

# Configurar o Fuso Horário (America/Recife)
export DEBIAN_FRONTEND=noninteractive
ln -fs /usr/share/zoneinfo/America/Recife /etc/localtime
apt install -y tzdata
dpkg-reconfigure --frontend noninteractive tzdata

# Verificar a Instalação do PHP
php -v

# Baixar o Instalador do Composer
curl -sS https://getcomposer.org/installer -o composer-setup.php

# Instalar o Composer
php composer-setup.php --install-dir=/usr/local/bin --filename=composer

# Verificar a Instalação do Composer
composer -v

# Limpar o Instalador do Composer
rm composer-setup.php

echo "Instalação concluída com sucesso!"
```

Para tornar o script executável, execute:

```bash
chmod +x install_laravel_env.sh
```

Em seguida, execute o script:

```bash
./install_laravel_env.sh
```

Esse script automatiza todas as etapas de instalação do PHP, das extensões necessárias, e do Composer, facilitando a configuração do ambiente Laravel.

### Configurações do Projeto

Após a instalação do PHP e do Composer, siga as etapas abaixo para configurar cada projeto Laravel:

1. **Instalar Dependências do Composer nos Ambientes Frontend (Admin) e Backend (Api)**:

   Navegue até a pasta de cada projeto e execute o comando abaixo:

   ```bash
   # Frontend (Admin)
   cd /caminho/para/Admin
   composer install
   
   # Backend (Api)
   cd /caminho/para/Api
   composer install
   ```

2. **Configuração do Arquivo `.env` em Cada Ambiente**:

   Copie o arquivo `.env.example` e renomeie para `.env` em cada um dos projetos:

   ```bash
   # Frontend (Admin)
   cp /caminho/para/Admin/.env.example /caminho/para/Admin/.env
   
   # Backend (Api)
   cp /caminho/para/Api/.env.example /caminho/para/Api/.env
   ```

   Modifique as informações abaixo em cada arquivo `.env`:

    - **Frontend (Admin)**:
        - **API_URL**: Defina a URL da API utilizada no frontend.
        - **Configurações de Email**: Defina as configurações de email, como `MAIL_MAILER`, `MAIL_HOST`, `MAIL_PORT`, `MAIL_USERNAME`, `MAIL_PASSWORD`, etc.

    - **Backend (Api)**:
        - **Banco de Dados**: Configure as credenciais de conexão para o banco de dados.
        - **FRONTEND_URL**: Defina a URL do frontend utilizada no backend.
        - **Bucket AWS**: Defina as configurações de acesso ao bucket AWS.
        - **Configurações de Email**: Defina as configurações de email, como `MAIL_MAILER`, `MAIL_HOST`, `MAIL_PORT`, `MAIL_USERNAME`, `MAIL_PASSWORD`, etc.
        - **QUEUE_CONNECTION**: Configure a fila como `sync`.

   Podemos configurar diferentes .env para cada ambiente, como **.env.local**, **.env.dev**, **.env.prod**, mas em cada um, o arquivo **.env** deve ser configurado com o APP_ENV correspondente. Exemplo:

   No arquivo **.env.production**:

    ```bash 
    APP_ENV=production
    ```

3. **Gerar a Chave da Aplicação**:

   Em cada projeto, execute o comando abaixo para gerar uma nova chave para a aplicação Laravel:

   ```bash
   # Frontend (Admin)
   php artisan key:generate
   
   # Backend (Api)
   php artisan key:generate
   ```

4. **Executar Migrações e Seeders no Backend (Api)**:

   No projeto backend (Api), execute as migrações e seeders para configurar o banco de dados:

   ```bash
   php artisan migrate --seed
   ```

5. **Executar o Servidor Laravel (Admin e Api)**:

   Para executar o servidor Laravel em produção, utilize o comando abaixo em cada um dos projetos (Admin e Api):

   ```bash
   php artisan serve --host=0.0.0.0 --port=80 --env=production
   ```

   Isso disponibiliza a aplicação em todas as interfaces de rede, na porta 80, usando as configurações de ambiente de produção.

6. **Configurar Execução Automática do Servidor Laravel**:

    - Crie um arquivo de serviço para o Laravel Admin:

      ```bash
      sudo nano /etc/systemd/system/laravel_admin.service
      ```

      Adicione o conteúdo abaixo:

      ```ini
      [Unit]
      Description=Laravel Admin Service
      After=network.target
 
      [Service]
      User=www-data
      Group=www-data
      WorkingDirectory=/caminho/para/Admin
      ExecStart=/usr/bin/php artisan serve --host=0.0.0.0 --port=80 --env=production
      Restart=always
 
      [Install]
      WantedBy=multi-user.target
      ```

    - Crie um arquivo de serviço para o Laravel Api:

      ```bash
      sudo nano /etc/systemd/system/laravel_api.service
      ```

      Adicione o conteúdo abaixo:

      ```ini
      [Unit]
      Description=Laravel API Service
      After=network.target
 
      [Service]
      User=www-data
      Group=www-data
      WorkingDirectory=/caminho/para/Api
      ExecStart=/usr/bin/php artisan serve --host=0.0.0.0 --port=80 --env=production
      Restart=always
 
      [Install]
      WantedBy=multi-user.target
      ```

    - Salve e feche os arquivos.

    - Para iniciar e habilitar os serviços para iniciar automaticamente com o sistema, execute:

      ```bash
      # Laravel Admin
      sudo systemctl start laravel_admin.service
      sudo systemctl enable laravel_admin.service
 
      # Laravel Api
      sudo systemctl start laravel_api.service
      sudo systemctl enable laravel_api.service
      ```

   Dessa forma, os servidores Laravel para Admin e Api serão iniciados automaticamente sempre que o sistema for iniciado.

### Executar os Jobs com o Scheduler do Laravel

1. **Configurar o `.env` do Backend (Api)**:

   No arquivo `.env` do projeto backend, defina a conexão da fila como `sync`:

   ```bash
   QUEUE_CONNECTION=sync
   ```

2. **Executar Jobs Manualmente**:

   Para executar os jobs agendados uma vez, use o comando:

   ```bash
   php artisan schedule:run
   ```

3. **Executar Jobs Periodicamente**:

   Para executar os jobs de forma contínua e periódica, utilize o comando:

   ```bash
   php artisan schedule:work
   ```

4. **Verificar os Jobs Agendados**:

   Para ver os jobs que estão agendados, use o comando:

   ```bash
   php artisan schedule:list
   ```

5. **Configurar Execução Automática dos Jobs com Systemd**:

   Para garantir que os jobs sejam executados automaticamente, crie um arquivo de serviço para o scheduler do Laravel:

    - Crie um arquivo de serviço para o scheduler do Laravel Api:

      ```bash
      sudo nano /etc/systemd/system/laravel_schedule.service
      ```

      Adicione o conteúdo abaixo:

      ```ini
      [Unit]
      Description=Laravel Scheduler Service
      After=network.target
 
      [Service]
      User=www-data
      Group=www-data
      WorkingDirectory=/caminho/para/Api
      ExecStart=/usr/bin/php artisan schedule:work
      Restart=always
 
      [Install]
      WantedBy=multi-user.target
      ```

    - Salve e feche o arquivo.

    - Para iniciar e habilitar o serviço para iniciar automaticamente com o sistema, execute:

      ```bash
      sudo systemctl start laravel_schedule.service
      sudo systemctl enable laravel_schedule.service
      ```

   Dessa forma, o scheduler do Laravel será executado automaticamente sempre que o sistema for iniciado, garantindo que os jobs sejam processados periodicamente.
