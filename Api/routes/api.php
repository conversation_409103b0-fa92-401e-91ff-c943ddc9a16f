<?php

use App\Http\Controllers\Api\BusinessWhatsappController;
use App\Http\Controllers\Api\ClientImportController;
use App\Http\Controllers\Api\SiteNotificationController;
use App\Http\Controllers\Api\AppController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\EmployeesController;
use App\Http\Controllers\Api\EvaluationController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\SystemController;
use App\Http\Controllers\Api\TopicController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\BusinessController;
use App\Http\Controllers\Api\PlanController;
use App\Http\Controllers\Api\SellerController;
use App\Http\Controllers\Api\FormController;
use App\Http\Controllers\Api\CashbackController;
use App\Http\Controllers\Api\ClientController;
use App\Http\Controllers\EvaluationController as EvaluationControllerAlias;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/phpinfo', function () {
    return response()->make(phpinfo(), 200)
                     ->header('Content-Type', 'text/html');
});

Route::group(['middleware' => ['api', 'auth:api']], function () {
    Route::get('/user/getUserLogged', [UserController::class, 'getLoggedUser']);
});

Route::middleware('api')->group(function () {
    Route::get('/system', [SystemController::class, 'getBasicSystemParameters']);
    Route::get('/system/get-contact', [SystemController::class, 'getContact']);

    Route::get('app_latest_version', [AppController::class, 'app_latest_version']);
    Route::get('get_app_link', [AppController::class, 'get_app_link']);

    Route::get('/evaluations/token/{token}', [EvaluationController::class, 'getByToken']);
    Route::post('/evaluations/token/{token}/response', [EvaluationController::class, 'storeResponse']);
    Route::get('/user/check-cpf/{cpf}', [UserController::class, 'checkCpf']); // Add this line
    Route::get('/user/check-phone/{phone_number}', [UserController::class, 'checkPhoneNumber']);
    Route::get('/user/check-email/{email}', [UserController::class, 'checkEmail']);
    Route::post('/user/start-phone-validation', [UserController::class, 'startPhoneValidation'])->name('api.user.start_phone_validation');
    Route::post('/user/verify-phone-validation-code', [UserController::class, 'verifyPhoneValidationCode'])->name('api.user.verify_phone_validation_code');
});

Route::group(['middleware' => 'api', 'prefix' => 'auth'], function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('refresh', [AuthController::class, 'refresh']);
    Route::post('me', [AuthController::class, 'me']);
});

Route::group(['middleware' => ['api', 'auth:api', 'checkSettingsPermission']], function () {
    Route::get('/settings/contact', [SystemController::class, 'getContact']);
    Route::put('/settings/contact', [SystemController::class, 'updateContact']);

    Route::get('/settings/system_parameters', [SystemController::class, 'index']);
    Route::put('/settings/system_parameters', [SystemController::class, 'update']);
});

Route::group(['middleware' => ['api', 'auth:api']], function () {
    Route::get('/system_parameters/{code_name}', [SystemController::class, 'getOne']);
});

Route::group(['middleware' => ['api', 'auth:api']], function () {
    Route::prefix('user')->group(function () {
        Route::put('/update', [UserController::class, 'update']);
        Route::post('/validate/update', [UserController::class, 'validateUpdate']);
        Route::post('/first_login', [UserController::class, 'firstLogin']);
        Route::post('/validate/email', [UserController::class, 'validateEmail']);
        Route::put('/password/update', [UserController::class, 'updatePassword']);
        Route::put('/updateLoginType', [UserController::class, 'updateLoginType']);
    });

    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'getUsers']);
    });
});

Route::group(['middleware' => ['api', 'auth:api']], function () {
    Route::get('/check-permission', [AppController::class, 'checkPermission']);
});

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::middleware('auth:api')->post('/user/update-email-verified-at', [UserController::class, 'updateEmailVerifiedAt']);
Route::get('/user/show/by-cpf', [UserController::class, 'showByCpf']);
Route::get('/user/checkCpfExists', [UserController::class, 'checkCpfExists']);
Route::get('/user/validate-employees', [UserController::class, 'validateEmployees'])->middleware(['api', 'auth:api']);
Route::get('/user/validate-clients', [UserController::class, 'validateClients'])->middleware(['api', 'auth:api']);
Route::get('/clients/validateEmail', [EvaluationController::class, 'validateEmail']);
Route::post('/clients/publicValidateStore', [ClientController::class, 'validatePublicStore']);
Route::post('/clients/publicStore', [ClientController::class, 'publicStore']);
Route::get('/businesses/validate/cnpj_in_use', [BusinessController::class, 'validateCnpjInUse']);
Route::post('/business/publicRegister', [BusinessController::class, 'publicRegister']);
Route::get('/businesses/validate/email_in_use', [EmployeesController::class, 'validateEmailExistPublic']);

Route::group(['middleware' => ['api', 'auth:api', 'checkPermission']], function () {
    Route::apiResource('employees', EmployeesController::class);
    Route::prefix('employees')->group(function () {
        Route::post('/validate/store', [EmployeesController::class, 'validateStore']);
        Route::post('/validate/update/{id}', [EmployeesController::class, 'validateUpdate']);
        Route::get('/validate/cpf_exist', [EmployeesController::class, 'validateCpfExist']);
        Route::get('/validate/email_exist', [EmployeesController::class, 'validateEmailExist']);
        Route::match(['get', 'post'], '/search', [EmployeesController::class, 'search']);
    });
});

Route::get('plans/showByTag/{tag}', [PlanController::class, 'getByTag']);
Route::get('sellers/getByCpf/{cpf}', [SellerController::class, 'getByCpf']);

Route::group(['middleware' => ['api', 'auth:api']], function () {
  Route::prefix('client-imports')->group(function () {
    Route::get('/', [ClientImportController::class, 'index']);
    Route::post('/', [ClientImportController::class, 'store']);
    Route::post('/incomplete', [ClientImportController::class, 'storeIncomplete']);
    Route::get('/{id}', [ClientImportController::class, 'show']);
    Route::get('/{id}/download-url', [ClientImportController::class, 'getDownloadUrl']);
  });

  Route::prefix('employees')->group(function() {
    Route::post('/toggleInformativeFlag', [EmployeesController::class, 'toggleInformativeFlag']);
  });

  Route::prefix('clients')->group(function() {
    Route::post('/toggleInformativeFlag', [ClientController::class, 'toggleInformativeFlag']);
  });
});

Route::group(['middleware' => ['api', 'auth:api']], function () {
    Route::prefix('business')->group(function () {
        // Rotas do WhatsApp
        Route::post('/{id}/whatsapp/init', [BusinessWhatsappController::class, 'initInstance']);
        Route::get('/{id}/whatsapp/qrcode', [BusinessWhatsappController::class, 'getQrCode']);
        Route::get('/{id}/whatsapp/status', [BusinessWhatsappController::class, 'checkStatus']);
        Route::post('/{id}/whatsapp/disconnect', [BusinessWhatsappController::class, 'disconnect']);
        Route::get('/whatsapp/instance/info', [BusinessController::class, 'checkWhatsappInstanceInfo']);

        Route::get('/getUserBusiness', [BusinessController::class, 'getUserBusiness']);
        Route::get('/getSellerBusiness', [BusinessController::class, 'getSellerBusiness']);
        Route::post('/selectBusiness', [BusinessController::class, 'selectBusiness']);
        Route::post('/selectAllBusinesses', [BusinessController::class, 'selectAllBusinesses']);
        Route::put('/updateAdmin/{id}', [BusinessController::class, 'updateBusinessAdmin']);
        Route::get('/{id}', [BusinessController::class, 'show']);
        Route::put('/{id}', [BusinessController::class, 'update']);
        Route::post('/saveBusinessAndAdministrador', [BusinessController::class, 'saveBusinessAndAdministrador']);
        Route::get('/validate/name', [BusinessController::class, 'validateExistingName']);
        Route::get('/validate/cnpj', [BusinessController::class, 'validateCnpj']);
        Route::post('/{id}/reactivate', [BusinessController::class, 'reactivate']);
        Route::post('/{id}/disable', [BusinessController::class, 'disable']);
        Route::put('/{id}/parameters', [BusinessController::class, 'updateParameters'])->name('businesses.parameters.update');
        Route::post('/{id}/cancel', [BusinessController::class, 'cancelBusiness'])
            ->name('business.cancel');
        Route::post('/{id}/approve', [BusinessController::class, 'approveBusiness']);
        Route::post('/{id}/reject', [BusinessController::class, 'rejectBusiness']);
        Route::post('/{id}/cancelPending', [BusinessController::class, 'cancelPendingBusiness']);
        Route::post('/{id}/reverseCancel', [BusinessController::class, 'reverseCancelBusiness']);
    });

    Route::get('/dashboard/relatorySeller/{id}', [DashboardController::class, 'relatorySeller']);
    Route::get('/dashboard/getEvaluationsFilteredSeller/{businessId}', [DashboardController::class, 'getEvaluationsFilteredSeller']);

    Route::get('sellers/getAllSellers', [SellerController::class, 'getAllSellers']);
    Route::get('sellersForSeller/getAllSellers', [SellerController::class, 'getAllSellersForSeller']);
    Route::post('sellersForSeller/store', [SellerController::class, 'storeForSeller']);
    Route::get('sellersForSeller/get/{id}', [SellerController::class, 'getForSeller']);
    Route::put('sellersForSeller/update/{id}', [SellerController::class, 'updateForSeller']);
    Route::delete('sellersForSeller/delete/{id}', [SellerController::class, 'deleteForSeller']);

    Route::prefix('sellers')->group(function () {
        Route::post('validate/store', [SellerController::class, 'validateStore']);
        Route::post('validate/update/{seller}', [SellerController::class, 'validateUpdate']);
    });
    Route::apiResource('sellers', SellerController::class);

    Route::get('plans/getActivePlans', [PlanController::class, 'getActivePlans']);
    Route::apiResource('plans', PlanController::class);
    Route::prefix('plans')->group(function () {
        Route::post('validate/store', [PlanController::class, 'validateStore']);
        Route::post('validate/update/{plan}', [PlanController::class, 'validateUpdate']);
        Route::get('validate/hasBusiness/{id}', [PlanController::class, 'hasBusiness']);
        Route::get('/{plan}/check-deletion', [PlanController::class, 'checkDeletion']);
    });

    Route::apiResource('cashback', CashbackController::class);
    Route::prefix('cashbacks')->group(function() {
        Route::get('client/getCashbacks/{cashbackId}', [CashbackController::class, 'showClientCashback']);
        Route::get('client/getCashbacks', [CashbackController::class, 'indexClient']);
        Route::get('client/businessesClient', [CashbackController::class, 'businessesClient']);
        Route::post('validateStore', [CashbackController::class, 'validateStore']);
        Route::post('validateUpdate/{id}', [CashbackController::class, 'validateUpdate']);
        // Novas rotas para o fluxo de conclusão de cashback
        Route::get('pending-client', [CashbackController::class, 'getPendingClient']);
        Route::post('send-validation-code', [CashbackController::class, 'sendClientValidationCode']);
        Route::post('validate-and-complete', [CashbackController::class, 'validateAndCompleteClient']);
        Route::post('direct', [CashbackController::class, 'storeDirect']);
    });

    Route::prefix('evaluations')->middleware('checkUserPermission:evaluations')->group(function () {
        Route::get('/', [EvaluationController::class, 'index']);
        Route::post('/', [EvaluationController::class, 'store']);
        Route::get('/{id}', [EvaluationController::class, 'show']);
        Route::put('/{id}', [EvaluationController::class, 'update']);
        Route::delete('/{id}', [EvaluationController::class, 'destroy']);
        Route::post('/client_check', [ClientController::class, 'check']);
        Route::get('/business/validate-shipments', [EvaluationController::class, 'validateBusinessShipments']);
    });

    Route::prefix('clientEvaluations')->group(function () {
        Route::get('/', [EvaluationController::class, 'indexClient']);
        Route::get('/businessesClient', [EvaluationController::class, 'businessesClient']);
    });


    Route::prefix('clients')->group(function() {
        Route::get('/by-business', [ClientController::class, 'getClientsByBusiness']);
        Route::post('/', [ClientController::class, 'store']);
        Route::post('/incomplete', [ClientController::class, 'storeIncompleteClient']);
        Route::get('/', [ClientController::class, 'index']);
        Route::post('/validateStore', [ClientController::class, 'validateStore']);
        Route::get('/{id}', [ClientController::class, 'show']);
        Route::put('/{id}', [ClientController::class, 'update']);
        Route::delete('/{id}', [ClientController::class, 'destroy']);
        Route::post('/validateUpdate/{id}', [ClientController::class, 'validateUpdate']);
    });

    Route::prefix('notificationClient')->group(function() {
        Route::get('/', [NotificationController::class, 'indexClient']);
        Route::get('/{id}', [NotificationController::class, 'showNotificationClient']);
    });
});

Route::group(['middleware' => ['api', 'auth:api']], function () {
    Route::prefix('topics')->group(function () {
        Route::get('/getTopicsByForm', [TopicController::class, 'getTopicsByForm']);
        Route::get('/getActiveTopics', [TopicController::class, 'getActiveTopics']);
        Route::get('/getAllTopics', [TopicController::class, 'getAllTopics']);
        Route::get('/getActiveRequiredTopics', [TopicController::class, 'getActiveRequiredTopics']);
    });
});

Route::group(['middleware' => ['api', 'auth:api', 'checkPermission']], function () {
    Route::prefix('topics')->group(function () {
        Route::get('/', [TopicController::class, 'index']);

        Route::get('/{id}', [TopicController::class, 'show']);
        Route::get('/{id}/check-deletion', [TopicController::class, 'checkDeletion']);
        Route::get('/{id}/with_questions_business', [TopicController::class, 'showWithQuestionsBusiness']);
        Route::put('/{id}/update/default', [TopicController::class, 'updateDefault']);
        Route::put('/{id}/update/default_for_business', [TopicController::class, 'updateDefaultForBusiness']);
        Route::post('/', [TopicController::class, 'store']);
        Route::put('/{id}/update', [TopicController::class, 'update']);
        Route::delete('/{id}/delete', [TopicController::class, 'destroy']);
    });

    Route::apiResource('clients', ClientController::class);
    Route::prefix('clients')->group(function() {
        Route::post('/validateStore', [ClientController::class, 'validateStore']);
        Route::post('/validateUpdate/{id}', [ClientController::class, 'validateUpdate']);
    });

    Route::prefix('dashboard')->group(function() {
        Route::get('/forms', [DashboardController::class, 'getFormsWithoutUserPermission']);
        Route::get('/relatory', [DashboardController::class, 'index']);
        Route::get('/getEvaluationsFiltered', [DashboardController::class, 'getEvaluationsFiltered']);
        Route::get('/relatory/{id}', [DashboardController::class, 'show']);
    });
});
Route::group(['middleware' => ['api', 'auth:api']], function () {
    Route::get('/getFormsToList', [FormController::class, 'getFormsToList']);

    Route::prefix('siteNotifications')->group(function () {
        Route::get('/list', [SiteNotificationController::class, 'getUserNotifications']);
        Route::get('/countUnread', [SiteNotificationController::class, 'getUnreadCount']);
        Route::post('/markAsRead', [SiteNotificationController::class, 'markAsRead']);
        Route::delete('/{id}', [SiteNotificationController::class, 'deleteUserNotification']);
    });
});

Route::group(['middleware' => ['api', 'auth:api', 'checkPermission']], function () {
    Route::prefix('forms')->group(function () {
        Route::get('/', [FormController::class, 'index']);
        Route::get('/{id}', [FormController::class, 'show']);
        Route::post('/', [FormController::class, 'store']);
        Route::put('/{id}/update', [FormController::class, 'update']);
        Route::delete('/{id}/delete', [FormController::class, 'destroy']);
    });

    Route::prefix('notifications')->group(function() {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/', [NotificationController::class, 'store']);
        Route::post('/validateStore', [NotificationController::class, 'validateStore']);
        Route::post('/validateUpdate', [NotificationController::class, 'validateUpdate']);
        Route::get('/clients/{id}', [NotificationController::class, 'getClientsByNotification']);
        Route::get('/{id}', [NotificationController::class, 'show']);
        Route::put('/{id}', [NotificationController::class, 'update']);
        Route::delete('/{id}', [NotificationController::class, 'show']);
        Route::post('/{id}/cancel', [NotificationController::class, 'cancel']);
    });

    Route::prefix('siteNotifications')->group(function() {
        Route::get('/', [SiteNotificationController::class, 'index']);
        Route::group(['middleware' => 'checkUserPermission:update_site_notifications'], function() {
            Route::post('/', [SiteNotificationController::class, 'store']);
            Route::post('/validateStore', [SiteNotificationController::class, 'validateStore']);
        });
        Route::get('/{id}/users', [SiteNotificationController::class, 'getUsersByNotification']);
        Route::get('/{id}/show', [SiteNotificationController::class, 'show']);
    });
});

Route::post('/users/check-contact', [\App\Http\Controllers\Api\UserController::class, 'checkUserByContact'])->middleware('auth:api'); // Adiciona middleware de autenticação se necessário
