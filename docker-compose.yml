services:
  frontend:
    build:
      context: ./Admin
      dockerfile: Dockerfile
    env_file:
      - path: ./Admin/.env.docker
    container_name: frontend
    working_dir: /app
    tty: true
    networks:
      - app-network
    ports:
      - "8001:8000"
    depends_on:
      - mysql

  backend:
    build:
      context: ./Api
      dockerfile: Dockerfile
    env_file:
      - path: ./Api/.env.docker
    container_name: backend
    working_dir: /app
    tty: true
    networks:
      - app-network
    ports:
      - "8080:8000"
    depends_on:
      - mysql

  mysql:
    image: mysql:latest
    container_name: visao-negocio-db-test-deploy
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: # Você pode definir uma senha segura aqui
      MYSQL_DATABASE: visao_negocio
      MYSQL_USER: visao_negocio
      MYSQL_PASSWORD: 123456
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
    ports:
      - "3308:3306"
    volumes:
      - db_data_test:/var/lib/mysql
    networks:
      - app-network

volumes:
  db_data_test:

networks:
  app-network:
    driver: bridge