#!/bin/bash
cd "$(dirname "$0")"

# Definir o caminho base para as pastas de cache, views e sessions
BASE_PATH="storage/framework"
CACHE_PATH="$BASE_PATH/cache"
VIEWS_PATH="$BASE_PATH/views"
SESSIONS_PATH="$BASE_PATH/sessions"

# Função para verificar e criar diretórios
check_and_create_dir() {
    DIR_PATH=$1
    if [ -d "$DIR_PATH" ]; then
        echo "A pasta '$DIR_PATH' já existe."
    else
        echo "Criando a pasta '$DIR_PATH'."
        mkdir -p "$DIR_PATH"
        if [ $? -eq 0 ]; then
            echo "Pasta '$DIR_PATH' criada com sucesso."
        else
            echo "Falha ao criar a pasta '$DIR_PATH'."
            exit 1
        fi
    fi
}

# Verificar e criar as pastas necessárias
check_and_create_dir "$CACHE_PATH"
check_and_create_dir "$VIEWS_PATH"
check_and_create_dir "$SESSIONS_PATH"

echo "Verificação de pastas concluída."

echo "Executando 'php artisan optimize'"
php artisan optimize

echo "Executando 'php artisan route:clear'"
php artisan route:clear

echo "Executando 'php artisan config:clear'"
php artisan config:clear

echo "Executando 'php artisan cache:clear'"
php artisan cache:clear

echo "Executando 'php artisan route:cache'"
php artisan route:cache

echo "Executando 'php artisan migrate'"
php artisan migrate --force

echo "Executando 'php artisan serve'"
php artisan serve --host=0.0.0.0 --port=8081
