# Use a imagem oficial do PHP 8.1 baseada no Alpine Linux
FROM php:8.1.30-cli-alpine3.20

# Defina o diretório de trabalho dentro do container
WORKDIR /app

# Instale dependências do sistema e extensões do PHP
RUN apk update && apk add --no-cache \
    bash \
    libpng-dev \
    libjpeg-turbo-dev \
    libxml2-dev \
    libzip-dev \
    oniguruma-dev \
    curl-dev \
    tzdata \
    && docker-php-ext-install pdo_mysql mbstring xml curl zip \
    && cp /usr/share/zoneinfo/America/Recife /etc/localtime \
    && echo "America/Recife" > /etc/timezone \
    && apk del tzdata

# Instale o Composer globalmente
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Copie o conteúdo atual do projeto para o diretório de trabalho no container
COPY . /app

# Instale as dependências do PHP usando o Composer
RUN composer install --no-dev --optimize-autoloader

# Exponha a porta 8000 para acesso externo
EXPOSE 8000

# Copy the entrypoint script to execute migrations and start the server
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set the entrypoint script
ENTRYPOINT ["entrypoint.sh"]

# Comando padrão para iniciar o servidor Laravel
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8000"]
