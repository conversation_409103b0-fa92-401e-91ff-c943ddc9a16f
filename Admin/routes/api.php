<?php

use App\Http\Controllers\Api\EmailController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::prefix('send/email')->group(function(){
    Route::post('confirm-client-email', [EmailController::class, 'sendConfirmClientEmail'])->name('send.email.client.confirm');
    Route::post('confirm-employee-email', [EmailController::class, 'sendConfirmEmployeeEmail'])->name('send.email.employee.confirm');
    Route::post('confirm-seller-email', [EmailController::class, 'sendConfirmSellerEmail'])->name('send.email.seller.confirm');
    Route::post('confirm-email', [EmailController::class, 'sendConfirmEmail'])->name('send.email.confirm');
});
