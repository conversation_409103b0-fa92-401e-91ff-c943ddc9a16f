<?php

use App\Http\Controllers\BusinessController;
use App\Http\Controllers\BusinessParametersController;
use App\Http\Controllers\CashbackController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DataProcessingAgreementController;
use App\Http\Controllers\EmployeeController;

use App\Http\Controllers\EvaluationController;
use App\Http\Controllers\FormController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\TopicController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthApiController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\SellersController;
use App\Http\Controllers\SiteNotificationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
*/

Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/support', [HomeController::class, 'support'])->name('support');
#Route::get('/.well-known/assetlinks.json', [DeepLinkApp::class, 'index'])->name('.well-known/assetlinks.json');
Route::get('/data-processing-agreement', [DataProcessingAgreementController::class, 'showDataProcessingAgreement'])->name('data-processing-agreement');
//app
Route::get('/app', [HomeController::class, 'app'])->name('app');

Route::prefix('auth')->group(function () {
    Route::get('login', [AuthApiController::class, 'loginPage'])->name('auth.api.get.login');
    Route::post('login', [AuthApiController::class, 'login'])->name('auth.api.login');
    Route::middleware('auth.api')->group(function () {
        Route::post('logout', [AuthApiController::class, 'logout'])->name('auth.api.logout');
        Route::get('user', [AuthApiController::class, 'user'])->name('auth.api.user');
    });
});

Route::get('/register-business/{planTag}', [BusinessController::class, 'publicRegisterBusiness'])
    ->name('businesses.publicRegister');
Route::post('/register-business/{planTag}', [BusinessController::class, 'handlePublicRegistration'])
    ->name('businesses.publicRegister.submit');

Route::get('/register-business/success/{businessId}', [BusinessController::class, 'publicRegisterSuccess'])
    ->name('businesses.publicRegisterSuccess');

Route::get('/evaluations/respond/{token}', [EvaluationController::class, 'respondEvaluation'])->name('evaluations.respond');
Route::post('/evaluations/respond/{token}', [EvaluationController::class, 'storeResponse'])->name('evaluations.respond.store');
Route::post('/evaluations/validateStore/{token}', [EvaluationController::class, 'validateStore'])->name('evaluations.respond.validate.store');

Route::get('/register', [ClientController::class, 'publicRegisterClient'])
    ->name('clients.publicRegister');

Route::post('/clients/publicStore', [ClientController::class, 'publicStore'])->name('clients.publicStore');

Route::get('/register/success', [ClientController::class, 'publicRegisterSuccess'])
    ->name('clients.publicRegisterSuccess');

Route::middleware('auth.api')->group(function () {
    Route::get('/verify_email', [HomeController::class, 'verify_email'])->name('verify_email');
    Route::post('/update_email_verified_at', [HomeController::class, 'update_email_verified_at'])->name('update_email_verified_at');
    Route::get('/settings', [HomeController::class, 'settings'])->name('settings');
    Route::post('/settings/contact', [HomeController::class, 'updateContact'])->name('settings.contact.update');
    Route::post('/settings/system_parameters', [HomeController::class, 'updateSystemParameters'])->name('settings.system_parameters.update');
    Route::resource('employees', EmployeeController::class)->except(['show'])
        ->middleware('check.any.employee.function:employees,release_all_business');
    Route::match(['get', 'post'], '/search/employees/', [EmployeeController::class, 'search'])
        ->middleware('check.any.employee.function:employees,release_all_business')
        ->name('employees.search');

    Route::group(['prefix' => 'parameters', 'middleware' => ['check.all.function:parameters']], function () {
        Route::get('/', [BusinessParametersController::class, 'index'])->name('businesses.parameters');
        Route::post('/update', [BusinessParametersController::class, 'update'])->name('businesses.parameters.update');

        // Rotas do WhatsApp
        Route::post('/{id}/whatsapp/connect', [BusinessParametersController::class, 'connectWhatsapp'])
            ->name('businesses.parameters.whatsapp.connect');
        Route::get('/{id}/whatsapp/qrcode', [BusinessParametersController::class, 'getWhatsappQrCode'])
            ->name('businesses.parameters.whatsapp.qrcode');
        Route::get('/{id}/whatsapp/status', [BusinessParametersController::class, 'checkWhatsappStatus'])
            ->name('businesses.parameters.whatsapp.status');
        Route::post('/{id}/whatsapp/disconnect', [BusinessParametersController::class, 'disconnectWhatsapp'])
            ->name('businesses.parameters.whatsapp.disconnect');
    });

    // Profile
    Route::get('/profile', [AuthApiController::class, 'profile'])->name('profile');
    Route::put('profile', [AuthApiController::class, 'update_user_profile'])->name('profile.update');
    Route::put('password/update', [AuthApiController::class, 'update_user_password'])->name('profile.update.password');

    Route::post('/refresh_system', [HomeController::class, 'refresh_system'])->name('refresh_system');
    Route::post('/refresh_user', [AuthApiController::class, 'refresh_user'])->name('refresh_user');
    Route::post('/changeLoginType', [AuthApiController::class, 'update_user_login_type'])->name('changeLoginType');

    Route::group(['prefix' => 'businesses'], function () {
        Route::group(['middleware' => ['checkLoginType:seller']], function () {
            Route::get('/seller', [BusinessController::class, 'indexSeller'])->name('businesses.seller.index');
            Route::get('/seller/show_plan/{id}', [PlanController::class, 'show'])->name('businesses.seller.show_plan');
            Route::get('/seller/dashboard/{id}', [DashboardController::class, 'showForSellerById'])->name('businesses.seller.dashboard');
            Route::get('/seller/dashboard/details/{id}', [DashboardController::class, 'detailsForSeller'])->name('businesses.seller.dashboard.details');
        });

        Route::get('/{id}', [BusinessController::class, 'show'])->name('businesses.show');

        Route::group(['middleware' => ['check.user.function.or.permission:update_business,manage_business_profile']], function () {
            Route::post('/{id}', [BusinessController::class, 'update'])->name('businesses.update');
        });

        Route::get('/show_plan/{id}', [PlanController::class, 'show'])->name('businesses.show_plan');

        Route::group(['middleware' => ['user.permission:update_business']], function () {
            Route::get('/', [BusinessController::class, 'index'])->name('businesses.index');
            Route::get('/editAdmin/{id}', [BusinessController::class, 'editAdmin'])->name('businesses.editAdmin');
            Route::post('/updateAdmin/{id}', [BusinessController::class, 'updateAdmin'])->name('businesses.updateAdmin');
            Route::group(['prefix' => 'create'], function () {
                Route::get('/create-step-one', [BusinessController::class, 'createStepOne'])->name('businesses.create.step.one');
                Route::post('/create-step-one', [BusinessController::class, 'postCreateStepOne'])->name('businesses.create.step.one.post');

                Route::get('/create-step-two', [BusinessController::class, 'createStepTwo'])->name('businesses.create.step.two');
                Route::post('/create-step-two', [BusinessController::class, 'postCreateStepTwo'])->name('businesses.create.step.two.post');

                Route::get('/create-step-three', [BusinessController::class, 'createStepThree'])->name('businesses.create.step.three');
                Route::post('/create-step-three', [BusinessController::class, 'postCreateStepThree'])->name('businesses.create.step.three.post');

                Route::get('/create-step-four', [BusinessController::class, 'createStepFour'])->name('businesses.create.step.four');
                Route::post('/create-step-four', [BusinessController::class, 'postCreateStepFour'])->name('businesses.create.step.four.post');
            });
            Route::post('/{id}/reactivate', [BusinessController::class, 'reactivate'])->name('businesses.reactivate');
            Route::delete('/destroy/{business}', [BusinessController::class, 'destroy'])->name('businesses.destroy');

            Route::group(['middleware' => ['checkLoginType:admin']], function () {
                Route::post('/{id}/approve', [BusinessController::class, 'approve'])->name('businesses.approve');
                Route::post('/{id}/reject', [BusinessController::class, 'reject'])->name('businesses.reject');
                Route::post('/{id}/cancelPending', [BusinessController::class, 'cancelPending'])->name('businesses.cancelPending');
                Route::post('/{id}/reverseCancel', [BusinessController::class, 'reverseCancel'])->name('businesses.reverseCancel');
            });
        });

        Route::post('/{id}/cancel', [BusinessController::class, 'cancel'])->name('business.cancel');

    });

    Route::resource('sellers', SellersController::class)
        ->middleware('check.employee.function:sellers');

    Route::get('/seller/sellers', [SellersController::class, 'indexSeller'])->name('sellers.seller.index');
    Route::get('/seller/sellers/create', [SellersController::class, 'createForSeller'])->name('sellers.seller.create');
    Route::post('/seller/sellers/store', [SellersController::class, 'storeForSeller'])->name('sellers.seller.store');
    Route::get('/seller/sellers/edit/{seller}', [SellersController::class, 'editForSeller'])->name('sellers.seller.edit');
    Route::put('/seller/sellers/{seller}/update', [SellersController::class, 'updateForSeller'])->name('sellers.seller.update');
    Route::delete('/seller/sellers/destroy/{seller}', [SellersController::class, 'destroyForSeller'])->name('sellers.seller.destroy');

    Route::resource('plans', PlanController::class)
        ->middleware('check.employee.function:plans');

    Route::group(['prefix' => 'topics', 'middleware' => ['check.permission.or.is.admin:topics']], function () {
        Route::get('/{topic}/show', [TopicController::class, 'show'])->name('topics.show');
        Route::group(['middleware' => ['check.any.employee.function:default_topics,update_topics']], function () {
            // Route::get('/', [TopicController::class, 'index'])->name('topics.index');
            // Route::get('/search', [TopicController::class, 'search'])->name('topics.search');
            Route::get('/create', [TopicController::class, 'create'])->name('topics.create');
            Route::post('/', [TopicController::class, 'store'])->name('topics.store');
        });

        Route::group(['middleware' => ['check.any.employee.function:update_topics,default_topics']], function () {
            Route::get('/{topic}/edit', [TopicController::class, 'edit'])->name('topics.edit');
            Route::put('/{topic}/update', [TopicController::class, 'update'])->name('topics.update');
            Route::delete('/{topic}/delete', [TopicController::class, 'destroy'])->name('topics.destroy');

            Route::get('/{topic}/edit/default', [TopicController::class, 'editDefault'])->name('topics.edit.default');
            Route::put('/{topic}/edit/default/update', [TopicController::class, 'updateDefault'])->name('topics.default.update');
            Route::put('/{topic}/edit/default/update_for_business', [TopicController::class, 'updateDefaultForBusiness'])->name('topics.default.update.for.business');
        });
    });

    Route::group(['prefix' => 'forms', 'middleware' => ['check.permission.or.is.admin:forms',]], function () {
        Route::get('/', [FormController::class, 'index'])->name('forms.index');
        Route::get('/search', [FormController::class, 'search'])->name('forms.search');
        Route::get('/create', [FormController::class, 'create'])->name('forms.create');
        Route::post('/', [FormController::class, 'store'])->name('forms.store');
        Route::get('/{form}/show', [FormController::class, 'show'])->name('forms.show');
        Route::get('/{form}/edit', [FormController::class, 'edit'])->name('forms.edit');
        Route::put('/{form}/update', [FormController::class, 'update'])->name('forms.update');
        Route::delete('/{form}/delete', [FormController::class, 'destroy'])->name('forms.destroy');
    });

    Route::prefix('cashbacks')->group(function () {
        Route::group(['middleware' => 'checkLoginType:client'], function () {
            Route::get('/client', [CashbackController::class, 'indexClient'])->name('cashbacks.clients.index');
            Route::get('/client/{cashbackId}/show', [CashbackController::class, 'showCashbackClient'])->name('cashbacks.clients.show');
        });

        Route::group(['middleware' => 'check.all.function:cashback'], function () {
            Route::get('/{cashbackId}/show', [CashbackController::class, 'show'])->name('cashbacks.show');
            Route::get('/', [CashbackController::class, 'index'])->name('cashbacks.index');
        });
        Route::group(['middleware' => 'check.all.function:update_cashback'], function () {
            Route::post('/store', [CashbackController::class, 'store'])->name('cashbacks.store');
            Route::get('/create', [CashbackController::class, 'create'])->name('cashbacks.create');
            Route::get('/{cashbackId}/edit', [CashbackController::class, 'edit'])->name('cashbacks.edit');
            Route::put('/{cashbackId}/update', [CashbackController::class, 'update'])->name('cashbacks.update');
            Route::delete('/{cashbackId}/destroy', [CashbackController::class, 'destroy'])->name('cashbacks.destroy');
        });
        
        Route::group(['middleware' => 'check.all.function:cashback'], function () {
            Route::post('/search-pending-client', [CashbackController::class, 'searchPendingClient'])->name('cashbacks.search-pending-client');
            Route::post('/send-validation-code', [CashbackController::class, 'sendValidationCode'])->name('cashbacks.send-validation-code');
            Route::post('/validate-and-complete', [CashbackController::class, 'validateAndComplete'])->name('cashbacks.validate-and-complete');
        });
    });

    Route::prefix('clients')->group(function () {
        Route::group(['middleware' => 'check.permission.or.is.admin:clients'], function () {
            Route::get('/', [ClientController::class, 'index'])->name('clients.index');
        });
        Route::group(['middleware' => 'check.permission.or.is.admin:update_clients'], function () {
            Route::get('/create', [ClientController::class, 'create'])->name('clients.create');
            Route::post('/store', [ClientController::class, 'store'])->name('clients.store');
            Route::post('/storeIncomplete', [ClientController::class, 'storeIncomplete'])->name('clients.store.incomplete');
            Route::get('/{clientId}/show', [ClientController::class, 'show'])->name('clients.show');
            Route::get('/{clientId}/edit', [ClientController::class, 'edit'])->name('clients.edit');
            Route::get('/adminEdit', [ClientController::class, 'adminEdit'])->name('clients.adminEdit');
            Route::put('/{clientId}/update', [ClientController::class, 'update'])->name('clients.update');
            Route::delete('/{clientId}/destroy', [ClientController::class, 'destroy'])->name('clients.destroy');
            Route::delete('/adminDestroy', [ClientController::class, 'adminDestroy'])->name('clients.adminDestroy');
            Route::get('/import', [ClientController::class, 'importIncomplete'])->name('clients.import');
            Route::get('/import/create', [ClientController::class, 'importCreateIncomplete'])->name('clients.import.create');
            Route::post('/import', [ClientController::class, 'importIncompleteStore'])->name('clients.import.store');
            Route::get('/import/{id}/show', [ClientController::class, 'importShow'])->name('clients.import.show');
            Route::get('/import/{id}/download', [ClientController::class, 'downloadImportFile'])->name('clients.import.download');
        });
    });

    Route::group(['prefix' => 'evaluations', 'middleware' => 'check.all.function:evaluations'], function () {
        Route::get('/', [EvaluationController::class, 'index'])->name('evaluations.index');
        Route::post('/store', [EvaluationController::class, 'store'])->name('evaluations.store');
    });

    Route::prefix('evaluations')->group(function () {
        Route::group(['middleware' => 'checkLoginType:client'], function () {
            Route::get('/client', [EvaluationController::class, 'indexClient'])->name('evaluations.clients.index');
        });
    });

    Route::prefix('notifications')->group(function () {
        Route::group(['middleware' => 'checkLoginType:client'], function () {
            Route::get('/client', [NotificationController::class, 'indexClient'])->name('notifications.clients.index');
            Route::get('/client/{notificationId}/show', [NotificationController::class, 'showNotificationClient'])->name('notifications.clients.show');
        });

        Route::group(['middleware' => 'check.permission.or.is.admin:notifications'], function () {
            Route::get('/', [NotificationController::class, 'index'])->name('notifications.index');
            Route::get('/{id}/show', [NotificationController::class, 'show'])->name('notifications.show');
            Route::post('/{id}/cancel', [NotificationController::class, 'cancel'])->name('notifications.cancel');
        });
        Route::group(['middleware' => 'check.permission.or.is.admin:update_notifications'], function () {
            Route::get('/create', [NotificationController::class, 'create'])->name('notifications.create');
            Route::post('/store', [NotificationController::class, 'store'])->name('notifications.store');
        });
    });

    Route::prefix('dashboard')->group(function () {
        Route::group(['middleware' => 'check.all.function:dashboard'], function () {
            Route::get('/', [DashboardController::class, 'index'])->name('dashboard.index');
            Route::get('/details', [DashboardController::class, 'details'])->name('dashboard.details');
            Route::get('/{id}/show', [DashboardController::class, 'show'])->name('dashboard.show');
        });
    });

    Route::group(['prefix' => 'informative'], function () {
        Route::get('/', [HomeController::class, 'informative'])->name('informative');
        Route::post('/', [HomeController::class, 'markInformativeAsRead'])->name('informative.markAsRead');
    });

    Route::group(['prefix' => 'quick_actions'], function () {
        Route::get('/', [HomeController::class, 'quickActions'])->name('quick_actions');
    });

    Route::prefix('siteNotifications')->group(function () {
        Route::group(['middleware' => 'check.permission.or.is.admin:site_notifications'], function () {
            Route::get('/', [SiteNotificationController::class, 'indexEmployee'])->name('siteNotifications.index');
            Route::get('/{id}/show', [SiteNotificationController::class, 'show'])->name('siteNotifications.show');
        });

        Route::group(['middleware' => 'check.permission.or.is.admin:update_site_notifications'], function () {
            Route::get('/create', [SiteNotificationController::class, 'create'])->name('siteNotifications.create');
            Route::post('/store', [SiteNotificationController::class, 'store'])->name('siteNotifications.store');
            Route::get('/load-table', [SiteNotificationController::class, 'loadTable'])->name('siteNotifications.loadTable');
        });
        
        Route::get('/user', [SiteNotificationController::class, 'indexUser'])->name('siteNotifications.indexUser');
    });
});

require __DIR__ . '/auth.php';
