<?php

namespace App\Services;

use App\Http\Controllers\Traits\ApiTrait;
use Illuminate\Pagination\LengthAwarePaginator;

class PlanService
{
    use ApiTrait;

    public function index(int $page, int $perPage, ?string $search, string $url): LengthAwarePaginator
    {
        $page = $this->get('api/plans', ['per_page' => $perPage, 'page' => $page, 'search' => $search])->json('plans');
        $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page']);
        $paginator->setPath($url);

        return $paginator;
    }

    public function getById(string|int $id): array
    {
        return $this->get("api/plans/$id")->json();
    }

    public function store(
        string $name,
        ?string $description,
        float $value,
        bool $active,
        bool $publiclyVisible,
        ?string $paymentSystemTag,
        array $authorizedFunctions,
        int $totalEmployees,
        bool $emailSending,
        bool $whatsappSending,
        int $totalEmailSends,
        string $emailSendsType = 'monthly',
        ?int $emailSendsDays = null,
        int $totalWhatsappSends,
        string $whatsappSendsType = 'monthly',
        ?int $whatsappSendsDays = null
    ) {
        return $this->post("api/plans", [
            'name' => $name,
            'description' => $description,
            'value' => $value,
            'active' => $active,
            'publicly_visible' => $publiclyVisible,
            'payment_system_tag' => $paymentSystemTag,
            ...$authorizedFunctions,
            'total_employees' => $totalEmployees,
            'email_sending' => $emailSending,
            'whatsapp_sending' => $whatsappSending,
            'total_email_sends' => $totalEmailSends,
            'email_sends_type' => $emailSendsType,
            'email_sends_days' => $emailSendsDays,
            'total_whatsapp_sends' => $totalWhatsappSends,
            'whatsapp_sends_type' => $whatsappSendsType,
            'whatsapp_sends_days' => $whatsappSendsDays
        ]);
    }

    public function update(
        string|int $id,
        string $name,
        ?string $description,
        float $value,
        bool $active,
        bool $publiclyVisible,
        ?string $paymentSystemTag,
        array $authorizedFunctions,
        int $totalEmployees,
        bool $emailSending,
        bool $whatsappSending,
        int $totalEmailSends,
        string $emailSendsType = 'monthly',
        ?int $emailSendsDays = null,
        int $totalWhatsappSends,
        string $whatsappSendsType = 'monthly',
        ?int $whatsappSendsDays = null
    ) {
        return $this->put("api/plans/$id", [
            'name' => $name,
            'description' => $description,
            'value' => $value,
            'active' => $active,
            'publicly_visible' => $publiclyVisible,
            'payment_system_tag' => $paymentSystemTag,
            ...$authorizedFunctions,
            'total_employees' => $totalEmployees,
            'email_sending' => $emailSending,
            'whatsapp_sending' => $whatsappSending,
            'total_email_sends' => $totalEmailSends,
            'email_sends_type' => $emailSendsType,
            'email_sends_days' => $emailSendsDays,
            'total_whatsapp_sends' => $totalWhatsappSends,
            'whatsapp_sends_type' => $whatsappSendsType,
            'whatsapp_sends_days' => $whatsappSendsDays
        ]);
    }

    public function destroy(int|string $id)
    {
        return $this->delete("api/plans/$id");
    }
}
