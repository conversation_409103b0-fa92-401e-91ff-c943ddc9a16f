<?php

namespace App\Helpers;

class StringMask
{
    public static function cpf(?string $cpf)
    {
        if($cpf == null){
            return '';
        }
        try {
            if(strlen($cpf) != 11) {
                return $cpf;
            } else {
                return substr($cpf, 0, 3).'.'.substr($cpf, 3, 3).'.'.substr($cpf, 6, 3).'-'.substr($cpf, 9, 2);
            }
        } catch(\Exception $e) {
            return $cpf;
        }
    }

    public static function cep($cep)
    {
        try {
            if(strlen($cep) != 8) {
                return $cep;
            } else {
                return substr($cep, 0, 2).'.'.substr($cep, 2, 3).'-'.substr($cep, 5);
            }
        } catch(\Exception $e) {
            return $cep;
        }
    }

    public static function phone(?string $phone)
    {
        try {
            if($phone == null) {
                return '-';
            }

            if(strlen($phone) == 10) {
                return '('.substr($phone, 0, 2).') '.substr($phone, 2, 4).'-'.substr($phone, 6);
            }
            if(strlen($phone) == 11) {
                return '('.substr($phone, 0, 2).') '.substr($phone, 2, 5).'-'.substr($phone, 7);
            } else {
                return $phone;
            }
        } catch(\Exception $e) {
            return $phone;
        }
    }

    public static function gender($gender)
    {
        try {
            if($gender == 'f') { return 'Feminino'; }
            if($gender == 'm') { return 'Masculino'; }
            else { return 'Outro'; }
        } catch(\Exception $e) {
            return $gender;
        }
    }

    public static function brDate(?string $date)
    {
        try {
            if ($date) {
                $carbonDate = \Carbon\Carbon::parse($date);
                return $carbonDate->format('d/m/Y');
            } else {
                return "";
            }
        } catch(\Exception $e) {
            return $date;
        }
    }

    public static function who_created(string $who)
    {
        if($who == 'employee') {
            return 'Funcionário';
        }
        if($who == 'requester') {
            return 'Solicitante';
        }

        return 'N/A';
    }

    public static function money(float $value)
    {
        return 'R$ '.number_format($value, 2);
    }
}
