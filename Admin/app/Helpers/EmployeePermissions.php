<?php

namespace App\Helpers;

/**
 * Classe helper para centralizar as permissões dos funcionários (Employee Permissions).
 */
class EmployeePermissions
{
    // Permissões Gerais
    public const EMPLOYEES = 'employees';
    public const SETTINGS = 'settings';
    public const RELEASE_ALL_BUSINESS = 'release_all_business';
    public const MANAGE_BUSINESS_PROFILE = 'manage_business_profile';
    public const PLANS = 'plans';
    public const SELLERS = 'sellers';

    // Permissões de Tópicos
    public const TOPICS = 'topics';
    public const DEFAULT_TOPICS = 'default_topics';
    public const UPDATE_TOPICS = 'update_topics';

    // Permissões de Formulários
    public const FORMS = 'forms';
    public const DEFAULT_FORMS = 'default_forms';
    public const UPDATE_FORMS = 'update_forms';

    // Permissões de Negócios (Business)
    public const UPDATE_BUSINESS = 'update_business';
    public const PARAMETERS = 'parameters';

    // Permissões de Cashback
    public const CASHBACK = 'cashback';
    public const UPDATE_CASHBACK = 'update_cashback';

    // Permissões de Clientes
    public const CLIENTS = 'clients';
    public const UPDATE_CLIENTS = 'update_clients';

    // Permissões de Avaliações
    public const EVALUATIONS = 'evaluations';

    // Permissões de Notificações
    public const NOTIFICATIONS = 'notifications';
    public const UPDATE_NOTIFICATIONS = 'update_notifications';

    // Permissões do Dashboard
    public const DASHBOARD = 'dashboard';

    // Permissões de Notificações do Site
    public const SITE_NOTIFICATIONS = 'site_notifications';
    public const UPDATE_SITE_NOTIFICATIONS = 'update_site_notifications';
}