<?php

namespace App\Helpers;

use App\Models\System;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class SystemHelper
{
    public static function get()
    {
        $cacheKey = 'system_data';
        $cacheTTL = 60 * 60; // 1 hora

        return Cache::remember($cacheKey, $cacheTTL, function () {
            return self::getSystemFromApi();
        });
    }

    public static function getContact(): array
    {
        $system = self::get();

        if(!is_null($system['PHONE_NUMBER'])){
            $system['PHONE_NUMBER'] = strlen($system['PHONE_NUMBER']) >= 11 ? preg_replace('~.*(\d{2})[^\d]{0,8}(\d{5})[^\d]{0,8}(\d{4}).*~', '($1) $2-$3', $system['PHONE_NUMBER']) : preg_replace('~.*(\d{2})[^\d]{0,7}(\d{4})[^\d]{0,9}(\d{4}).*~', '($1) $2-$3', $system['PHONE_NUMBER']);
        }

        return [
            'email' => $system['EMAIL'],
            'phone_number' => $system['PHONE_NUMBER'],
        ];
    }

    public static function getAppLinks(): array
    {
        $system = self::get();

        return [
            'link_app_android' => $system['LINK_APP_ANDROID'],
            'link_app_ios' => $system['LINK_APP_IOS'],
        ];
    }

    private static function getSystemFromApi()
    {
        $url = env('API_URL');

        $response = Http::get("$url/api/system");

        if($response->successful()) {
            return $response->json();
        } else {
            throw new Exception($response->json('msg'), $response->status(), );
        }
    }

    public static function forceUpdate()
    {
        $cacheKey = 'system_data';
        Cache::forget($cacheKey);
        return self::getSystemFromApi();
    }

    /**
     * Obter tempo de reenvio de códigos de autenticação em segundos
     * @return int Tempo em segundos (padrão: 180 segundos = 3 minutos)
     */
    public static function getAuthCodeResendTimeSeconds(): int
    {
        $system = self::get();
        $timeInMinutes = $system['AUTH_CODE_RESEND_TIME_MINUTES'] ?? null;
        
        if ($timeInMinutes !== null && is_numeric($timeInMinutes) && $timeInMinutes > 0) {
            return (int) ($timeInMinutes * 60);
        }
        
        return 180;
    }
}
