<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class ApiUser
{
    public static function get()
    {
        try {
            $user = session('AUTH_USER');
            $idDevice = session('ID_DEVICE');

            if (!is_null($user)) {
                return $user;
            }
            if (is_null(session('API_TOKEN'))) {
                return null;
            }

            $cacheKey = 'user_data';
            $cacheTTL = 60 * 60; // 1 hora

            if (!is_null($idDevice)) {
                $cacheTTL = null;
            }

            return Cache::remember($cacheKey, $cacheTTL, function () {
                return self::getUser();
            });
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function getLoginType()
    {
        $user = self::get();
        return $user['login_type'] ?? null;
    }

    public static function isSeller()
    {
        return self::getLoginType() == 'seller';
    }

    public static function isClient()
    {
        return self::getLoginType() == 'client';
    }

    public static function isAdminOfBusiness($business)
    {
        $user = self::get();
        return $user && isset($business['admin']) && $business['admin']['id'] == $user['id'];
    }

    public static function isEmployee()
    {
        return self::getLoginType() == 'employee' || self::getLoginType() == 'admin';
    }

    public static function isBusinessSelected()
    {
        $isEmployee = self::isEmployee();
        return $isEmployee && !is_null(self::get()['business_selected']);
    }


    public static function update()
    {
        $user = self::getUser();
        if (!is_null($user)) {
            Cache::put('user_data', $user, 60 * 60);
        }
    }

    private static function getUser()
    {
        $url = env('API_URL');
        $deviceId = session('ID_DEVICE');

        $response = Http::withToken(session('API_TOKEN'))->get("$url/api/user/getUserLogged");
        if($response->successful()) {
            $data = $response->json();
            if (!is_null($deviceId)) {
                $data['id_device'] = $deviceId;
            }
            session()->forget('AUTH_USER');
            session()->put('AUTH_USER', $data);
            return $data;
        } else {
            return null;
        }
    }

        public static function reset()
    {
        session()->forget('AUTH_USER');
        session()->forget('API_TOKEN');
        session()->forget('ID_DEVICE');
        Cache::forget('user_data');
    }

    public static function logged()
    {
        return self::get() != null;
    }

    public static function hasPermission($permission)
    {
        $user = self::get();
        $authorizedPlanFunctions = $user['business_selected']['plan']['authorizedFunctions'] ?? [];
        $authorizedUserFunctions = $user['authorizedFunction'] ?? [];

        $hasPlanFunction = ($authorizedPlanFunctions[$permission] ?? false);
        $hasUserFunction = ($authorizedUserFunctions[$permission] ?? false);

        return $hasPlanFunction && $hasUserFunction;
    }

    public static function hasPlanFunction($permission)
    {
        $user = self::get();
        $authorizedPlanFunctions = $user['business_selected']['plan']['authorizedFunctions'] ?? [];

        return ($authorizedPlanFunctions[$permission] ?? false);
    }

    public static function hasUserFunction($permission)
    {
        $user = self::get();
        $authorizedUserFunctions = $user['authorizedFunction'] ?? [];

        return ($authorizedUserFunctions[$permission] ?? false);
    }

    public static function hasUserFunctionAndIsAdmin($permission) {

        return (self::hasUserFunction($permission) &&
                self::hasUserFunction('release_all_business') &&
                ApiUser::get()['business_selected'] == null
            );
    }

    public static function hasPermissionOrIsAdmin($permission)
    {
        $user = self::get();
        if (is_null($user))
            return false;

        return self::hasPermission($permission) || self::hasUserFunctionAndIsAdmin($permission);
    }

    public static function hasFunctionAndAdminBusinessSelected($permission) {
        $user = self::get();
        if (is_null($user))
            return false;

        if(ApiUser::get()['business_selected'] == null)
            return false;

        return self::hasUserFunction($permission) && $user['business_selected']['id'] == SystemHelper::get()['ADMIN_BUSINESS_ID'];
    }

    public static function hasPermissionOrAdminBusinessSelected($permission)
    {
        $user = self::get();
        if (is_null($user))
            return false;

        return self::hasPermission($permission) || 
            (self::hasUserFunction($permission) && self::isAdminBusinessSelected());
    }

    public static function updateLoginType($loginType)
    {
        $url = env('API_URL');
        $result = Http::withToken(session('API_TOKEN'))->put("$url/api/user/updateLoginType", ['login_type' => $loginType]);

        if ($result->successful()) {
            self::update();
            return true;
        }else{
            return false;
        }
    }

    public static function isAdminBusinessSelected() {
        $user = self::get();
        $businessSelected = $user['business_selected'];
        if (is_null($businessSelected))
            return false;

        $adminBusinessId = (int) SystemHelper::get()['ADMIN_BUSINESS_ID'];

        return self::hasUserFunction('release_all_business') && $businessSelected['id'] === $adminBusinessId;
    }
}
