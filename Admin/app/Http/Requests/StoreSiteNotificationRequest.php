<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSiteNotificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Altere para `true` se o usuário estiver autorizado a fazer essa requisição
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'icon' => 'nullable|string|min:4',
            'title' => 'required|string|min:4',
            'description' => 'required|string|min:6',
            'filtersJSON' => 'required|json',
            'url_click' => 'nullable|regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.required' => 'O campo título é obrigatório.',
            'title.string' => 'O campo título deve ser uma string.',
            'title.min' => 'O campo título deve ter pelo menos 4 caracteres.',
            'description.required' => 'O campo descrição é obrigatório.',
            'description.string' => 'O campo descrição deve ser uma string.',
            'description.min' => 'O campo descrição deve ter pelo menos 6 caracteres.',
            'filters.required' => 'O campo filters é obrigatório.',
            'filters.json' => 'O campo filters deve ser um JSON válido.',
            'url_click.regex' => 'O campo link deve ser uma URL válida.',
        ];
    }
}