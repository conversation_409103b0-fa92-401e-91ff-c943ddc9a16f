<?php

namespace App\Http\Requests\Businesses;

use Illuminate\Foundation\Http\FormRequest;

class PostCreateStepOneRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3'],
            'description' => ['nullable','string', 'min:3'],
            'latitude' => ['nullable', 'numeric'],
            'longitude' => ['nullable', 'numeric'],
            'logo' => ['nullable', 'string'],
            'logo_file' => ['nullable', 'string'],
            'cnpj' => ['required_without:cnpj_optional', 'string'],
            'cnpj_optional' => ['sometimes', 'boolean'],
            'corporate_reason' => ['nullable', 'string', 'min:3'],
            'neighborhood' => ['nullable', 'string', 'min:3'],
            'street' => ['nullable', 'string', 'min:3'],
            'number' => ['nullable', 'string'],
            'city' => ['nullable', 'string', 'min:3'],
            'state' => ['nullable', 'string', 'min:2'],
            'cep' => ['nullable', 'string'],
            'complement' => ['nullable', 'string'],
            'url_facebook' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
            'url_instagram' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
            'url_google' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
            'url_linkedin' => ['nullable', 'string', 'min:5', 'regex:/(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'O campo nome é obrigatório.',
            'name.string' => 'O campo nome deve ser uma string.',
            'name.min' => 'O campo nome deve ter no mínimo 3 caracteres.',
            'description.string' => 'O campo descrição deve ser uma string.',
            'description.min' => 'O campo descrição deve ter no mínimo 3 caracteres.',
            'latitude.numeric' => 'O campo latitude deve ser um número.',
            'longitude.numeric' => 'O campo longitude deve ser um número.',
            'logo.string' => 'O campo logo deve ser uma string.',
            'logo_file.string' => 'O campo logo_file deve ser uma string.',
            'cnpj.string' => 'O campo CNPJ deve ser uma string.',
            'cnpj.min' => 'O campo CNPJ deve ter 14 caracteres.',
            'cnpj.max' => 'O campo CNPJ deve ter 14 caracteres.',
            'corporate_reason.string' => 'O campo razão social deve ser uma string.',
            'corporate_reason.min' => 'O campo razão social deve ter no mínimo 3 caracteres.',
            'neighborhood.string' => 'O campo bairro deve ser uma string.',
            'neighborhood.min' => 'O campo bairro deve ter no mínimo 3 caracteres.',
            'street.string' => 'O campo rua deve ser uma string.',
            'street.min' => 'O campo rua deve ter no mínimo 3 caracteres.',
            'number.string' => 'O campo número deve ser uma string.',
            'city.string' => 'O campo cidade deve ser uma string.',
            'city.min' => 'O campo cidade deve ter no mínimo 3 caracteres.',
            'state.string' => 'O campo estado deve ser uma string.',
            'state.min' => 'O campo estado deve ter no mínimo 2 caracteres.',
            'cep.string' => 'O campo CEP deve ser uma string.',
            'cep.min' => 'O campo CEP deve ter 8 caracteres.',
            'cep.max' => 'O campo CEP deve ter 8 caracteres.',
            'complement.string' => 'O campo complemento deve ser uma string.',
            'url_facebook.regex' => 'O URL do Facebook deve ser um endereço válido.',
            'url_facebook.min' => 'O URL do Facebook deve ter pelo menos 5 caracteres.',
            'url_instagram.regex' => 'O URL do Instagram deve ser um endereço válido.',
            'url_instagram.min' => 'O URL do Instagram deve ter pelo menos 5 caracteres.',
            'url_google.regex' => 'O URL do Google deve ser um endereço válido.',
            'url_google.min' => 'O URL do Google deve ter pelo menos 5 caracteres.',
            'url_linkedin.regex' => 'O URL do LinkedIn deve ser um endereço válido.',
            'url_linkedin.min' => 'O URL do LinkedIn deve ter pelo menos 5 caracteres.',
        ];
    }
}
