<?php

namespace App\Http\Requests\Businesses;

use Illuminate\Foundation\Http\FormRequest;

class PostCreateStepTwoRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'plan_id' => ['required', 'numeric'],
            'plan_name' => ['required', 'string'],
            'seller_id' => ['nullable', 'numeric'],
            'seller_name' => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'plan_id.required' => 'plan_id é obrigatório.',
            'plan_id.numeric' => 'plan_id deve ser um número.',
            'plan_name.required' => 'plan_name é obrigatório.',
            'plan_name.string' => 'plan_name deve ser uma string.',
            'seller_id.required' => 'seller_id é obrigatório.',
            'seller_id.numeric' => 'seller_id deve ser um número.',
            'seller_name.required' => 'seller_name é obrigatório.',
            'seller_name.string' => 'seller_name deve ser uma string.',
        ];
    }
}
