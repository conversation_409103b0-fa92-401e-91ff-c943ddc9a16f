<?php

namespace App\Http\Requests\Businesses;

use Illuminate\Foundation\Http\FormRequest;

class PublicRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'isCreateNewEmployeeAndUser' => 'required|boolean',
            'isEditEmployee' => 'required|boolean',
            'isCreateNewEmployee' => 'required|boolean',
            'old_email' => 'nullable|email',
            'is_cnpj_optional' => 'boolean',
            'cnpj' => 'nullable|string|required_if:is_cnpj_optional,false',
            'business_deleted' => 'required|boolean',
            'email_validated' => 'required|boolean',
            'name' => 'required|string',
            'email' => 'required|email',
            'cpf_admin' => 'required|string',
            'business_name' => 'nullable|string',
            'plan_id' => 'required|string',
            'seller_id' => 'nullable|string',
            'employee_id' => 'nullable|string',
            'phone_number' => 'required|string',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'isCreateNewEmployeeAndUser.required' => 'O campo isCreateNewEmployeeAndUser é obrigatório.',
            'isCreateNewEmployeeAndUser.boolean' => 'O campo isCreateNewEmployeeAndUser deve ser um booleano.',
            'isEditEmployee.required' => 'O campo isEditEmployee é obrigatório.',
            'isEditEmployee.boolean' => 'O campo isEditEmployee deve ser um booleano.',
            'isCreateNewEmployee.required' => 'O campo isCreateNewEmployee é obrigatório.',
            'isCreateNewEmployee.boolean' => 'O campo isCreateNewEmployee deve ser um booleano.',
            'old_email.email' => 'O campo old_email deve ser um e-mail válido.',
            'is_cnpj_optional.boolean' => 'O campo is_cnpj_optional deve ser um booleano.',
            'cnpj.required_if' => 'O campo CNPJ é obrigatório',
            'cnpj.string' => 'O campo cnpj deve ser uma string.',
            'business_deleted.required' => 'O campo business_deleted é obrigatório.',
            'business_deleted.boolean' => 'O campo business_deleted deve ser um booleano.',
            'email_validated.required' => 'O campo email_validated é obrigatório.',
            'email_validated.boolean' => 'O campo email_validated deve ser um booleano.',
            'name.required' => 'O campo name é obrigatório.',
            'name.string' => 'O campo name deve ser uma string.',
            'email.required' => 'O campo email é obrigatório.',
            'email.email' => 'O campo email deve ser um e-mail válido.',
            'cpf_admin.required' => 'O campo cpf_admin é obrigatório.',
            'cpf_admin.string' => 'O campo cpf_admin deve ser uma string.',
            'business_name.string' => 'O campo business_name deve ser uma string.',
            'plan_id.required' => 'O campo plan_id é obrigatório.',
            'plan_id.string' => 'O campo plan_id deve ser uma string.',
            'seller_id.string' => 'O campo seller_id deve ser uma string.',
            'employee_id.string' => 'O campo employee_id deve ser uma string.',
            'phone_number.required' => 'O campo phone_number é obrigatório.',
            'phone_number.string' => 'O campo phone_number deve ser uma string.',
        ];
    }
}
