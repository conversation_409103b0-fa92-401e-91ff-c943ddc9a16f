<?php

namespace App\Http\Requests\Businesses;

use App\Rules\PhoneNumber;
use Illuminate\Foundation\Http\FormRequest;

class PostCreateStepThreeRequest extends FormRequest
{


    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'employee_id' => ['nullable', 'numeric'],
            'name' => ['required', 'min:5', 'max:255'],
            'email' => ['required', 'email'],
            'password' => ['min:8', 'confirmed', 'nullable'],
            'cpf' => ['required', 'regex:/\d{3}\.\d{3}\.\d{3}-\d{2}/'],
            'gender' => ['nullable', 'in:f,m,other'],
            'phone_number_1' => ['required', new PhoneNumber],
            'phone_number_2' => ['nullable', new PhoneNumber],
            'neighborhood' => ['required', 'min:2'],
            'street' => ['required', 'min:2'],
            'number' => ['required'],
            'city' => ['required'],
            'state' => ['required'],
            #12345-678
            'cep' => ['required', 'regex:/\d{5}-\d{3}/'],
            'complement' => ['nullable', 'min:2'],
            'employees' => ['boolean'],
            'sellers' => ['boolean'],
            'settings' => ['boolean'],
            'email_validated' => ['boolean'],
            'reset_password' => ['in:on'],
        ];
    }

    public function messages(): array
    {
        return [
            'employee_id.numeric' => 'employee_id deve ser um número.',
            'name.required' => 'O campo nome é obrigatório.',
            'name.min' => 'O campo nome deve ter pelo menos :min caracteres.',
            'name.max' => 'O campo nome não deve ter mais de :max caracteres.',
            'email.email' => 'O campo email deve ser um endereço de e-mail válido.',
            'email.required' => 'O campo email é obrigatório.',
            'password.required' => 'O campo senha é obrigatório.',
            'password.min' => 'A senha deve ter pelo menos :min caracteres.',
            'password.confirmed' => 'A confirmação da senha não corresponde.',
            'cpf.required' => 'O campo CPF é obrigatório.',
            'cpf.regex' => 'CPF inválido',
            'gender.in' => 'O campo gênero deve ser feminino, masculino ou não informado.',
            'phone_number_1.required' => 'O campo telefone 1 é obrigatório.',
            'phone_number_1.phone_number' => 'Telefone 1 inválido',
            'phone_number_2.phone_number' => 'Telefone 2 inválido',
            'neighborhood.required' => 'O campo bairro é obrigatório.',
            'neighborhood.min' => 'O campo bairro deve ter pelo menos :min caracteres.',
            'street.required' => 'O campo rua é obrigatório.',
            'street.min' => 'O campo rua deve ter pelo menos :min caracteres.',
            'number.required' => 'O campo número é obrigatório.',
            'city.required' => 'O campo cidade é obrigatório.',
            'state.required' => 'O campo estado é obrigatório.',
            'cep.required' => 'O campo CEP é obrigatório.',
            'cep.regex' => 'CEP inválido',
            'complement.min' => 'O campo complemento deve ter pelo menos :min caracteres.',
        ];
    }
}
