<?php

namespace App\Http\Requests\Evaluation;

use App\Rules\PhoneNumber;
use Illuminate\Foundation\Http\FormRequest;

class ResponseEvaluationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'anonymous' => 'required|boolean',
            'responses' => 'required|array',
            'responses.*' => 'required|string',
            'justifications' => 'array',
            'justifications.*' => 'nullable|string',
            'client' => 'nullable|array',
            'client.name' => ['nullable', 'min:5', 'max:255'],
            'client.email' => ['nullable', 'email'],
            'client.password' => ['nullable','min:8', 'confirmed', 'nullable'],
            'client.birth_date' => ['nullable', 'date', 'before:today'],
            'client.cpf' => ['nullable', 'regex:/\d{3}\.\d{3}\.\d{3}-\d{2}/'],
            'client.gender' => ['nullable', 'in:f,m,other'],
            'client.phone_number_1' => ['nullable', new PhoneNumber],
            'client.phone_number_2' => ['nullable', new PhoneNumber],
            'client.is_entrepreneur' => ['nullable', 'boolean'],
            'client.user_exists' => ['nullable', 'boolean'],
            'client.email_validated' => ['nullable', 'boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'anonymous.required' => 'O campo anônimo é obrigatório.',
            'anonymous.boolean' => 'O campo anônimo deve ser um booleano.',
            'responses.required' => 'O campo respostas é obrigatório.',
            'responses.array' => 'O campo respostas deve ser um array.',
            'responses.*.required' => 'O campo respostas deve ser uma string.',
            'justifications.array' => 'O campo justificativas deve ser um array.',
            'justifications.*.nullable' => 'O campo justificativas deve ser uma string.',
            'client.name.required' => 'O campo nome é obrigatório.',
            'client.name.min' => 'O campo nome deve ter pelo menos :min caracteres.',
            'client.name.max' => 'O campo nome não deve ter mais de :max caracteres.',
            'client.email.email' => 'O campo email deve ser um endereço de e-mail válido.',
            'client.email.required' => 'O campo email é obrigatório.',
            'client.password.required' => 'O campo senha é obrigatório.',
            'client.password.min' => 'A senha deve ter pelo menos :min caracteres.',
            'client.password.confirmed' => 'A confirmação da senha não corresponde.',
            'client.birth_date.required' => 'O campo data de nascimento é obrigatório.',
            'client.birth_date.date' => 'O campo data de nascimento deve ser uma data válida.',
            'client.birth_date.before' => 'A data de nascimento deve ser anterior à data atual.',
            'client.cpf.required' => 'O campo CPF é obrigatório.',
            'client.cpf.regex' => 'O campo CPF deve ser um CPF válido.',
            'client.phone_number_1.required' => 'O campo telefone 1 é obrigatório.',
            'client.is_entrepreneur.required' => 'O campo é empreendedor é obrigatório.',
            'client.is_entrepreneur.boolean' => 'O campo é empreendedor deve ser um booleano.'
        ];
    }
}
