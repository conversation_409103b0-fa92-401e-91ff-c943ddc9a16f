<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckBusinessPlanAuthorization
{
    public function handle(Request $request, Closure $next, $requiredFunction)
    {
        $user = ApiUser::get();
        $businessSelected = $user['business_selected'] ?? null;

        if ($businessSelected != null && $businessSelected['plan']['authorizedFunctions'][$requiredFunction] ?? false) {
            return $next($request);
        }

        return redirect()->route('home')->with('error', 'Selecione um negócio com um plano que possua essa funcionalidade.');
    }
}
