<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;

class CheckPermissionOrIsAdmin
{
    public function handle($request, Closure $next, $permission)
    {
        if(ApiUser::hasPermissionOrIsAdmin($permission)) {
            return $next($request);
        }
        if (ApiUser::getLoginType() == 'client' || ApiUser::getLoginType() == 'seller'){
            return redirect()->route('home');
        } return redirect()->route('home')->with('error', 'Você não tem permissão para acessar essa página');
    }
}
