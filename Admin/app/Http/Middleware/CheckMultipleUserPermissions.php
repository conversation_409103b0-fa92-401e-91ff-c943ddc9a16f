<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;
use Illuminate\Support\Facades\Auth;

class CheckMultipleUserPermissions
{
    public function handle($request, Closure $next, ...$permissions)
    {
        foreach($permissions as $permission) {
            if(!ApiUser::hasUserFunction($permission)) {
                if (ApiUser::getLoginType() == 'client' || ApiUser::getLoginType() == 'seller'){
                    return redirect()->route('home');
                }
                return redirect()->route('home')->with('error', 'Você não tem permissão para acessar este recurso');
            }
        }

        return $next($request);
    }
}
