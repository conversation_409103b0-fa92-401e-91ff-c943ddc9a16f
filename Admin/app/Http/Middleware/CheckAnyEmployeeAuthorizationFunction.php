<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;
use Illuminate\Http\Request;

class CheckAnyEmployeeAuthorizationFunction
{
    public function handle(Request $request, Closure $next, ...$requiredFunctions)
    {
        $user = ApiUser::get();

        // Verifica se o usuário tem qualquer uma das funções necessárias
        foreach ($requiredFunctions as $requiredFunction) {
            if (!empty($user['authorizedFunction'][$requiredFunction])) {
                return $next($request);
            }
        }
        if (ApiUser::getLoginType() == 'client' || ApiUser::getLoginType() == 'seller'){
            return redirect()->route('home');
        } return redirect()->route('home')->with('error', 'Você não tem permissão para acessar essa página');
    }
}
