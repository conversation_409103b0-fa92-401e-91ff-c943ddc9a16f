<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckLoginType
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $type): Response
    {
        if (ApiUser::get() != null && ApiUser::getLoginType() != $type)
            return redirect()->route('home')->with('error', 'Você não tem permissão para acessar esta página.');

        return $next($request);
    }
}
