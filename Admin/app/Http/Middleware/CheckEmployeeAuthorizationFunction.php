<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckEmployeeAuthorizationFunction
{
    public function handle(Request $request, Closure $next, $requiredFunction)
    {
        $user = ApiUser::get();
        $hasUserFunction = $user['authorizedFunction'][$requiredFunction] ?? false;
        if ($hasUserFunction) {
            return $next($request);
        }
        if (ApiUser::getLoginType() == 'client' || ApiUser::getLoginType() == 'seller'){
            return redirect()->route('home');
        }return redirect()->route('home')->with('error', 'Você não tem permissão para acessar essa página');
    }
}
