<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAllAuthorizationFunction
{
    public function handle(Request $request, Closure $next, $requiredFunction)
    {
        if(ApiUser::hasPermission($requiredFunction)){
            return $next($request);
        }
        if (ApiUser::getLoginType() == 'client' || ApiUser::getLoginType() == 'seller'){
            return redirect()->route('home');
        } return redirect()->route('home')->with('error', 'Você não tem permissão para acessar essa página');
    }
}
