<?php

namespace App\Http\Middleware;

use App\Helpers\ApiUser;
use Closure;
use Illuminate\Support\Facades\Auth;

class CheckUserFunctionOrPermission
{
    public function handle($request, Closure $next, ...$permissions)
    {
        if (ApiUser::getLoginType() == 'client' || ApiUser::getLoginType() == 'seller'){
            return redirect()->route('home');
        }

        $userFunction = $permissions[0];
        $permission = $permissions[1];

        if (ApiUser::hasPermission($permission) || ApiUser::hasUserFunction($userFunction))
            return $next($request);
        
        return redirect()->route('home')->with('error', 'Você não tem permissão para acessar este recurso');
    }
}
