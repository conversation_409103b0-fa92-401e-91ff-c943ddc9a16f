<?php

namespace App\Http\Controllers;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class EmployeeController extends Controller
{
    public function index(Request $request)
    {
        try {
            $page = $this->get('api/employees', ['per_page' => '5', 'page' => ($request->page ?? 1)])->json('employees');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page']);
            $paginator->setPath($request->url());
            return view('employee.index', ['employees' => $paginator, 'base_url' => env('APP_URL') . '/employees']);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function create(Request $request)
    {
        $cpf = $request->cpf;
        try {
            $response = $this->get("api/user/show/by-cpf", ['cpf' => $cpf], true, false);
            if ($response->status() == 404) {
                return view('employee.create', ['cpf' => $cpf]);
            }
            $user = $response->json('user');
            if ($response->json('employee')) {
                return back()->with('error', 'Funcionário com cpf ' . $cpf . ' já cadastrado');
            }
            return view('employee.create', ['user' => $user, 'cpf' => $cpf]);
        } catch (\Exception $e) {
            return view('employee.create', ['cpf' => $cpf]);
        }
    }

    public function store(Request $request)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->post('api/employees', $data);
            session()->flash('success', 'Funcionário adicionado!');
            return redirect()->route('employees.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function edit($id, $redirectTo = null)
    {
        try {
            $response = $this->get("api/employees/$id");
            $employee = $response->json('employee');
            if($employee['admin']){
                return back()->with('error', 'Você não tem permissão para editar esse funcionário');
            }
            return view('employee.edit', ['employee' => $employee, 'redirectTo' => $redirectTo]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->put("api/employees/$id", $data);

            return redirect()->back()->with('success', 'Funcionário atualizado!');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        try {
            $this->delete("api/employees/$id");
            session()->flash('success', 'Funcionário removido');
            return redirect()->route('employees.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function search(Request $request)
    {
        try {
            $page = $this->post('api/employees/search', ['per_page' => '5', 'page' => ($request->page ?? 1), 'name' => $request->name, 'search' => $request->search])->json('employees');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page'], ['path' => $request->url(), 'query' => ['search' => $request->search, 'name' => $request->name]]);
            $paginator->appends('search', $request->search);

            $data = [
                'employees' => $paginator,
                'search' => $request->search,
            ];

            return view('employee.index', $data);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

}
