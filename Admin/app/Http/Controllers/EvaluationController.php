<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Http\Requests\Evaluation\ResponseEvaluationRequest;
use App\Rules\PhoneNumber;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class EvaluationController extends Controller
{
    public function index(Request $request)
    {
        try {
            $params = [
                'per_page' => '5',
                'page' => $request->page ?? 1,
                'search' => $request->search,
                'form_id' => $request->form_id,
                'shipping_status' => $request->shipping_status,
                'cashback_filter' => $request->cashback_filter,
            ];

            $requests = [
                'evaluations' => [
                    'uri' => 'api/evaluations',
                    'params' => $params,
                    'withToken' => true,
                ],
                'forms' => [
                    'uri' => 'api/getFormsToList',
                    'withToken' => true,
                ],
            ];

            // Dispara as requisições em paralelo
            $responses = $this->parallelGet($requests);

            $evaluationsResponse = $responses['evaluations'];
            $formsResponse = $responses['forms'];

            $page = $evaluationsResponse->json('evaluations');
            $forms = $formsResponse->json('forms');

            $paginator = new LengthAwarePaginator(
                $page['data'],
                $page['total'],
                $page['per_page'],
                $page['current_page'],
                ['path' => $request->url(), 'query' => $request->query()]
            );

            return view('evaluations.index', [
                'evaluations' => $paginator,
                'forms' => $forms,
                'statuses' => ['SENT' => 'Enviado', 'RESPONDED' => 'Respondido', 'EXPIRED' => 'Expirado'],
                'search' => $request->search,
                'form_id' => $request->form_id,
                'shipping_status' => $request->shipping_status,
                'cashback_filter' => $request->cashback_filter,
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function store(Request $request)
    {
        try {
            $data = $request->all();

            if(isset($data['number_phone']) && $data['number_phone'] != '' && $data['number_phone'] != null) {
                $data['number_phone'] = preg_replace('/\D/', '', $data['number_phone']);
            }

            $data['anonymous'] = isset($data['anonymous']) && $data['anonymous'] == 'on';

            unset($data['_token']);

            $result = $this->sendPostRequest('api/evaluations', $data);
            if ($result['success'] == false && $result['status'] == 400 && $result['message'] == 'Whatsapp não conectado.') {
                ApiUser::update();
                return redirect()->back()
                    ->with([
                        'error' => 'O WhatsApp não está conectado. Por favor, conecte o WhatsApp para enviar a avaliação.',
                        'whatsappNotConnected' => true,
                    ]);
            }

            if ($result['success'] == false && $result['status'] == 404 && $result['message'] == 'Nenhum whatsapp encontrado para o número informado.') {
                return redirect()->back()
                    ->with([
                        'error' => 'Nenhum whatsapp encontrado para o número informado.',
                        'whatsappNotFound' => true,
                    ]);
            }

            return redirect()->back()
                ->with('success', $result['data']['msg'] ?? 'Avaliação criada com sucesso.');
        } catch (HttpResponseException|\Exception $e) {
            return redirect()->back()->with('error', 'Ocorreu um erro inesperado: ' . $e->getMessage());
        }
    }

    private function getAvailableForms()
    {
        $response = $this->get('api/getFormsToList');
        return $response->json('forms');
    }
    private function getBusinessesClient()
    {
        $response = $this->get('api/clientEvaluations/businessesClient');
        return $response->json('businesses');
    }

    public function respondEvaluation($token) : View
    {
        try {
            $response = $this->get("api/evaluations/token/{$token}");
            $evaluation = $response->json('evaluation');
            $isExpired = $response->json('isExpired');

            if($evaluation['shipping_status'] == 'RESPONDED') {
                $errorMessage = 'A avaliação já foi respondida.';
                return view('evaluations.respond.respond', ['errorMessage' => $errorMessage]);
            }

            if ($isExpired) {
                $errorMessage = 'O link da avaliação expirou.';
                return view('evaluations.respond.respond', ['errorMessage' => $errorMessage]);
            }

            return view('evaluations.respond.respond', ['evaluation' => $evaluation]);
        } catch (HttpResponseException $e) {
            $errorMessage = 'O link da avaliação não existe ou está expirado.';
            return view('evaluations.respond.respond', ['errorMessage' => $errorMessage]);
        } catch (\Exception $e) {
            $errorMessage = 'Ocorreu um erro ao processar sua solicitação.';
            return view('evaluations.respond.respond', ['errorMessage' => $errorMessage]);
        }
    }

    public function storeResponse(ResponseEvaluationRequest $request, $token): JsonResponse
    {
        try {
            $validatedData = $request->validated();

            $apiData = [
                'anonymous' => $validatedData['anonymous'],
                'responses' => $validatedData['responses'],
                'justifications' => $validatedData['justifications'] ?? [],
            ];

            if(isset($validatedData['client'])) {
                $user_exists = $validatedData['client']['user_exists'] ?? false;
            } else {
                $user_exists = false;
            }
            $apiData['user_exists'] = $user_exists;

            if (isset($validatedData['client'])) {
                $apiData['client'] = $validatedData['client'];
                $apiData['cpf'] = preg_replace('/\D/', '', $apiData['client']['cpf']);
                if(!$user_exists){
                    $apiData['phone_number_1'] = preg_replace('/\D/', '', $apiData['client']['phone_number_1']);
                    $apiData['client']['phone_number_1'] = $apiData['phone_number_1'];
                    if(isset($apiData['client']['phone_number_2']) && $apiData['client']['phone_number_2'] != '' && $apiData['client']['phone_number_2'] != null) {
                        $apiData['phone_number_2'] = preg_replace('/\D/', '', $apiData['client']['phone_number_2']);
                        $apiData['client']['phone_number_2'] = $apiData['phone_number_2'];
                    }
                    if($apiData['client']['gender'] == 'other') {
                        $apiData['client']['gender'] = null;
                    }
                }
            }

            $response = $this->post("api/evaluations/token/{$token}/response", $apiData);

            if ($response->successful()) {
                $responseData = $response->json();

                if($responseData['error']) {
                    return response()->json([
                        'error' => true,
                        'msg' => $responseData['msg']], 200);
                }

                if (isset($responseData['redirect_to_registration']) && $responseData['redirect_to_registration']) {
                    return response()->json(['redirect_to_registration' => true]);
                }

                $responseJson = ['success' => $responseData['msg'], 'evaluation' => $responseData['evaluation']];

                return response()->json($responseJson);
            } else {
                $errorMsg = $response->json('msg') ?? 'Ocorreu um erro ao enviar sua avaliação.';
                return response()->json(['error' => $errorMsg], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function validateStore(ResponseEvaluationRequest $request, $token): JsonResponse
    {
        try {
            if(!isset($request->client['user_exists']) || !$request->client['user_exists']) {
                $response = $this->get("api/clients/validateEmail", [
                  'email' => $request->client['email'],
                  'cpf' => $request->client['cpf'],
              ], withToken: false);
                $responseData = $response->json();
                if($responseData['error']) {
                    return response()->json([
                        'error' => true,
                        'msg' => 'O e-mail informado já está cadastrado em nossa base de dados.',
                    ], 200);
                }
            }


            return response()->json(['success' => 'Avaliação validada com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function indexClient(Request $request)
    {
        try {
            $params = [
                'per_page' => '5',
                'page' => $request->page ?? 1,
                'search' => $request->search,
                'business_id' => $request->business_id,
                'shipping_status' => $request->shipping_status,
            ];

            $requests = [
                'clientEvaluations' => [
                    'uri' => 'api/clientEvaluations',
                    'params' => $params,
                    'withToken' => true,
                ],
                'businessesClient' => [
                    'uri' => 'api/clientEvaluations/businessesClient',
                    'withToken' => true,
                ],
            ];

            // Dispara as requisições em paralelo
            $responses = $this->parallelGet($requests);

            $clientEvaluationsResponse = $responses['clientEvaluations'];
            $businessesClientResponse  = $responses['businessesClient'];

            $page = $clientEvaluationsResponse->json('evaluations');
            $businesses = $businessesClientResponse->json('businesses');

            $paginator = new LengthAwarePaginator(
                $page['data'],
                $page['total'],
                $page['per_page'],
                $page['current_page'],
                ['path' => $request->url(), 'query' => $request->query()]
            );

            return view('evaluations.index_client', [
                'evaluations' => $paginator,
                'businesses' => $businesses,
                'statuses' => ['SENT' => 'Enviado', 'RESPONDED' => 'Respondido', 'EXPIRED' => 'Expirado'],
                'search' => $request->search,
                'business_id' => $request->business_id,
                'shipping_status' => $request->shipping_status,
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
}
