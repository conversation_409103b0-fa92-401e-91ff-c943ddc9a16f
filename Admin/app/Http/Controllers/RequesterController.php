<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\ApiTrait;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class RequesterController extends Controller
{
    use ApiTrait;

    public function index(Request $request){

        try {
            $page = $this->get('api/requester', ['per_page' => '5', 'page' => ($request->page ?? 1)])->json('requesters');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page']);
            $paginator->setPath($request->url());
            return view('requesters.index', ['requesters' => $paginator, 'base_url' => env('APP_URL').'/requesters']);
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function destroy($id){
        $this->delete("api/requester/$id");
        return redirect()->route('requesters.index')->with('success', 'Solicitante excluído com sucesso!');
    }

    public function show($id)
    {
        try {
            $requester = $this->get("api/requester/$id")->json('requester');
            $repairs = $this->post('api/repair_request/search/by-cpf', ['cpf' => $requester['cpf'], 'birth_date' => $requester['birth_date']], false)->json('repairs');

            return view('requesters.show', ['requester' => $requester, 'repairs' => $repairs, 'requesterCpf' => $requester['cpf'], 'requesterBirthDate' => $requester['birth_date']]);
        } catch(HttpResponseException $e) {
            return back()->with('error', session('error'))->withInput();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function search(Request $request){
        try{
            $page = $this->post('api/requester/search', ['per_page' => '5', 'page' => ($request->page ?? 1), 'name' => $request->name, 'search' => $request->search])->json('requesters');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page'], ['path' => $request->url(), 'query' => ['name' => route('requesters.search')]]);
            $paginator->appends('search', $request->search);
            return view('requesters.index', ['requesters' => $paginator, 'base_url' => env('APP_URL').'/requesters', 'search' => $request->search]);
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
}
