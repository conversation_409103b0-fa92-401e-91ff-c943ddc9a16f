<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\ApiTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Helpers\ApiUser;
use App\Providers\RouteServiceProvider;

class AuthApiController extends Controller
{
    use ApiTrait;

    public function loginPage()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        try {
            $url = $this->getApiUrl();
            $deviceId = session('ID_DEVICE') ?? null;
            if ($deviceId) {
                $request['id_device'] = $deviceId;
            }
            $response = Http::post("$url/api/auth/login", $request->all());
            if ($response->successful()) {
                $this->handleLoginRemender($request);
                session()->put('API_TOKEN', $response->json('access_token'));
                ApiUser::update();
                return redirect()->route(route: 'home')->with('success', 'Logado com sucesso!');
            }
            if ($response->status() == 422) {
                return back()->withErrors($response->json('errors'))->withInput();
            }
            if ($response->status() == 400) {
                //verificar se é cpf ou email
                $login = $request->email;
                $cpf = preg_replace('/[^0-9]/', '', $login);
                // dd( $cpf );
                if (filter_var($login, FILTER_VALIDATE_EMAIL)) {
                    return back()->with('error', 'Email ou senha inválidos')->withInput();
                } elseif (preg_match('/^\d{11}$/', $cpf)) {
                    return back()->with('error', 'CPF ou senha inválidos')->withInput();
                } else {
                    return back()->with('error', 'Email, CPF ou senha inválidos')->withInput();
                }

            }
            $msg = $response->json('msg') ?? 'Senha inválida';
            return back()->with('error', $msg)->withInput();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public
    function profile(Request $request)
    {
        ApiUser::update();
        $user = (object)ApiUser::get();
        return view('profile', ['user' => $user]);
    }

    public
    function refresh_user(): JsonResponse
    {
        ApiUser::update();
        return response()->json(['msg' => 'Usuário atualizado com sucesso']);
    }

    public
    function update_user_login_type(Request $request): void
    {
        $login_type = $request->login_type;
        ApiUser::updateLoginType($login_type);
    }

    public
    function update_user_profile(Request $request)
    {
        try {
            $data = $this->removeMasks($request->except(['_method', '_token']));
            $this->put('api/user/update', $data);

            ApiUser::update();
            return back()->with('success', 'Usuário atualizado!');
        } catch (\Illuminate\Http\Exceptions\HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public
    function update_user_password(Request $request)
    {
        try {
            $data = $request->except(['_method', '_token']);
            $this->put('api/user/password/update', $data);

            return back()->with('success', 'Senha atualizada!');
        } catch (\Illuminate\Http\Exceptions\HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public
    function logout()
    {
        try {
            $deviceId = session('ID_DEVICE');
            if ($deviceId) {
                $this->post("api/auth/logout", ['id_device' => $deviceId]);
            }
            session()->forget('ID_DEVICE');
            session()->forget('API_TOKEN');
            session()->forget('AUTH_USER');
            session()->flash('success', 'Sessão encerrada!');
            if ($deviceId) {
                return redirect()->intended('?id_device='.$deviceId);
            } else {
                return redirect()->route('home');
            }
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public
    function user()
    {
        try {
            $url = $this->getApiUrl();
            $response = Http::withToken(session('API_TOKEN'))->get("$url/api/user");
            if ($response->successful()) {
                return $response->json();
            } else {
                return null;
            }
        } catch (\Exception $e) {
            return null;
        }
    }

    private
    function handleLoginRemender(Request $request)
    {
        session()->forget(['remember_email', 'remember_password']);
        $remember = $request->boolean('remember');

        if ($remember) {
            session()->put('remember_email', $request->email);
            session()->put('remember_password', $request->password);
            session()->put('remember_login', $remember);
        }
    }
}
