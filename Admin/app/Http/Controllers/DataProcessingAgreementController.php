<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DataProcessingAgreementController extends Controller
{
    public function showDataProcessingAgreement(Request $request)
    {
        if($request->user_type != null){
            $user_type = $request->user_type;
        }else{
            $user_type = 'client';
        }
        return view('data_processing_agreement', ['user_type' => $user_type]);
    }
}
