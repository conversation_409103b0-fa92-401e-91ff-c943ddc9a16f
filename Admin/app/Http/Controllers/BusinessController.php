<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Http\Requests\Businesses\PostCreateStepOneRequest;
use App\Http\Requests\Businesses\PostCreateStepThreeRequest;
use App\Http\Requests\Businesses\PostCreateStepTwoRequest;
use App\Http\Requests\Businesses\PublicRegisterRequest;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Console\Input\Input;
use function PHPUnit\Framework\isEmpty;

class BusinessController extends Controller
{
    public function index(Request $request)
    {
        try {
            $response = $this->get("api/business/getUserBusiness", [
                "per_page" => "5",
                "page" => $request->page ?? 1,
                "search" => $request->search,
                "filter_disabled" => $request->filter_disabled ?? 'all',
                "status" => $request->status,
                "seller_id" => $request->seller_id ?? null,
                "with_sum" => true,
            ]);

            $page = $response->json("businesses");

            $total_value = $response->json("total_value");

            $paginator = new LengthAwarePaginator(
                $page["data"],
                $page["total"],
                $page["per_page"],
                $page["current_page"],
                [
                    "path" => $request->url(),
                    "query" => [
                        "search" => $request->search,
                        "filter_disabled" => $request->filter_disabled,
                        "status" => $request->status,
                        "seller_id" => $request->seller_id,
                    ],
                ]
            );
            $paginator->appends("search", $request->search);
            $paginator->appends("filter_disabled", $request->filter_disabled);
            $paginator->appends("status", $request->status);
            $paginator->appends("seller_id", $request->seller_id);

            $sellers = $this->get("api/sellers/getAllSellers")->json("sellers");

            $data = [
                "businesses" => $paginator,
                "search" => $request->search,
                "filter_disabled" => $request->filter_disabled,
                "status" => $request->status,
                "seller_id" => $request->seller_id,
                "sellers" => $sellers,
                "total_value" => $total_value,
            ];

            return view("businesses.index", $data);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with("error", $e->getMessage());
        }
    }

    public function show(Request $request, $id)
    {
        try {
            $response = $this->get("api/business/$id");
            $business = $response->json("business");

            $plans = $this->get("api/plans/getActivePlans");
            $plans = $plans->json("plans");

            $sellers = $this->get("api/sellers/getAllSellers");
            $sellers = $sellers->json("sellers");

            return view("businesses.show", [
                "business" => $business,
                "plans" => $plans,
                "sellers" => $sellers,
                "fromSeller" => $request->fromSeller ?? false,
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with("error", $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $data = $this->removeMasks($request->except("_token", "_method"));
            if ($request->has('latitude') && $request->latitude == "Não informado") {
                $data['latitude'] = null;
            }
            if ($request->has('longitude') && $request->longitude == "Não informado") {
                $data['longitude'] = null;
            }
            $response = $this->put(
                "api/business/$id",
                $data
            );
            if (
                ApiUser::get()["business_selected"] != null &&
                ApiUser::get()["business_selected"]["id"] == $id
            ) {
                ApiUser::update();
            }
            return redirect()
                ->route("businesses.show", $id)
                ->with("success", $response->json("msg"));
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with("error", $e->getMessage());
        }
    }

    public function createStepOne(Request $request): View
    {
        if ($request->create_new != null) {
            session()->forget("business");
        }
        $business_session = session("business");
        if (!$business_session) {
            $business_session = [
                "name" => "",
                "description" => null,
                "latitude" => null,
                "longitude" => null,
                "logo" => null,
                "logo_file" => null,
                "plan_id" => null,
                "seller_id" => null,
                "cnpj" => null,
                "cnpj_optional" => null,
                "corporate_reason" => null,
                "neighborhood" => null,
                "street" => null,
                "number" => null,
                "city" => null,
                "state" => null,
                "cep" => null,
                "complement" => null,
                "url_facebook" => null,
                "url_google" => null,
                "url_instagram" => null,
                "url_linkedin" => null,
            ];
            session(["business" => $business_session]);
        }
        return view("businesses.create.step_one", [
            "business" => $business_session,
        ]);
    }

    public function postCreateStepOne(
        PostCreateStepOneRequest $request
    ): RedirectResponse
    {
        $data = $this->removeMasks($request->validated());
        $business_session = session("business");
        foreach ($data as $key => $value) {
            $business_session[$key] = $value;
        }
        session(["business" => $business_session]);
        if (!empty($data['cnpj'])) {
          $this->get('api/business/validate/cnpj', ['cnpj' => $data['cnpj']]);
        }

        return redirect()->route("businesses.create.step.two");
    }

    public function createStepTwo(): View
    {
        $business_session = session("business");
        return view("businesses.create.step_two", [
            "business" => $business_session,
        ]);
    }

    public function postCreateStepTwo(
        PostCreateStepTwoRequest $request
    ): RedirectResponse
    {
        $data = $request->validated();
        $business_session = session("business");
        $business_session["plan_id"] = $data["plan_id"];
        $business_session["plan_name"] = $data["plan_name"];
        $business_session["seller_id"] = $data["seller_id"] ?? null;
        $business_session["seller_name"] = $data["seller_name"] ?? null;

        session(["business" => $business_session]);
        return redirect()->route("businesses.create.step.three");
    }

    public function createStepThree(Request $request): View
    {
        $business_session = session("business");
        if (!$business_session) {
            return redirect()->route("businesses.create.step.one");
        }
        if (!isset($business_session["employee"])) {
            $business_session["employee"] = [];
            $business_session["employee_id"] = null;
            $business_session["old_user"] = null;
        }

        $isCreateNewEmployeeAndUser = false;
        $isEditEmployee = false;
        $isCreateNewEmployee = false;

        $cpfSearch = $request->cpf_search;
        if ($cpfSearch) {
            $cpfSearch = preg_replace("/[^0-9]/", "", $cpfSearch);
            $response = $this->get(
                "api/user/show/by-cpf",
                ["cpf" => $cpfSearch],
                true,
                false
            );
            if ($response->status() == 404) {
                $business_session["employee_id"] = null;
                $business_session["employee"] = [];
                $business_session["old_user"] = null;
                $isCreateNewEmployeeAndUser = true;
            } else {
                $hasEmployee = $response->json("employee") != null;
                if ($hasEmployee) {
                    $business_session["employee_id"] = $response->json(
                        "employee"
                    )["id"];
                    $business_session["employee"] = $response->json("user");
                    $business_session["old_user"] = $response->json("user");
                    $isEditEmployee = true;
                } else {
                    $business_session["employee_id"] = 0;
                    $business_session["employee"] = $response->json("user");
                    $business_session["old_user"] = $response->json("user");
                    $isCreateNewEmployee = true;
                }
            }
            $business_session["isCreateNewEmployee"] = $isCreateNewEmployee;
            $business_session["isEditEmployee"] = $isEditEmployee;
            $business_session["isCreateNewEmployeeAndUser"] = $isCreateNewEmployeeAndUser;
            session(["business" => $business_session]);
        } else {
            if (
                $business_session["employee_id"] != null &&
                !empty($business_session["employee"])
            ) {
                $isEditEmployee = true;
            } elseif (
                $business_session["employee_id"] === 0 &&
                !empty($business_session["employee"])
            ) {
                $isCreateNewEmployee = true;
            } elseif (
                $business_session["employee_id"] == null &&
                !empty($business_session["employee"])
            ) {
                $isCreateNewEmployeeAndUser = true;
            }
            $business_session["isCreateNewEmployeeAndUser"] = $isCreateNewEmployeeAndUser;
            $business_session["isEditEmployee"] = $isEditEmployee;
            $business_session["isCreateNewEmployee"] = $isCreateNewEmployee;
            session(["business" => $business_session]);

        }

        return view("businesses.create.step_three", [
            "business" => $business_session,
            "cpfSearch" => $cpfSearch,
            "isCreateNewEmployeeAndUser" => $isCreateNewEmployeeAndUser,
            "isEditEmployee" => $isEditEmployee,
            "isCreateNewEmployee" => $isCreateNewEmployee,
            "employee" => $business_session["employee"],
        ]);
    }

    public function postCreateStepThree(
        PostCreateStepThreeRequest $request
    ): RedirectResponse
    {
        $business_session = session("business");

        $data = $request->validated();
        $business_session["employee"] = $data;
        if (isset($data['reset_password']) && $data['reset_password'] != null) {
            $business_session["employee"]["reset_password"] = $data['reset_password'] == 'on' ? 1 : 0;
        } else {
            $business_session["employee"]["reset_password"] = 0;
        }
        session(["business" => $business_session]);

        return redirect()->route("businesses.create.step.four");
    }

    public function createStepFour(): View
    {
        $business_session = session("business");
        return view("businesses.create.step_four", [
            "business" => $business_session,
        ]);
    }

    public function postCreateStepFour(Request $request): RedirectResponse
    {
        $business_session = session("business");
        $business_session["employee"] = $this->removeMasks(
            $business_session["employee"]
        );
        $business_session = $this->removeMasks($business_session);
        $this->post(
            "api/business/saveBusinessAndAdministrador",
            $business_session
        );
        return redirect()
            ->route("businesses.index")
            ->with("success", "Negócio cadastrado com sucesso!");
    }

    public function reactivate($id)
    {
        $this->post("api/business/$id/reactivate");
        return redirect()->route("businesses.index")->with("success", "Negócio reativado com sucesso!");
    }

    public function destroy($id)
    {
        $this->post("api/business/$id/disable");
        return redirect()->route("businesses.index")->with("success", "Negócio desativado com sucesso!");
    }

    public function cancel($id)
    {
        $response = $this->post("api/business/$id/cancel");
        $responseJson = $response->json();
        if($responseJson['logout']){
            session()->forget('API_TOKEN');
            ApiUser::reset();
            return redirect()->route("login");
        }
        ApiUser::update();
        return redirect()->route("home")->with("success", $responseJson['msg']);
    }

    public function editAdmin(Request $request, $id): View|RedirectResponse
    {
        try {
            $response = $this->get("api/business/$id");
            $business = $response->json("business");
            $business["employee_id"] = $business["admin"]["id"];

            $isCreateNewEmployeeAndUser = false;
            $isEditEmployee = false;
            $isCreateNewEmployee = false;

            $cpfSearch = $request->cpf_search;
            $employee = [];

            if ($cpfSearch) {
                $cpfSearchNumbers = preg_replace("/[^0-9]/", "", $cpfSearch);
                $responseUser = $this->get("api/user/show/by-cpf", ["cpf" => $cpfSearchNumbers], true, false);

                if ($responseUser->status() == 404) {
                    $isCreateNewEmployeeAndUser = true;
                    $employee = [];
                    $business['employee_id'] = null;
                } else {
                    $hasEmployee = $responseUser->json("employee") != null;
                    if ($hasEmployee) {
                        $business['employee_id'] = $responseUser->json("employee")["id"];
                        $employee = $responseUser->json("user");
                        $isEditEmployee = true;
                    } else {
                        $business['employee_id'] = 0;
                        $employee = $responseUser->json("user");
                        $isCreateNewEmployee = true;
                    }
                }
            }

            return view('businesses.edit_admin', [
                'business' => $business,
                'cpfSearch' => $cpfSearch,
                'isCreateNewEmployeeAndUser' => $isCreateNewEmployeeAndUser,
                'isEditEmployee' => $isEditEmployee,
                'isCreateNewEmployee' => $isCreateNewEmployee,
                'employee' => $employee,
            ]);
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }


    public function updateAdmin(Request $request, $id): RedirectResponse
    {
        try {
            $data = $request->all();

            $data['employee'] = [
                'id' => $data['employee_id'],
                'name' => $data['name'],
                'gender' => $data['gender'],
                'birth_date' => $data['birth_date'],
                'cpf' => $data['cpf'],
                'cep' => $data['cep'],
                'neighborhood' => $data['neighborhood'],
                'street' => $data['street'],
                'number' => $data['number'],
                'complement' => $data['complement'],
                'city' => $data['city'],
                'state' => $data['state'],
                'phone_number_1' => $data['phone_number_1'],
                'phone_number_2' => $data['phone_number_2'],
                'email' => $data['email'],
                'email_validated' => $data['email_validated'],
            ];

            $data['employee'] = $this->removeMasks($data['employee']);

            $response = $this->put("api/business/updateAdmin/$id", $data);

            if ($response->status() == 200) {
                return redirect()->route('businesses.show', $id)->with('success', 'Administrador atualizado com sucesso!');
            } else {
                return back()->with('error', $response->json('msg'));
            }

        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function publicRegisterBusiness(Request $request, $planTag)
    {
        try {
            // Receber parâmetros
            $sellerCpf = $request->query('sellerCpf'); // Obtém o sellerCpf da query string

            // Buscar o plano pela tag
            $planResponse = $this->get("api/plans/showByTag/$planTag", [], false, false);
            if ($planResponse->status() !== 200) {
                throw new \Exception("O plano selecionado é inválido.");
            }

            $plan = $planResponse->json('plan');
            if(!$plan['active'] || !$plan['publicly_visible']) {
                throw new \Exception("O plano selecionado não está disponível para registro público.");
            }

            // Buscar o vendedor pelo CPF, se fornecido
            $seller = null;
            if ($sellerCpf) {
                $sellerResponse = $this->get("api/sellers/getByCpf/$sellerCpf", [], false, false);
                if ($sellerResponse->status() === 200) {
                    $seller = $sellerResponse->json();
                }
            }

            return view('businesses.public_register', [
                'plan' => $plan,
                'seller' => $seller,
            ]);
        } catch (\Exception $e) {
            return view('businesses.public_register', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function handlePublicRegistration(PublicRegisterRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();
            $data['cpf'] = $data['cpf_admin'];
            unset($data['cpf_admin']);
            $data = $this->removeMasks($data);

            $response = $this->post('api/business/publicRegister', $data);

            return redirect()->route('businesses.publicRegisterSuccess', $response->json('business')['id'])
                ->with([
                    'isCreateNewEmployeeAndUser' =>$data['isCreateNewEmployeeAndUser'],
                    'isEditEmployee' => $data['isEditEmployee'],
                    'isCreateNewEmployee' => $data['isCreateNewEmployee'],
                ]);
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function publicRegisterSuccess(Request $request,$businessId): View
    {
        return view('businesses.public_register_success', [
            'businessId' => $businessId,
            'isCreateNewEmployeeAndUser' => $request->session()->get('isCreateNewEmployeeAndUser'),
            'isEditEmployee' => $request->session()->get('isEditEmployee'),
            'isCreateNewEmployee' => $request->session()->get('isCreateNewEmployee'),
        ]);
    }

    public function indexSeller(Request $request): View|RedirectResponse
    {
        try {
            if(ApiUser::getLoginType() != 'seller') {
                return redirect()->route('home')->with('error', 'Para acessar essa página, você precisa ser um vendedor.');
            }
            $response = $this->get("api/business/getSellerBusiness", [
              "per_page" => "5",
              "page" => $request->page ?? 1,
              "filter_disabled" => $request->filter_disabled ?? 'active',
              "search" => $request->search,
              "seller_id" => $request->seller_id ?? null,
              "with_sum" => true,
            ]);

            $page = $response->json("businesses");

            $total_value = $response->json("total_value");

            $paginator = new LengthAwarePaginator(
                $page["data"],
                $page["total"],
                $page["per_page"],
                $page["current_page"],
                [
                    "path" => $request->url(),
                    "query" => array_merge($request->query(), [
                        'filter_disabled' => $request->filter_disabled ?? 'active',
                        'search' => $request->search,
                        'seller_id' => $request->seller_id ?? null,
                    ]),

                ]
            );

            $child_sellers = $response->json("child_sellers");

            return view("businesses.index_seller", [
                "businesses" => $paginator,
                "currentPage" => $paginator->currentPage(),
                "lastPage" => $paginator->lastPage(),
                "filter_disabled" => $request->filter_disabled ?? 'active',
                "search" => $request->search,
                "seller_id" => $request->seller_id ?? null,
                "child_sellers" => $child_sellers,
                "total_value" => $total_value,
              ]);
        } catch (\Exception $e) {
            return back()->with("error", $e->getMessage());
        }
    }

    public function approve($id)
    {
        $this->post("api/business/$id/approve");
        return redirect()->route("businesses.index")->with("success", "Negócio aprovado com sucesso!");
    }

    public function reject($id)
    {
        $this->post("api/business/$id/reject");
        return redirect()->route("businesses.index")->with("success", "Negócio rejeitado com sucesso!");
    }

    public function cancelPending($id)
    {
        $this->post("api/business/$id/cancelPending");
        return redirect()->route("businesses.index")->with("success", "Negócio cancelado com sucesso!");
    }

    public function reverseCancel($id)
    {
        $this->post("api/business/$id/reverseCancel");
        return redirect()->route("businesses.index")->with("success", "Cancelamento revertido com sucesso!");
    }
}
