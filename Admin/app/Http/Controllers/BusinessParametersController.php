<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class BusinessParametersController extends Controller
{
    public function index(): View
    {
        $response = $this->get('api/getFormsToList');
        $forms = $response->json('forms');

        $instanceKey = ApiUser::get()['business_selected']['parameters']['whatsapp_instance_key'];
        if ($instanceKey) {
            $resultValidateWhatsapp = $this->getWithoutCheckResponse('api/business/whatsapp/instance/info',
                ['key' => $instanceKey, 'business_id' => ApiUser::get()['business_selected']['id']]
            );
            if ($resultValidateWhatsapp->json('error')) {
                ApiUser::update();
            }
        }
        return view('parameters.index', ['forms' => $forms]);
    }

    public function update(Request $request): RedirectResponse
    {
        try {
            $id = ApiUser::get()['business_selected']['id'];

            $data = $request->only([
                'evaluation_expiration_option',
                'custom_evaluation_expiration_days',
                'cashback_expiration_option',
                'custom_cashback_expiration_days',
                'cashback_percentage',
                // 'evaluation_interval_day',
                'form_id',
                'can_set_cashback_value',
            ]);

            // Converte os valores de "outros" para horas
            if ($data['evaluation_expiration_option'] === 'other') {
                $data['evaluation_expiration_hours'] = (int)$data['custom_evaluation_expiration_days'] * 24;
            } else {
                $data['evaluation_expiration_hours'] = (int)$data['evaluation_expiration_option'];
            }

            if ($data['cashback_expiration_option'] === 'other') {
                $data['cashback_expiration_hours'] = (int)$data['custom_cashback_expiration_days'] * 24;
            } else {
                $data['cashback_expiration_hours'] = (int)$data['cashback_expiration_option'];
            }

            // Remove os campos temporários
            unset($data['evaluation_expiration_option'], $data['custom_evaluation_expiration_days']);
            unset($data['cashback_expiration_option'], $data['custom_cashback_expiration_days']);


            $response = $this->put("api/business/{$id}/parameters", $data);

            ApiUser::update();
            return redirect()->route('businesses.parameters')->with('success', $response->json('msg'));
        } catch (\Exception $e) {
            return back()->withErrors($e->getMessage())->withInput();
        }
    }

    /**
     * Inicia a conexão com WhatsApp gerando um QR code
     */
    public function connectWhatsapp(Request $request, $id): JsonResponse
    {
        try {
            $business = ApiUser::get()['business_selected'];
            if ($business['id'] != $id) {
                throw new \Exception('Negócio não encontrado');
            }

            // Faz a requisição para inicializar a instância do WhatsApp
            $response = $this->post("api/business/{$id}/whatsapp/init");
            $data = $response->json();

            if (!($data['success'] ?? false)) {
                throw new \Exception($data['message'] ?? 'Erro ao inicializar WhatsApp');
            }

            ApiUser::update();

            return response()->json([
                'success' => true,
                'message' => 'Iniciando conexão com WhatsApp'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Obtém o QR code para conexão do WhatsApp
     */
    public function getWhatsappQrCode(Request $request, $id): JsonResponse
    {
        try {
            $business = ApiUser::get()['business_selected'];
            if ($business['id'] != $id) {
                throw new \Exception('Negócio não encontrado');
            }

            // Faz a requisição para obter o QR code
            $response = $this->get("api/business/{$id}/whatsapp/qrcode");
            $data = $response->json();

            if (!($data['success'] ?? false)) {
                throw new \Exception($data['message'] ?? 'Erro ao obter QR Code');
            }

            return response()->json([
                'success' => true,
                'qrcode' => $data['qrcode']
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Verifica o status da conexão com WhatsApp
     */
    public function checkWhatsappStatus(Request $request, $id): JsonResponse
    {
        try {
            $business = ApiUser::get()['business_selected'];
            if ($business['id'] != $id) {
                throw new \Exception('Negócio não encontrado');
            }

            // Faz a requisição para verificar o status
            $response = $this->get("api/business/{$id}/whatsapp/status");
            $data = $response->json();

            if (!($data['success'] ?? false)) {
                throw new \Exception($data['message'] ?? 'Erro ao verificar status');
            }

            $connected = $data['data']['connected'] ?? false;
            $phone = $data['data']['phone_number'] ?? null;
            $oldPhone = $business['parameters']['whatsapp_phone_number'];

            if ($phone != $oldPhone) {
                ApiUser::update();
            }

            return response()->json([
                'success' => true,
                'connected' => $connected,
                'phone_number' => $data['data']['phone_number'] ?? null
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Desconecta o WhatsApp
     */
    public function disconnectWhatsapp(Request $request, $id): JsonResponse
    {
        try {
            $business = ApiUser::get()['business_selected'];
            if ($business['id'] != $id) {
                throw new \Exception('Negócio não encontrado');
            }

            $data = $this->sendPostRequest("api/business/{$id}/whatsapp/disconnect");

            if (!($data['success'] ?? false)) {
                if ($data['message'] === 'Instância não inicializada') {
                    ApiUser::update();
                    return response()->json(['success' => true, 'message' => 'WhatsApp desconectado com sucesso']);
                }
                return response()->json(['error' => $data['message'] ?? 'Erro ao desconectar WhatsApp'], 400);
            }

            ApiUser::update();

            return response()->json([
                'success' => true,
                'message' => 'WhatsApp desconectado com sucesso'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }
}
