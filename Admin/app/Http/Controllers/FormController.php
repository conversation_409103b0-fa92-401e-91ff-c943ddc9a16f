<?php

namespace App\Http\Controllers;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class FormController extends Controller
{
    public function index(Request $request)
    {
        try {
            $page = $this->get('api/forms', ['per_page' => '5', 'page' => ($request->page ?? 1)])->json('forms');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page']);
            $paginator->setPath($request->url());
            return view('form.index', ['forms' => $paginator, 'base_url' => env('APP_URL') . '/forms']);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function create()
    {
        return view('form.create');
    }

    public function store(Request $request)
    {
        try {
            $data = $request->all();
            $data['default'] = isset($data['default']) ? true : false;

            if(isset($data['active']) || !$data['default']) {
                $data['active'] = true;
            } else {
                $data['active'] = false;
            }

            if (isset($data['topicsJson'])) {
                $data['topics'] = json_decode($data['topicsJson'], true);
            }

            if (isset($data['businessesIds'])) {
                $data['businesses'] = $data['businessesIds'];
            }

            unset($data['topicsJson']);
            unset($data['businessesIds']);
            unset($data['_token']);

            $this->post('api/forms', $data);
            session()->flash('success', 'Formulário criado');
            return redirect()->route('forms.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function show($id)
    {
        try {
            $response = $this->get("api/forms/$id");
            return view('form.show', ['form' => $response->json('form')]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function edit($id)
    {
        try {
            $response = $this->get("api/forms/$id");
            return view('form.edit', ['form' => $response->json('form')]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $data = $request->all();
            $data['default'] = isset($data['default']) ? true : false;

            if(isset($data['active']) || !$data['default']) {
                $data['active'] = true;
            } else {
                $data['active'] = false;
            }

            if (isset($data['topicsJson'])) {
                $data['topics'] = json_decode($data['topicsJson'], true);
            }

            if (isset($data['businessesIds'])) {
                $data['businesses'] = $data['businessesIds'];
            }

            unset($data['topicsJson']);
            unset($data['businessesIds']);
            unset($data['_token']);

            $this->put("api/forms/$id/update", $data);

            return redirect()->back()->with('success', 'Formulário atualizado');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        try {
            $this->delete("api/forms/$id/delete");
            session()->flash('success', 'Formulário deletado');
            return redirect()->route('forms.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function search(Request $request)
    {
        try {
            $page = $this->get('api/forms', [
                'per_page' => '5',
                'page' => ($request->page ?? 1),
                'search' => $request->search,
                'filter_default' => $request->filter_default,
            ])->json('forms');

            $paginator = new LengthAwarePaginator(
                $page['data'],
                $page['total'],
                $page['per_page'],
                $page['current_page'],
                [
                    'path' => $request->url(),
                    'query' => [
                        'search' => $request->search,
                        'filter_default' => $request->filter_default,
                    ]
                ]
            );
            $paginator->appends('search', $request->search);
            $paginator->appends('filter_default', $request->filter_default);

            $data = [
                'forms' => $paginator,
                'search' => $request->search,
                'filter_default' => $request->filter_default,
            ];

            return view('form.index', $data);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
}
