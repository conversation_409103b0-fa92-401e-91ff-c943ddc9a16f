<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\ApiTrait;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Str;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests, ApiTrait;

    protected function removeMasks(array $data)
    {
        !isset($data['cpf']) ? : $data['cpf'] = str_replace(['.', '-'], '', $data['cpf']);
        !isset($data['cnpj']) ? : $data['cnpj'] = str_replace(['.', '-', '/'], '', $data['cnpj']);
        !isset($data['cep']) ? : $data['cep'] = str_replace(['-'], '', $data['cep']);
        !isset($data['phone_number_1']) ? : $data['phone_number_1'] = str_replace([' ', '(', ')', '-'], '', $data['phone_number_1']);
        !isset($data['phone_number_2']) ? : $data['phone_number_2'] = str_replace([' ', '(', ')', '-'], '', $data['phone_number_2']);
        !isset($data['phone_number']) ? : $data['phone_number'] = str_replace([' ', '(', ')', '-'], '', $data['phone_number']);

        return $data;
    }

    private function normalize($string)
    {
        $slug = Str::slug($string, ' ');
        return Str::lower($slug);
    }
}
