<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Http\Controllers\Traits\ApiTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class CashbackController extends Controller
{
    use ApiTrait;
    public function __construct(){}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $page = $this->get('api/cashback', [
            'per_page' => $request->input('per_page', 5),
            'page' => $request->input('page', 1),
            'search' => $request->search,
            'status' => $request->input('status', 'pending'),
        ])->json();
        $cashbacks = $page['cashbacks'];
        $totalAmount = $page['totalAmount'];
        $paginator = new LengthAwarePaginator(
            $cashbacks['data'],
            $cashbacks['total'],
            $cashbacks['per_page'],
            $cashbacks['current_page']
        );
        $paginator->setPath($request->url());
        return view('cashbacks.index', [
            'cashbacks' => $paginator,
            'totalAmount' => $totalAmount,
            'queryParams' => $request->except('_token'),
            ...$request->all()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cashbacks.form', ['cashback' => null]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->post("api/cashback/", [
            'status' => $request->status,
            'expiration_date' => Carbon::parse($request->expiration_date),
            'percentage' => $request->percentage,
            'business_id' => $request->business_id,
            'clients_id' => $request->clients_id
        ]);
        return redirect()->route('cashbacks.index')->with('success', 'Cashbacks criados!');
    }

    /**
     * Display the specified resource.
     */
    public function show(int $cashbackId)
    {
        $cashback = $this->get("api/cashback/$cashbackId")->json();
        return view('cashbacks.show', ['cashback' => $cashback]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $cashbackId)
    {
        $cashback = $this->get("api/cashback/$cashbackId")->json();
        return view('cashbacks.form', ['cashback' => $cashback]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $cashbackId)
    {
        $this->put("api/cashback/$cashbackId", ['status' => $request->status]);
        return redirect()->route('cashbacks.index')->with('success', 'Cashback atualizado!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $cashbackId)
    {
    }

    public function indexClient(Request $request)
    {
        $page = $this->get("api/cashbacks/client/getCashbacks", [
            'per_page' => $request->input('per_page', 5),
            'page' => $request->input('page', 1),
            'business_id' => $request->input('business_id'),
            'status' => $request->input('status', 'pending'),
        ])->json();
        $cashbacks = $page['cashbacks'];
        $totalAmount = $page['totalAmount'];
        $paginator = new LengthAwarePaginator(
            $cashbacks['data'],
            $cashbacks['total'],
            $cashbacks['per_page'],
            $cashbacks['current_page']
        );
        $paginator->setPath($request->url());

        $businesses = $this->get('api/cashbacks/client/businessesClient')->json('businesses');
        return view('cashbacks.clients_index', [
            'cashbacks' => $paginator,
            'totalAmount' => $totalAmount,
            'businesses' => $businesses,
            'queryParams' => $request->except('_token'),
            ...$request->all()
        ]);
    }

    public function showCashbackClient(int $cashbackId)
    {
        $cashback = $this->get("api/cashbacks/client/getCashbacks/$cashbackId")->json();
        return view('cashbacks.show', ['cashback' => $cashback]);
    }
    /**
     * Busca cashbacks pendentes do cliente
     */
    public function searchPendingClient(Request $request)
    {
        try {
            $searchType = $request->input('search_type');
            $searchValue = $request->input('search');

            if (!$searchType || !$searchValue) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tipo de busca e valor são obrigatórios'
                ], 400);
            }

            // Fazer requisição para a API
            $response = $this->getWithoutCheckResponse('api/cashbacks/pending-client', [
                'search_type' => $searchType,
                'search_value' => $searchValue
            ]);

            if ($response->status() == 404) {
                return response()->json([
                    'success' => false,
                    'message' => 'Nenhum cliente encontrado.'
                ], 404);
            }

            $data = $response->json();

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'data' => $data
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $data['message'] ?? 'Erro ao buscar cashbacks pendentes'
                ], $response->status());
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro interno: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Envia código de validação para o cliente
     */
    public function sendValidationCode(Request $request)
    {
        try {
            $clientId = $request->input('client_id');
            $sendMethod = $request->input('send_method');

            if (!$clientId || !$sendMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID do cliente e método de envio são obrigatórios'
                ], 400);
            }

            // Fazer requisição para a API
            $result  = $this->sendPostRequest('api/cashbacks/send-validation-code', [
                'client_id' => $clientId,
                'send_method' => $sendMethod
            ]);

            if ($result['success'] != false){
                $data = $result['data'];
                return response()->json([
                    'success' => true,
                    'message' => $data['message']
                ]);
            } else {
                // Verificar se é erro de conexão do WhatsApp
                if ($result['status'] == 500 && ($result['message'] == 'Chave de instância do WhatsApp não configurada para o negócio.'
                        || $result['message'] == 'Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado.')
                        || $result['message'] == 'WhatsApp não conectado.'
                ) {
                    ApiUser::update();
                    return response()->json([
                        'success' => false,
                        'message' => $result['message'],
                        'whatsapp_connection_error' => true
                    ], $result['status']);
                }

                return response()->json([
                    'success' => false,
                    'message' => $result['message'] ?? 'Erro ao enviar código de validação'
                ], $result['status']);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro interno: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Valida código e conclui cashbacks
     */
    public function validateAndComplete(Request $request)
    {
        try {
            $clientId = $request->input('client_id');
            $validationCode = $request->input('validation_code');
            $encryptedCashbackIds = $request->input('encrypted_cashback_ids');

            if (!$clientId || !$validationCode || !$encryptedCashbackIds) {
                return response()->json([
                    'success' => false,
                    'message' => 'Todos os campos são obrigatórios'
                ], 400);
            }

            // Fazer requisição para a API
            $response = $this->sendPostRequest('api/cashbacks/validate-and-complete', [
                'client_id' => $clientId,
                'validation_code' => $validationCode,
                'encrypted_cashback_ids' => $encryptedCashbackIds
            ]);

            if ($response['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $response['data']['message']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response['message'] ?? 'Erro ao validar e concluir cashbacks'
                ], $response['status']);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro interno: ' . $e->getMessage()
            ], 500);
        }
    }
}
