<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Helpers\SystemHelper;
use Exception;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\View\Factory;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        $deviceId = $request->input('id_device', null);
        if (!session()->has('ID_DEVICE') && $deviceId) {
            session()->put('ID_DEVICE', $deviceId);
        }
        try {
            if (ApiUser::logged()) {
                $user = ApiUser::get();
                $this->checkIncompleteProfileData($user);
                if (
                    (
                        $user['login_type'] == 'employee' ||
                        $user['login_type'] == 'admin' ||
                        $user['login_type'] == 'client' ||
                        $user['login_type'] == 'seller'
                    ) &&
                    $user['email_verified_at'] == null
                ) {
                    return redirect()->route('verify_email');
                }

                if ($user['login_type'] == 'employee' || $user['login_type'] == 'admin') {
                    $initialScreen = $user['initial_screen'] ?? null;
                    switch ($initialScreen) {
                        case 'evaluations':
                            if (ApiUser::hasPermission('evaluations')) {
                                return redirect()->route('evaluations.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'dashboard':
                            if (ApiUser::hasPermission('dashboard')) {
                                return redirect()->route('dashboard.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'employees':
                            if (ApiUser::hasPermission('employees')) {
                                return redirect()->route('employees.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'manage_business_profile':
                            if (ApiUser::hasPermission('manage_business_profile')) {
                                $business = $user['business_selected'];
                                return redirect()->route('businesses.show', ['id' => $business['id']])->with('success', session()->get('success'));
                            }
                            break;
                        case 'topics':
                            if (ApiUser::hasPermission('topics')) {
                                return redirect()->route('topics.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'forms':
                            if (ApiUser::hasPermission('forms')) {
                                return redirect()->route('forms.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'clients':
                            if (ApiUser::hasPermission('clients')) {
                                return redirect()->route('clients.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'cashback':
                            if (ApiUser::hasPermission('cashback')) {
                                return redirect()->route('cashbacks.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'notifications':
                            if (ApiUser::hasPermission('notifications')) {
                                return redirect()->route('notifications.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'plans':
                            if (ApiUser::hasUserFunction('plans')) {
                                return redirect()->route('plans.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'update_business':
                            if (ApiUser::hasUserFunction('update_business')) {
                                return redirect()->route('businesses.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'sellers':
                            if (ApiUser::hasUserFunction('sellers')) {
                                return redirect()->route('sellers.index')->with('success', session()->get('success'));
                            }
                            break;
                        case 'settings':
                            if (ApiUser::hasUserFunction('settings')) {
                                return redirect()->route('settings')->with('success', session()->get('success'));
                            }
                            break;
                        case 'quick_actions':
                            if (ApiUser::hasPermission('dashboard') || ApiUser::hasPermission('evaluations') || ApiUser::hasPermission('cashback')) {
                                return redirect()->route('quick_actions')->with('success', session()->get('success'));
                            }
                            break;
                        case 'informative':
                            redirect()->route('informative')->with('success', session()->get('success'));
                            break;
                    }
                }

                if (($user['login_type'] == 'employee' || $user['login_type'] == 'admin')
                    && ApiUser::get()['business_selected'] != null
                ) {
                    return redirect()->route('informative')->with('success', session()->get('success'));
                }

                // if (($user['login_type'] == 'employee' || $user['login_type'] == 'admin')
                //     && ApiUser::get()['business_selected'] != null
                //     && (ApiUser::hasPermission('dashboard') || ApiUser::hasPermission('evaluations') || ApiUser::hasPermission('cashback'))
                // ) {
                //     return redirect()->route('quick_actions')->with('success', session()->get('success'));
                // }

                if (ApiUser::hasPermission('dashboard') && request()->is('/'))
                    return redirect()->route(route: 'dashboard.index');

                if (ApiUser::getLoginType() == 'client') {
                    return redirect()->route('informative');
                }

                if (($user['login_type'] == 'employee' || $user['login_type'] == 'admin') && ApiUser::get()['business_selected'] == null) {
                    return redirect()->route('profile');
                }

                if($user['login_type'] == 'seller') {
                    return redirect()->route('businesses.seller.index');
                }

                return view('welcome');
            } else {
                return view('auth.login');
            }
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (Exception $e) {
            session()->flash('error', $e->getMessage());
            return view('components.error_screen')->with('error', $e->getMessage());
        }
    }

    private function checkIncompleteProfileData(): void
    {
        $apiUser = ApiUser::get();

        if (!$apiUser) {
            return;
        }

        $isProfileIncomplete = false;
        $incompleteBusinessIds = [];

        if (
            empty($apiUser['birth_date']) ||
            empty($apiUser['phone_number_1']) ||
            (isset($apiUser['address']) && (
                    empty($apiUser['address']['cep']) ||
                    empty($apiUser['address']['city']) ||
                    empty($apiUser['address']['state']) ||
                    empty($apiUser['address']['neighborhood']) ||
                    empty($apiUser['address']['number'])
                ))
        ) {
            $isProfileIncomplete = true;
        }

        if (isset($apiUser['businesses']) && is_array($apiUser['businesses'])) {
            foreach ($apiUser['businesses'] as $business) {
                if (
                    isset($business['admin']['id']) &&
                    $business['admin']['id'] == $apiUser['id']
                ) {
                    if (
                        empty($business['corporate_reason']) ||
                        empty($business['cep']) ||
                        empty($business['city']) ||
                        empty($business['state']) ||
                        empty($business['neighborhood']) ||
                        empty($business['number'])
                    ) {
                        $incompleteBusinessIds[] = $business['id'];
                    }
                }
            }
        }

        $messages = [];

        if ($isProfileIncomplete) {
            $profileUrl = route('profile');
            $messages[] = "Seu perfil está incompleto. <a href='$profileUrl'>Clique aqui</a> para atualizar suas informações.";
        }

        if (!empty($incompleteBusinessIds)) {
            foreach ($incompleteBusinessIds as $businessId) {
                $business = collect($apiUser['businesses'])->firstWhere('id', $businessId);
                $businessName = $business['name'] ?? 'Seu negócio';
                $businessProfileUrl = route('businesses.show', $businessId);  // Ajuste se necessário

                $messages[] = "As informações do seu negócio '{$businessName}' estão incompletas. <a href='$businessProfileUrl'>Clique aqui</a> para atualizar.";
            }
        }

        if (!empty($messages)) {
            $alertMessage = implode('<br>', $messages);
            session()->flash('alert_incomplete_data', $alertMessage);
        }
    }


    public function dashboard()
    {
        return redirect()->route('home');
    }

    public function refresh_system(): JsonResponse
    {
        SystemHelper::forceUpdate();
        return response()->json(['msg' => 'Sistema atualizado com sucesso']);
    }

    public function settings()
    {
        $havePermission = $this->get('api/check-permission', ['permission' => 'settings'])->json();
        if (!$havePermission['permission']) {
            return back()->with('error', 'Você não tem permissão para acessar essa página');
        }
        $system = SystemHelper::get();
        $systemParameters = [];

        if (ApiUser::get()['authorizedSettingsFunction']['contact']) {
            $phoneNumber = preg_replace('/[^0-9]/', '', $system['PHONE_NUMBER']);

            $length = strlen($phoneNumber);

            if ($length == 11) {
                $system['PHONE_NUMBER'] = preg_replace('/(\d{2})(\d{5})(\d{4})/', '($1) $2-$3', $phoneNumber);
            } elseif ($length == 10) {
                $system['PHONE_NUMBER'] = preg_replace('/(\d{2})(\d{4})(\d{4})/', '($1) $2-$3', $phoneNumber);
            }
        } else {
            unset($system['EMAIL']);
            unset($system['PHONE_NUMBER']);
        }
        if (ApiUser::get()['authorizedSettingsFunction']['system_parameters']) {
            $systemParameters = $this->get('api/settings/system_parameters')->json();
        }

        return view('employee.settings', [
            'email' => $system['EMAIL'] ?? '',
            'phone_number' => $system['PHONE_NUMBER'] ?? '',
            'system_parameters' => $systemParameters
        ]);
    }

    public function support()
    {
        $contact = SystemHelper::getContact();
        return redirect(env('NOTION_APP_LINK'));
    }

    public function verify_email()
    {
        return view('verify_email');
    }

    public function update_email_verified_at(Request $request)
    {
        $this->post('api/user/update-email-verified-at', ['email' => $request->email]);
        ApiUser::update();
        return redirect()->route('home');
    }

    public function updateContact(Request $request)
    {
        try {
            $this->put('api/settings/contact', [
                'email' => $request->email,
                'phone_number' => $request->phone_number
            ]);
            SystemHelper::forceUpdate();

            session()->flash('success', 'Contato atualizado com sucesso!');
            return redirect()->route('settings');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (Exception $e) {
            session()->flash('error', $e->getMessage());
            return view('components.error_screen')->with('error', $e->getMessage());
        }
    }

    public function updateSystemParameters(Request $request)
    {
        try {
            $this->put('api/settings/system_parameters', $request->all());
            SystemHelper::forceUpdate();
            session()->flash('success', 'Parâmetros do sistema atualizados com sucesso!');
            return redirect()->route('settings');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (Exception $e) {
            session()->flash('error', $e->getMessage());
            return view('components.error_screen')->with('error', $e->getMessage());
        }
    }

    public function app()
    {
        return view('app');
    }

    public function quickActions()
    {
        if ((ApiUser::hasPermission('dashboard') ||
                ApiUser::hasPermission('evaluations') ||
                ApiUser::hasPermission('cashback'))
            && ApiUser::get()['business_selected'] != null
        ) {
            if (ApiUser::hasPermission('evaluations')) {
                $forms = $this->get('api/getFormsToList')->json('forms');
            }
            return view('quick_actions', [
                'forms' => $forms ?? [],
                'formId' => null,
            ]);
        } else {
            return redirect()->route('home');
        }
    }

    public function informative() {
        $user = ApiUser::get();

        if ($user['login_type'] === 'client')
            return view('informative_client');

        return view('informative');
    }

    public function markInformativeAsRead() {
        $user = ApiUser::get();
        if ($user['login_type'] === 'client')
            $response = $this->post('api/clients/toggleInformativeFlag')->json();
        else
            $response = $this->post('api/employees/toggleInformativeFlag')->json();
        
        if ($response['success']) {
            ApiUser::update();
        }

        return redirect()->route('informative');
    }
}
