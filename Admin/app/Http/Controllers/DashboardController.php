<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Http\Controllers\Traits\ApiTrait;
use Carbon\Carbon;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Log;

class DashboardController extends Controller
{
    use ApiTrait;

    public function index(Request $request) {
        try {
            $form = $request->input('form_id', null);
            $period = $request->input('period', 'TODAY');
            $startDate = $request->input('startDate', null);
            $endDate = $request->input('endDate', null);
            $data = $this->get('api/dashboard/relatory', [
                'period' => $period,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'form_id' => $form
            ]);
            $generated_at = Carbon::now();

            $extractInfo = function ($topicData) {
                $array = [];

                $array['ids'] = array_map(function ($topic) {
                    return $topic['id'];
                }, $topicData);

                $array['labels'] = array_map(function ($topic) {
                    return $topic['name'];
                }, $topicData);

                $array['data'] = array_map(function ($topic) {
                    return $topic['average_response'];
                }, $topicData);

                return $array;
            };
            $topicsDefault = $extractInfo($data['topicsDefault']);
            $topicsNonDefault = $extractInfo($data['topicsNonDefault']);

            return view('dashboard.index', [
                "topicsDefault" => $topicsDefault,
                "topicsNonDefault" => $topicsNonDefault,
                "period" => $period,
                "form" => $form,
                "startDate" => $startDate,
                "endDate" => $endDate,
                "generated_at" => $generated_at
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function showForSellerById(Request $request, $id) {
      try {
          if(ApiUser::getLoginType() != 'seller') {
              return response()->json(['error' => 'Para acessar essa página, você precisa ser um vendedor.'], 403);
          }

          $form = $request->input('form_id', null);
          $period = $request->input('period', 'TODAY');
          $startDate = $request->input('startDate', null);
          $endDate = $request->input('endDate', null);
          $data = $this->get("api/dashboard/relatorySeller/$id", [
              'period' => $period,
              'startDate' => $startDate,
              'endDate' => $endDate,
              'form_id' => $form
          ]);
          $generated_at = Carbon::now();

          $extractInfo = function ($topicData) {
              $array = [];

              $array['ids'] = array_map(function ($topic) {
                  return $topic['id'];
              }, $topicData);

              $array['labels'] = array_map(function ($topic) {
                  return $topic['name'];
              }, $topicData);

              $array['data'] = array_map(function ($topic) {
                  return $topic['average_response'];
              }, $topicData);

              return $array;
          };
          $topicsDefault = $extractInfo($data['topicsDefault']);
          $topicsNonDefault = $extractInfo($data['topicsNonDefault']);
          $business = $data['business'];

          return view('dashboard.index', [
              "topicsDefault" => $topicsDefault,
              "topicsNonDefault" => $topicsNonDefault,
              "period" => $period,
              "form" => $form,
              "startDate" => $startDate,
              "endDate" => $endDate,
              "generated_at" => $generated_at,
              "business" => $business,
              "isSellerDashboard" => true
          ]);
      } catch (HttpResponseException $e) {
          return $e->getResponse();
      } catch (\Exception $e) {
          return back()->with('error', $e->getMessage());
      }
    }

    public function show($id, Request $request) {
        try {
            $period = $request->input('period', 'TODAY');
            $startDate = $request->input('startDate', null);
            $endDate = $request->input('endDate', null);
            $generated_at = $request->input('generated_at', Carbon::now());
            $data = $this->get("api/dashboard/relatory/$id", [
                'period' => $period,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'generated_at' => $generated_at
            ]);
            $fiveStarQuestions = $data['fiveStarQuestions'];
            $yesNoQuestions = $data['yesNoQuestions'];
            return view('dashboard.show', ['fiveStarQuestions' => $fiveStarQuestions, 'yesNoQuestions' => $yesNoQuestions]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function details(Request $request) {
        try {
            $period = $request->input('period', 'TODAY');
            $formId = $request->input('form_id');
            $evaluationType = $request->input('evaluation_type', 'five_star');
            $filters = $request->input('filters', null);

            if (isset($filters)) {
                $filters = json_decode($filters, true);
            } else {
                $filters = [];
            }

            $filters['period'] = $period;
            $filters['evaluation_type'] = $evaluationType;
            
            if ($formId) {
                $filters['form_id'] = $formId;
            }

            $page = $this->get('api/dashboard/getEvaluationsFiltered', [
                'per_page' => $request->input('per_page', 5),
                'page' => $request->input('page', 1),
                'filters' => $filters,
            ])->json('responses');
            $paginator = new LengthAwarePaginator(
                $page['data'],
                $page['total'],
                $page['per_page'],
                $page['current_page']
            );
            $paginator->setPath($request->url());
            return view('dashboard.details', ['responses' => $paginator,
                'filters' => $filters,
                'queryParams' => $request->except('_token')
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function detailsForSeller(Request $request, $id) {
        try {
            if(ApiUser::getLoginType() != 'seller') {
                return response()->json(['error' => 'Unauthorized access'], 403);
            }

            $period = $request->input('period', 'TODAY');
            $formId = $request->input('form_id');
            $evaluationType = $request->input('evaluation_type', 'five_star');
            $filters = $request->input('filters', null);

            if (isset($filters)) {
                $filters = json_decode($filters, true);
            } else {
                $filters = [];
            }

            $filters['period'] = $period;
            $filters['evaluation_type'] = $evaluationType;

            if ($formId) {
                $filters['form_id'] = $formId;
            }
            
            $response = $this->get("api/dashboard/getEvaluationsFilteredSeller/$id", [
                'per_page' => $request->input('per_page', 5),
                'page' => $request->input('page', 1),
                'filters' => $filters,
            ]);
            $business = $response->json('business');

            $page = $response->json('responses');
            $paginator = new LengthAwarePaginator(
                $page['data'],
                $page['total'],
                $page['per_page'],
                $page['current_page']
            );
            $paginator->setPath($request->url());
            return view('dashboard.details', ['responses' => $paginator,
                'filters' => $filters,
                'queryParams' => $request->except('_token'),
                'business' => $business,
                'isSellerDashboard' => true
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
}
