<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Helpers\SystemHelper;
use App\Http\Controllers\Traits\ApiTrait;
use App\Http\Requests\StoreSiteNotificationRequest;
use Exception;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class SiteNotificationController extends Controller
{
    use ApiTrait;
    
    public function indexEmployee(Request $request)
    {
        $page = $this->get('api/siteNotifications', [
            'per_page' => $request->input('per_page', 5),
            'page' => $request->input('page', 1),
            'search' => $request->search,
        ])->json('notifications');
        $paginator = new LengthAwarePaginator(
            $page['data'],
            $page['total'],
            $page['per_page'],
            $page['current_page'],
        );
        $paginator->setPath($request->url());
        return view('siteNotifications.index_employee', [
            'notifications' => $paginator,
            'queryParams' => $request->except('_token'),
            ...$request->all()
        ]);
    }

    public function create()
    {
        return view('siteNotifications.create');
    }

    public function store(StoreSiteNotificationRequest $request)
    {
        try {
            $data = $request->all();

            if (isset($data['filtersJSON'])) {
                $data['filters'] = json_decode($data['filtersJSON'], true);
            }
            
            if (isset($data['userIdsJSON'])) {
                $data['userIds'] = json_decode($data['userIdsJSON'], true);
            }

            if (isset($data['exceptIdsJSON'])) {
                $data['exceptIds'] = json_decode($data['exceptIdsJSON'], true);
            }

            $this->post("api/siteNotifications/", $data);
            session()->flash('success', 'Notificação adicionada!');
            return redirect()->route('siteNotifications.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function loadTable(Request $request) {
        $type = $request->input('type', 'client');
        if (ApiUser::hasUserFunction('release_all_business') && 
            ApiUser::get()['business_selected']['id'] == SystemHelper::get()['ADMIN_BUSINESS_ID']
        ) {
            return view('siteNotifications.tables.admin_table');
        }
        
        if (in_array($type, ['client', 'seller', 'employee'])) {
            return view('siteNotifications.tables.' . $type . '_table');
        }
        return response('Tipo inválido', 400);
    }

    public function indexUser(Request $request) {
        $page = $this->get('api/siteNotifications/list', [
            'per_page' => $request->input('per_page', 5),
            'page' => $request->input('page', 1),
            'search' => $request->search,
            'viewed' => $request->input('viewed', 'all')
        ])->json('notifications');
        $paginator = new LengthAwarePaginator(
            $page['data'],
            $page['total'],
            $page['per_page'],
            $page['current_page'],
        );
        $paginator->setPath($request->url());
        return view('siteNotifications.index_user', [
            'notifications' => $paginator,
            'queryParams' => $request->except('_token'),
            ...$request->all()
        ]);
    }

    public function show(Request $request, $notificationId) {
        $notification = $this->get("api/siteNotifications/$notificationId/show")->json('notification');
        return view('siteNotifications.show', ['notification' => $notification]);
    }
}
