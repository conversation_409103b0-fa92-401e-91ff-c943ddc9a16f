<?php

namespace App\Http\Controllers\Traits;

use Illuminate\Http\Client\Pool;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Log;

trait ApiTrait
{
    public function getApiUrl()
    {
        return env('API_URL');
    }

    public function checkResponse(Response $response)
    {
        $redirect_to = null;

        if($response->status() == 400) {
            $redirect_to = redirect()->route('home')->with('error', $response->json('msg'));
            $exception_code = $response->json('code');
            if($exception_code == 'RequesterNotFoundException') { $redirect_to = back()->with('error', $response->json('msg')); }
        }
        if($response->status() == 401) {
            session()->forget('API_TOKEN');
            session()->forget('AUTH_USER');
            $redirect_to = redirect()->route('login')->with('error', 'Voçê precisa estar logado.');
        }
        if($response->status() == 403) {
            $redirect_to = back()->with('error', $response->json('msg'))->withInput();
        }
        if($response->status() == 422) {
            $redirect_to = back()->withErrors($response->json('errors'))->withInput();
            if($redirect_to->getRequest()->routeIs('repair_requests.index')) { $redirect_to->with('clean', true); }
        }

        if(!$response->successful()) {
            $message = $response->json('msg');
            if($message) { $redirect_to = back()->with('error', $message); }
            else { $redirect_to = back()->with('error', 'Falha de comunicação com a API'); }
        }
        if(isset($redirect_to)) {
            throw new \Illuminate\Http\Exceptions\HttpResponseException($redirect_to);
        }
    }

    public function get(string $uri, Array $query = [], bool $withToken = true, bool $withCheckResponse = true)
    {
        $url = $this->getApiUrl();
        $response = $withToken ? Http::withToken(session('API_TOKEN'))->get("$url/$uri", $query) : Http::get("$url/$uri",$query);

        if($withCheckResponse) {
            $this->checkResponse($response);
        }

        return $response;
    }

    //get request without checkResponse
    public function getWithoutCheckResponse(string $uri, Array $query = [], bool $withToken = true)
    {
        $url = $this->getApiUrl();
        return $withToken ? Http::withToken(session('API_TOKEN'))->get("$url/$uri", $query) : Http::get("$url/$uri",$query);
    }

    public function parallelGet(array $requests): array
    {
        $url = $this->getApiUrl();

        // Dispara todas as requisições simultaneamente
        $responses = Http::pool(function (Pool $pool) use ($requests, $url) {
            foreach ($requests as $key => $request) {
                $fullUrl = rtrim($url, '/').'/'.ltrim($request['uri'], '/');

                if (!empty($request['withToken'])) {
                    $pool->as($key)
                        ->withToken(session('API_TOKEN'))
                        ->get($fullUrl, $request['params'] ?? []);
                } else {
                    $pool->as($key)
                        ->get($fullUrl, $request['params'] ?? []);
                }
            }
        });

        // Valida cada Response com o checkResponse
        foreach ($responses as $key => $response) {
            $this->checkResponse($response);
        }

        return $responses;
    }

    public function post(string $uri, Array $data = [], bool $withToken = true)
    {
        $url = $this->getApiUrl();
        $response = $withToken ? Http::withToken(session('API_TOKEN'))->post("$url/$uri", $data) : Http::post("$url/$uri", $data);

        $this->checkResponse($response);

        return $response;
    }

    public function sendMultipartRequest(string $uri, array $data = [], bool $withToken = true): array
    {
        $url = $this->getApiUrl();

        try {
            // Configura a requisição multipart com attach para arquivo
            $request = Http::asMultipart()->accept('application/json');

            // Adiciona o token se necessário
            if ($withToken) {
                $request = $request->withToken(session('API_TOKEN'));
            }

            // Prepara os dados do formulário
            $formData = [];

            // Log para debug
            Log::info('Preparando request multipart', ['data_keys' => array_keys($data)]);

            foreach ($data as $key => $value) {
                if ($value instanceof \Illuminate\Http\UploadedFile) {
                    // Log do arquivo
                    Log::info('Processando arquivo', [
                        'key' => $key,
                        'original_name' => $value->getClientOriginalName(),
                        'mime_type' => $value->getMimeType(),
                        'size' => $value->getSize()
                    ]);

                    // Anexa o arquivo como multipart
                    $formData[] = [
                        'name' => $key,
                        'contents' => fopen($value->getPathname(), 'r'),
                        'filename' => $value->getClientOriginalName(),
                        'headers' => [
                            'Content-Type' => $value->getMimeType()
                        ]
                    ];
                } else {
                    // Para outros tipos de dados
                    $formData[] = [
                        'name' => $key,
                        'contents' => is_array($value) ? json_encode($value) : $value
                    ];
                }
            }

            // Envia a requisição
            $response = $request->post("$url/$uri", $formData);

            // Log da resposta
            Log::info('Resposta da API', [
                'status' => $response->status(),
                'body' => $response->json()
            ]);

            return $this->handleApiResponse($response);

        } catch (\Exception $e) {
            Log::error('Erro ao enviar requisição multipart', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'status' => 500,
                'message' => 'Erro ao processar upload: ' . $e->getMessage()
            ];
        }
    }

    public function sendPostRequest(string $uri, array $data = [], bool $withToken = true): array
    {
        $url = $this->getApiUrl();
        $response = $withToken ? Http::withToken(session('API_TOKEN'))->post("$url/$uri", $data) : Http::post("$url/$uri", $data);

        return $this->handleApiResponse($response);
    }

    public function handleApiResponse(Response $response) : array
    {
        if ($response->successful()) {
            return ['success' => true, 'data' => $response->json()];
        }

        $status = $response->status();
        $message = null;
        if ($status != 200 && $response->json('msg') != null) {
            $message = $response->json('msg');
        } elseif ($status != 200 && $response->json('message') != null) {
            $message = $response->json('message');
        } elseif ($status != 200 && $response->json('error') != null) {
            $message = $response->json('error');
        }

        $errors = $response->json('errors') ?? [];

        return [
            'success' => false,
            'status' => $status,
            'message' => $message,
            'errors' => $errors,
        ];
    }

    protected function handleApiError(array $result)
    {
        if (!$result['success']) {
            switch ($result['status']) {
                case 400:
                    return redirect()->route('home')->with('error', $result['message']);
                case 401:
                    session()->forget('API_TOKEN');
                    session()->forget('AUTH_USER');
                    return redirect()->route('login')->with('error', 'Você precisa estar logado.');
                case 403:
                    return redirect()->back()->with('error', $result['message'])->withInput();
                case 422:
                    return redirect()->back()->withErrors($result['errors'])->withInput();
                default:
                    return redirect()->back()->with('error', $result['message']);
            }
        }

        return null;
    }

    public function put(string $uri, Array $data = [], bool $withToken = true)
    {
        $url = $this->getApiUrl();
        $response = $withToken ? Http::withToken(session('API_TOKEN'))->put("$url/$uri", $data) : Http::put("$url/$uri", $data);

        $this->checkResponse($response);

        return $response;
    }

    public function delete(string $uri, Array $data = [], bool $withToken = true)
    {
        $url = $this->getApiUrl();
        $response = $withToken ? Http::withToken(session('API_TOKEN'))->delete("$url/$uri", $data) : Http::delete("$url/$uri", $data);

        $this->checkResponse($response);

        return $response;
    }
}
