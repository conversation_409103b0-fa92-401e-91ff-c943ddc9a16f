<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class TopicController extends Controller
{
    public function index(Request $request)
    {
        try {
            $page = $this->get('api/topics', ['per_page' => '5', 'page' => ($request->page ?? 1)])->json('topics');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page']);
            $paginator->setPath($request->url());
            return view('topic.index', ['topics' => $paginator, 'base_url' => env('APP_URL') . '/topics']);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function create(Request $request)
    {
        $isDefaultFromUrl = $request->input('default') === 'true';
        return view('topic.create', ['is_default_from_url' => $isDefaultFromUrl]);
    }

    public function store(Request $request)
    {
        try {
            $data = $request->all();
            if(isset($data['default'])) {
                $data['default'] = true;
            } else {
                $data['default'] = false;
            }
            if(isset($data['active']) || !$data['default']) {
                $data['active'] = true;
            } else {
                $data['active'] = false;
            }
            if(isset($data['required']) || !$data['default']) {
                $data['required'] = true;
            } else {
                $data['required'] = false;
            }
            if(isset($data['questions'])) {
                $data['questions'] = json_decode($data['questions'], true);
            }
            if(isset($data['businessesIds'])) {
                $data['businesses'] = $data['businessesIds'];
            }

            unset($data['businessesIds']);
            unset($data['_token']);
            unset($data['_method']);

            $response = $this->post('api/topics', $data); 
            $topicData = $response->json('topic');

            session()->flash('success', 'Categoria criada');
            session()->flash('topic_action_success', true);
            
            if ($topicData && isset($topicData['id']) && isset($topicData['name'])) {
                session()->flash('topic_action_data', ['id' => $topicData['id'], 'name' => $topicData['name']]);
            }
            return redirect()->back();
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function show(Request $request, $id)
    {
        try {
            $response = $this->get("api/topics/$id");
            return view('topic.show', [
                'topic' => $response->json('topic'),
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function edit(Request $request, $id)
    {
        try {
            $redirectBy = $request->input('redirectBy', null);
            $redirectUrl = $request->input('redirectUrl', null);
            $response = $this->get("api/topics/$id");
            return view('topic.edit', [
                'topic' => $response->json('topic'),
                'redirectBy' => $redirectBy,
                'redirectUrl' => $redirectUrl
            ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $data = $request->all();

            if(isset($data['default'])) {
                $data['default'] = true;
            } else {
                $data['default'] = false;
            }
            if(isset($data['active']) || !$data['default']) {
                $data['active'] = true;
            } else {
                $data['active'] = false;
            }
            if(isset($data['required']) || !$data['default']) {
                $data['required'] = true;
            } else {
                $data['required'] = false;
            }
            if(isset($data['questions'])) {
                $data['questions'] = json_decode($data['questions'], true);
            }
            if(isset($data['businessesIds'])) {
                $data['businesses'] = $data['businessesIds'];
            }

            unset($data['businessesIds']);
            unset($data['_token']);
            unset($data['_method']);

            $response = $this->put("api/topics/$id/update", $data); 
            $topicData = $response->json('topic');

            session()->flash('topic_action_success', true);
             // Armazena os dados do tópico na sessão se disponíveis
            if ($topicData && isset($topicData['id']) && isset($topicData['name'])) {
                session()->flash('topic_action_data', ['id' => $topicData['id'], 'name' => $topicData['name']]);
            }
            return redirect()->back()->with('success', 'Categoria atualizada');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
    public function editDefault(Request $request, $id)
    {
        try {
            $redirectBy = $request->input('redirectBy', null);
            $redirectUrl = $request->input('redirectUrl', null);
            $response = $this->get("api/topics/$id/with_questions_business");
            return view('topic.edit_default_for_business', ['topic' => $response->json('topic'), 'redirectBy' => $redirectBy, 'redirectUrl' => $redirectUrl]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function updateDefault(Request $request, $id){
        try {
            $data = $request->all();

            if(isset($data['questions'])) {
                $data['questions'] = json_decode($data['questions'], true);
            }
            if(isset($data['questions_business'])) {
                $data['questions_business'] = json_decode($data['questions_business'], true);
            }

            unset($data['_token']);
            unset($data['_method']);

            $response = $this->put("api/topics/$id/update/default", $data);
            $topicData = $response->json('topic');

            session()->flash('topic_action_success', true);

            if ($topicData && isset($topicData['id']) && isset($topicData['name'])) {
                session()->flash('topic_action_data', ['id' => $topicData['id'], 'name' => $topicData['name']]);
            }
            return redirect()->back()->with('success', 'Categoria Padrão atualizada');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function updateDefaultForBusiness(Request $request, $id)
    {
        try {
            $data = $request->all();

            if(isset($data['questions'])) {
                $data['questions'] = json_decode($data['questions'], true);
            }

            unset($data['_token']);
            unset($data['_method']);

            $response = $this->put("api/topics/$id/update/default_for_business", $data);
            $topicData = $response->json('topic');

            session()->flash('topic_action_success', true);
            
            if ($topicData && isset($topicData['id']) && isset($topicData['name'])) {
                session()->flash('topic_action_data', ['id' => $topicData['id'], 'name' => $topicData['name']]);
            }
            return redirect()->back()->with('success', 'Categoria atualizada');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }

    }


    public function destroy(Request $request, $id) // Adicionado Request $request
    {
        try {
            $response = $this->delete("api/topics/$id/delete"); // Captura a resposta da API

            // Verifica se a requisição é AJAX
            if ($request->ajax() || $request->wantsJson()) {
                // Verifica se a API retornou sucesso (ajuste conforme a resposta real da sua API)
                if ($response->successful()) {
                     return response()->json(['success' => true, 'message' => 'Categoria deletada com sucesso.']);
                } else {
                    // Tenta extrair a mensagem de erro da API
                    $errorMessage = $response->json('message') ?? 'Erro ao deletar categoria na API.';
                    return response()->json(['success' => false, 'message' => $errorMessage], $response->status());
                }
            }

            // Comportamento padrão para requisições não-AJAX
            session()->flash('success', 'Categoria deletada');
            return redirect()->route('topics.index');

        } catch (HttpResponseException $e) {
            if ($request->ajax() || $request->wantsJson()) {
                // Tenta retornar o erro da HttpResponseException como JSON
                 return response()->json(['success' => false, 'message' => $e->getMessage() ?: 'Erro na comunicação com a API.'], $e->getResponse()->getStatusCode());
            }
            return $e->getResponse(); // Comportamento padrão
        } catch (\Exception $e) {
             if ($request->ajax() || $request->wantsJson()) {
                 return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
             }
            return back()->with('error', $e->getMessage()); // Comportamento padrão
        }
    }

    public function search(Request $request)
    {
        try {
            $page = $this->get('api/topics', [
                'per_page' => '5',
                'page' => ($request->page ?? 1),
                'search' => $request->search,
                'filter_default' => $request->filter_default,
            ])->json('topics');

            $paginator = new LengthAwarePaginator(
                $page['data'],
                $page['total'],
                $page['per_page'],
                $page['current_page'],
                ['path' => $request->url(),
                    'query' => [
                        'search' => $request->search,
                        'filter_default' => $request->filter_default,
                    ]
                ]
            );
            $paginator->appends('search', $request->search);
            $paginator->appends('filter_default', $request->filter_default);

            $data = [
                'topics' => $paginator,
                'search' => $request->search,
                'filter_default' => $request->filter_default,
            ];

            return view('topic.index', $data);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }

    }
}
