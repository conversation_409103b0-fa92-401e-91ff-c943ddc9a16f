<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Services\PlanService;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    public function __construct(private PlanService $plans)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $plans = $this->plans->index($request->input('page', 1), $request->input('per_page', 5), $request->search, $request->url());

        return view('plans.index', ['plans' => $plans, 'queryParams' => $request->except('_token'), ...$request->all()]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('plans.form', ['plan' => null, 'id_form' => 'form-add-plan']);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $authorizedFunctions = [
            'evaluations' => $request->evaluations ?? false,
            'parameters' => $request->parameters ?? false,
            'employees' => $request->employees ?? false,
            'manage_business_profile' => $request->manage_business_profile ?? false,
            'dashboard' => $request->dashboard ?? false,
            'topics' => $request->topics ?? false,
            'update_topics' => $request->update_topics ?? false,
            'forms' => $request->forms ?? false,
            'update_forms' => $request->update_forms ?? false,
            'cashback' => $request->cashback ?? false,
            'update_cashback' => $request->update_cashback ?? false,
            'clients' => $request->clients ?? false,
            'update_clients' => $request->update_clients ?? false,
            'notifications' => $request->notifications ?? false,
            'update_notifications' => $request->update_notifications ?? false,
            'site_notifications' => $request->site_notifications ?? false,
            'update_site_notifications' => $request->update_site_notifications ?? false,
        ];

        $this->plans->store(
            $request->name,
            $request->description,
            $request->value,
            $request->active,
            $request->publicly_visible,
            $request->payment_system_tag,
            $authorizedFunctions,
            $request->total_employees ?? 0,
            $request->email_sending ?? false,
            $request->whatsapp_sending ?? false,
            $request->total_email_sends ?? 0,
            $request->email_sends_type ?? 'monthly',
            $request->email_sends_days,
            $request->total_whatsapp_sends ?? 0,
            $request->whatsapp_sends_type ?? 'monthly',
            $request->whatsapp_sends_days
        );
        return redirect()->route('plans.index')->with('success', 'Plano Adicionado!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $plan = $this->plans->getById($id);
        return view('plans.seller_show', ['plan' => $plan]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $plan = $this->plans->getById($id);
        return view('plans.form', ['plan' => $plan, 'id_form' => 'form-edit-plan']);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $authorizedFunctions = [
            'evaluations' => $request->evaluations ?? false,
            'parameters' => $request->parameters ?? false,
            'employees' => $request->employees ?? false,
            'manage_business_profile' => $request->manage_business_profile ?? false,
            'dashboard' => $request->dashboard ?? false,
            'topics' => $request->topics ?? false,
            'update_topics' => $request->update_topics ?? false,
            'forms' => $request->forms ?? false,
            'update_forms' => $request->update_forms ?? false,
            'cashback' => $request->cashback ?? false,
            'update_cashback' => $request->update_cashback ?? false,
            'clients' => $request->clients ?? false,
            'update_clients' => $request->update_clients ?? false,
            'notifications' => $request->notifications ?? false,
            'update_notifications' => $request->update_notifications ?? false,
            'site_notifications' => $request->site_notifications ?? false,
            'update_site_notifications' => $request->update_site_notifications ?? false,
        ];

        $this->plans->update(
            $id,
            $request->name,
            $request->description,
            $request->value,
            $request->active,
            $request->publicly_visible,
            $request->payment_system_tag,
            $authorizedFunctions,
            $request->total_employees ?? 0,
            $request->email_sending ?? false,
            $request->whatsapp_sending ?? false,
            $request->total_email_sends ?? 0,
            $request->email_sends_type ?? 'monthly',
            $request->email_sends_days,
            $request->total_whatsapp_sends ?? 0,
            $request->whatsapp_sends_type ?? 'monthly',
            $request->whatsapp_sends_days
        );

        ApiUser::update();
        return redirect()->route('plans.index')->with('success', 'Plano Atualizado!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->plans->destroy($id);
        return redirect()->route('plans.index');
    }
}
