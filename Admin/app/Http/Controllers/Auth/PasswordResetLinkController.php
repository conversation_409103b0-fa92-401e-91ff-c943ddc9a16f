<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;

use App\Http\Requests\Auth\ForgotPassword;
use App\Models\User;
use App\Helpers\SystemHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('auth.forgot-password');
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(ForgotPassword $request)
    {
        $status = $this->sendResetLinkEmail($request);

        return $status == Password::RESET_LINK_SENT
            ? back()->with(['status' => __($status), 'reset_email' => $request->email])
            : back()->withInput($request->only('email'))
                ->withErrors(['email' => __($status)]);
    }

    protected function sendResetLinkEmail(Request $request)
    {
        $user = User::where('email', $request->email)->where('deleted_at', null)->whereNotNull('cpf')->first();

        if (!$user) {
            return Password::INVALID_USER;
        }

        $response = Password::sendResetLink(
            $request->only('email')
        );

        return $response;
    }
}
