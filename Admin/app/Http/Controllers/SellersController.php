<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class SellersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $page = $this->get('api/sellers', [
                'per_page' => '5',
                'page' => $request->input('page', 1),
                'search' => $request->input('search', ''),
                'seller_type' => $request->input('seller_type', 'all'),
            ])->json('sellers');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page']);
            $paginator->setPath($request->url());
            $paginator->appends([
                'search' => $request->input('search', ''),
                'seller_type' => $request->input('seller_type', 'all')
            ]);

            return view('sellers.index', [
                'sellers' => $paginator,
                'base_url' => env('APP_URL').'/sellers',
                'search' => $request->input('search', ''),
                'seller_type' => $request->input('seller_type', 'all')
                ]);
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function indexSeller(Request $request)
    {
        try {
            if(ApiUser::getLoginType() != 'seller') {
              return redirect()->route('home')->with('error', 'Para acessar essa página, você precisa ser um vendedor.');
            }
            $page = $this->get('api/sellersForSeller/getAllSellers', [
                'per_page' => '5',
                'page' => $request->input('page', 1),
                'search' => $request->input('search', ''),
            ])->json('sellers');
            $paginator = new LengthAwarePaginator($page['data'], $page['total'], $page['per_page'], $page['current_page']);
            $paginator->setPath($request->url());

            return view('sellers.index', [
                'sellers' => $paginator,
                'base_url' => env('APP_URL').'/sellers',
                'search' => $request->input('search', ''),
                'fromSeller' => true
                ]);
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $cpf = $request->cpf;
        try {
            $response = $this->get("api/user/show/by-cpf", ['cpf' => $cpf], true, false);
            if ($response->status() == 404) {
                return view('sellers.create', ['cpf' => $cpf]);
            }
            $user = $response->json('user');
            if ($response->json('seller')) {
                return back()->with('error', 'Vendedor com cpf ' . $cpf . ' já cadastrado');
            }
            return view('sellers.create', ['user' => $user, 'cpf' => $cpf]);
        } catch (\Exception $e) {
            return view('sellers.create', ['cpf' => $cpf]);
        }
    }

    public function createForSeller(Request $request)
    {
        $cpf = $request->cpf;
        try {
            $response = $this->get("api/user/show/by-cpf", ['cpf' => $cpf], true, false);
            if ($response->status() == 404) {
                return view('sellers.create', ['cpf' => $cpf, 'fromSeller' => true]);
            }
            $user = $response->json('user');
            if ($response->json('seller')) {
                return back()->with('error', 'Vendedor com cpf ' . $cpf . ' já cadastrado');
            }
            return view('sellers.create', ['user' => $user, 'cpf' => $cpf, 'fromSeller' => true]);
        } catch (\Exception $e) {
            return view('sellers.create', ['cpf' => $cpf, 'fromSeller' => true]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->post('api/sellers', $data);
            session()->flash('success', 'Vendedor adicionado!');
            return redirect()->route('sellers.index');
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }   
    
    public function storeForSeller(Request $request)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->post('api/sellersForSeller/store', $data);
            session()->flash('success', 'Vendedor adicionado!');
            return redirect()->route('sellers.seller.index');
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id, $redirectTo = null)
    {
        $seller = $this->get("api/sellers/$id", )->json();

        return view('sellers.edit', ['seller' => $seller, 'redirectTo' => $redirectTo]);
    }

    public function editForSeller(string $id)
    {
        $seller = $this->get("api/sellersForSeller/get/$id", )->json();

        return view('sellers.edit', ['seller' => $seller, 'fromSeller' => true]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->put("api/sellers/$id", $data);

            return redirect()->back()->with('success', 'Vendedor atualizado');
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function updateForSeller(Request $request, string $id)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->put("api/sellersForSeller/update/$id", $data);

            return redirect()->back()->with('success', 'Vendedor atualizado');
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $this->delete("api/sellers/$id");
            session()->flash('success', 'Vendedor removido!');
            return redirect()->route('sellers.index');
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function destroyForSeller(string $id)
    {
        try {
            $this->delete("api/sellersForSeller/delete/$id");
            session()->flash('success', 'Vendedor removido!');
            return redirect()->route('sellers.seller.index');
        } catch(HttpResponseException $e) {
            return $e->getResponse();
        } catch(\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
}
