<?php

namespace App\Http\Controllers;

use App\Helpers\SystemHelper;
use App\Http\Controllers\Traits\ApiTrait;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class NotificationController extends Controller
{
    use ApiTrait;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $page = $this->get('api/notifications', [
            'per_page' => $request->input('per_page', 5),
            'page' => $request->input('page', 1),
            'search' => $request->search,
            'sending_type' => $request->input('sending_type', 'all'),
            'status' => $request->input('status', 'all'),
            'period' => $request->input('period', 'ALL'),
            'startDate' => $request->input('startDate', null),
            'endDate' => $request->input('endDate', null),
        ])->json('notifications');
        $paginator = new LengthAwarePaginator(
            $page['data'],
            $page['total'],
            $page['per_page'],
            $page['current_page'],
        );
        $paginator->setPath($request->url());
        return view('notifications.index', [
            'notifications' => $paginator,
            'queryParams' => $request->except('_token'),
            ...$request->all()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('notifications.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $request->all();
            $this->post("api/notifications", $data);
            session()->flash('success', 'Campanha adicionada!');
            return redirect()->route('notifications.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $response = $this->get("api/notifications/$id");
            return view('notifications.show', ['notification' => $response->json('notification')]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function indexClient(Request $request)
    {
        $page = $this->get('api/notificationClient', [
            'per_page' => $request->input('per_page', 5),
            'page' => $request->input('page', 1),
            'search' => $request->search,
            'sending_type' => $request->input('sending_type', 'all'),
            'status' => $request->input('status', 'all'),
            'period' => $request->input('period', 'ALL'),
            'startDate' => $request->input('startDate', null),
            'endDate' => $request->input('endDate', null),
        ])->json('notifications');
        $paginator = new LengthAwarePaginator(
            $page['data'],
            $page['total'],
            $page['per_page'],
            $page['current_page'],
        );
        $paginator->setPath($request->url());
        return view('notifications.index_client', [
            'notifications' => $paginator,
            'queryParams' => $request->except('_token'),
            ...$request->all()
        ]);
    }

    public function showNotificationClient(string $id)
    {
        try {
            $response = $this->get("api/notificationClient/$id");
            $notification = $response->json('notification');

            $systemLogo = SystemHelper::get()['MAIN_LOGO'] ?? '/icon/logo_visao_negocio.svg';
            $mainColor = SystemHelper::get()['MAIN_COLOR'];

            $businessName = $notification['notification']['business']['name'] ?? 'Visão negócio';
            $businessLogo = $notification['notification']['business']['logo'] ?? null;
            $notificationTitle = $notification['notification']['title'];
            $notificationDescription = $notification['notification']['description'];
            $urlClick = $notification['notification']['url_click'];

            return view('notifications.show_client', [
                                                                    'systemLogo' => $systemLogo,
                                                                    'mainColor' => $mainColor,
                                                                    'businessName' => $businessName,
                                                                    'businessLogo' => $businessLogo,
                                                                    'notificationTitle' => $notificationTitle,
                                                                    'notificationDescription' => $notificationDescription,
                                                                    'urlClick' => $urlClick
                                                                ]);
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function cancel(string $id)
    {
        try {
            $this->post("api/notifications/$id/cancel");
            session()->flash('success', 'Campanha cancelada!');
            return redirect()->route('notifications.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
}
