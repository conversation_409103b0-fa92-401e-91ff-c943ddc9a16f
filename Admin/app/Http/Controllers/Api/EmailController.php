<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ApiUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ConfirmEmployeeEmail;
use App\Http\Requests\ConfirmEmailRequest;
use App\Mail\ConfirmEmployeeEmail as MailConfirmEmployeeEmail;
use App\Mail\ConfirmClientEmail as MailConfirmClientEmail;
use App\Mail\ConfirmSellerEmail;
use App\Mail\MailConfirmEmail;
use Illuminate\Support\Facades\Mail;

class EmailController extends Controller
{
    public function sendConfirmClientEmail(ConfirmEmployeeEmail $request)
    {
        try {
            $code = random_int(100000, 999999);
            Mail::to($request->email)->send(mailable: new MailConfirmClientEmail($code, $request->name));
            return response()->json(['msg' => 'Email enviado!', 'code' => $code]);
        } catch(\Exception $e) {
            return response()->json(['msg' => $e->getMessage()], 400);
        }
    }

    public function sendConfirmEmployeeEmail(ConfirmEmployeeEmail $request)
    {
        try {
            $code = random_int(100000, 999999);
            Mail::to($request->email)->send(new MailConfirmEmployeeEmail($code, $request->name));

            return response()->json(['msg' => 'Email enviado!', 'code' => $code]);
        } catch(\Exception $e) {
            return response()->json(['msg' => $e->getMessage()], 400);
        }
    }

    public function sendConfirmSellerEmail(ConfirmEmployeeEmail $request)
    {
        try {
            $code = random_int(100000, 999999);
            Mail::to($request->email)->send(new ConfirmSellerEmail($code, $request->name));

            return response()->json(['msg' => 'Email enviado!', 'code' => $code]);
        } catch(\Exception $e) {
            return response()->json(['msg' => $e->getMessage()], 400);
        }
    }

    public function sendConfirmEmail(ConfirmEmailRequest $request)
{
    try {
        $code = random_int(100000, 999999);
        Mail::to($request->email)->send(new MailConfirmEmail($code, $request->name));
        return response()->json(['msg' => 'Email enviado!', 'code' => $code]);
    } catch (\Exception $e) {
        return response()->json(['msg' => $e->getMessage(), 'exception' => get_class($e)], 400);
    }
}
}
