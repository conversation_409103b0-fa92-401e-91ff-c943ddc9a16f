<?php

namespace App\Http\Controllers;

use App\Helpers\ApiUser;
use App\Helpers\SystemHelper;
use App\Http\Controllers\Traits\ApiTrait;
use App\Services\ClientService;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class ClientController extends Controller
{
    use ApiTrait;

    public function __construct()
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $adminBusinessesId = (int) SystemHelper::get()['ADMIN_BUSINESS_ID'];
        $isAdminBusinessSelected = isset(ApiUser::get()['business_selected']) && ApiUser::get()['business_selected']['id'] === $adminBusinessesId;
        $search = $request->input('search', '');
        $profile = $request->input('profile', '');
        
        $page = $isAdminBusinessSelected ? 
            $this->get('api/users', [
                'per_page' => $request->input('per_page', 5),
                'page' => $request->input('page', 1),
                'search' => $search,
                'profile' => $profile,
            ])->json('users') : $this->get('api/clients', [
                'per_page' => $request->input('per_page', 5),
                'page' => $request->input('page', 1),
                'search' => $search,
            ])->json('clients');
        

        $paginator = new LengthAwarePaginator(
            $page['data'],
            $page['total'],
            $page['per_page'],
            $page['current_page']
        );
        
        $paginator->appends('search', $search);
        $paginator->appends('profile', $profile);
        $paginator->setPath($request->url());
        return view('clients.index', ['clients' => $paginator, 'search' => $search, 'profile' => $profile]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $cpf = $request->cpf;
        try {
            $response = $this->get("api/user/show/by-cpf", ['cpf' => $cpf], true, false);
            if ($response->status() == 404) {
                return view('clients.create', ['cpf' => $cpf]);
            }
            $user = $response->json('user');
            if ($response->json('client')) {
                return back()->with('error', 'Cliente com cpf ' . $cpf . ' já cadastrado');
            }
            return view('clients.create', ['user' => $user, 'cpf' => $cpf]);

        } catch (\Exception $e) {
            return view('clients.create', ['cpf' => $cpf]);
        }
    }

    public function publicRegisterClient(Request $request)
    {
        $cpf = $request->cpf;
        if (empty($cpf)) {
            return view('clients.public_register', ['cpf' => null]);
        }
        try {
            $response = $this->get("api/user/show/by-cpf", ['cpf' => $cpf], true, false);
            if ($response->status() == 404) {
                return view('clients.public_register', ['cpf' => $cpf]);
            }
            $user = $response->json('user');
            if ($response->json('client') || $response->json('seller') || $response->json('employee')) {
                return redirect()->route('clients.publicRegister')
                    ->with('danger', 'CPF já cadastrado no sistema. Para acessar sua conta, faça login.');
            }

            return view('clients.public_register', ['user' => $user, 'cpf' => $cpf]);

        } catch (\Exception $e) {
            return view('clients.public_register', ['cpf' => $cpf]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->post("api/clients", $data);
            session()->flash('success', 'Cliente adicionado!');
            return redirect()->route('clients.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function publicStore(Request $request)
    {
        try {
            $data = $this->removeMasks($request->all());
            // Chama a API e obtém a resposta completa
            $response = $this->post("api/clients/publicStore", $data);
            // Decodifica o JSON da resposta
            $result = $response->json();

            // Extrai a flag 'isCreateNewClientAndUser' da resposta da API
            // Define um valor padrão (false) caso a flag não esteja presente
            $isCreateNewClientAndUser = $result['isCreateNewClientAndUser'] ?? false;

            return redirect()->route('clients.publicRegisterSuccess', [
                'isCreateNewClientAndUser' => $isCreateNewClientAndUser
            ]);

        } catch (HttpResponseException $e) {
            if ($e->getResponse()->getStatusCode() == 422) {
                $errors = json_decode($e->getResponse()->getContent(), true);
                $errorMessage = $errors['msg'] ?? 'Ocorreu um erro durante o cadastro.';

                if (isset($errors['errors'])) {
                    $fieldErrors = implode(' ', array_map(function ($fieldErrors) {
                        return implode(' ', $fieldErrors);
                    }, $errors['errors']));
                    $errorMessage .= ' Detalhes: ' . $fieldErrors;
                }
                return back()->with('error', $errorMessage)->withInput();
            }
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage())->withInput();
        }
    }

    public function publicRegisterSuccess(Request $request)
    {
        $isCreateNewClientAndUser = $request->input('isCreateNewClientAndUser', false);
        return view('clients.public_register_success', [
            'isCreateNewClientAndUser' => $isCreateNewClientAndUser
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $response = $this->get("api/clients/$id");
            $client = $response->json('client');
            return view('clients.show', ['client' => $client]);
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id, $redirectTo = null)
    {
        $response = $this->get("api/clients/$id");
        $client = $response->json('client');
        return view('clients.edit', ['client' => $client, 'redirectTo' => $redirectTo]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->put("api/clients/$id", $data);
            return redirect()->back()->with('success', 'Cliente atualizado!');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $this->delete("api/clients/$id");
            session()->flash('success', 'Cliente removido');
            return redirect()->route('clients.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function adminEdit(Request $request)
    {
        try {
            $profile = $request->input('client_type');

            $handlers = [
                'client' => function() use ($request) {
                    $clientId = $request->input('client_id');
                    return $this->edit($clientId, 'clients.index');
                },
                'seller' => function() use ($request) {
                    $sellerId = $request->input('seller_id');
                    return app(SellersController::class)->edit($sellerId, 'clients.index');
                },
                'employee' => function() use ($request) {
                    $employeeId = $request->input('employee_id');
                    return app(EmployeeController::class)->edit($employeeId, 'clients.index');
                },
                'admin' => function() use ($request) {
                    $employeeId = $request->input('employee_id');
                    return app(EmployeeController::class)->edit($employeeId, 'clients.index');
                },
            ];

            if (!isset($handlers[$profile])) {
                return back()->with('error', 'Tipo de perfil inválido.');
            }

            return $handlers[$profile]();
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

     public function adminDestroy(Request $request)
    {
        try {
            $profile = $request->input('client_type');

            $handlers = [
                'client' => function() use ($request) {
                    $id = $request->input('client_id');
                    $this->destroy($id);
                },
                'seller' => function() use ($request) {
                    $id = $request->input('seller_id');
                    $this->delete("api/sellers/{$id}");
                },
                'employee' => function() use ($request) {
                    $id = $request->input('employee_id');
                    $this->delete("api/employees/{$id}");
                },
                'admin' => function() use ($request) {
                    $id = $request->input('employee_id');
                    $this->delete("api/employees/{$id}");
                },
            ];

            if (!isset($handlers[$profile])) {
                return back()->with('error', 'Tipo de perfil inválido.');
            }

            $handlers[$profile]();

            session()->flash('success', 'Cliente removido com sucesso.');
            return redirect()->route('clients.index');

        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Display the client import jobs listing page.
     */
    public function import()
    {
        if (!ApiUser::isBusinessSelected()) {
            return redirect()->route('home')->with('error', 'Selecione um negócio para continuar');
        }
        $file_name = request('file_name');
        $start_date = request('start_date');
        $end_date = request('end_date');
        $params = [
            'per_page' => request('per_page', 10),
            'page' => request('page', 1),
            'file_name' => $file_name,
            'start_date' => $start_date,
            'end_date' => $end_date,
        ];

        $response = $this->get('api/client-imports', array_filter($params));

        $data = $response->json();
        $paginator = new LengthAwarePaginator(
            $data['data'],
            $data['total'],
            $data['per_page'],
            $data['current_page']
        );
        $paginator->appends('file_name', $file_name);
        $paginator->appends('start_date', $start_date);
        $paginator->appends('end_date', $end_date);
        $paginator->setPath(request()->url());

        return view('clients.import_jobs', ['batches' => $paginator]);
    }

    /**
     * Display the client import jobs listing page.
     */
    public function importIncomplete()
    {
        if(!ApiUser::isBusinessSelected()){
            return redirect()->route('home')->with('error', 'Selecione um negócio para continuar');
        }
        $file_name = request('file_name');
        $start_date = request('start_date');
        $end_date = request('end_date');
        $params = [
            'per_page' => request('per_page', 10),
            'page' => request('page', 1),
            'file_name' => $file_name,
            'start_date' => $start_date,
            'end_date' => $end_date,
        ];

        $response = $this->get('api/client-imports', array_filter($params));

        $data = $response->json();
        $paginator = new LengthAwarePaginator(
            $data['data'],
            $data['total'],
            $data['per_page'],
            $data['current_page']
        );
        $paginator->appends('file_name', $file_name);
        $paginator->appends('start_date', $start_date);
        $paginator->appends('end_date', $end_date);
        $paginator->setPath(request()->url());

        return view('clients.import_jobs_incomplete', ['batches' => $paginator]);
    }

    /**
     * Show the form for creating a new client import.
     */
    public function importCreate()
    {
        return view('clients.import');
    }

    
    /**
     * Show the form for creating a new client import (INCOMPLETE CREATE).
     */
    public function importCreateIncomplete()
    {
        return view('clients.import_incomplete');
    }

    /**
     * Store a new client import job.
     */
    public function importStore(Request $request)
    {
        try {
            // Envia o post para a API
            $response = $this->post('api/client-imports', [
                'file' => $request->file,
                'file_name' => $request->file_name,
                'file_type' => $request->file_type,
                'mapping' => json_decode($request->mapping, true),
                'has_headers' => $request->boolean('has_headers'),
                'separator' => $request->separator
            ]);

            return redirect()
                ->route('clients.import')
                ->with('success', 'Importação iniciada com sucesso!');
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

        /**
     * Store a new client import job.
     */
    public function importIncompleteStore(Request $request)
    {
        try {
            // Envia o post para a API
            $response = $this->post('api/client-imports/incomplete', [
                'file' => $request->file,
                'file_name' => $request->file_name,
                'file_type' => $request->file_type,
                'mapping' => json_decode($request->mapping, true),
                'has_headers' => $request->boolean('has_headers'),
                'separator' => $request->separator
            ]);

            return redirect()
                ->route('clients.import')
                ->with('success', 'Importação iniciada com sucesso!');
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Mostrar detalhes de um batch de importação específico
     */
    public function importShow($id)
    {
        try {
            $response = $this->get("api/client-imports/{$id}");
            return response()->json($response->json());
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function downloadImportFile($id)
    {
        try {
            $response = $this->get("api/client-imports/{$id}/download-url");
            return response()->json($response->json());
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function storeIncomplete(Request $request)
    {
        try {
            $data = $this->removeMasks($request->all());
            $this->post("api/clients/incomplete", $data);
            session()->flash('success', 'Cliente adicionado!');
            return redirect()->route('clients.index');
        } catch (HttpResponseException $e) {
            return $e->getResponse();
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }
}
