<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PhoneNumber implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $numericPhone = preg_replace('/\D/', '', $value);

        if (strlen($numericPhone) !== 10 && strlen($numericPhone) !== 11) {
            $fail('O campo :attribute deve ser um número de telefone válido.');
        }
    }
}
