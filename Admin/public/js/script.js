async function getMapLocation(lat, lng, location_name, map_id) {
    if (lat != null && lng != null) {
        let map;

        const { Map } = await google.maps.importLibrary("maps");
        const { AdvancedMarkerElement, PinElement } =
            await google.maps.importLibrary("marker");

        map = new Map(document.getElementById(map_id), {
            center: { lat: lat, lng: lng },
            zoom: 15,
            mapId: map_id,
        });

        const marker = new google.maps.marker.AdvancedMarkerElement({
            map,
            position: { lat: lat, lng: lng },
            title: location_name,
        });
    }
}

function checkPhoneMask(element, key = "", counter) {
    if (element.value.length == 0) {
        counter.value = 1;
    }
    if ($.isNumeric(key) && counter.value < 11) {
        counter.value++;
    } else if (key == "Backspace") {
        counter.value--;
    }

    if (element.value.length == 14) {
        $("#" + event.target.id).mask("(00) 0000-0000");
    }
    if (counter.value > 10 && element.value.length < 15) {
        element.value = element.value + key;
        $("#" + event.target.id).mask("(00) 90000-0000");
    }
    if (element.value.length == 14) {
        counter.value = 10;
    }
}

function getMask(elementId) {
    let element = document.getElementById(elementId);
    if (element.value.length === 10) {
        return "(00) 0000-0000";
    } else {
        return "(00) 90000-0000";
    }
}

function validateUpdate(formId, oldEmail) {
    const form = document.getElementById(formId);
    const isValid = form.reportValidity();
    const newEmail = document.getElementById("email").value;

    if (isValid) {
        $("#loading").modal("show");

        if (oldEmail == newEmail) {
            form.submit();
        } else {
            confirmEmail(formId);
        }
    }
}

const errorsToRemove = [];

function cleanErrors() {
    for (var error of errorsToRemove) {
        document.getElementById(error).innerHTML = "";
    }
}

function fillErrors(errors) {
    Object.keys(errors).forEach(function (error) {
        const errorId = `error_${error}`;
        const errorLabel = document.getElementById(errorId);
        errorsToRemove.push(errorId);

        if (errorLabel) {
            errorLabel.innerHTML = errors[error][0];
        }
    });
}

function confirmEmail(formId, form = null) {
    const email = document.getElementById("email").value;
    const name = document.getElementById("name").value;

    if (form == null) {
        var form = document.getElementById(formId);
    }

    if (form.reportValidity() && email) {
        const formData = new FormData();
        formData.append("email", email);
        formData.append("name", name);

        $("#loading").modal("show");

        $.ajax({
            url: "/api/send/email/confirm-email",
            type: "post",
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (response) {
                const code = response.code;
                const modal = document.getElementById("modal-confirm-email");
                modal.dataset.code = code;
                modal.dataset.formId = formId;
                modal.dataset.email = email;
                modal.dataset.name = name;

                $("#loading").modal("hide");
                $("#modal-confirm-email").modal("toggle");
            },
            error: function (response) {
                $("#loading").modal("hide");
                alert("Falha ao enviar email de confirmação!");
            },
        });
    }
}

let errorsIdToRemove = [];
let removeIsInvalidClassId = [];

function removeErrorMessages() {
    for (let errorId of errorsIdToRemove) {
        document.getElementById(errorId).innerHTML = "";
    }

    errorsIdToRemove = [];
}

function removeIsInvalidClass() {
    for (var elementId of removeIsInvalidClassId) {
        document.getElementById(elementId).classList.remove("is-invalid");
    }

    removeIsInvalidClassId = [];
}

function renderPagination(
    containerId,
    current,
    last,
    onPageChange,
    startCount = 2,
    endCount = 2
) {
    const pagination = $(`#${containerId}`);

    // só renderiza os botões de anterior e próximo se a quantidade de páginas for maior que a exibida inicialmente
    const mustRenderButtons = last - (startCount + endCount) >= 1;

    pagination.empty();

    if (mustRenderButtons) {
        // botão pra voltar
        pagination.append(`
            <li class="page-item ${current === 1 ? "disabled" : ""}">
                <a class="page-link" href="#" data-page="${current - 1}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `);
    }

    const pageSet = new Set();

    // páginas iniciais fixas (ex: páginas 1 e 2)
    for (let i = 1; i <= startCount && i <= last; i++) {
        pageSet.add(i);
    }

    // páginas finais fixas (ex: últimas 2 páginas)
    for (let i = last - endCount + 1; i <= last; i++) {
        if (i >= 1) {
            pageSet.add(i);
        }
    }

    // exibir apenas a página ativa quando estiver no "meio"
    if (current > startCount && current < last - endCount + 1) {
        pageSet.add(current);
    }

    const sortedPages = [...pageSet].sort((a, b) => a - b);

    // aplica as reticências
    let lastRendered = 0;
    sortedPages.forEach((page) => {
        if (page - lastRendered > 1) {
            pagination.append(
                `<li class="page-item disabled"><span class="page-link">...</span></li>`
            );
        }

        pagination.append(`
            <li class="page-item ${page === current ? "active" : ""}">
                <a class="page-link" href="#" data-page="${page}">${page}</a>
            </li>
        `);

        lastRendered = page;
    });

    if (mustRenderButtons) {
        // botão pra página seguinte
        pagination.append(`
            <li class="page-item ${current === last ? "disabled" : ""}">
                <a class="page-link" href="#" data-page="${current + 1}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `);
    }

    // handle de evento de clique
    pagination.find("a.page-link").on("click", function (e) {
        e.preventDefault();
        const page = $(this).data("page");
        if (page && page !== current && page >= 1 && page <= last) {
            onPageChange(page);
        }
    });
}
