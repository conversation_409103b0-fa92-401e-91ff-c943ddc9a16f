function cnpjIsValid(cnpj) {
    cnpj = cnpj.replace(/[^\d]+/g, "");

    if (cnpj.length !== 14) {
        return false;
    }

    if (/^(\d)\1{13}$/.test(cnpj)) {
        return false;
    }

    let sum = 0;
    let multiplier = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    for (let i = 0; i < 12; i++) {
        sum += parseInt(cnpj.charAt(i)) * multiplier[i];
    }

    let digit1 = 11 - (sum % 11);
    if (digit1 === 10 || digit1 === 11) {
        digit1 = 0;
    }

    if (parseInt(cnpj.charAt(12)) !== digit1) {
        return false;
    }

    sum = 0;
    multiplier = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    for (let i = 0; i < 13; i++) {
        sum += parseInt(cnpj.charAt(i)) * multiplier[i];
    }

    let digit2 = 11 - (sum % 11);
    if (digit2 === 10 || digit2 === 11) {
        digit2 = 0;
    }

    if (parseInt(cnpj.charAt(13)) !== digit2) {
        return false;
    }

    return true;
}
