// Função para calcular e enviar a altura do conteúdo
function sendHeight() {
    const bodyHeight = document.body.scrollHeight;
    const height = bodyHeight; // Usar apenas body.scrollHeight
    window.parent.postMessage({
        type: 'frameHeight',
        height: height
    }, '*');
}

// Responde à solicitação de altura do pai
window.addEventListener('message', function(e) {
    if (e.data && e.data.type === 'requestHeight') {
        sendHeight();
    }
});

// Envia altura inicial e configura observer após o carregamento
document.addEventListener('DOMContentLoaded', function() {
    const observer = new MutationObserver(() => {
        sendHeight();
    });
    observer.observe(document.body, {
        attributes: true,
        childList: true,
        subtree: true
    });
    
    sendHeight();
});

// Envia altura quando imagens ou outros recursos carregarem
window.addEventListener('load', function() {
    // console.log('[<PERSON>rame] Window load event fired.'); // Log removido
    const images = document.getElementsByTagName('img');
    for (let img of images) {
        if (!img.complete) {
            img.addEventListener('load', () => {
                sendHeight();
            });
        }
    }
    sendHeight();
});

// Envia altura quando o tamanho da janela mudar
window.addEventListener('resize', () => {
    sendHeight();
});
