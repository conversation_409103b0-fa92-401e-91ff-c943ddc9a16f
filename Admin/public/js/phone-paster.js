/* Monitora o evento de paste em todo o DOM, verifica se o evento de paste foi em um
 * input de classe .phone-input pra fazer as modificações
 */
document.addEventListener("DOMContentLoaded", function () {
    document.addEventListener("paste", function (e) {
        const target = e.target;

        if (target.matches(".phone-input")) {
            e.preventDefault();

            const pastedText = (
                e.clipboardData || window.clipboardData
            ).getData("text");
            const cleanedPasted = cleanPhoneNumber(pastedText);
            const currentValue = cleanPhoneNumber(target.value);
            const combinedDigits = currentValue + cleanedPasted;
            const formattedNumber = formatPhoneNumber(combinedDigits);
            const startPos = target.selectionStart + cleanedPasted.length;

            target.value = formattedNumber;
            target.setSelectionRange(startPos, startPos);

            const inputEvent = new Event("input", { bubbles: true });
            Object.defineProperty(inputEvent, "target", { value: target });
            target.dispatchEvent(inputEvent);
        }
    });

    function cleanPhoneNumber(number) {
        return number.replace(/^[\+\s]*(?:00|0|55)?\s*/, "").replace(/\D/g, "");
    }

    function formatPhoneNumber(digits) {
        const limitedDigits = digits.slice(0, 11);

        if (limitedDigits.length > 10) {
            return limitedDigits.replace(/(\d{2})(\d{5})(\d{4})/, "$1 $2-$3");
        } else if (limitedDigits.length > 6) {
            return limitedDigits.replace(/(\d{2})(\d{4})(\d{4})/, "$1 $2-$3");
        } else if (limitedDigits.length > 2) {
            return limitedDigits.replace(/(\d{2})(\d{0,4})/, "$1 $2");
        }
        return limitedDigits;
    }
});
