@import url("https://fonts.googleapis.com/css?family=Open+Sans");
.menu_principal {
    padding-bottom: 0.8rem; /*box-shadow: -1px 4px 11px -9px rgba(0,0,0,0.56);
        -webkit-box-shadow: -1px 4px 11px -9px rgba(0,0,0,0.56);
        -moz-box-shadow: -1px 4px 11px -9px rgba(0,0,0,0.56);*/
}
.card_noticia {
    /* cursor: pointer; */
    border-radius: 12px;
    border-width: 0px;
    box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
    -webkit-box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
    -moz-box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
}
.card_noticia:hover {
    cursor: pointer;
    border-radius: 12px;
    border-width: 0px;
    box-shadow: 3px 5px 31px -15px rgba(0, 0, 0, 0.75);
    -webkit-box-shadow: 3px 5px 31px -15px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: 3px 5px 31px -15px rgba(0, 0, 0, 0.75);
    -webkit-transition: 0.5s ease-in;
    -moz-transition: 0.5s ease-in;
    -o-transition: 0.5s ease-in;
    transition: 0.5s ease-in;
}

.card_model {
    border-radius: 12px;
    border-width: 0px;
    box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
    -webkit-box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
    -moz-box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
}

.card_mais_acessados {
    cursor: pointer;
    border-radius: 12px;
    border-width: 1px;
    margin-bottom: 15px;
}
.card_numero_mais_acessados {
    margin-top: 2rem;
    margin-left: 1rem;
    font-size: 40px;
    font-family: Arial, Helvetica, sans-serif;
    color: #0842a0;
}
.card_conteudo {
    border-radius: 12px;
    border-width: 0px;
    box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
    -webkit-box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
    -moz-box-shadow: 2px 0px 31px -12px rgba(0, 0, 0, 0.29);
}

/* Report */
.report-link {
    font-size: 24px;
    font-weight: bold;
    line-height: 28px;
}

.report-link,
a:hover {
    color: black;
    text-decoration: none;
}

/* Preview of photos */

p,
ol,
#message {
    font-family: "Open Sans";
}
#multiple_upload {
    position: relative;
}
#photos {
    position: absolute;
    top: 2px;
    left: 0;
    opacity: 0.01;
    border: none;
    width: 355px;
    padding: 10px;
    z-index: 1;
    cursor: pointer;
}
#message {
    border: 2px solid #ccc;
    background: #fff;
    padding: 10px;
    width: 250px;
    float: left;
    margin: 4px;
    overflow: hidden;
    color: #333;
}
#botao {
    border: 1px solid #f7b90a;
    background: #f7b90a;
    color: #ffffff;
    font-family: "Open Sans";
    font-size: 15px;
    font-weight: bold;
    padding-left: 10px;
    padding-right: 10px;
    margin: 4px 8px;
    height: 45px;
}
#multiple_upload:hover > #botao {
    background: #f3c034;
    border-color: #f3c034;
}
#preview ol {
    margin-left: -16px;
}
#preview ol li {
    border-bottom: 1px solid #eee;
    padding: 10px;
    display: block;
    clear: left;
    margin-bottom: 2px;
}
#preview ol li.item_grey {
    background: #f9f9f9;
}
a.remove {
    text-decoration: none;
    color: red;
    display: block;
    font-size: 16px;
    width: 20px;
    float: right;
    font-weight: bold;
}
a.remove:hover {
    color: red;
}

img.item {
    max-width: 100%;
    max-height: 100%;
}

.box-images {
    height: 30px;
    width: 30px;
    background-color: #eee;
    border: 1px solid #eee;
    margin-bottom: 15px;
    /* Centralizando imagens */
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
    margin: 0 10px 20px 0;
}

/* Preview editar */
#new-preview ol {
    margin-left: -16px;
}
#new-preview ol li {
    border-bottom: 1px solid #eee;
    padding: 10px;
    display: block;
    clear: left;
    margin-bottom: 2px;
}
#new-preview ol li.item_grey {
    background: #f9f9f9;
}

.styleBotaoAdicionar {
    margin-bottom: -5px;
    display: block;
    cursor: pointer;
    float: right;
    margin-left: 5px;
}
.styleBotaoAdicionarCard {
    margin-bottom: -5px;
    display: block;
    cursor: pointer;
    float: right;
    margin-left: 5px;
}

@media screen and (max-width: 795px) and (min-width: 0px) {
    .styleBotaoAdicionar {
        margin-bottom: -5px;
        cursor: pointer;
        display: none;
    }
}

@media screen and (max-width: 992px) and (min-width: 767px) {
    .styleBotaoAdicionarCard {
        margin-bottom: -5px;
        cursor: pointer;
        display: none;
    }
}

.cardView {
    width: 16rem;
    margin: 9px;
}
.styleCard {
    border-radius: 8px;
    transition: 0s;
    border: 0.1px solid #fff;
}
.styleCard:hover {
    border-radius: 8px;
    box-shadow: 2px 6px 50px -15px rgba(0, 0, 0, 0.75);
    -webkit-box-shadow: 2px 6px 50px -15px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: 2px 6px 50px -15px rgba(0, 0, 0, 0.75);
    transition: 0s;
    transform: scale(1.03);
}
@media screen and (max-width: 1199px) and (min-width: 992px) {
    .cardView {
        width: 18rem;
        margin: 9px;
    }
}
@media screen and (max-width: 991px) and (min-width: 767px) {
    .cardView {
        width: 20rem;
        margin: 9px;
    }
}
@media screen and (max-width: 767px) and (min-width: 0px) {
    .cardView {
        width: 100%;
        margin: 9px;
    }
    .table-legend {
        display: none;
    }
}

.style_campo_estatico {
    border: 0 none;
    outline: 0;
    background-color: #f5f5f5;
}
.style_campo_titulo {
    font-size: 20px;
    font-family: Arial, Helvetica, sans-serif;
    color: #2494c7;
    margin-bottom: 5px;
}

/* Search loader */
.overlay {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 999;
    background: rgba(255, 255, 255, 0.8) url("/img/loader.gif") center no-repeat;
    background-size: 10%;
}

body.loading .overlay {
    display: block;
}

body.loading {
    overflow: hidden;
}
/* end search loader */

/* Mapa google */
.example-card-img-responsive {
    border-radius: 12px;
    object-fit: cover;
}
.example-card-img {
    border-radius: 12px;
    object-fit: cover;
    width: 150px;
    height: 150px;
}
.example-card-descricao {
    display: block;
}

.mapaGoogle {
    height: 650px;
    width: 750px;
    margin: 0 auto;
}
.botoesAcoes {
    display: block;
}
.dropdownAcoes {
    display: none;
}
.styleMenuPrincipal_block {
    display: block;
}
.styleMenuPrincipal_none {
    display: none;
}
.style_card_img_outras_noticias {
    display: block;
}
.style_acoes_mobile {
    display: none;
}
.style_acoes_desktop {
    display: block;
}
.style_pesquisar_desktop {
    display: block;
}
.style_pesquisar_mobile {
    display: none;
}

.buttons-search {
    display: block;
    background: none;
    border: none;
    padding: 0.25rem 1.5rem;
    white-space: nowrap;
    width: 100%;
    text-align: inherit;
}

.buttons-search:hover {
    background-color: #e9ecef;
}

@media screen and (max-width: 1199px) and (min-width: 415px) {
    .example-card-img {
        border-radius: 12px;
        object-fit: cover;
        width: 120px;
        height: 160px;
    }
    .example-card-descricao {
        display: block;
    }
}
@media screen and (max-width: 992px) and (min-width: 458px) {
    .mapaGoogle {
        height: 350px;
        width: 450px;
        margin: 0 auto;
    }
    .botoesAcoes {
        display: none;
    }
    .dropdownAcoes {
        display: block;
    }
}
@media screen and (max-width: 992px) and (min-width: 767px) {
    .style_card_img_outras_noticias {
        display: none;
    }
}
@media screen and (max-width: 767px) and (min-width: 0px) {
    .style_card_img_outras_noticias {
        display: block;
    }
    .style_acoes_mobile {
        display: block;
    }
    .style_acoes_desktop {
        display: none;
    }
}
@media screen and (max-width: 458px) and (min-width: 415px) {
    .mapaGoogle {
        height: 300px;
        width: 450px;
        margin: 0 auto;
    }
    .botoesAcoes {
        display: none;
    }
    .dropdownAcoes {
        display: block;
    }
    .style_pesquisar_desktop {
        display: none;
    }
    .style_pesquisar_mobile {
        display: block;
    }
}
@media screen and (max-width: 414px) and (min-width: 0px) {
    .example-card-img {
        border-radius: 12px;
        object-fit: cover;
        width: 120px;
        height: 150px;
    }
    .example-card-descricao {
        display: none;
    }
    .style_pesquisar_desktop {
        display: none;
    }
    .style_pesquisar_mobile {
        display: block;
    }
}

@media screen and (max-width: 411px) and (min-width: 376px) {
    .mapaGoogle {
        height: 250px;
        width: 350px;
        margin: 0 auto;
    }
    .botoesAcoes {
        display: none;
    }
    .dropdownAcoes {
        display: block;
    }
}
@media screen and (max-width: 375px) and (min-width: 361px) {
    .mapaGoogle {
        height: 200px;
        width: 300px;
        margin: 0 auto;
    }
    .botoesAcoes {
        display: none;
    }
    .dropdownAcoes {
        display: block;
    }
}
@media screen and (max-width: 360px) and (min-width: 321px) {
    .mapaGoogle {
        height: 220px;
        width: 320px;
        margin: 0 auto;
    }
    .botoesAcoes {
        display: none;
    }
    .dropdownAcoes {
        display: block;
    }
}
@media screen and (max-width: 320px) and (min-width: 280px) {
    .mapaGoogle {
        height: 180px;
        width: 280px;
        margin: 0 auto;
    }
    .botoesAcoes {
        display: none;
    }
    .dropdownAcoes {
        display: block;
    }
    .example-card-img {
        border-radius: 12px;
        object-fit: cover;
        width: 50px;
        height: 155px;
    }
    .example-card-descricao {
        display: none;
    }
}
@media screen and (max-width: 280px) and (min-width: 0px) {
    .example-card-img {
        border-radius: 12px;
        object-fit: cover;
        width: 50px;
        height: 150px;
    }
    .example-card-descricao {
        display: none;
    }
}

@media screen and (max-width: 750px) and (min-width: 0px) {
    .styleMenuPrincipal_block {
        display: none;
    }
    .styleMenuPrincipal_none {
        display: block;
    }
}

/* card do agendamento - perfil agente de saude */
.style_card_data_das_consultas_data {
    list-style-type: none;
    margin-top: 20px;
    margin-bottom: 6px;
}
.style_card_data_das_consultas_icon {
    margin-right: 10px;
    margin-top: 3px;
}
.style_card_data_das_consultas {
    background-color: #ffffff;
}
.style_card_data_das_consultas:hover {
    background-color: #f6fcff;
}
.style_card_data_das_consultas_titulo {
    margin-top: 5px;
    margin-bottom: 5px;
    color: #2670ba;
    font-weight: bold;
}
.style_card_data_das_consultas_especialista {
    margin-bottom: -1px;
    color: rgb(0, 0, 0);
}
.style_card_data_das_consultas_horario {
    margin-bottom: -17px;
    color: rgb(0, 0, 0);
}

/*card das imagens - Adicionar agendamento*/
.card_img_addAgendamento {
    width: 135px;
    height: 135px;
    background-color: #c7c7c7;
    border-radius: 8px;
    margin: 1.8px;
}
.card_img_background_addAgendamento {
    width: 100%;
    height: 100%;
    background-size: cover;
    border-radius: 8px;
}

.display_none_inportant {
    display: none !important;
}

.new_tab input {
    color: rgb(69, 157, 223);
}

.new_tab:hover input {
    color: white;
}

.sidebar .sidebar-content::-webkit-scrollbar,
.filters-container::-webkit-scrollbar {
    width: 4px;
}

.sidebar.collapsed .sidebar-content::-webkit-scrollbar {
    display: none;
}

.sidebar .sidebar-content::-webkit-scrollbar-track,
.filters-container::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 6px;
}

.sidebar .sidebar-content::-webkit-scrollbar-thumb,
.filters-container::-webkit-scrollbar-thumb {
    background-color: rgb(63 63 70);
    border-radius: 6px;
}

@media screen and (max-width: 1200px) and (min-width: 991px) {
    .card_img_addAgendamento {
        width: 139px;
        height: 139px;
        background-color: #c7c7c7;
        border-radius: 8px;
        margin: 1.8px;
    }
}
@media screen and (max-width: 991px) and (min-width: 767px) {
    .card_img_addAgendamento {
        width: 135px;
        height: 135px;
        background-color: #c7c7c7;
        border-radius: 8px;
        margin: 1.8px;
    }
}
@media screen and (max-width: 767px) and (min-width: 575px) {
    .card_img_addAgendamento {
        width: 115px;
        height: 115px;
        background-color: #c7c7c7;
        border-radius: 8px;
        margin: 1.8px;
    }
}
@media screen and (max-width: 575px) and (min-width: 376px) {
    .card_img_addAgendamento {
        width: 80px;
        height: 80px;
        background-color: #c7c7c7;
        border-radius: 8px;
        margin: 1.8px;
    }
}
@media screen and (max-width: 375px) and (min-width: 361px) {
    .card_img_addAgendamento {
        width: 75px;
        height: 75px;
        background-color: #c7c7c7;
        border-radius: 8px;
        margin: 1.8px;
    }
}
@media screen and (max-width: 360px) and (min-width: 0px) {
    .card_img_addAgendamento {
        width: 70px;
        height: 70px;
        background-color: #c7c7c7;
        border-radius: 8px;
        margin: 1.8px;
    }
}

.zoom {
    zoom: 105%;
}

.card-sized {
    width: inherit;
    height: 25em;
}

.form-title {
    font-size: 20px;
    font-family: Arial, Helvetica, sans-serif;
    color: #2494c7;
    margin-bottom: 5px;
}

.preview-img-container {
    width: 100%;
    height: 15em;
}

.sized-img-card {
    min-width: 20em;
    height: 10em;
}

.sized-preview-img {
    min-width: 20em;
    height: 10em;
}

.img-sized {
    width: 10em;
    height: 15em;
}

#images {
    position: absolute;
    top: 2px;
    left: 0;
    opacity: 0.01;
    border: none;
    max-width: 355px;
    padding: 10px;
    z-index: 1;
    cursor: pointer;
}

.bg-gray {
    background-color: #d3d3d3;
}

.card-sized-show {
    border-radius: 12px;
}

.img-sized-show {
    width: 15em;
    height: 20em;
}

.img-cover {
    object-fit: cover;
}

.td-limited {
    max-width: 6.6em;
}

.overflow-x {
    overflow-x: auto;
    white-space: nowrap;
}

.mouse-pointer {
    cursor: pointer;
}

.text-black {
    color: black;
}

.accessory-card-limit {
    max-height: 25em;
}

.td-w-50 {
    width: 50%;
}

.paginator-container {
    max-width: 100%;
}

.custom-title {
    font-size: 25px;
    font-family: Arial, Helvetica, sans-serif;
    color: #0842a0;
}

.custom-subtitle {
    font-size: 1rem;
    font-family: Arial, Helvetica, sans-serif;
    color: #0842a0;
}

.btn-step {
    width: 10rem;
}

button.btn.btn-outline-primary.btn-block {
    color: #2494c7;
    border-color: #2494c7;
}

button.btn.btn-outline-primary.btn-block:hover {
    background-color: #2494c7; /* Green */
    color: white;
}

/* #map {

    width: 100%;

    height: 400px;

    background-color: grey;

} */

.hidden {
    display: none;
}

.max-w-100 {
    max-width: 100%;
}

.max-w-90 {
    max-width: 90%;
}

.td-address {
    max-width: 21em;
    min-width: 21em;
}

.td-accessory-address {
    max-width: 8em;
}

.custom-modal-header {
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}

/* Componente de Notificação */
.notification-item {
    display: flex;
    align-items: center;
    padding: 1rem 0.8rem;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    margin-bottom: 10px;
    transition: border-color 0.3s ease, opacity 0.3s ease,
        background-color 0.3s ease;
    color: black;
}

.notification-item.removing {
    opacity: 0.5;
    background-color: #f8f9fa;
}

.notification-icon-img {
    border-radius: 50%;
}

.notification-item:hover {
    border-color: #007bff;
}

.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 50%;
    margin-right: 15px;
}

.notification-icon img {
    width: 24px;
    height: 24px;
}

.notification-content {
    flex: 1;
    overflow: hidden;
    max-width: 100%;
}

.notification-content h6 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 95%;
}

.notification-content p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
    white-space: normal;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    max-width: 95%;
}

.notification-action {
    margin-left: 15px;
    color: #dc3545;
    cursor: pointer;
}

.notification-action:hover {
    color: #c82333;
}

.notification-body {
    display: flex;
    width: 100%;
    flex-direction: column;
}

.notification-main {
    display: flex;
    align-items: center;
}

.notification-viewed {
    display: flex;
    justify-content: flex-end;
    height: 1px;
    color: #007bff;
    margin-left: 0.25rem;
    font-size: 12px;
}

.info-placeholder {
    color: #999;
    font-style: italic;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.875rem;
}

.info-placeholder i {
    font-size: 0.8rem;
    color: #bbb;
}

.ellipsis-td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 28ch;
}

.table-disabled {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-disabled::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 10;
}
