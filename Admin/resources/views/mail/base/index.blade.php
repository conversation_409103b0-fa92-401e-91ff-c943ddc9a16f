@php
    use App\Helpers\SystemHelper;
    $systemLogo = SystemHelper::get()['MAIN_LOGO'] ?? route('home') . '/icon/logo_visao_negocio.svg';
    $mainColor = SystemHelper::get()['MAIN_COLOR'];
@endphp

        <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>

    <style>

        :root {
            --main-color: {{ $mainColor ?? '#FBBC05' }};
        }

        /* Reset básico para garantir consistência */
        body, p, h1, h2, h3, h4, h5, h6, a {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            color: #333333;
        }

        body {
            background-color: #f4f4f4;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            background-color: #ffffff;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: #FFFFFF;
            padding: 8px;
            text-align: center;
            border-bottom: 5px solid{{ $mainColor ?? '#FBBC05' }};
        }

        .header img.system-logo {
            max-width: 150px;
            height: auto;
        }

        .business-logo-title {
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 20px;
        }

        .business-logo {
            max-width: 100px;
            height: auto;
            margin-top: 10px;
        }

        .content {
            padding: 20px;
        }

        .content h2 {
            color: {{ $mainColor ?? '#FBBC05' }};
            margin-bottom: 15px;
            font-size: 24px;
            text-align: start;
        }

        .content p {
            line-height: 1.5;
            margin-bottom: 15px;
            font-size: 16px;
            text-align: justify;
        }

        .button-container {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            font-size: 16px;
            color: #ffffff;
            background-color: {{ $mainColor ?? '#FBBC05' }};
            text-decoration: none;
            border-radius: 25px;
            transition: background-color 0.3s ease;
        }

        .footer {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            font-size: 14px;
            color: #777777;
        }

        .footer a {
            color: {{ $mainColor ?? '#FBBC05' }};
            text-decoration: none;
            margin: 0 5px;
            font-weight: bold;
        }

        /* Responsividade */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
                border-radius: 0;
            }

            .header img.system-logo {
                max-width: 120px;
            }

            .business-logo {
                max-width: 80px;
            }

            .btn {
                width: 100%;
                padding: 15px 0;
            }

            .content p {
                text-align: left;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <!-- Cabeçalho com Logo do Sistema -->
    <div class="header">
        <!-- Logo do Sistema -->

        @if ($systemLogo)
            <img src="{{ $systemLogo }}" alt="{{ config('app.name') }} Logo" class="system-logo">
        @else
            <h2 style="color: black;">{{ config('app.name') }}</h2>
        @endif
    </div>

    <!-- Conteúdo Principal -->
    <div class="content">
        @yield('content')
    </div>

    <!-- Rodapé com Informações Adicionais -->
    <div class="footer">
        <p><a href="{{ route('home') }}">Visite nosso site</a></p>
    </div>
</div>
</body>
</html>
