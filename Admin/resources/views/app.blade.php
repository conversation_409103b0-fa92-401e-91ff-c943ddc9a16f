@php 
use App\Helpers\SystemHelper; 
use App\Helpers\ApiUser; 

$layout = ApiUser::get() !== null ? 'layouts.app' : 'layouts.guest';
@endphp

@extends($layout, ['activePage' => 'app'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12" style="margin-left: 12px; margin-top:2rem; margin-bottom:1rem">
                <h4 class="">Visão Negócio</h4>
                <p style="margin-bottom: 2rem; font-size: 20px">O Aplicativo +Default foi feito exclusivamente para
                    solicitar reparos nos postes de iluminação. Instale ele em seu aparelho e acompanhe de forma simples
                    e prática as solicitações feitas sobre os ajustes, além de ficar por dentro de informações sobre os
                    postes de luz da rede pública do seu município.</p>
                <img src="{{ SystemHelper::get()['APP_LOGO'] }}" alt="" width="100%">
            </div>
        </div>
    </div>
    
    @if(SystemHelper::getAppLinks()['link_app_ios'] != null || SystemHelper::getAppLinks()['link_app_android'] != null)
        <div class="col-md-12" style="margin-bottom:3rem; text-align:center">
            <h6 style="color:#909090; font-size:18px; margin-top:1rem; margin-bottom:1rem">Disponível para baixar:</h6>
            <div class="btn-group">
                @if(SystemHelper::getAppLinks()['link_app_ios'] != null)
                    <a href="{{SystemHelper::getAppLinks()['link_app_ios']}}" target="_blank"><img
                            src="{{asset('icon/selo-appstore.svg')}}" alt="" width="90%" style="margin-right: 10px"></a>
                @endif
                @if(SystemHelper::getAppLinks()['link_app_android'] != null)
                    <a href="{{SystemHelper::getAppLinks()['link_app_android']}}" target="_blank"><img
                            src="{{asset('icon/selo-googleplay.svg')}}" alt="" width="90%"></a>
                @endif
            </div>
        </div>
    @endif
@endsection
