<?php
    use Carbon\Carbon;
?>
@extends('layouts.app', ['activePage' => 'notifications'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Campanhas</h5>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{route('notifications.clients.index')}}" onsubmit="handleSearch(event)" method="get" id="form_search_notifications">
                                    @csrf
                                    <div class="input-group " id="search_form_desktop">
                                        <input type="text" class="form-control mr-2" name="search"
                                               aria-describedby="search" style="flex: 4;"
                                               placeholder="Pesquisar campanha por título/descrição"
                                               value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        <div class="input-group-append mr-2">
                                            <button class="btn btn-light" type="submit" id="btnSearch" 
                                                {{isset($period) && $period === 'ESPECIFIC' ? "style=display:none" : ''}}>
                                                <img class="card-img-left example-card-img-responsive"
                                                        src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <select class="form-control mr-2" name="sending_type"
                                                style="flex: 2;"
                                                aria-describedby="sending_type" id="type_desktop">
                                            <option value="all"
                                                    @if((isset($sending_type) && $sending_type == 'all') || !isset($sending_type)) selected @endif>
                                                Tipo de Envio: Todas
                                            </option>
                                            <!-- <option value="notification_push"
                                                @if(isset($sending_type) && $sending_type == 'notification_push') selected @endif>
                                                Aplicativo
                                            </option> -->
                                            <option value="email"
                                                    @if(isset($sending_type) && $sending_type == 'email') selected @endif>
                                                Tipo de envio: Email
                                            </option>
                                            <!-- <option value="whatsapp"
                                                @if(isset($sending_type) && $sending_type == 'whatsapp') selected @endif>
                                                Whatsapp
                                            </option> -->
                                        </select>

                                        <select class="form-control" style="flex:2" name="period" aria-describedby="period" id="period_desktop">
                                            <option value="TODAY" @if(isset($period) && $period == 'TODAY') selected @endif>Hoje</option>
                                            <option value="LAST_30DAYS" @if(isset($period) && $period == 'LAST_30DAYS') selected @endif>Últimos 30 dias</option>
                                            <option value="ESPECIFIC" @if(isset($period) && $period == 'ESPECIFIC') selected @endif>Escolher um período</option>
                                            <option value="ALL" @if((isset($period) && $period == 'ALL') || !isset($period)) selected @endif>Todo período</option>
                                        </select>
                                    </div>
                                    <div class="input-group d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="search"
                                                   aria-describedby="search"
                                                   placeholder="Pesquisar campanha por título/descrição"
                                                   value="{{ isset($search) ? $search : '' }}" id="search_mobile">
                                        </div>
                                        <div class="input-group mb-2">
                                            <button class="btn btn-light btn-block" type="submit" id="btnSearchMobile"
                                            {{isset($period) && $period === 'ESPECIFIC' ? "style=display:none" : ''}}>
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <div class="input-group mb-2">
                                            <select class="form-control" name="sending_type" style="flex: 2;"
                                                    aria-describedby="sending_type" id="type_mobile"
                                            >
                                                <option value="all"
                                                        @if((isset($sending_type) && $sending_type == 'all') || !isset($sending_type)) selected @endif>
                                                    Tipo de envio: Todos
                                                </option>
                                                <!-- <option value="notification_push"
                                                    @if(isset($sending_type) && $sending_type == 'notification_push') selected @endif>
                                                    Aplicativo
                                                </option> -->
                                                <option value="email"
                                                        @if(isset($sending_type) && $sending_type == 'email') selected @endif>
                                                    Tipo de envio: Email
                                                </option>
                                                <!-- <option value="whatsapp"
                                                    @if(isset($sending_type) && $sending_type == 'whatsapp') selected @endif>
                                                    Whatsapp
                                                </option> -->
                                            </select>
                                        </div>
                                        <select class="form-control" style="flex:2" name="period" aria-describedby="period" id="period_mobile">
                                            <option value="TODAY" @if(isset($period) && $period == 'TODAY') selected @endif>Hoje</option>
                                            <option value="LAST_30DAYS" @if(isset($period) && $period == 'LAST_30DAYS') selected @endif>Últimos 30 dias</option>
                                            <option value="ESPECIFIC" @if(isset($period) && $period == 'ESPECIFIC') selected @endif>Escolher um período</option>
                                            <option value="ALL" @if((isset($period) && $period == 'ALL') || !isset($period)) selected @endif>Todo período</option>
                                        </select>
                                    </div>

                                    <div class="form-row dateInputFilterWrapper" style="display: {{ !isset($startDate)  ? 'none !important' : 'flex !important' }}">
                                        <div class="col-md-5 mt-2 d-flex align-items-center ">
                                            <input
                                                id="startDateInput"
                                                name="startDate"
                                                type="date"
                                                class="form-control"
                                                value="{{ isset($startDate) ? Carbon::parse($startDate)->format('Y-m-d') : Carbon::now()->sub('1 day')->format('Y-m-d') }}"
                                                @if (!isset($startDate)) disabled @endif
                                            />
                                        </div>
                                        <div class="col-md-5 mt-2 d-flex align-items-center">
                                            <input
                                                id="endDateInput"
                                                name="endDate"
                                                type="date"
                                                class="form-control"
                                                value="{{ isset($endDate) ? Carbon::parse($endDate)->format('Y-m-d') : Carbon::now()->format('Y-m-d') }}"
                                                @if (!isset($endDate)) disabled @endif
                                            />
                                        </div>
                                        <div class="col-md-2 mt-2">
                                            <button class="btn btn-light w-100" type="submit" id="searchWithPeriod">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="notifications-table">
                            <thead>
                            <tr>
                                <th scope="col" style="text-align: center">Ações</th>
                                <th scope="col">Título</th>
                                <th scope="col">Descrição</th>
                                <th scope="col">Negócio</th>
                                <th scope="col">Tipo</th>
                                <th scope="col">Data</th>
                            </tr>
                            </thead>
                            <tbody>
                                @foreach ($notifications as $notification)
                                @php
                                    $castNotificationType = ['email' => 'Email',
                                                            'whatsapp' => 'Whatsapp',
                                                            'notification_push' => 'Aplicativo'];
                                    $notificationType = $castNotificationType[$notification['notification']['sending_type']];
                                @endphp
                                <tr>
                                    <td style="text-align: center">
                                        <div class="btn-group">
                                            <button
                                                class="btn btn-secondary"
                                                style="margin-right: 10px"
                                                onclick="openModalShow('modal-view-notification', {{$notification['notification_client_id']}})"
                                            >
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                            </button>
                                        </div>
                                    </td>
                                    <td title="{{$notification['notification']['title']}}">
                                        {{ Str::limit($notification['notification']['title'], 16, '...') }}
                                    </td>
                                    <td title="{{ $notification['notification']['description'] }}">
                                        {{ Str::limit($notification['notification']['description'], 16) }}
                                    </td>
                                    <td title="{{$notification['notification']['business']['name'] ?? config('app.name')}}">
                                        {{ isset($notification['notification']['business']) ? Str::limit($notification['notification']['business']['name'], 16) : config('app.name') }}
                                    </td>
                                    <td>{{ $notificationType }}</td>
                                    @if (isset($notification['shipping_date']))
                                    <td>
                                        {{ Carbon::parse($notification['shipping_date'])->format('d/m/Y') }}
                                        <p>
                                            {{ Carbon::parse($notification['shipping_date'])->format('H:i:s') }}
                                        </p>
                                    </td>
                                    @else
                                        <td>Não informada</td>
                                    @endif
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div/>

                        {{ $notifications->onEachSide(0)->appends($queryParams)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                    style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                    @include('notifications.legend_client')
                </div>
            </div>
        </div>
    </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...'])
    @include('notifications.modal_show', ['modalId' => "modal-view-notification"])
@endsection

@push('js')
<script type="text/javascript">
    $(document).ready(() => {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });
        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }
    });

    function handleSearch(event) {
        event.preventDefault();
        const startDateInput = document.getElementById('startDateInput');
        const endDateInput = document.getElementById('endDateInput');
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);
        if (startDate > endDate) return alert('A data de início não pode ser após a data de fim!');
        event.target.submit()
    }

    $('#period_desktop, #period_mobile').on('change', function() {
        const inputValue = $(this).val();
        const form = $('#form_search_notifications')
        const startDateInput = document.getElementById('startDateInput');
        const endDateInput = document.getElementById('endDateInput');
        const btnSearchDesktop = $('#btnSearch');
        const btnSearchMobile = $('#btnSearchMobile');
        const searchEnd = $('#searchWithPeriod');

        if (inputValue !== 'ESPECIFIC') {
            $('#loadingDashboard').modal('show');
            startDateInput.value = null;
            endDateInput.value = null;
            form.submit();
            return;
        }

        btnSearchMobile.hide();
        btnSearchDesktop.hide();
        searchEnd.show();
        startDateInput.disabled = false;
        endDateInput.disabled = false;
        $('.dateInputFilterWrapper').each(function () {
            $(this).show();
        })
        $('#searchFilterButton').show();
    })

    let type_desktop = document.getElementById('type_desktop');
    let type_mobile = document.getElementById('type_mobile');

    type_desktop.addEventListener('change', function () {
        type_mobile.value = type_desktop.value;
        $('#form_search_notifications').submit();
    });

    type_mobile.addEventListener('change', function () {
        type_desktop.value = type_mobile.value;
        $('#form_search_notifications').submit();
    });

    let search_desktop = document.getElementById('search_desktop');
    let search_mobile = document.getElementById('search_mobile');

    search_desktop.addEventListener('keyup', function () {
        search_mobile.value = search_desktop.value;
    });

    search_mobile.addEventListener('keyup', function () {
        search_desktop.value = search_mobile.value;
    });

    window.addEventListener('resize', function () {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });
        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }
    });

    function addLoading(containerId) {
        $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
    }

    function openModalShow(modalId, notificationId) {
        addLoading('notification_show_modal_body')
        $(`#${modalId}`).modal('show');
        $.ajax({
            url: `/notifications/client/${notificationId}/show`,
            type: 'get',
            data: {},
            success: function(response) {
                $('#notification_show_modal_body').html(response);
                const event = new Event('openShowNotification');
                document.dispatchEvent(event);
            },
            error: function(response, textStatus, msg) {
                $('#notification_show_modal_body').html(msg);
            }
        });
    }
</script>
@endpush
