<?php

use App\Helpers\ApiUser;
use Carbon\Carbon;

?>
<style>
    .menu-container {
        position: absolute;
        right: 0;
        top: -10px;
    }
    .menu-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 5px;
        cursor: pointer;
    }
    .menu-btn i {
        margin-right: 5px;
    }
    .menu-dropdown {
        display: none;
        position: absolute;
        right: 0;
        background: white;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
        border-radius: 5px;
        padding: 10px;
        width: 180px;
        z-index: 10;
    }
    .menu-dropdown button {
        display: block;
        width: 100%;
        text-align: left;
        background: none;
        border: none;
        padding: 8px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s;
    }
    .menu-dropdown button:hover {
        background: #f0f0f0;
    }
</style>
@php
    $options = [];
    $whatsappEnabled = ApiUser::get()['business_selected'] == null || ApiUser::get()['business_selected']['plan']['authorizedFunctions']['whatsapp_sending'];
    $whatsappConfigured = !empty(ApiUser::get()['business_selected']['parameters']['whatsapp_phone_number']) &&
                        !empty(ApiUser::get()['business_selected']['parameters']['whatsapp_instance_key']);
    
    $emailEnabled = ApiUser::get()['business_selected'] == null || ApiUser::get()['business_selected']['plan']['authorizedFunctions']['email_sending'];
    // Verifica se precisa mostrar alerta de whatsapp não configurado
    $whatsappNeedConfig = false;
    if ($whatsappEnabled && !$whatsappConfigured && !$emailEnabled) {
        $whatsappNeedConfig = true;
    }
    
    if (ApiUser::get()['business_selected'] == null || ApiUser::get()['business_selected']['plan']['authorizedFunctions']['email_sending']) {
        $options[] = 'EMAIL';
    }
    if ($whatsappEnabled && $whatsappConfigured) {
        $options[] = 'WHATSAPP';
    }

    $selectedOption = count($options) > 0 ? $options[0] : '';
    if (count($options) > 1) {
        $selectedOption = '';
    }
@endphp

<div class="form-row">
    <div class="col-md-12">
        <p class="form-title">
            Tipo de campanha
            <b class='text-danger'>*</b>
        </p>
        @if($whatsappNeedConfig)
            <span style="color:rgb(189, 40, 40); ">O WhatsApp não está configurado. Por favor, configure o número do WhatsApp.</span>
        @endif
    </div>
    <div class="col-md-12 mb-2">
        <div class="d-flex">
            @if($whatsappNeedConfig)
                <script>
                    $(document).ready(function () {
                          Swal.fire({
                            icon: 'warning',
                            title: 'WhatsApp Não Configurado',
                            text: 'O WhatsApp não está configurado. Por favor, configure o número do WhatsApp.',
                            confirmButtonText: 'Ok',
                            @if(ApiUser::hasPermission('parameters'))
                            showCancelButton: true,
                            cancelButtonText: 'Configurar Whatsapp',
                            cancelButtonColor: '#3085d6'
                            @endif
                        }).then((result) => {
                            @if(ApiUser::hasPermission('parameters'))
                            if (result.dismiss === Swal.DismissReason.cancel) {
                                window.location.href = "{{ route('businesses.parameters') }}";
                            }
                            @endif
                        });
                    });
                </script>
            @endif
            <!-- <div class="form-check">
                <input class="form-check-input" type="radio" id="notification_push" name="sending_type" value="notification_push">
                <label class="form-check-label" for="notification_push">
                    Aplicativo
                </label>
            </div> -->
            @if(in_array('EMAIL', $options))
                <div class="form-check" style="margin-left: 20px;">
                    <input class="form-check-input" type="radio" id="email" name="sending_type" value="email" 
                        @if ($selectedOption === 'EMAIL') checked @endif>
                    <label class="form-check-label" for="email">
                        Email
                    </label>
                </div>
            @endif
            @if(in_array('WHATSAPP', $options))
                <div class="form-check" style="margin-left: 20px;">
                    <input class="form-check-input" type="radio" id="whatsapp" name="sending_type" value="whatsapp"
                        @if ($selectedOption === 'WHATSAPP') checked @endif>
                    <label class="form-check-label" for="whatsapp">
                        Whatsapp
                    </label>
                </div>
            @endif
        </div>
        <small id="error-sending_type" class="text-danger"></small>
    </div>
    <div class="col-md-12"><p class="form-title">Dados campanha</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'title',
                'label' => 'Título',
                'placeholder' => 'Digite o título da campanha',
                'withErrorText' => true,
            ])
        </div>
    </div>

    <div class="col-md-12">
        <div class="form-group mb-2" style="position: relative">
            @include('components.inputs.text_area', [
                'name' => 'description',
                'label' => 'Descrição',
                'placeholder' => 'Digite a descrição da campanha',
                'withErrorText' => true,
            ])
            <div class="menu-container">
                <button type="button" class="menu-btn" onclick="toggleMenu()">
                    <i class="fas fa-bars"></i> Variáveis
                </button>
                <div class="menu-dropdown" id="menuDropdown">
                    <button type="button" onclick="addTagToTextArea('@{{NOME_CLIENTE}}')">
                        <i class="fas fa-user"></i> Nome do Cliente
                    </button>
                    <button type="button" onclick="addTagToTextArea('@{{CPF_CLIENTE}}')">
                        <i class="fas fa-id-card"></i> CPF do Cliente
                    </button>
                </div>
            </div>        
        </div>
        <div class="form-group mb-2 d-none" id="linkGroup">
            @include('components.inputs.text', [
                'name' => 'url_click',
                'label' => 'Link',
                'placeholder' => 'Https://',
                'required' => false,
                'disabled' => true,
                'withErrorText' => true
            ])
            <small class="form-text text-muted">Certifique-se de inserir um link válido. Exemplos de links válidos
                abaixo</small>
            <small class="form-text text-muted" style="font-weight: bold"
                   title="Ao enviar uma campanha colocando um dos links abaixo no campo 'Link', caso o usuário tenha o aplicativo instalado, irá abrir direto no aplicativo e não no site.">Links
                que os clientes tem acesso e que são abertos no aplicativo:</small>
            <ul style="width: 100%; height: 100px; overflow: auto">
                <li><small class="form-text text-muted">Tela inicial: <a target="_blank"
                                                                         href="{{Request::root()}}">{{Request::root()}}</a></small>
                </li>
                <li><small class="form-text text-muted">Tela de Campanhas: <a target="_blank"
                                                                              href="{{Request::root() . '/notifications/client'}}">{{Request::root() . '/notifications/client'}}</a></small>
                </li>
                <li><small class="form-text text-muted">Tela de Cashbacks: <a target="_blank"
                                                                              href="{{Request::root() . '/cashbacks/client'}}">{{Request::root() . '/cashbacks/client'}}</a></small>
                </li>
                <li><small class="form-text text-muted">Tela de Contatos: <a target="_blank"
                                                                             href="{{Request::root() . '/contact'}}">{{Request::root() . '/contact'}}</a></small>
                </li>
            </ul>
        </div>
    </div>
    <div class="col-md-12 mb-2">
        <h6 class="style_campo_titulo">
            Clientes (<span id="total_clients"></span>)
            <b class='text-danger'>*</b>
        </h6>
        <span style="color: #909090; ">* A campanha será enviada para todos os clientes listados na tabela abaixo</span>
    </div>
    <div class="form-group my-2 col-md-12">
        <hr id="clientsSeparator"/>
        <form id="search-client-form" method="GET">
            @csrf
            <div class="d-flex flex-column flex-sm-row" style="gap: 4px">
                <input type="text" id="clientsSearch" class="form-control mb-sm-0 w-100"
                       placeholder="Pesquisar cliente..." style="flex: 4;">
                <select class="form-control mb-2 mb-sm-0 flex-grow-1" name="is_entrepreneur" id="entrepreneur_desktop"
                        style="flex: 2;">
                    <option value="all" selected>Todos</option>
                    <option value="true">Clientes Empreendedores</option>
                    <option value="false">Clientes Não Empreendedores</option>
                </select>
                <div class="input-group-append mb-2 mb-sm-0 col-md-1">
                    <button class="btn btn-light w-100" type="button" id="search-notification-add">
                        <img class="card-img-left example-card-img-responsive" src="{{url('icon/icon_search.png')}}"
                             width="20px"/>
                    </button>
                </div>
        </form>
    </div>
    <div class="table-responsive-sm">
        <table class="table" style="white-space: nowrap;" id="clientsTable">
            <thead>
            <tr>
                <th style="width: 50%">Nome</th>
                <th style="width: 30%">CPF</th>
                <th style="width: 20%">Empreendedor</th>
            </tr>
            </thead>
            <tbody id="clientsTableBody">
            <div class="d-flex justify-content-center loading pb-1 d-none">
                <div class="spinner-border loading" role="status">
                    <span class="sr-only loading">Loading...</span>
                </div>
            </div>
            <!-- Clientes serão carregados aqui -->
            </tbody>
        </table>
        <p><small id="error-number_clients" class="p-1 text-danger"></small></p>
    </div>
    <nav>
        <ul class="pagination" id="clientPagination">
            <!-- Paginação será carregada aqui -->
        </ul>
    </nav>
</div>
<input type="hidden" name="number_clients" id="numberClients">
<input type="hidden" name="filters" id="filtersInput">
</div>

<script text="text/javascript">
    $('document').ready(function () {
        loadClients()
    });

    $(document).click(function(event) {
        if (!$(event.target).closest('.menu-container').length) {
            $('#menuDropdown').hide();
        }
    });

    let previousSelection = ''

    function toggleMenu() {
        $('#menuDropdown').toggle();
    }

    function addTagToTextArea(tag) {
        let textarea = $('#description');
        
        let cursorPos = textarea.prop('selectionStart');
        let prevText = textarea.val().substring(0, cursorPos);
        let afterText = textarea.val().substring(cursorPos);

        let isWhatsappNotification = $('#whatsapp').is(':checked')
        if (isWhatsappNotification) 
            tag = `*${tag}*`;
        textarea.val(prevText + tag + afterText);
        textarea.focus();
        $('#menuDropdown').hide();
    }

    $('input[name="sending_type"]').on('change', function () {
        const titleInput = document.getElementById('title');
        const descriptionInput = document.getElementById('description');
        const titleLabel = document.getElementById('title_maxLength');
        const descriptionLabel = document.getElementById('description_maxLength');
        if ($('#notification_push').is(':checked')) {
            $('#linkGroup').removeClass('d-none');
            $('#linkGroup').find('input').prop('disabled', false);

            if (titleInput.value.length > 50) {
                titleInput.value = titleInput.value.slice(0, 50);
            }
            if (descriptionInput.value.length > 150) {
                descriptionInput.value = descriptionInput.value.slice(0, 150);
            }
            titleInput.maxLength = 50;
            titleLabel.innerHTML = '(50 caracteres)';

            descriptionInput.maxLength = 150
            descriptionLabel.innerHTML = '(150 caracteres)';
            previousSelection = 'notification_push';
            loadClients();
        } else {
            if (previousSelection === 'notification_push') {
                loadClients();
            }

            $('#linkGroup').addClass('d-none');
            $('#linkGroup').find('input').prop('disabled', true);

            titleInput.maxLength = 255;
            titleLabel.innerHTML = '';

            descriptionInput.maxLength = 2000;
            descriptionLabel.innerHTML = '';
            previousSelection = 'other';
        }
    });

    $('#search-notification-add').on('click', function () {
        loadClients();
    })

    let errorsList = [];
    showHideNoClientSelectedMessage()

    function fillErrors(errors) {
        errorsList.forEach((id) => {
            document.getElementById(id).innerHTML = '';
            document.getElementById(id).classList.add('d-none');
        });

        Object.keys(errors).forEach((key) => {
            let error = document.getElementById(`error-${key}`);
            console.log(error);
            if (error) {
                error.innerHTML = errors[key][0];
                error.classList.remove('d-none');
                errorsList.push(error.id);
            }
        });
    }

    function showHideNoClientSelectedMessage() {
        if ($('#clients_selected').children().length === 0) {
            $('#clientsSelectedLabel').addClass('d-none');
            $('#clientsSeparator').addClass('d-none');
        } else {
            $('#clientsSelectedLabel').removeClass('d-none');
            $('#clientsSeparator').removeClass('d-none');
        }
    }

    function loadClients(page = 1, show_defaut_option = true) {
        const clients_id = [];
        const searchQuery = $('#clientsSearch').val();
        const entrepreneurFilter = $('#entrepreneur_desktop').val();
        const numberClientsInput = document.querySelector("#numberClients");
        const totalClientsSpan = document.querySelector("#total_clients");
        const isNotificationPush = $('#notification_push').is(':checked');

        function formatCPF(cpf) {
            return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        }

        $.ajax({
            url: `<?= env('API_URL') ?>/api/clients/by-business?search=${searchQuery}&page=${page}&per_page=5`,
            type: 'get',
            data: {
                search: searchQuery,
                page: page,
                per_page: 5,
                is_entrepreneur: entrepreneurFilter,
                has_devices: isNotificationPush
            },
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#clientsTable').addClass('d-none');
                $('#clientPagination').addClass('d-none');
                $('.loading').removeClass('d-none');
            },
            success: function (response) {
                const filters = {
                    is_entrepreneur: entrepreneurFilter,
                    has_devices: isNotificationPush,
                    search: searchQuery,
                }
                filtersInput.value = JSON.stringify(filters);
                $('.loading').addClass('d-none');

                $('#clientsTableBody').empty();
                const clients = response.clients.data;
                numberClientsInput.value = response.clients.total;
                totalClientsSpan.innerHTML = response.clients.total;
                if (clients.length > 0) {
                    $.each(clients, function (index, client) {
                        $('#clientsTableBody').append(`
                        <tr>
                            <td>
                                ${client.name}
                            </td>
                            <td>
                                ${formatCPF(client.cpf)}
                            </td>
                            <td>
                                ${client.is_entrepreneur ? 'Sim' : 'Não'}
                            </td>
                            <div>
                                <small id="error-clients_id${index}" class="p-1 text-danger"></small>
                            </div>
                        </tr>
                    `);
                    });
                } else {
                    $('#clientsTableBody').append('<tr><td colspan="3">Nenhum cliente encontrado</td></tr>');
                }

                // Paginação
                $('#clientPagination').empty();
                if (response.clients.last_page > 1) {
                    for (let i = 1; i <= response.clients.last_page; i++) {
                        $('#clientPagination').append(`
                            <li class="page-item ${i === response.clients.current_page ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i}</a>
                            </li>
                        `);
                    }
                }

                $('#clientPagination a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadClients(page);
                });

                $('#clientsTable').removeClass('d-none');
                $('#clientPagination').removeClass('d-none');
            },
            error: function (response, textStatus, msg) {
                $('.loading').addClass('d-none');
            }
        });
    }

    async function validateStore(formId) {
        @if(ApiUser::hasUserFunction('release_all_business') && ApiUser::get()['business_selected'] === null)
            // Se o usuário tem permissão para enviar para todos os negócios e não tem nenhum negócio selecionado pula a validação de envios
            validateStoreOriginal(formId);
            return;
        @endif

        try {
            const form = document.getElementById(formId);
            if (!form.reportValidity())
                return;
            const formData = new FormData(form);
            formData.delete('is_entrepreneur')

            const emailRadio = document.getElementById('email');
            const whatsappRadio = document.getElementById('whatsapp');

            if (!emailRadio?.checked && !whatsappRadio?.checked) {
                alert('Você deve selecionar um tipo de campanha!');
                return;
            }

            // Verifica total de clientes selecionados
            const totalClients = parseInt(document.querySelector("#numberClients").value) || 0;
            
            if (totalClients === 0) {
                fillErrors({
                    number_clients: ['Selecione pelo menos um cliente para enviar a campanha.']
                });
                return;
            }

            // Verifica limite de envios
            const response = await fetch("{{ env('API_URL') }}/api/evaluations/business/validate-shipments", {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Erro ao verificar limite de envios');
            }

            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.msg || 'Erro ao verificar limite de envios');
            }

            const sendingType = $('input[name="sending_type"]:checked').val();
            const remainingShipments = sendingType === 'email' 
                ? data.data.remaining_email_shipments 
                : data.data.remaining_whatsapp_shipments;
            const totalShipmentsPlan = sendingType === 'email'
                ? data.data.total_email_sends_plan
                : data.data.total_whatsapp_sends_plan;
            const sendsType = sendingType === 'email'
                ? data.data.email_sends_type
                : data.data.whatsapp_sends_type;

            if (totalClients > remainingShipments) {
                const typeLabel = sendingType === 'email' ? 'Email' : 'WhatsApp';
                const freqLabel = sendsType === 'monthly' ? 'mensal' : 'diário';

                Swal.fire({
                    icon: 'error',
                    title: 'Limite de Envios Insuficiente',
                    html: `Você não possui envios de ${typeLabel} suficientes para esta campanha.<br><br>` +
                          `Total necessário: <b>${totalClients}</b><br>` +
                          `Envios restantes: <b>${remainingShipments}</b><br>` +
                          `Limite ${freqLabel} do plano: <b>${totalShipmentsPlan}</b>`,
                    confirmButtonText: 'Ok',
                    @if(ApiUser::hasPermission('dashboard'))
                    showCancelButton: true,
                    cancelButtonText: 'Ver Limites de Envio',
                    cancelButtonColor: '#3085d6'
                    @endif
                }).then((result) => {
                    @if(ApiUser::hasPermission('dashboard'))
                    if (result.dismiss === Swal.DismissReason.cancel) {
                        window.location.href = "{{ route('dashboard.index') }}";
                    }
                    @endif
                });
                return;
            }

            // Se passou pelas validações, continua com a validação original
            validateStoreOriginal(formId);

        } catch (error) {
            console.error('Erro:', error);
            Swal.fire({
                icon: 'error',
                title: 'Erro',
                text: 'Erro ao verificar disponibilidade de envios. Por favor, tente novamente.'
            });
        }
    }

    function validateStoreOriginal(formId) {
        const form = document.getElementById(formId);
        const formData = new FormData(form);
        formData.delete('is_entrepreneur')

        formData.forEach(function (value, key) {
            if (key == '_method') {
                formData.delete(key);
            }
        });

        const notificationPushSelected = $('#notification_push').is(':checked');
        const url_click = $('#url_click').val();
        const pattern = /(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/g;

        if (notificationPushSelected && url_click !== '' && !url_click.match(pattern)) {
            return alert('Você precisa digitar um link válido!');
        }

        $.ajax({
            url: `<?= env('API_URL') ?>/api/notifications/validateStore`,
            type: 'post',
            cache: false,
            contentType: false,
            processData: false,
            data: formData,
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
            },
            success: function (response) {
                fillErrors([]);
                form.submit();
            },
            error: function (response, textStatus, msg) {
                if (response.status == 422) {
                    fillErrors(response.responseJSON.errors);
                }
            }
        });
    }
</script>
