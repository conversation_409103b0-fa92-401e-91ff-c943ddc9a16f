<?php

use App\Helpers\StringMask;
use Carbon\Carbon;

?>

<div class="form-row">
    <div class="col-md-12 mb-2">
        <h6 class="style_campo_titulo">Campanha</h6>
    </div>
    <div class="col-md-12 mb-2">
        <label for="">Título</label>
        <input type="text" class="form-control style_campo_estatico"
               value="{{ $notification['title'] }}" disabled>
    </div>
    <div class="col-md-12 mb-2">
        <label for="">Descrição</label>
        <textarea
            class="form-control style_campo_estatico"
            style="width: 100%; min-height: 100px; max-height: 100px"
            disabled
        >{{ $notification['description'] }}</textarea>
    </div>
    @if (isset($notification['url_click']))
        <div class="col-md-12 mb-2">
            <label for="">Link</label>
            <div class="form-control style_campo_estatico" @readonly(true)>
                <a href="{{ $notification['url_click'] }}" target="_blank" rel="noopener noreferrer">
                    {{ $notification['url_click'] }}
                </a>
            </div>
        </div>
    @endif
    <div class="form-row col-md-12 mb-2">
        <div class="col-md-4">
            <label for="">Nº clientes</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ $notification['number_clients'] }}" disabled>
        </div>
        <div class="col-md-4">
            <label for="">
                Nº sucessos
                @if ($notification['sending_type'] == 'notification_push')
                    <i class="fas fa-info-circle d-inline"
                       title="A soma dos envios que ocorreram com sucesso, mais os envios que falharam, podem ser diferentes da quantidade total de clientes, pois pode haver 0 ou mais dispositivos celulares por cliente."></i>
                @endif
            </label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ $notification['sending_number_success'] }}" id="sending_number_success" disabled>
        </div>
        <div class="col-md-4">
            <label for="">
                Nº falhas
                @if ($notification['sending_type'] == 'notification_push')
                    <i class="fas fa-info-circle d-inline"
                       title="A soma dos envios que ocorreram com sucesso, mais os envios que falharam, podem ser diferentes da quantidade total de clientes, pois pode haver 0 ou mais dispositivos celulares por cliente."></i>
                @endif
            </label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ $notification['sending_number_failed'] }}"
                   id="sending_number_failed"
                   disabled
            >
        </div>
    </div>
    <div class="form-row col-md-12 mb-2">
        <div class="col-md-4">
            <label for="">Data criação</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ isset($notification['created_at']) ? Carbon::parse($notification['created_at'])->format('d/m/Y H:i:s') : 'Não informada' }}"
                   - disabled
            >
        </div>
        <div class="col-md-4">
            <label for="">Data envio</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ isset($notification['started_at']) ? Carbon::parse($notification['started_at'])->format('d/m/Y H:i:s') : 'Não informada' }}"
                   disabled
            >
        </div>
        <div class="col-md-4">
            <label for="">Data finalização</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ isset($notification['finished_at']) ? Carbon::parse($notification['finished_at'])->format('d/m/Y H:i:s') : 'Não informada' }}"
                   disabled>
        </div>
    </div>
    <div class="form-row col-md-12 mb-2">
        <div class="col-md-4">
            @php
                $castNotificationType = ['email' => 'Email',
                                        'whatsapp' => 'Whatsapp',
                                        'notification_push' => 'Aplicativo'];
                $notificationType = $castNotificationType[$notification['sending_type']];
            @endphp
            <label for="">Tipo de envio</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ $notificationType }}"
                   disabled>
        </div>
        <div class="col-md-4">
            @php
                if ($notification['canceled_at'] != null) {
                    $notificationStatus = "Cancelada";
                }else if ($notification['created_at'] != null &&
                    $notification['finished_at'] == null &&
                    $notification['started_at'] == null
                ) {
                    $notificationStatus = "Pendente";
                } elseif ($notification['started_at'] != null && $notification['finished_at'] == null) {
                    $notificationStatus = "Enviando";
                } elseif ($notification['started_at'] != null && $notification['finished_at'] != null) {
                    $notificationStatus = "Finalizada";
                } else {
                    $notificationStatus = "Desconhecido";
                }
            @endphp
            <label for="">Status</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ $notificationStatus }}"
                   disabled>
        </div>
        @if (ApiUser::get()['business_selected'] == null)
            <div class="col-md-4">
                <label for="">Negócio</label>
                <input type="text" class="form-control style_campo_estatico"
                       value="{{ $notification['business']['name'] ?? 'Sem negócio' }}"
                       disabled>
            </div>
        @endif
    </div>
    <div class="col-md-12">
        <h6 class="style_campo_titulo">Clientes</h6>
    </div>
    <div class="form-group my-2 col-md-12">
        <hr id="clientsSeparator"/>
        <form id="search-client-form" onsubmit="loadClients()" method="GET">
            @csrf
            <div class="d-flex flex-column flex-sm-row" style="gap: 4px">
                <input type="text" id="clientsSearch" class="form-control mb-sm-0 w-100"
                       placeholder="Pesquisar cliente..." style="flex: 4;">
                <select class="form-control mb-2 mb-sm-0 flex-grow-1" name="shipping_status"
                        aria-describedby="shipping_status" style="flex: 2" id="shippingStatus_desktop">
                    <option value="all" selected>Todos</option>
                    <option value="waiting">Aguardando</option>
                    <option value="sent">Enviado</option>
                    <option value="failed">Falhado</option>
                    <option value="canceled">Cancelado</option>
                </select>
                <div class="input-group-append mb-2 mb-sm-0">
                    <button class="btn btn-light w-100" type="submit" id="search-notification-modal">
                        <img class="card-img-left example-card-img-responsive"
                             src="{{url('icon/icon_search.png')}}" width="20px"/>
                    </button>
                </div>
        </form>
    </div>
    <div class="table-responsive-sm">
        <table class="table" style="white-space: nowrap;" id="clientsTable">
            <thead>
            <tr>
                <th style="width: 40%">Nome</th>
                <th style="width: 40%">CPF</th>
                <th style="width: 20%">Status</th>
            </tr>
            </thead>
            <tbody id="clientsTableBody">
            <div class="d-flex justify-content-center loading pb-1 d-none">
                <div class="spinner-border loading" role="status">
                    <span class="sr-only loading">Loading...</span>
                </div>
            </div>
            <!-- Clientes serão carregados aqui -->
            </tbody>
        </table>
        <small id="error-clients_id" class="p-1 text-danger"></small>
    </div>
    <nav>
        <ul class="pagination" id="clientPagination">
            <!-- Paginação será carregada aqui -->
        </ul>
    </nav>
</div>
</div>
<script text="text/javascript">
    document.addEventListener('openShowNotification', function () {
        loadClients();
    });

    showHideNoClientSelectedMessage()

    function showHideNoClientSelectedMessage() {
        if ($('#clients_selected').children().length === 0) {
            $('#clientsSelectedLabel').addClass('d-none');
            $('#clientsSeparator').addClass('d-none');
        } else {
            $('#clientsSelectedLabel').removeClass('d-none');
            $('#clientsSeparator').removeClass('d-none');
        }
    }

    function loadClients(page = 1, show_defaut_option = true) {
        event.preventDefault();
        const searchQuery = $('#clientsSearch').val();
        const shippingStatusQuery = $('#shippingStatus_desktop').val();
        let notificationId = {{$notification['id']}};

        function formatCPF(cpf) {
            return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        }

        const castShippingStatus = {
            'sent': {
                status: "Enviado",
                badgeClass: 'badge bg-success'
            },
            'waiting': {
                status: "Aguardando",
                badgeClass: 'badge bg-warning'
            },
            'failed': {
                status: "Falhou",
                badgeClass: 'badge bg-danger'
            },
            'canceled': {
                status: "Cancelado",
                badgeClass: 'badge bg-danger'
            }
        }

        $.ajax({
            url: `<?= env('API_URL') ?>/api/notifications/clients/${notificationId}?search=${searchQuery}&shipping_status=${shippingStatusQuery}&page=${page}&per_page=5`,
            type: 'get',
            data: {
                search: searchQuery,
                shipping_status: shippingStatusQuery,
                page: page,
                per_page: 5,
            },
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#clientsTable').addClass('d-none');
                $('#clientPagination').addClass('d-none');
                $('.loading').removeClass('d-none');
            },
            success: function (response) {
                $('.loading').addClass('d-none');

                $('#clientsTableBody').empty();
                const notificationClients = response.clients.data;
                if (notificationClients.length > 0) {
                    $.each(notificationClients, function (index, notificationClient) {
                        $('#clientsTableBody').append(`
                        <tr>
                            <td>
                                ${notificationClient.client.name}
                            </td>
                            <td>
                                ${formatCPF(notificationClient.client.cpf)}
                            </td>
                            <td>
                                <span class="${castShippingStatus[notificationClient.shipping_status].badgeClass} p-2 text-light" style="width:100px" >
                                    ${castShippingStatus[notificationClient.shipping_status].status}
                                </span>
                            </td>
                        </tr>`);
                    });
                } else {
                    $('#clientsTableBody').append('<tr><td colspan="3">Nenhum cliente encontrado</td></tr>');
                }

                // Paginação
                $('#clientPagination').empty();
                if (response.clients.last_page > 1) {
                    for (let i = 1; i <= response.clients.last_page; i++) {
                        $('#clientPagination').append(`
                            <li class="page-item ${i === response.clients.current_page ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i}</a>
                            </li>
                        `);
                    }
                }

                $('#clientPagination a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadClients(page);
                });

                $('#clientsTable').removeClass('d-none');
                $('#clientPagination').removeClass('d-none');
            },
            error: function (response, textStatus, msg) {
                $('.loading').addClass('d-none');
            }
        });
    }
</script>
