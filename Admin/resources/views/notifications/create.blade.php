@extends('layouts.app', ['activePage' => 'notifications'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-md-12" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex justify-content-between align-items-center"
                                 style="margin-top: 9px; margin-bottom:6px">
                                <h5 class="card-title mb-0"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Cadastrar campanha</h5>
                            </div>
                            <div class="col-3">
                                <span style="color:red">* Campos obrigatórios</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('notifications.store') }}" id="form-create-notification" method="POST"
                              onsubmit="event.preventDefault();">
                            @csrf
                            @include('notifications.form')
                            <div class="modal-footer d-flex flex-column align-items-start w-100">
                                <div class="d-flex justify-content-end w-100">
                                    <button id="submit-button" type="button" class="btn btn-success"
                                            onclick="validateStore('form-create-notification');">Adicionar campanha
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if($message = Session::get('danger'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>

    <script>
    </script>

@endsection
