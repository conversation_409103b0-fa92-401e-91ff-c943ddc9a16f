<div>
    <div class="text-center text-md py-3 border-bottom" style="border-bottom-width: 5px; border-bottom-color: {{ $mainColor ?? '#FBBC05' }};">
        @if ($systemLogo)
            <img src="{{ $systemLogo }}" alt="{{ config('app.name') }} Logo" class="img-fluid system-logo" style="max-width: 150px;">
        @else
            <h2 class="text-dark">{{ config('app.name') }}</h2>
        @endif
    </div>

    <div class="p-4">
        <div class="business-logo-title text-center mb-4">
            @if($businessLogo)
                <img src="{{ $businessLogo }}" alt="{{ $businessName }} Logo" class="img-fluid business-logo" style="max-width: 60px;">
            @endif
            <h5 class="font-weight-bold text-dark">{{ $businessName ?? config('app.name') }}</h4>
        </div>

        <h5 class="text-start" style="color: {{ $mainColor ?? '#FBBC05' }}; margin-bottom: 0px;">{{ $notificationTitle }}</h5>
        <p class="text-justify" style="font-size: 1rem; white-space: pre-line">
            {{ e($notificationDescription) }}
            <p class="text-justify">Atenciosamente,<br>{{ $businessName ?? config('app.name') }}</p>

            @if($urlClick)
                <div class="text-center my-2">
                    <a href="{{ $urlClick }}" target="_blank" rel="noopener noreferrer" class="btn btn-primary rounded px-2 py-2 font-weight-bold">
                        Abrir Campanha
                    </a>
                </div>
            @endif
        </p>
    </div>
</div>
