<x-modal.modal_confirm_action 
    id="{{$modalId}}" 
    title="Deletar cliente"
    onSubmit="$('#client-delete-{{$client['id']}}').submit()"
>
    <form action="{{ $isAdminBusinessSelected ? route('clients.adminDestroy') : route('clients.destroy', $client['id']) }}" id="client-delete-{{$client['id']}}" method="POST">
        @csrf
        @method('delete')
        <div class="row">
            <div class="col">
                @if (ApiUser::get()["business_selected"] != null && !$isAdminBusinessSelected)
                    Você deseja mesmo deletar <b>{{ $client['name'] }}</b> do negócio: 
                    <b>{{ ApiUser::get()["business_selected"]['name'] }}</b>?
                @elseif ($isAdminBusinessSelected)
                    @if(isset($client['client']))
                        <input type="hidden" id="client_id" name="client_id" value="{{$client['client']['id']}}">
                    @endif

                    @if(isset($client['employee']))
                        <input type="hidden" id="employee_id" name="employee_id" value="{{$client['employee']['id']}}">
                    @endif

                    @if(isset($client['seller']))
                        <input type="hidden" id="seller_id" name="seller_id" value="{{$client['seller']['id']}}">
                    @endif
                    
                    <div class="mb-3">
                        <label for="client_type">Escolha o perfil a deletar</label>
                        <select class="form-select form-control" name="client_type" id="client_type_select">
                            @php
                                // ordem de prioridade
                                $typesPriorityOrder = ['admin', 'client', 'employee', 'seller'];

                                $canSeeSellers = ApiUser::hasUserFunction('sellers') ?? false;
                                $canSeeClients = ApiUser::hasUserFunction('clients') ?? false;

                                if (!$canSeeSellers) {
                                    unset($typesPriorityOrder['seller']);
                                }

                                // Filtra e ordena os tipos conforme a prioridade
                                $orderedTypes = collect($typesPriorityOrder)
                                    ->filter(fn($type) => isset($client['types'][$type]) && $client['types'][$type]['hasType'])
                                    ->mapWithKeys(fn($type) => [$type => $client['types'][$type]]);
                            @endphp
                            @foreach ($orderedTypes as $type => $details)
                                <option value="{{ $type }}" @if ($loop->first) selected @endif>{{ $details['name'] }}</option>
                            @endforeach
                        </select>
                    </div>

                    <p id="delete-message">
                        @php
                            $selectedType = $orderedTypes->keys()->first();
                            $mapType = [
                                'admin' => 'Administrador',
                                'client' => 'Cliente',
                                'employee' => 'Funcionário',
                                'seller' => 'Vendedor',
                            ];
                        @endphp
                        Tem certeza que quer deletar o <b>{{ $mapType[$selectedType] ?? '' }}</b> <b>{{ $client['name'] }}</b> 
                        @if ($selectedType === 'seller')
                            ?
                        @else
                            de todos os negócios?
                        @endif
                    </p>
                @endif
            </div>
        </div>
    </form>
</x-modal.modal_confirm_action>


<script>
    $(document).on('shown.bs.modal', '#{{$modalId}}', function () {
        const modal = $(this);
        const clientName = @json($client['name']);
        const mapType = {
            'admin': "Administrador",
            'seller': "Vendedor",
            'employee': "Funcionário",
            'client': "Cliente"
        }

        modal.find('#client_type_select').off('change').on('change', function() {
            const typeSelected = $(this).val();
            let message = '';

            message = `Tem certeza que quer deletar o <b>${mapType[typeSelected] ? mapType[typeSelected] : ''}</b> <b>${clientName}</b> 
                ${typeSelected === 'seller' ?  '?' : 'de todos os negócios'}
            `;

            modal.find('#delete-message').html(message);
        });
    });
</script>
