@extends('layouts.app', ['activePage' => 'clients'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-md-8" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex justify-content-between align-items-center"
                                 style="margin-top: 9px; margin-bottom:6px">
                                <div class="d-flex align-items-center ">
                                    @if (isset($redirectTo))
                                        <a href="{{route($redirectTo)}}" class="mx-2" id="back-button" style="cursor: pointer">
                                            <i class="fas fa-chevron-left fa-fw"></i>
                                        </a>
                                    @else
                                        <a href="{{route('clients.index')}}" class="mx-2" id="back-button" style="cursor: pointer">
                                            <i class="fas fa-chevron-left fa-fw"></i>
                                        </a>
                                    @endif
                                    <h5 class="card-title mb-0" style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                        Atualizar cliente
                                    </h5>
                                </div>
                            </div>
                            <div class="col-3">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('clients.update', $client['id']) }}" id="form-update-client"
-                              method="POST" onsubmit="event.preventDefault();">
                            @csrf
                            @method('put')
                            @include('clients.form')
                            <div class="modal-footer">
                                <div style="padding: 0 0 10px 4px;">
                                    <input type="checkbox" class="form-check-input" id="term-checkbox" name="term"
                                           required>
                                    <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os
                                        dados passados de acordo com o termo em anexo <a
                                            href="{{route('data-processing-agreement', ['user_type' => 'client'])}}"
                                            target="_blank">(clique aqui para ler o termo).</a></label>
                                </div>
                                <button id="submit-button" disabled type="button" onclick="submitUpdate();"
                                        class="btn btn-success">Atualizar cliente
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if($message = Session::get('danger'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
@endsection
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js"></script>
<script type="text/javascript">

    function submitUpdate() {
        const email = document.getElementById('email').value;
        const oldEmail = "<?= $client['email'] ?>";
        const formId = 'form-update-client';
        const clientId = "<?= $client['id'] ?>";
        const emailChange = (email != oldEmail);

        validateStoreUpdate(formId, clientId, emailChange);
    }
</script>

<script>

    $(document).ready(function () {
        let botaoSalvarClicado = false;
        let business_alterados = false;

        const botaoSalvar = $('#submit-button');
        botaoSalvar.click(function (e) {
            e.preventDefault();
            botaoSalvarClicado = true;
            $('#form-update-client').submit();
        });

        const formulario = document.getElementById('form-update-client');

        const campos = formulario.querySelectorAll('input, textarea, select');

        const valoresIniciais = Array.from(campos).reduce((obj, campo) => {
            obj[campo.name] = campo.value;
            if (campo.name === 'highlight') {
                obj[campo.name] = document.getElementById('highlight-yes').checked ? '1' : '0';
            }
            if (campo.name === 'release_all_business') {
                obj[campo.name] = document.getElementById('release_all_business').checked ? '1' : '0';
            }
            return obj;
        }, {});

        function validateBusinesses() {
            let old = oldBusinessesIds;
            let selected = businessesSelectedIds;
            //verifica se os arrays são diferentes
            if (old.length !== selected.length) {
                business_alterados = true;
                return;
            }
            for (let i = 0; i < old.length; i++) {
                if (!selected.includes(old[i])) {
                    business_alterados = true;
                    return;
                }
            }
            business_alterados = false;
        }

        function mudou() {
            for (let i = 0; i < campos.length; i++) {
                const campo = campos[i];
                if (campo.name === 'cep') {
                    let cep = campo.value.replace('-', '');
                    let valorInicial = valoresIniciais[campo.name].replace('-', '');
                    if (cep !== valorInicial) {
                        return true;
                    } else {
                        continue;
                    }
                }

                if (campo.name === 'businessesIds[]') {
                    continue;
                }

                if (campo.value !== valoresIniciais[campo.name]) {
                    return true;
                }
            }
            return false;
        }

        function exibirAlertaSaida(event) {
            if (mudou() && botaoSalvarClicado === false) {
                event.preventDefault();
                var mensagem = 'Tem certeza que deseja sair desta página?';
                event.returnValue = mensagem;
                return mensagem;
            } else {
                botaoSalvarClicado = false;
            }
        }

        window.addEventListener('beforeunload', exibirAlertaSaida);

        $("#term-checkbox").click(showHideFormButton);

        function showHideFormButton() {
            if ($("#term-checkbox").is(":checked")) {
                $("#submit-button").prop("disabled", false);
            } else {
                $("#submit-button").prop("disabled", true);
            }
        }
    });
</script>
