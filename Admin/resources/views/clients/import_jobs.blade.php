@extends('layouts.app', ['activePage' => 'clients'])

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="card card_conteudo">
                <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-between gap-3">
                                <div class="d-flex align-items-center">
                                    <a href="{{route('clients.index')}}" class="mx-2" style="cursor: pointer">
                                        <i class="fas fa-chevron-left fa-fw"></i>
                                    </a>
                                    <h5 class="card-title mb-0" style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                        Importações de Clientes
                                    </h5>
                                </div>
                                <div>
                                    <a href="{{ route('clients.import.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Nova Importação
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3 filter-container">
                        <form action="{{ route('clients.import') }}" method="GET" id="filterForm">
                            <div class="card shadow-sm border-0 p-2">
                                <div class="row g-2">
                                    <div class="col-lg-6 col-md-12 p-0 pr-2">
                                        <label for="file_name" class="form-label text-muted small mb-1">Nome do Arquivo</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-file-alt text-secondary"></i>
                                            </span>
                                            <input type="text"
                                                   class="form-control"
                                                   name="file_name"
                                                   id="file_name"
                                                   placeholder="Buscar por nome do arquivo"
                                                   value="{{ request('file_name') }}">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 p-0 pr-2">
                                        <label for="start_date" class="form-label text-muted small mb-1">Data Inicial</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-calendar-alt text-secondary"></i>
                                            </span>
                                            <input type="date"
                                                   class="form-control"
                                                   name="start_date"
                                                   id="start_date"
                                                   value="{{ request('start_date', now()->subDays(30)->format('Y-m-d')) }}">
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 p-0 pr-2">
                                        <label for="end_date" class="form-label text-muted small mb-1">Data Final</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-calendar-alt text-secondary"></i>
                                            </span>
                                            <input type="date"
                                                   class="form-control"
                                                   name="end_date"
                                                   id="end_date"
                                                   value="{{ request('end_date', now()->format('Y-m-d')) }}">
                                        </div>
                                    </div>
                                    <div class="col-12 d-flex gap-2 mt-2 p-0">
                                        <button type="submit" class="btn btn-primary mr-2">
                                            <i class="fas fa-search"></i> Buscar
                                        </button>
                                        <a href="{{ route('clients.import') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i> Limpar Filtros
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Ações</th>
                                    <th>Arquivo</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Processados</th>
                                    <th>Falhas</th>
                                    <th>Data de Envio</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($batches as $batch)
                                    <tr data-batch-id="{{ $batch['id'] }}">
                                        <td>
                                            <button class="btn btn-secondary"
                                                    onclick="$('#detailsModal{{ $batch['id'] }}').modal('show')"
                                                    style="margin-right: 10px">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                            </button>
                                        </td>
                                        <td>{{ $batch['file_name'] }}</td>
                                        <td data-batch-status>
                                            @switch($batch['status'])
                                                @case('pending')
                                                    <span class="badge bg-secondary">Pendente</span>
                                                    @break
                                                @case('processing')
                                                    <span class="badge bg-info">Processando</span>
                                                    @break
                                                @case('completed')
                                                    <span class="badge bg-success">Concluído</span>
                                                    @break
                                                @case('failed')
                                                    <span class="badge bg-danger">Falhou</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">{{ $batch['status'] }}</span>
                                            @endswitch
                                        </td>
                                        <td>{{ $batch['total_records'] }}</td>
                                        <td>{{ $batch['processed_records'] }}</td>
                                        <td>{{ $batch['failed_records'] }}</td>
                                        <td>{{ \Carbon\Carbon::parse($batch['created_at'])->setTimezone('America/Recife')->format('d/m/Y H:i:s') }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <em>Nenhuma importação encontrada</em>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    {{ $batches->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modais -->
@foreach($batches as $batch)
<div class="modal fade" id="detailsModal{{ $batch['id'] }}" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel{{ $batch['id'] }}">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="detailsModalLabel{{ $batch['id'] }}">
                    <i class="fas fa-file-import mr-2"></i> Detalhes da Importação
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Fechar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="card bg-light mb-4 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary mb-0">
                                <i class="fas fa-info-circle mr-1"></i> Informações do Lote
                            </h6>
                            <span class="badge badge-light border">
                                <i class="fas fa-file-excel text-success mr-1"></i>
                                <a href="javascript:void(0)" onclick="downloadFile({{ $batch['id'] }}, '{{ $batch['file_name'] }}')" class="text-primary">
                                    {{ $batch['file_name'] }}
                                </a>
                            </span>
                        </div>
                        
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="far fa-calendar-alt text-secondary mr-2"></i>
                                    <span>{{ \Carbon\Carbon::parse($batch['created_at'])->setTimezone('America/Recife')->format('d/m/Y H:i:s') }}</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tag text-secondary mr-2"></i>
                                    <span id="status-badge-{{ $batch['id'] }}" data-status>
                                        @switch($batch['status'])
                                            @case('pending')
                                                <span class="badge badge-pill bg-secondary text-white px-3">Pendente</span>
                                                @break
                                            @case('processing')
                                                <span class="badge badge-pill bg-info text-white px-3">Processando</span>
                                                @break
                                            @case('completed')
                                                <span class="badge badge-pill bg-success text-white px-3">Concluído</span>
                                                @break
                                            @case('failed')
                                                <span class="badge badge-pill bg-danger text-white px-3">Falhou</span>
                                                @break
                                        @endswitch
                                    </span>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-1 d-flex justify-content-between">
                                    <strong id="total-records-{{ $batch['id'] }}">
                                        <i class="fas fa-chart-pie text-primary"></i> Total: {{ $batch['total_records'] }}
                                    </strong>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div id="progress-success-{{ $batch['id'] }}"
                                         class="progress-bar bg-success"
                                         role="progressbar"
                                         style="width: {{ $batch['total_records'] > 0 ? ($batch['processed_records'] / $batch['total_records'] * 100) : 0 }}%"
                                         aria-valuenow="{{ $batch['processed_records'] }}"
                                         aria-valuemin="0"
                                         aria-valuemax="{{ $batch['total_records'] }}">
                                    </div>
                                    <div id="progress-danger-{{ $batch['id'] }}"
                                         class="progress-bar bg-danger"
                                         role="progressbar"
                                         style="width: {{ $batch['total_records'] > 0 ? ($batch['failed_records'] / $batch['total_records'] * 100) : 0 }}%"
                                         aria-valuenow="{{ $batch['failed_records'] }}"
                                         aria-valuemin="0"
                                         aria-valuemax="{{ $batch['total_records'] }}">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between small">
                                    <span id="processed-count-{{ $batch['id'] }}">
                                        <i class="fas fa-check-circle text-success"></i> {{ $batch['processed_records'] }} processados
                                    </span>
                                    <span id="failed-count-{{ $batch['id'] }}">
                                        <i class="fas fa-times-circle text-danger"></i> {{ $batch['failed_records'] }} falhas
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="text-primary mb-0">
                            <i class="fas fa-list mr-1"></i> Registros
                        </h6>
                        <div class="form-inline">
                            <div class="input-group input-group-sm">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                </div>
                                <input type="text" class="form-control form-control-sm" id="searchInput{{ $batch['id'] }}" 
                                       placeholder="Buscar registros..." onkeyup="searchRecords{{ $batch['id'] }}(this.value)">
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover border">
                            <thead class="thead-light">
                                <tr>
                                    <th>Linha</th>
                                    <th>Nome</th>
                                    <th>CPF</th>
                                    <th>Email</th>
                                    <th>Telefone</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTable{{ $batch['id'] }}">
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="sr-only"></span>
                                            </div>
                                            <div class="mt-2 text-muted">Carregando registros...</div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <nav>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted small" id="pageInfo{{ $batch['id'] }}"></div>
                            <ul class="pagination pagination-sm justify-content-center mt-2 mb-0" id="itemsPagination{{ $batch['id'] }}"></ul>
                        </div>
                    </nav>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                    <i class="fas fa-times-circle"></i> Fechar
                </button>
                <button type="button" class="btn btn-primary btn-sm" onclick="loadItems{{ $batch['id'] }}()">
                    <i class="fas fa-sync-alt"></i> Atualizar
                </button>
            </div>
        </div>
    </div>
</div>
@endforeach
@endsection

@push('js')
<script>
async function downloadFile(batchId, fileName) {
    try {
        const response = await fetch(`{{ url('clients/import') }}/${batchId}/download`);
        if (!response.ok) {
            throw new Error('Erro ao gerar URL de download');
        }
        
        const data = await response.json();
        if (data.url) {
            window.open(data.url, '_blank');
        } else {
            throw new Error('URL de download não disponível');
        }
    } catch (error) {
        console.error('Erro ao baixar arquivo:', error);
        alert('Erro ao baixar arquivo: ' + error.message);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Configurações globais
    const itemsPerPage = 10;

    // Controle de datas do filtro
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');

    if (startDate && endDate) {
        startDate.addEventListener('change', function() {
            if (endDate.value && this.value > endDate.value) {
                endDate.value = this.value;
            }
            endDate.min = this.value;
        });

        endDate.addEventListener('change', function() {
            if (startDate.value && this.value < startDate.value) {
                startDate.value = this.value;
            }
            startDate.max = this.value;
        });

        // Inicializa os valores min/max
        if (startDate.value) {
            endDate.min = startDate.value;
        }
        if (endDate.value) {
            startDate.max = endDate.value;
        }
    }

    // Funções utilitárias
    function formatCPF(cpf) {
        // Remove caracteres não numéricos
        cpf = cpf.replace(/\D/g, '');
        
        // Verifica se tem 11 dígitos
        if (cpf.length !== 11) return cpf;
        
        // Formata como XXX.XXX.XXX-XX
        return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    }

    function formatPhone(phone) {
        // Remove caracteres não numéricos
        phone = phone.replace(/\D/g, '');
        
        // Se não tiver pelo menos 10 dígitos, retorna o original
        if (phone.length < 10) return phone;
        
        // Formata celular (11 dígitos) ou fixo (10 dígitos)
        if (phone.length === 11) {
            return phone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
        }
        return phone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }

    function getStatusBadge(status, errorMessage = '') {
        const badges = {
            completed: '<span class="badge bg-success text-white" data-toggle="tooltip" title="Cliente importado com sucesso"><i class="fas fa-check-circle mr-1"></i> Importado</span>',
            failed: `<span class="badge bg-danger text-white" style="cursor: help" data-toggle="tooltip" title="${errorMessage || 'Erro na importação'}"><i class="fas fa-exclamation-circle mr-1"></i> Falha</span>`,
            processing: '<span class="badge bg-info text-white" data-toggle="tooltip" title="Processando importação"><i class="fas fa-spinner fa-spin mr-1"></i> Processando</span>',
            pending: '<span class="badge bg-secondary text-white" data-toggle="tooltip" title="Aguardando processamento"><i class="fas fa-clock mr-1"></i> Pendente</span>'
        };
        
        return badges[status] || `<span class="badge bg-secondary text-white">${status}</span>`;
    }

    function initTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    // Inicializa os tooltips globalmente
    initTooltips();

    // Inicializa o estado dos modais
    const modalStates = {};
    
    // Para cada modal
    @foreach($batches as $batch)
        modalStates['{{ $batch['id'] }}'] = {
            items: [],
            currentPage: 1
        };

        // Registra o evento de abrir modal
        $('#detailsModal{{ $batch['id'] }}').on('show.bs.modal', function () {
            loadItems{{ $batch['id'] }}();
        });

        // Função de carregar itens
        window.loadItems{{ $batch['id'] }} = async function() {
            const tableBody = document.getElementById('itemsTable{{ $batch['id'] }}');
            try {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="d-flex flex-column align-items-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only"></span>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;

                const response = await fetch('{{ route("clients.import.show", ["id" => $batch["id"]]) }}');
                if (!response.ok) {
                    throw new Error('Erro ao carregar dados');
                }
                
                const data = await response.json();
                
                if (!data || (!data.items && !data.batch)) {
                    throw new Error('Dados inválidos recebidos do servidor');
                }

                // Atualiza os dados do lote
                if (data.batch) {
                    const batchData = data.batch;
                    const batchId = '{{ $batch['id'] }}';

                    // Atualiza o status
                    const statusElement = document.getElementById(`status-badge-${batchId}`);
                    if (statusElement) {
                        const newStatusHtml = (() => {
                            switch(batchData.status) {
                                case 'pending': return '<span class="badge badge-pill bg-secondary text-white px-3">Pendente</span>';
                                case 'processing': return '<span class="badge badge-pill bg-info text-white px-3">Processando</span>';
                                case 'completed': return '<span class="badge badge-pill bg-success text-white px-3">Concluído</span>';
                                case 'failed': return '<span class="badge badge-pill bg-danger text-white px-3">Falhou</span>';
                                default: return `<span class="badge badge-pill bg-secondary text-white px-3">${batchData.status}</span>`;
                            }
                        })();
                        statusElement.innerHTML = newStatusHtml;
                        
                        // Atualiza o status na tabela principal
                        const tableStatusElement = document.querySelector(`tr[data-batch-id="${batchId}"] td[data-batch-status]`);
                        if (tableStatusElement) {
                            const tableStatusHtml = (() => {
                                switch(batchData.status) {
                                    case 'pending': return '<span class="badge bg-secondary">Pendente</span>';
                                    case 'processing': return '<span class="badge bg-info">Processando</span>';
                                    case 'completed': return '<span class="badge bg-success">Concluído</span>';
                                    case 'failed': return '<span class="badge bg-danger">Falhou</span>';
                                    default: return `<span class="badge bg-secondary">${batchData.status}</span>`;
                                }
                            })();
                            tableStatusElement.innerHTML = tableStatusHtml;
                        }
                    }

                    // Atualiza as barras de progresso
                    const progressBarSuccess = document.getElementById(`progress-success-${batchId}`);
                    const progressBarDanger = document.getElementById(`progress-danger-${batchId}`);
                    const totalRecords = batchData.total_records || 0;
                    
                    if (progressBarSuccess && progressBarDanger) {
                        const successWidth = totalRecords > 0 ? (batchData.processed_records / totalRecords * 100) : 0;
                        const failedWidth = totalRecords > 0 ? (batchData.failed_records / totalRecords * 100) : 0;
                        
                        progressBarSuccess.style.width = `${successWidth}%`;
                        progressBarSuccess.setAttribute('aria-valuenow', batchData.processed_records);
                        progressBarSuccess.setAttribute('aria-valuemax', totalRecords);
                        
                        progressBarDanger.style.width = `${failedWidth}%`;
                        progressBarDanger.setAttribute('aria-valuenow', batchData.failed_records);
                        progressBarDanger.setAttribute('aria-valuemax', totalRecords);
                    }

                    // Atualiza os contadores
                    const elements = {
                        processed: document.getElementById(`processed-count-${batchId}`),
                        failed: document.getElementById(`failed-count-${batchId}`),
                        total: document.getElementById(`total-records-${batchId}`)
                    };

                    if (elements.processed) {
                        elements.processed.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${batchData.processed_records} processados`;
                    }
                    if (elements.failed) {
                        elements.failed.innerHTML = `<i class="fas fa-times-circle text-danger"></i> ${batchData.failed_records} falhas`;
                    }
                    if (elements.total) {
                        elements.total.innerHTML = `<i class="fas fa-chart-pie text-primary"></i> Total: ${batchData.total_records}`;
                    }
                }

                modalStates['{{ $batch['id'] }}'].items = data.items || data.batch.items;
                renderItems{{ $batch['id'] }}();
            } catch (error) {
                console.error('Erro ao carregar items:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-danger">
                            <div class="d-flex flex-column align-items-center">
                                <p class="mb-2">Erro ao carregar registros: ${error.message}</p>
                                <button class="btn btn-sm btn-outline-primary" onclick="loadItems{{ $batch['id'] }}()">
                                    <i class="fas fa-sync-alt"></i> Tentar novamente
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }
        };

        // Função de renderizar itens
        window.renderItems{{ $batch['id'] }} = function() {
            const state = modalStates['{{ $batch['id'] }}'];
            const tableBody = document.getElementById('itemsTable{{ $batch['id'] }}');
            const start = (state.currentPage - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            const pageItems = state.items.slice(start, end);
            const pageInfo = document.getElementById('pageInfo{{ $batch['id'] }}');
            
            // Atualiza informação de página
            if (state.items.length > 0) {
                pageInfo.innerHTML = `Exibindo ${start + 1}-${Math.min(end, state.items.length)} de ${state.items.length} registros`;
            } else {
                pageInfo.innerHTML = '';
            }

            if (pageItems.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="d-flex flex-column align-items-center">
                                <i class="fas fa-folder-open text-muted mb-2" style="font-size: 2.5rem;"></i>
                                <p class="text-muted">Nenhum registro encontrado</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tableBody.innerHTML = pageItems.map(item => {
                const rawData = item.raw_data || {};
                let url = '{{ route("clients.show", "id") }}';
                url = url.replace('id', item.client ? item.client.id : '');
                return `
                    <tr class="${item.status === 'failed' ? 'table-danger' : ''}">
                        <td>${item.row_number || '-'}</td>
                        <td>${rawData.name ? rawData.name : '-'}</td>
                        <td>${rawData.cpf ? formatCPF(rawData.cpf) : '-'}</td>
                        <td>${rawData.email ? rawData.email : '-'}</td>
                        <td>${rawData.phone ? formatPhone(rawData.phone) : '-'}</td>
                        <td>
                            ${getStatusBadge(item.status, item.error_message)}
                        </td>
                        <td>
                            ${item.status === 'completed' && item.client ?
                                `<a href="${url}"
                                    target="_blank"
                                    class="btn btn-primary btn-sm"
                                    title="Ver Cliente"
                                    data-toggle="tooltip">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>` :
                                '-'
                            }
                        </td>
                    </tr>
                `;
            }).join('');

            renderPagination{{ $batch['id'] }}();
            initTooltips();
        };
        
        // Adicionar função de busca
        window.searchRecords{{ $batch['id'] }} = function(query) {
            if (!modalStates['{{ $batch['id'] }}'].originalItems) {
                modalStates['{{ $batch['id'] }}'].originalItems = [...modalStates['{{ $batch['id'] }}'].items];
            }
            
            if (!query.trim()) {
                modalStates['{{ $batch['id'] }}'].items = [...modalStates['{{ $batch['id'] }}'].originalItems];
            } else {
                query = query.toLowerCase();
                modalStates['{{ $batch['id'] }}'].items = modalStates['{{ $batch['id'] }}'].originalItems.filter(item => {
                    const rawData = item.raw_data || {};
                    return (
                        (rawData.name && rawData.name.toLowerCase().includes(query)) ||
                        (rawData.cpf && rawData.cpf.toLowerCase().includes(query)) ||
                        (rawData.email && rawData.email.toLowerCase().includes(query)) ||
                        (rawData.phone && rawData.phone.toLowerCase().includes(query))
                    );
                });
            }
            
            modalStates['{{ $batch['id'] }}'].currentPage = 1;
            renderItems{{ $batch['id'] }}();
        };

        // Função de renderizar paginação
        window.renderPagination{{ $batch['id'] }} = function() {
            const state = modalStates['{{ $batch['id'] }}'];
            const totalPages = Math.ceil(state.items.length / itemsPerPage);
            const pagination = document.getElementById('itemsPagination{{ $batch['id'] }}');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let html = '';
            
            // Botão anterior
            html += `
                <li class="page-item ${state.currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage{{ $batch['id'] }}(${state.currentPage - 1}); return false;" aria-label="Anterior">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            `;

            // Determinar quais páginas mostrar
            let startPage = Math.max(1, state.currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }

            // Primeira página
            if (startPage > 1) {
                html += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage{{ $batch['id'] }}(1); return false;">1</a>
                    </li>
                `;
                if (startPage > 2) {
                    html += '<li class="page-item disabled"><a class="page-link">...</a></li>';
                }
            }

            // Páginas intermediárias
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === state.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage{{ $batch['id'] }}(${i}); return false;">${i}</a>
                    </li>
                `;
            }

            // Última página
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += '<li class="page-item disabled"><a class="page-link">...</a></li>';
                }
                html += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage{{ $batch['id'] }}(${totalPages}); return false;">${totalPages}</a>
                    </li>
                `;
            }

            // Botão próximo
            html += `
                <li class="page-item ${state.currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage{{ $batch['id'] }}(${state.currentPage + 1}); return false;" aria-label="Próximo">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            `;
            
            pagination.innerHTML = html;
        };

        // Função de trocar página
        window.changePage{{ $batch['id'] }} = function(page) {
            const state = modalStates['{{ $batch['id'] }}'];
            if (page < 1 || page > Math.ceil(state.items.length / itemsPerPage)) {
                return;
            }
            state.currentPage = page;
            renderItems{{ $batch['id'] }}();
        };
    @endforeach
});
</script>
@endpush
