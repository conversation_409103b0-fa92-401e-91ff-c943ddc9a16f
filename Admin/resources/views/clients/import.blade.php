@extends('layouts.app', ['activePage' => 'clients'])

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="card card_conteudo">
                <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                    <div class="row">
                        <div class="col-12">
                            <h5 class="card-title mb-0" style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                Importar Clientes
                            </h5>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs" id="importTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#step-upload" type="button" role="tab" aria-controls="step-upload" aria-selected="true">Upload</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="mapping-tab" data-bs-toggle="tab" data-bs-target="#step-mapping" type="button" role="tab" aria-controls="step-mapping" aria-selected="false" disabled>Mapeamento</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#step-import" type="button" role="tab" aria-controls="step-import" aria-selected="false" disabled>Importação</button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content mt-4" id="importTabsContent">
                        <!-- Step 1: Upload -->
                        <div class="tab-pane fade show active" id="step-upload" role="tabpanel" aria-labelledby="upload-tab">
                            <div id="error-alert" class="alert alert-danger d-none">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <div id="error-message">Erro ao processar o arquivo.</div>
                                </div>
                            </div>

                            <div class="upload-container">
                                <form id="upload-form">
                                    @csrf
                                    <input type="file" class="form-control d-none" id="file" name="file" accept=".xlsx,.xls,.csv" required>
                                    
                                    <div id="drop-area">
                                        <div class="upload-area p-5 rounded-3 bg-light border border-2 border-dashed text-center" id="upload-content">
                                            <i class="fas fa-file-excel fa-3x mb-4" style="color: #0842A0;"></i>
                                            <h5 class="mb-3">Arraste e solte um arquivo .xlsx ou .csv aqui</h5>
                                            <p class="text-muted mb-4">Ou clique para selecionar um arquivo</p>
                                            <button type="button" class="btn btn-outline-primary px-4 py-2" id="select-file-btn">
                                                <i class="fas fa-upload me-2"></i> Selecionar Arquivo
                                            </button>
                                        </div>
                                        
                                        <div class="text-center d-none" id="file-info">
                                            <div class="file-preview p-3 bg-white rounded-3 shadow-sm border mb-3">
                                                <div class="d-flex flex-column flex-sm-row align-items-center gap-2">
                                                    <div class="file-icon me-0 me-sm-3 mb-2 mb-sm-0">
                                                        <i class="fas fa-file-excel fa-2x text-primary transition-all"></i>
                                                    </div>
                                                    <div class="file-info flex-grow-1 text-center text-sm-start mb-2 mb-sm-0">
                                                        <span class="filename h6 mb-0 text-dark text-break" id="filename-display"></span>
                                                    </div>
                                                    <div>
                                                        <button type="button" class="btn btn-link text-danger p-2 rounded hover-opacity" id="remove-file">
                                                            <i class="fas fa-times fa-lg me-1"></i>
                                                            Remover
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="success-message text-center">
                                                <i class="fas fa-check-circle me-2 text-success"></i>
                                                <span class="text-success">Arquivo carregado com sucesso</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4 d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="window.location.href='{{ route('clients.import') }}'">Voltar</button>
                                        <button type="button" class="btn btn-primary" id="btn-proximo" disabled>Continuar</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Step 2: Mapping -->
                        <div class="tab-pane fade" id="step-mapping" role="tabpanel" aria-labelledby="mapping-tab">
                            <div class="mapping-container">
                                <div class="mb-4">
                                    <h5 class="mb-2">Arquivo: <span id="mapping-filename" class="text-primary"></span></h5>
                                    <p class="text-muted">Mapeie as colunas do seu arquivo para os campos do sistema</p>
                                </div>

                                <!-- Adiciona legenda de campos obrigatórios -->
                                <div class="mb-3">
                                    <span class="small text-danger">* Campos obrigatórios</span>
                                </div>

                                <div class="mapping-table mb-4">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th style="width: 40%">Campo do Sistema</th>
                                                    <th>Coluna do Arquivo</th>
                                                </tr>
                                            </thead>
                                            <tbody id="mapping-fields">
                                                <!-- Field mapping rows will be added here dynamically -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h5 class="mb-2">Prévia dos dados</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped" id="preview-table">
                                            <thead id="preview-header">
                                                <!-- Preview headers will be added here dynamically -->
                                            </thead>
                                            <tbody id="preview-body">
                                                <!-- Preview data will be added here dynamically -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <p class="text-muted small" id="preview-info">Mostrando 0 de 0 registros</p>
                                </div>

                                <div class="mt-4 d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" id="btn-voltar-mapping">Voltar</button>
                                    <button type="button" class="btn btn-primary" id="btn-continuar-mapping">Continuar</button>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Import -->
                        <div class="tab-pane fade" id="step-import" role="tabpanel" aria-labelledby="import-tab">
                            <!-- Confirm Import -->
                            <div id="import-confirm" class="p-3">
                                <div class="mb-4">
                                    <h5 class="mb-2">Confirmar Importação</h5>
                                    <p class="text-muted">
                                        Você está prestes a importar <span id="records-count" class="fw-bold">0</span> clientes. 
                                        Verifique se o mapeamento está correto antes de continuar.
                                    </p>
                                    <!-- Adiciona legenda de campos obrigatórios -->
                                    <div class="mt-2">
                                        <span class="small text-danger">* Campos obrigatórios</span>
                                    </div>
                                </div>

                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Mapeamento de Campos</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush" id="field-mapping-summary">
                                            <!-- Field mapping summary will be added here dynamically -->
                                        </ul>
                                    </div>
                                </div>

                                <!-- Adiciona prévia dos dados mapeados -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">Prévia dos Dados Mapeados</h6>
                                        <span class="badge text-dark" id="mapped-preview-count">0 registros</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-sm" id="mapped-preview-table">
                                                <thead id="mapped-preview-header">
                                                    <!-- Mapped preview headers will be added here dynamically -->
                                                </thead>
                                                <tbody id="mapped-preview-body">
                                                    <!-- Mapped preview data will be added here dynamically -->
                                                </tbody>
                                            </table>
                                        </div>
                                        <p class="text-muted small">Mostrando apenas os primeiros 3 registros</p>
                                    </div>
                                </div>

                                <div class="mt-4 d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" id="btn-voltar-import">Voltar</button>
                                    <button type="button" class="btn btn-primary" id="btn-iniciar-import">Importar Clientes</button>
                                </div>
                            </div>

                            <!-- O processamento será feito no backend -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    #upload-content {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    #upload-content:hover, .dragover #upload-content {
        border-color: #0842A0 !important;
        background-color: #f8f9fa !important;
    }

    .preview-container {
        max-height: 400px;
        overflow-y: auto;
    }

    .nav-tabs .nav-link.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    .nav-tabs .nav-link {
        color: #0842A0;
    }
    
    .nav-tabs .nav-link.active {
        font-weight: 600;
        color: #0842A0;
        border-bottom: 2px solid #0842A0;
    }

    .transition-all {
        transition: all 0.3s ease;
    }

    .hover-opacity:hover {
        opacity: 0.7;
    }

    .file-preview {
        transition: transform 0.2s ease;
    }

    .file-preview:hover {
        transform: translateY(-1px);
    }

    .file-icon {
        width: 40px;
        text-align: center;
    }

    @media (max-width: 575.98px) {
        .file-preview {
            width: 100%;
        }
        
        .file-info {
            max-width: 100%;
            word-wrap: break-word;
        }
        
        .filename {
            display: inline-block;
            max-width: 100%;
        }
    }
</style>

<!-- Adiciona a biblioteca XLSX -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements - Upload Tab
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file');
    const selectFileBtn = document.getElementById('select-file-btn');
    const uploadContent = document.getElementById('upload-content');
    const fileInfo = document.getElementById('file-info');
    const filenameDisplay = document.getElementById('filename-display');
    const removeFileBtn = document.getElementById('remove-file');
    const btnProximo = document.getElementById('btn-proximo');
    const errorAlert = document.getElementById('error-alert');
    const errorMessage = document.getElementById('error-message');
    
    // Armazena o texto original do botão de importar
    let originalBtnText = document.getElementById('btn-iniciar-import')?.innerHTML || 'Importar Clientes';
    
    // Constante para o tamanho máximo do arquivo (100MB)
    const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB em bytes
    
    // Função para detectar e converter números seriais de data do Excel para YYYY-MM-DD
    function excelDateToFormattedDate(value) {
        // Se for string em formato de data (D/M/YYYY ou DD/MM/YYYY), converte para YYYY-MM-DD
        if (typeof value === 'string') {
            const dateMatch = value.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
            if (dateMatch) {
                const [_, day, month, year] = dateMatch;
                // Validar se é uma data válida
                const date = new Date(year, month - 1, day);
                if (date.getFullYear() == year && date.getMonth() == month - 1 && date.getDate() == day) {
                    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                } else {
                    console.warn('Data inválida detectada:', value);
                    return value;
                }
            }
        }
        
        // Verifica se o valor é um número e potencialmente uma data do Excel
        if (typeof value === 'number' && value > 0 && value < 80000) {
            try {
                // Algoritmo de conversão mais preciso, sem problemas de fuso horário
                // Ajusta o número serial para lidar com o bug do Excel (1900 não era bissexto)
                let excelDays = value;
                if (excelDays > 59) {
                    excelDays -= 1; // Ajusta para o erro do dia 29/02/1900 (que não existiu)
                }
                
                let year = 1900;
                let month = 1;
                let day = 0;
                
                const daysPerMonth = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
                day = Math.floor(excelDays);
                
                // Primeira iteração para encontrar o ano
                while (day > 365) {
                    if (year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0)) { // É bissexto
                        if (day > 366) {
                            day -= 366;
                            year += 1;
                        } else {
                            break;
                        }
                    } else { // Não é bissexto
                        day -= 365;
                        year += 1;
                    }
                }
                
                // Agora determinamos o mês
                let isLeapYear = year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
                
                // Ajusta fevereiro para anos bissextos
                daysPerMonth[2] = isLeapYear ? 29 : 28;
                
                // Encontra o mês
                while (day > daysPerMonth[month]) {
                    day -= daysPerMonth[month];
                    month++;
                    
                    if (month > 12) {
                        month = 1;
                        year++;
                        isLeapYear = year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
                        daysPerMonth[2] = isLeapYear ? 29 : 28;
                    }
                }
                
                // Formata a data no padrão YYYY-MM-DD
                return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
            } catch(e) {
                return value;
            }
        }
        
        // Se não for um número reconhecido como data, retorna o valor original
        return value;
    }
    
    // DOM Elements - Tabs
    const uploadTab = document.getElementById('upload-tab');
    const mappingTab = document.getElementById('mapping-tab');
    const importTab = document.getElementById('import-tab');
    
    // DOM Elements - Mapping Tab
    const mappingFilename = document.getElementById('mapping-filename');
    const mappingFields = document.getElementById('mapping-fields');
    const previewHeader = document.getElementById('preview-header');
    const previewBody = document.getElementById('preview-body');
    const previewInfo = document.getElementById('preview-info');
    const btnVoltarMapping = document.getElementById('btn-voltar-mapping');
    const btnContinuarMapping = document.getElementById('btn-continuar-mapping');
    
    // DOM Elements - Import Tab
    const recordsCount = document.getElementById('records-count');
    const fieldMappingSummary = document.getElementById('field-mapping-summary');
    const mappedPreviewHeader = document.getElementById('mapped-preview-header');
    const mappedPreviewBody = document.getElementById('mapped-preview-body');
    const mappedPreviewCount = document.getElementById('mapped-preview-count');
    const btnVoltarImport = document.getElementById('btn-voltar-import');
    const btnIniciarImport = document.getElementById('btn-iniciar-import');
    const importConfirm = document.getElementById('import-confirm');
    
    // Estado da aplicação
    let fileData = [];
    let columnMap = {};
    let currentFile = null;
    let isCsvFile = false;
    
    // Campos esperados do sistema
    const expectedFields = [
        { key: 'name', label: 'Nome', required: true },
        { key: 'email', label: 'Email', required: true },
        { key: 'phone', label: 'Telefone', required: true },
        { key: 'cpf', label: 'CPF', required: true },
        { key: 'birth_date', label: 'Data de Nascimento', required: false },
        { key: 'zipCode', label: 'CEP', required: false },
        { key: 'neighborhood', label: 'Bairro', required: false },
        { key: 'street', label: 'Rua', required: false },
        { key: 'number', label: 'Número', required: false },
        { key: 'complement', label: 'Complemento', required: false },
        { key: 'city', label: 'Cidade', required: false },
        { key: 'state', label: 'Estado', required: false }
    ];
    
    // Previne o comportamento padrão de arrastar e soltar
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Destaca a área quando um arquivo é arrastado sobre ela
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });

    // Manipula o arquivo solto
    dropArea.addEventListener('drop', handleDrop, false);

    // Manipula o clique no botão de seleção
    selectFileBtn.addEventListener('click', () => {
        fileInput.click();
    });

    // Manipula a mudança no input file
    fileInput.addEventListener('change', handleFiles);

    // Remove o arquivo
    removeFileBtn.addEventListener('click', removeFile);

    // Navega para o mapeamento
    btnProximo.addEventListener('click', () => {
        if (fileInput.files && fileInput.files[0]) {
            activateTab('mapping-tab');
        } else {
            showError('Por favor, selecione um arquivo primeiro.');
        }
    });

    // Volta para o upload
    btnVoltarMapping.addEventListener('click', () => {
        activateTab('upload-tab');
    });
    
    // Continua para a importação
    btnContinuarMapping.addEventListener('click', () => {
        if (validateMapping()) {
            prepareImportSummary();
            activateTab('import-tab');
        } else {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-3';
            alertDiv.role = 'alert';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                Por favor, mapeie todos os campos obrigatórios antes de continuar.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            `;
            document.querySelector('.mapping-table').insertAdjacentElement('beforebegin', alertDiv);
        }
    });
    
    // Volta para o mapeamento
    btnVoltarImport.addEventListener('click', () => {
        activateTab('mapping-tab');
    });
    
    // Inicia a importação
    btnIniciarImport.addEventListener('click', handleImport);
    
    // Reset da importação
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        dropArea.classList.add('dragover');
    }

    function unhighlight(e) {
        dropArea.classList.remove('dragover');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        // Atualiza o input file com o arquivo arrastado
        if (files && files.length > 0) {
            // Cria um novo objeto DataTransfer para manipular o input file
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(files[0]);
            fileInput.files = dataTransfer.files;
        }
        
        handleFiles({ target: { files: files } });
    }
    
    function showError(message) {
        errorMessage.textContent = message;
        errorAlert.classList.remove('d-none');
        errorAlert.classList.add('d-block');
    }
    
    function hideError() {
        errorAlert.classList.remove('d-block');
        errorAlert.classList.add('d-none');
    }

    // Função auxiliar para processar linha do CSV
    function parseCSVLine(line, separator) {
        const values = [];
        let inQuotes = false;
        let currentValue = '';
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"' && (i === 0 || line[i-1] !== '\\')) {
                inQuotes = !inQuotes;
            } else if (char === separator && !inQuotes) {
                values.push(currentValue.trim().replace(/^["'](.+(?=["']$))["']$/, '$1'));
                currentValue = '';
            } else {
                currentValue += char;
            }
        }
        
        // Adiciona o último valor
        values.push(currentValue.trim().replace(/^["'](.+(?=["']$))["']$/, '$1'));
        
        return values;
    }

    function showFileInfo(file) {
        hideError();
        uploadContent.classList.add('d-none');
        fileInfo.classList.remove('d-none');
        
        // Adiciona o tamanho do arquivo ao nome
        const fileSize = formatFileSize(file.size);
        filenameDisplay.textContent = `${file.name} (${fileSize})`;
        mappingFilename.textContent = `${file.name} (${fileSize})`;
        
        dropArea.classList.remove('bg-light');
        dropArea.classList.add('bg-white', 'border-primary');
        btnProximo.disabled = false;
        currentFile = file;
        isCsvFile = file.name.toLowerCase().endsWith('.csv');

        // Se não existir as opções de arquivo já criadas, crie-as
        if (!document.getElementById('file-options')) {
            const fileOptionsDiv = document.createElement('div');
            fileOptionsDiv.id = 'file-options';
            fileOptionsDiv.className = 'mt-3 p-3 border rounded bg-light';
            
            // Conteúdo inicial comum
            let fileOptionsContent = `
                <h6 class="mb-3">Opções de Importação</h6>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="has-headers" checked>
                        <label class="form-check-label" for="has-headers">
                            A primeira linha contém cabeçalhos
                        </label>
                    </div>
                    <div class="form-text text-muted small">Desmarque se o arquivo não possui uma linha de cabeçalhos</div>
                </div>
            `;
            
            // Adiciona opções específicas para CSV
            if (isCsvFile) {
                fileOptionsContent += `
                    <div class="mb-3">
                        <label for="csv-separator" class="form-label fw-semibold">Separador CSV:</label>
                        <div class="row g-2">
                            <div class="col-md-6">
                                <select id="csv-separator" class="form-control">
                                    <option value="," selected>Vírgula (,)</option>
                                    <option value=";">Ponto e vírgula (;)</option>
                                    <option value="|">Pipe (|)</option>
                                    <option value="custom">Outro</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <input type="text" id="custom-separator" class="form-control" maxlength="1" placeholder="Digite um caractere" style="display: none;">
                            </div>
                        </div>
                        <div class="form-text text-muted small mt-1">Selecione o caractere que separa as colunas no seu arquivo CSV</div>
                    </div>
                `;
            }
            
            fileOptionsDiv.innerHTML = fileOptionsContent;
            
            // Inserir antes do botão próximo
            const uploadForm = document.getElementById('upload-form');
            const buttonRow = document.querySelector('#upload-form .mt-4');
            uploadForm.insertBefore(fileOptionsDiv, buttonRow);

            // Adiciona evento para o separador CSV
            if (isCsvFile) {
                const csvSeparator = document.getElementById('csv-separator');
                const customSeparator = document.getElementById('custom-separator');
                
                csvSeparator.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customSeparator.style.display = 'block';
                        customSeparator.focus();
                    } else {
                        customSeparator.style.display = 'none';
                    }
                    
                    if (currentFile) {
                        readFile(currentFile);
                    }
                });
                
                customSeparator.addEventListener('input', function() {
                    if (currentFile && this.value) {
                        readFile(currentFile);
                    }
                });
            }
            
            // Adiciona evento para o checkbox de cabeçalhos
            document.getElementById('has-headers').addEventListener('change', function() {
                if (currentFile) {
                    readFile(currentFile);
                }
            });
        }
    }

    function removeFile() {
        fileInput.value = '';
        uploadContent.classList.remove('d-none');
        fileInfo.classList.add('d-none');
        dropArea.classList.remove('bg-white', 'border-primary');
        dropArea.classList.add('bg-light');
        btnProximo.disabled = true;
        fileData = [];
        columnMap = {};
        currentFile = null;
        hideError();
        
        // Remover opções de arquivo se existirem
        const fileOptions = document.getElementById('file-options');
        if (fileOptions) {
            fileOptions.remove();
        }
    }
    
    function activateTab(tabId) {
        // Encontra todos os elementos de tab e content
        const tabs = document.querySelectorAll('[role="tab"]');
        const tabPanes = document.querySelectorAll('[role="tabpanel"]');
        
        // Desativa todas as tabs e esconde todos os painéis
        tabs.forEach(tab => {
            tab.classList.remove('active');
            tab.setAttribute('aria-selected', 'false');
        });
        
        tabPanes.forEach(pane => {
            pane.classList.remove('show', 'active');
        });
        
        // Ativa a tab selecionada
        const selectedTab = document.getElementById(tabId);
        selectedTab.classList.add('active');
        selectedTab.setAttribute('aria-selected', 'true');
        
        // Mostra o painel correspondente
        const targetId = selectedTab.getAttribute('data-bs-target');
        const targetPane = document.querySelector(targetId);
        targetPane.classList.add('show', 'active');
    }

    function handleFiles(e) {
        hideError();
        const files = e.target?.files || e;
        if (files && files[0]) {
            const file = files[0];
            const extension = file.name.split('.').pop().toLowerCase();
            
            // Verificar o tamanho do arquivo
            if (file.size > MAX_FILE_SIZE) {
                showError(`O arquivo é muito grande. O tamanho máximo permitido é 100 MB.`);
                removeFile();
                return;
            }
            
            if (['xlsx', 'xls', 'csv'].includes(extension)) {
                showFileInfo(file);
                readFile(file);
            } else {
                showError('Por favor, selecione um arquivo .xlsx, .xls ou .csv');
                removeFile();
            }
        }
    }

    function readFile(file) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            try {
                let jsonData;
                const data = e.target.result;
                const extension = file.name.split('.').pop().toLowerCase();
                const hasHeaders = document.getElementById('has-headers')?.checked ?? true;
                
                if (extension === 'csv') {
                    // Obtém o separador selecionado
                    const separatorSelect = document.getElementById('csv-separator');
                    let separator = ','; // valor padrão
                    
                    if (separatorSelect) {
                        if (separatorSelect.value === 'custom') {
                            const customSeparator = document.getElementById('custom-separator');
                            separator = customSeparator.value || ','; // usa vírgula se estiver vazio
                        } else {
                            separator = separatorSelect.value;
                        }
                    }
                    
                    // Para arquivos CSV, processa manualmente para preservar caracteres especiais
                    const lines = data.split(/\r\n|\n/).filter(line => line.trim());
                    let headers, dataLines;

                    if (hasHeaders) {
                        headers = lines[0].split(separator).map(header =>
                        header.trim()
                            .replace(/^\uFEFF/, '') // Remove BOM se existir
                            .replace(/^["'](.+(?=["']$))["']$/, '$1') // Remove aspas
                        );
                        dataLines = lines.slice(1);
                    } else {
                        // Criar cabeçalhos automáticos e usar todas as linhas
                        const firstLine = parseCSVLine(lines[0], separator);
                        headers = Array.from({ length: firstLine.length }, (_, i) => `Coluna ${i + 1}`);
                        dataLines = lines;
                    }
                    
                    jsonData = dataLines
                        .map(line => {
                            const values = parseCSVLine(line, separator);
                            
                            const rowData = {};
                            headers.forEach((header, index) => {
                                rowData[header] = values[index] || '';
                            });
                            
                            return rowData;
                        });
                } else {
                    // Para arquivos Excel (.xlsx, .xls)
                    const workbook = XLSX.read(data, {type: 'binary'});
                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                    
                    // Se não tiver cabeçalhos, configuramos a opção header para gerar automaticamente
                    const excelOptions = {
                        header: hasHeaders ? undefined : 1,
                        raw: true,
                        defval: ''
                    };
                    
                    // Converter para JSON
                    let rawData = XLSX.utils.sheet_to_json(firstSheet, excelOptions);
                    
                    // CORREÇÃO: Filtra linhas vazias (linhas que só contêm células vazias)
                    rawData = rawData.filter(row => {
                        // Verifica se pelo menos uma propriedade tem valor não vazio
                        return Object.values(row).some(value => 
                            value !== undefined && value !== null && value !== '');
                    });
                    
                    // Processa e converte datas do Excel
                    rawData = rawData.map(row => {
                        const processedRow = {};
                        Object.entries(row).forEach(([key, value]) => {
                            processedRow[key] = excelDateToFormattedDate(value);
                        });
                        return processedRow;
                    });
                    
                    // Continua com o processamento...
                    if (!hasHeaders && rawData.length > 0) {
                        // Renomear colunas de números para "Coluna X"
                        const firstRow = rawData[0];
                        const columnCount = Object.keys(firstRow).length;
                        
                        // Usar os índices numéricos diretamente
                        jsonData = rawData;
                    } else {
                        jsonData = rawData;
                    }
                }
                
                if (jsonData.length > 0) {
                    fileData = jsonData;
                    
                    // Habilita a tab de mapeamento
                    mappingTab.disabled = false;
                    
                    // Prepara o mapeamento automático
                    prepareMapping(Object.keys(jsonData[0]));
                    
                    // Prepara a prévia dos dados
                    preparePreview(jsonData);
                } else {
                    showError('O arquivo não contém dados.');
                }
            } catch (error) {
                showError('Erro ao processar o arquivo. Verifique se o formato é válido.');
            }
        };
        
        reader.onerror = function() {
            showError('Erro ao ler o arquivo.');
        };
        
        if (file.name.endsWith('.csv')) {
            reader.readAsText(file, 'UTF-8');
        } else {
            reader.readAsBinaryString(file);
        }
    }
    
    function prepareMapping(columns) {
        // Limpa o conteúdo anterior
        mappingFields.innerHTML = '';
        
        // Inicializa o mapeamento de colunas
        columnMap = {};
        
        // Tenta mapear automaticamente colunas com nomes similares
        expectedFields.forEach(field => {
            let mappedColumn = '';
            
            // Tenta encontrar uma correspondência
            columns.forEach(column => {
                const normalizedColumn = column.toLowerCase().trim();
                if (normalizedColumn.includes(field.key.toLowerCase()) || 
                    normalizedColumn.includes(field.label.toLowerCase())) {
                    mappedColumn = column;
                }
            });
            
            columnMap[field.key] = mappedColumn;
            
            // Adiciona a linha de mapeamento
            const row = document.createElement('tr');
            
            const fieldCell = document.createElement('td');
            // Melhora o visual dos campos obrigatórios deixando mais explícito
            if (field.required) {
                fieldCell.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span>${field.label}</span>
                        <span class="text-danger ms-1" title="Campo obrigatório">*</span>
                    </div>
                `;
            } else {
                fieldCell.textContent = field.label;
            }
            
            const selectCell = document.createElement('td');
            const select = document.createElement('select');
            select.className = 'form-control';
            select.dataset.field = field.key;
            select.dataset.required = field.required;
            
            // Opção vazia
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = field.required ? '-- Selecione uma coluna * --' : '-- Selecione uma coluna --';
            emptyOption.className = 'text-muted';
            select.appendChild(emptyOption);
            
            // Adiciona validação visual
            select.addEventListener('change', function() {
                this.classList.remove('is-invalid');
                if (field.required && !this.value) {
                    this.classList.add('is-invalid');
                }
            });
            
            // Aplica validação inicial se for campo obrigatório
            if (field.required && !mappedColumn) {
                select.classList.add('is-invalid');
            }
            
            // Adiciona as opções de colunas
            const hasHeaders = document.getElementById('has-headers')?.checked ?? true;

            columns.forEach((column, index) => {
                const option = document.createElement('option');
                if (hasHeaders) {
                    option.value = column;
                    option.textContent = column;
                    option.selected = column === mappedColumn;
                } else {
                    option.value = index;
                    option.textContent = `Coluna ${index + 1}`;
                    option.selected = index === mappedColumn;
                }
                select.appendChild(option);
            });
            
            // Evento de mudança
            select.addEventListener('change', function() {
                const hasHeaders = document.getElementById('has-headers')?.checked ?? true;
                if (!hasHeaders) {
                    // Quando não tem headers, converte para número (mantém como número mesmo que seja 0)
                    columnMap[field.key] = this.value === '' ? '' : parseInt(this.value);
                } else {
                    columnMap[field.key] = this.value;
                }
            });
            
            // Adiciona mensagem de validação para campos obrigatórios
            if (field.required) {
                const validationMessage = document.createElement('div');
                validationMessage.className = 'invalid-feedback';
                validationMessage.textContent = 'Este campo é obrigatório';
                selectCell.appendChild(select);
                selectCell.appendChild(validationMessage);
            } else {
                selectCell.appendChild(select);
            }
            
            row.appendChild(fieldCell);
            row.appendChild(selectCell);
            mappingFields.appendChild(row);
        });
    }
    
    function preparePreview(data) {
        // Limpa o conteúdo anterior
        previewHeader.innerHTML = '';
        previewBody.innerHTML = '';
        
        // Obtém as colunas e verifica se tem cabeçalhos
        const columns = Object.keys(data[0]);
        const hasHeaders = document.getElementById('has-headers')?.checked ?? true;
        
        // Cria o cabeçalho
        const headerRow = document.createElement('tr');
        columns.forEach((column, index) => {
            const th = document.createElement('th');
            th.textContent = hasHeaders ? column : `Coluna ${index + 1}`;
            headerRow.appendChild(th);
        });
        previewHeader.appendChild(headerRow);
        
        // Cria as linhas de dados (máximo de 5)
        const previewData = data.slice(0, 5);
        previewData.forEach(row => {
            const tr = document.createElement('tr');
            columns.forEach(column => {
                const td = document.createElement('td');
                let value = row[column];
                
                // Se o valor existir e a coluna parece ser uma data
                if (value !== undefined && value !== '') {
                    // Verifica se a coluna tem "data" ou "birth" no nome (case insensitive)
                    if (column.toLowerCase().includes('data') || column.toLowerCase().includes('birth')) {
                        // Tenta converter para YYYY-MM-DD primeiro
                        const isoDate = excelDateToFormattedDate(value);
                        // Depois converte para formato brasileiro
                        value = formatDateToPtBR(isoDate);
                    }
                }
                
                td.textContent = value !== undefined ? value : '';
                tr.appendChild(td);
            });
            previewBody.appendChild(tr);
        });
        
        // Atualiza a informação da prévia
        previewInfo.textContent = `Mostrando ${Math.min(5, data.length)} de ${data.length} registros`;
    }
    
    function validateMapping() {
        // Verifica se todos os campos obrigatórios foram mapeados
        let isValid = true;
        
        expectedFields.forEach(field => {
            if (field.required) {
                const value = columnMap[field.key];
                
                // Verifica se o valor é undefined, null, string vazia ou não é um número válido
                if (value === undefined || value === null || value === '' ||
                    (typeof value === 'number' && isNaN(value))) {
                    isValid = false;
                }
            }
        });
        
        return isValid;
    }
    
    function prepareImportSummary() {
        // Limpa o conteúdo anterior
        fieldMappingSummary.innerHTML = '';
        mappedPreviewHeader.innerHTML = '';
        mappedPreviewBody.innerHTML = '';
        
        // Adiciona cada campo mapeado
        expectedFields.forEach(field => {
            const li = document.createElement('li');
            li.className = 'list-group-item d-flex justify-content-between align-items-center';
            
            const fieldName = document.createElement('span');
            fieldName.textContent = field.label + (field.required ? ' *' : '');
            
            const columnName = document.createElement('span');
            columnName.className = columnMap[field.key] !== undefined && columnMap[field.key] !== '' ? 'text-success' : 'text-muted';
            let isHasHeaders = document.getElementById('has-headers')?.checked ?? true; 
            if (!isHasHeaders) {
                columnName.textContent = `Coluna ${columnMap[field.key]}`;
            }else{
                columnName.textContent = columnMap[field.key];
            }
            
            li.appendChild(fieldName);
            li.appendChild(columnName);
            fieldMappingSummary.appendChild(li);
        });
        
        // Adiciona a configuração de cabeçalho
        const hasHeaders = document.getElementById('has-headers');
        if (hasHeaders) {
            const headerItem = document.createElement('li');
            headerItem.className = 'list-group-item d-flex justify-content-between align-items-center';
            
            const headerLabel = document.createElement('span');
            headerLabel.textContent = 'Primeira linha contém cabeçalhos';
            
            const headerValue = document.createElement('span');
            headerValue.className = 'text-info';
            headerValue.textContent = hasHeaders.checked ? 'Sim' : 'Não';
            
            headerItem.appendChild(headerLabel);
            headerItem.appendChild(headerValue);
            fieldMappingSummary.appendChild(headerItem);
        }
        
        // Adiciona informações do CSV se aplicável
        if (isCsvFile) {
            const separatorSelect = document.getElementById('csv-separator');
            
            if (separatorSelect) {
                // Adiciona separador info
                const separatorItem = document.createElement('li');
                separatorItem.className = 'list-group-item d-flex justify-content-between align-items-center';
                
                const separatorLabel = document.createElement('span');
                separatorLabel.textContent = 'Separador CSV';
                
                const separatorValue = document.createElement('span');
                separatorValue.className = 'text-info';
                
                let separatorText = '';
                if (separatorSelect.value === 'custom') {
                    const customSeparator = document.getElementById('custom-separator');
                    separatorText = `Personalizado: "${customSeparator.value || ','}"`;
                } else {
                    const separatorMapping = {
                        ',': 'Vírgula (,)',
                        ';': 'Ponto e vírgula (;)',
                        '|': 'Pipe (|)'
                    };
                    separatorText = separatorMapping[separatorSelect.value] || separatorSelect.value;
                }
                
                separatorValue.textContent = separatorText;
                
                separatorItem.appendChild(separatorLabel);
                separatorItem.appendChild(separatorValue);
                fieldMappingSummary.appendChild(separatorItem);
            }
        }
        
        // Atualiza a contagem de registros
        recordsCount.textContent = fileData.length;
        mappedPreviewCount.textContent = `${Math.min(3, fileData.length)} de ${fileData.length} registros`;
        
        // Prepara a prévia dos dados mapeados
        prepareMappedPreview();
    }
    
    // Nova função para preparar prévia dos dados mapeados
    // Função para formatar data YYYY-MM-DD para DD/MM/YYYY
    function formatDateToPtBR(value) {
        if (!value) return '';
        
        // Verifica se é uma data no formato YYYY-MM-DD
        const match = value.match(/^(\d{4})-(\d{2})-(\d{2})$/);
        if (match) {
            const [_, year, month, day] = match;
            return `${day}/${month}/${year}`;
        }
        return value;
    }

    function prepareMappedPreview() {
        // Se não houver dados, não faz nada
        if (fileData.length === 0) return;
        
        // Limpa o conteúdo anterior
        mappedPreviewHeader.innerHTML = '';
        mappedPreviewBody.innerHTML = '';
        
        // Cria o cabeçalho com os campos do sistema
        const headerRow = document.createElement('tr');
        expectedFields.forEach(field => {
            const th = document.createElement('th');
            th.textContent = field.label;
            if (field.required) {
                th.innerHTML = `${field.label} <span class="text-danger">*</span>`;
            } else {
                th.textContent = field.label;
            }
            headerRow.appendChild(th);
        });
        mappedPreviewHeader.appendChild(headerRow);
        
        // Limita a 3 registros para a prévia
        const previewData = fileData.slice(0, 3);
        
        // Adiciona as linhas com os dados mapeados
        previewData.forEach(row => {
            const tr = document.createElement('tr');
            
            // Para cada campo esperado, mostra o valor correspondente do arquivo
            expectedFields.forEach(field => {
                const td = document.createElement('td');
                
                const hasHeaders = document.getElementById('has-headers')?.checked ?? true;
                const mappedColumn = columnMap[field.key];
                
                if (mappedColumn !== undefined && mappedColumn !== '') {
                    let value;
                    if (!hasHeaders) {
                        // Quando não tem headers, usa o índice numérico diretamente
                        value = row[parseInt(mappedColumn)];
                    } else {
                        value = row[mappedColumn];
                    }
                    
                    // Aplica processamento de data para campos específicos
                    if (field.key === 'birth_date' && value !== undefined && value !== '') {
                        // Primeiro converte para o formato YYYY-MM-DD
                        const isoDate = excelDateToFormattedDate(value);
                        // Depois converte para exibição em formato brasileiro
                        value = formatDateToPtBR(isoDate);
                    }
                    
                    td.textContent = value || '';
                } else {
                    td.textContent = '';
                    td.className = 'text-muted fst-italic';
                    
                    // Mostra um aviso se for campo obrigatório sem mapeamento
                    if (field.required) {
                        td.textContent = 'Não mapeado';
                        td.className = 'text-danger fst-italic';
                    }
                }
                
                tr.appendChild(td);
            });
            
            mappedPreviewBody.appendChild(tr);
        });
    }
    
    async function handleImport() {
        try {
            // Desabilita o botão e mostra loading
            const btnIniciarImport = document.getElementById('btn-iniciar-import');
            const originalBtnText = btnIniciarImport.innerHTML;
            btnIniciarImport.disabled = true;
            btnIniciarImport.innerHTML = `
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                Iniciando importação...
            `;

            // Verifica se o arquivo existe
            if (!currentFile) {
                throw new Error('Por favor, selecione um arquivo para importar.');
            }

            // Converte o arquivo para base64
            const fileBase64 = await new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(currentFile);
                reader.onload = () => resolve(reader.result.split(',')[1]);
                reader.onerror = error => reject(error);
            });

            // Prepara os dados para envio
            const formattedMapping = {};
            Object.entries(columnMap).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    formattedMapping[key] = value;
                }
            });

            const requestData = {
                file: fileBase64,
                file_name: currentFile.name,
                file_type: currentFile.type,
                mapping: JSON.stringify(formattedMapping),
                has_headers: document.getElementById('has-headers')?.checked ?? true
            };

            // Adiciona separador para CSV
            if (isCsvFile) {
                const separatorSelect = document.getElementById('csv-separator');
                if (separatorSelect) {
                    let separatorValue = separatorSelect.value;
                    if (separatorValue === 'custom') {
                        const customSeparator = document.getElementById('custom-separator');
                        separatorValue = customSeparator.value || ',';
                    }
                    requestData.separator = separatorValue;
                }
            }

            try {
                // Converte o arquivo para base64
                const fileBase64 = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.readAsDataURL(currentFile);
                    reader.onload = () => resolve(reader.result.split(',')[1]);
                    reader.onerror = error => reject(error);
                });

                // Envia os dados via post tradicional
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/clients/import';

                // Adiciona o CSRF token
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = document.querySelector('input[name="_token"]').value;
                form.appendChild(csrfInput);

                // Adiciona os dados como inputs hidden
                const formData = {
                    file: fileBase64,
                    file_name: currentFile.name,
                    file_type: currentFile.type,
                    mapping: formattedMapping,
                    has_headers: document.getElementById('has-headers')?.checked ?? true,
                    separator: (() => {
                        if (!isCsvFile) return ',';
                        const separatorSelect = document.getElementById('csv-separator');
                        if (!separatorSelect) return ',';
                        return separatorSelect.value === 'custom'
                            ? document.getElementById('custom-separator')?.value || ','
                            : separatorSelect.value;
                    })()
                };

                Object.entries(formData).forEach(([key, value]) => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = typeof value === 'object' ? JSON.stringify(value) : value;
                    form.appendChild(input);
                });

                // Adiciona o form ao body e submete
                document.body.appendChild(form);
                form.submit();
            } catch (error) {
                // Mostra erro
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
                alertDiv.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div>Erro ao processar arquivo: ${error.message}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                `;
                document.getElementById('import-confirm').prepend(alertDiv);
            }

        } catch (error) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
            alertDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <div>${error.message}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            `;
            document.getElementById('import-confirm').prepend(alertDiv);

            // Reseta o botão
            btnIniciarImport.disabled = false;
            btnIniciarImport.innerHTML = originalBtnText;
        }
    }
    
    // Funções de progresso removidas pois o processamento será feito no backend
    
    function resetAll() {
        // Reseta o upload
        removeFile();
        
        // Reseta o mapeamento
        columnMap = {};
        fileData = [];
        mappingFields.innerHTML = '';
        previewHeader.innerHTML = '';
        previewBody.innerHTML = '';
        
        // Reseta a importação
        importConfirm.classList.remove('d-none');
        
        // Desabilita as abas
        mappingTab.disabled = true;
        importTab.disabled = true;
        
        // Volta para a primeira aba
        activateTab('upload-tab');
    }

    // Adiciona função para formatar o tamanho do arquivo em MB
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        
        return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>
@endsection