<div class="modal fade" tabindex="-1" role="dialog" id="{{ $modalId }}">
    <div class="modal-dialog modal-xl modal-dialog-scrollable modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adicionar cliente</h5>
                <div>
                  <span style="color:red" class="table-legend">* Campos obrigatórios</span>
                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span>
                  </button>
                </div>
            </div>
            <div class="modal-body" id="client_create_modal_body" style="max-height: calc(100vh - 150px);">
                <form 
                    action="{{ route('clients.store.incomplete') }}" 
                    id="form-create-incomplete-client" 
                    method="POST"
                >
                    @csrf
                    @include('clients.incompleteRegister.basic_info')
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="submit" class="btn btn-success" id="submit-client-button" form="form-create-incomplete-client">Salvar</button>
            </div>
        </div>
    </div>
</div>
@push('js')
<script type="text/javascript">
    $('#form-create-incomplete-client').on('submit', function(event) {
        event.preventDefault();

        const formData = new FormData(this);
        const email = formData.get('email')?.trim() || '';
        const phone = formData.get('phone_number_1')?.trim() || '';
        const errorElement = document.getElementById('email-phone-error');

        if (!email && !phone) {
            Swal.fire({
                icon: 'warning',
                title: 'Preencha corretamente',
                text: 'É necessário informar pelo menos o email ou telefone.'
            });
            return false;
        }

        const submitButton = document.getElementById('submit-client-button');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Salvando...';

        this.submit();
    })
</script>
@endpush
