@php
    use App\Helpers\ApiUser;
    use App\Helpers\StringMask;

    $adminBusinessesId = (int) SystemHelper::get()['ADMIN_BUSINESS_ID'];
    $isAdminBusinessSelected =
        isset(ApiUser::get()['business_selected']) && ApiUser::get()['business_selected']['id'] === $adminBusinessesId;
    $canReleaseAllBusinesses = ApiUser::hasUserFunction('release_all_business');

@endphp
@extends('layouts.app', ['activePage' => 'clients'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                        style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Clientes</h5>
                            </div>
                            <div class="col-lg-9 col-md-6 col-sm-6 col-2 mb-2 d-flex justify-content-end">
                                <div class="d-flex">
                                    @if (ApiUser::get()['business_selected'] !== null &&
                                            (ApiUser::hasUserFunction('release_all_business') || ApiUser::hasUserFunction('update_clients')))
                                        <button onclick="$('#modalCreateClient').modal('show')" type="button"
                                            class="btn btn-success mr-1">
                                            <img class="card-img-left example-card-img-responsive"
                                                src="{{ url('icon/icon_plus.png') }}" width="15px"
                                                style="margin-top: -2px;" />
                                            <label class="styleBotaoAdicionar">Adicionar cliente</label>
                                        </button>
                                        @if (ApiUser::isBusinessSelected())
                                            <a href="{{ route('clients.import') }}" class="btn btn-primary mr-1">
                                                <i class="fas fa-upload"></i>
                                                <label id="labelImportar"
                                                    style="margin-bottom: -5px;cursor: pointer;">Importar</label>
                                            </a>
                                        @endif
                                    @endif
                                    <div class="modal fade" id="cpfModal" tabindex="-1" aria-labelledby="cpfModalLabel"
                                        aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="cpfModalLabel">Digite o CPF do
                                                        Cliente</h5>
                                                    <button type="button" class="close" data-dismiss="modal"
                                                        aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    <form id="cpfForm">
                                                        <input type="text" class="form-control mb-3" id="cpf_search"
                                                            name="cpf" maxlength="14" required>
                                                        <button type="button" class="btn btn-primary"
                                                            onclick="search()">Continuar
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-secondary" onclick="showNegociosUser(false)">
                                        <img class="card-img-left example-card-img-responsive"
                                            src="{{ url('icon/icon_filtro.png') }}" width="15px"
                                            style="margin-top: -2px;" />
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <form action="{{ route('clients.index') }}" method="get" id="form_search_clients_index">
                                    @csrf
                                    <div class="d-lg-flex d-md-block d-sm-block">
                                        <div class="flex-grow-1 mb-2 mb-lg-0 mr-lg-2">
                                            <input type="text" class="form-control flex-grow-1 mr-2" name="search"
                                                aria-describedby="search" placeholder="Nome, E-mail, CPF ou Telefone"
                                                value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        </div>

                                        <div class="d-block d-lg-none w-100 mb-2">
                                            <button class="btn btn-light w-100" type="submit">
                                                <img src="{{ url('icon/icon_search.png') }}" width="20px"
                                                    class="mr-2" />
                                            </button>
                                        </div>

                                        <!-- Botão de busca - Versão desktop (pequeno) -->
                                        <div class="d-none d-lg-block mr-lg-2" style="min-width: 40px;">
                                            <button class="btn btn-light h-100" type="submit">
                                                <img src="{{ url('icon/icon_search.png') }}" width="20px" />
                                            </button>
                                        </div>

                                        @if ($isAdminBusinessSelected || ApiUser::hasUserFunction('release_all_business'))
                                            <div class="flex-grow-1">
                                                <select class="form-control w-100" name="profile" id="clientProfileSelect">
                                                    <option value="all"
                                                        @if (!isset($profile) || $profile === 'all') selected @endif>
                                                        Perfil: Todos os perfis</option>
                                                    <option value="client"
                                                        @if (isset($profile) && $profile === 'client') selected @endif>
                                                        Apenas Clientes</option>
                                                    <option value="employee"
                                                        @if (isset($profile) && $profile === 'employee') selected @endif>
                                                        Apenas Funcionários</option>
                                                    @if (ApiUser::hasUserFunction('sellers'))
                                                        <option value="seller"
                                                            @if (isset($profile) && $profile === 'seller') selected @endif>Apenas
                                                            Vendedores
                                                        </option>
                                                    @endif
                                                </select>
                                            </div>
                                        @endif
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100" style="white-space: nowrap;" id="clients-table">
                                <thead>
                                    <tr>
                                        <th scope="col" style="text-align: center">Ações</th>
                                        <th scope="col">Nome/CPF</th>
                                        <th scope="col">Email/Telefone</th>
                                        <th scope="col">Negócios</th>
                                        @if ($isAdminBusinessSelected)
                                            <th scope="col">Perfil</th>
                                        @endif
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (count($clients) === 0)
                                        <tr>
                                            <td colspan="{{ $isAdminBusinessSelected ? 5 : 4 }}" class="text-center py-5">
                                                <div class="d-flex flex-column align-items-center justify-content-center">
                                                    <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                                                    <h5 class="text-muted">Nenhum cliente encontrado</h5>
                                                    <p class="text-muted">Adicione um novo cliente ou ajuste seus filtros
                                                        de
                                                        busca</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @else
                                        @foreach ($clients as $client)
                                            @php
                                                $userTypes = [];
                                                $typesCount = 0;

                                                if (!empty($client['types'])) {
                                                    foreach ($client['types'] as $type => $details) {
                                                        if ($details['hasType']) {
                                                            $userTypes[] = $details['name'];
                                                            $typesCount++;
                                                        }
                                                    }
                                                    sort($userTypes);

                                                    $userTypes = implode(', ', $userTypes);
                                                } else {
                                                    $userTypes = '';
                                                }

                                                if (empty($userTypes)) {
                                                    $userTypes = 'Sem perfil';
                                                }
                                            @endphp
                                            <tr>
                                                <td style="text-align: center">
                                                    <div class="btn-group">
                                                        <button class="btn btn-secondary"
                                                            onclick="$('#modal-view-client-{{ $client['id'] }}').modal('show')"
                                                            style="margin-right: 10px">
                                                            <img class="card-img-left example-card-img-responsive"
                                                                src="{{ url('icon/icon_visualizar.svg') }}"
                                                                width="24px" />
                                                        </button>
                                                        @if (ApiUser::hasPermissionOrIsAdmin('update_clients'))
                                                            @if (($isAdminBusinessSelected || $canReleaseAllBusinesses) && $typesCount > 0)
                                                                <button class="btn btn-primary"
                                                                    onclick="$('#edit-collaborator-{{ $client['id'] }}').modal('show')"
                                                                    style="margin-right: 10px">
                                                                    <img class="card-img-left example-card-img-responsive"
                                                                        src="{{ url('icon/icon_editar.png') }}"
                                                                        width="20px" />
                                                                </button>
                                                                {{-- @else
                                                        <div>
                                                            <a class="btn btn-primary" style="margin-right: 10px"
                                                            href="{{ route('clients.edit', $client['id']) }}">
                                                                <img class="card-img-left example-card-img-responsive"
                                                                    src="{{url('icon/icon_editar.png')}}" width="20px"/>
                                                            </a>
                                                        </div> --}}
                                                            @endif
                                                            <div>
                                                                <button
                                                                    onclick="$('#delete-collaborator-{{ $client['id'] }}').modal('show')"
                                                                    class="btn btn-danger">
                                                                    <img class="card-img-left example-card-img-responsive"
                                                                        src="{{ url('icon/icon_lixeira.png') }}"
                                                                        width="18px" />
                                                                </button>
                                                                @include('clients.modal_delete', [
                                                                    'modalId' =>
                                                                        'delete-collaborator-' . $client['id'],
                                                                ])
                                                            </div>
                                                        @endif
                                                    </div>
                                                    @if ($isAdminBusinessSelected)
                                                        {{-- Modais do negócio ADMIN --}}
                                                        @include('clients.adminBusiness.modal_edit', [
                                                            'modalId' => 'edit-collaborator-' . $client['id'],
                                                        ])
                                                        @include('clients.adminBusiness.modal_show', [
                                                            'modalId' => 'modal-view-client-' . $client['id'],
                                                        ])
                                                    @else
                                                        {{-- Modais PADRÃO --}}
                                                        @include('clients.modal_show', [
                                                            'modalId' => 'modal-view-client-' . $client['id'],
                                                        ])
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="btn-form">
                                                        <div>{{ $client['name'] }}</div>
                                                        <div style="color: #909090">{{ StringMask::cpf($client['cpf']) }}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="btn-form">
                                                        <div>{{ $client['email'] }}</div>
                                                        <div>{{ StringMask::phone($client['phone_number_1']) }}</div>
                                                    </div>
                                                </td>
                                                @php
                                                    $businesses = [];
                                                    $allBusinesses = [];
                                                    $clientBusinesses = $client['businesses'];
                                                    $user = ApiUser::get();
                                                    $userSelectedBusiness = $user['business_selected'] ?? null;
                                                    $canReleaseAllBusiness =
                                                        $user['authorizedFunction']['release_all_business'];

                                                    $businesses = array_map(function ($business) {
                                                        return $business['name'];
                                                    }, $clientBusinesses);
                                                    usort($businesses, function ($a, $b) {
                                                        return $a <=> $b;
                                                    });
                                                    $businesses = implode(', ', $businesses);

                                                    if (count($clientBusinesses) == 0) {
                                                        $businesses = 'Sem negócio';
                                                    }
                                                @endphp
                                                <td style="max-width: 12rem; text-overflow: ellipsis; overflow: hidden;"
                                                    title="{{ $businesses }}">
                                                    {{ $businesses }}
                                                </td>
                                                @if ($isAdminBusinessSelected)
                                                    <td style="max-width: 12rem; text-overflow: ellipsis; overflow: hidden;"
                                                        title="{{ $userTypes }}">
                                                        {{ $userTypes }}
                                                    </td>
                                                @endif
                                            </tr>
                                        @endforeach
                                    @endif
                                </tbody>
                            </table>
                        </div>

                        {{ $clients->onEachSide(0)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                        style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('clients.subtitle')
                    </div>
                </div>
            </div>
        </div>

    </div>
    @if ($message = Session::get('success'))
        <div class="alert alert-success alert-block shadow fade show"
            style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
            <strong>{{ $message }}</strong>
        </div>
    @elseif($message = Session::get('error'))
        <div class="alert alert-danger alert-block shadow fade show"
            style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
            <strong>{{ $message }}</strong>
        </div>
    @endif
    </div>
    <style>
        @media screen and (max-width: 795px) and (min-width: 0px) {
            #labelImportar {
                display: none;
            }
        }
    </style>
    @include('components.loading', ['message' => 'Carregando...', 'id' => 'loadingclient'])
    @include('clients.modal_create', ['modalId' => 'modalCreateClient'])
    <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>
    <script>
        function formatCPF(cpf) {
            cpf = cpf.replace(/\D/g, ""); // Remove all non-digit characters
            cpf = cpf.replace(/(\d{3})(\d)/, "$1.$2"); // Add a dot after the first 3 digits
            cpf = cpf.replace(/(\d{3})(\d)/, "$1.$2"); // Add a dot after the next 3 digits
            cpf = cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2"); // Add a dash before the last 2 digits
            return cpf;
        }

        document.addEventListener('DOMContentLoaded', function() {
            const cpfInput = document.getElementById('cpf_search');
            cpfInput.addEventListener('input', function() {
                cpfInput.value = formatCPF(cpfInput.value);
            });
        });
    </script>
    <script>
        $(document).ready(() => {
            $('#clientProfileSelect').on('change', () => {
                $('#form_search_clients_index').submit();
            })
        })

        function validateCpf() {
            let cpfInput = document.getElementById('cpf_search');
            let cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

            let isValid = cpfIsValid(cpf);

            if (isValid) {
                cpfInput.classList.remove("is-invalid")
                cpfInput.classList.add("is-valid")
                return true;
            } else {
                cpfInput.classList.add("is-invalid")
                return false;
            }
        }

        function search() {
            if (validateCpf()) {
                $('#cpfModal').modal('hide');
                $('#loadingclient').modal('show');
                searchclient();
            } else {
                alert('CPF inválido');
            }
        }

        function searchclient() {
            let cpf = $('#cpf_search').val().replace(/\D/g, '');
            $.ajax({
                url: "<?= env('API_URL') ?>/api/user/show/by-cpf",
                type: 'get',
                data: {
                    cpf: cpf
                },
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function(response) {
                    $('#loadingclient').modal('hide');
                    if (response.employee != null) {
                        if (response.employee.id === {{ ApiUser::get()['id'] }} && response.client != null) {
                            alert(
                                "Não é possível alterar seus dados por essa tela. Para alterar seus dados, entre na tela de perfil."
                            );
                            return;
                        }

                        if (response.employee.admin) {
                            alert(
                                'Esse cpf já está sendo utilizado por outro cliente e você não tem permissão para alterar seus dados.'
                            );
                            return;
                        }
                    }

                    if (response.client != null) {
                        let clientId = response.client.id;
                        let urlEdit = "{{ route('clients.edit', 'id') }}";
                        urlEdit = urlEdit.replace('id', clientId);
                        window.location.href = urlEdit;
                    } else {
                        window.location.href = "{{ route('clients.create') }}?cpf=" + cpf;
                    }
                },
                error: function(response, textStatus, msg) {
                    $('#loadingclient').modal('hide');
                    window.location.href = "{{ route('clients.create') }}?cpf=" + cpf;
                },
            });
        }
    </script>
@endsection
