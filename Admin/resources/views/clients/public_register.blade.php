@extends('layouts.guest', ['activePage' => 'clients'])

@section('content')
    @if(!isset($cpf) || $cpf == null)
      <div class="container">
        <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
          <div class="card shadow-sm" style="width: 100%; max-width: 400px; border-radius: 12px;">
            <div class="card-body">
              <h5 class="card-title text-center mb-4" style="font-size: 1.5rem; color: #0842A0;">
                Digite o CPF para continuar
              </h5>
              <form id="cpf-search-form" onsubmit="event.preventDefault(); search();">
                <div class="mb-3">
                  <label for="cpf_search" class="form-label" style="font-weight: 500;">CPF <span style="color:red">*</span></label>
                  <input type="text" class="form-control" id="cpf_search" name="cpf" maxlength="14" required placeholder="000.000.000-00" autocomplete="off" style="border-radius: 8px;">
                </div>
                <button type="submit" class="btn btn-primary w-100" style="border-radius: 8px;">
                  Continuar
                </button>
                <a href="{{ route('login') }}" class="btn btn-secondary w-100 mt-2" style="border-radius: 8px;">
                    Voltar
                </a>
              </form>
            </div>
          </div>
        </div>
      </div>
      @include('components.loading', ['message' => 'Carregando...', 'id' => 'loadingclient'])
      <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>
      <script>
        function search() {
            if (validateCpf()) {
                $('#cpfModal').modal('hide');
                $('#loadingclient').modal('show');
                searchclient();
            } else {
                Swal.fire({
                    title: 'CPF inválido',
                    text: 'Por favor, insira um CPF válido.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        }

        function searchclient() {
            let cpf = $('#cpf_search').val().replace(/\D/g, '');
            $.ajax({
                url: "<?= env('API_URL') ?>/api/user/checkCpfExists",
                type: 'get',
                data: {cpf: cpf},
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {
                    $('#loadingclient').modal('hide');

                    if (response.user != null && response.user == true) {
                        Swal.fire({
                          title: 'CPF já cadastrado',
                          text: 'Este CPF já está cadastrado no sistema. Para acessar sua conta, faça login.',
                          icon: 'info',
                          showCancelButton: true,
                          confirmButtonText: 'Fazer Login',
                          cancelButtonText: 'Tentar outro CPF'
                        }).then((result) => {
                          if (result.isConfirmed) {
                            window.location.href = "{{ route('login') }}";
                          }
                        });
                        return;
                    }
                    window.location.href = "{{ route('clients.publicRegister') }}?cpf=" + cpf;
                },
                error: function (response, textStatus, msg) {
                    alert('Erro ao buscar CPF');
                    $('#loadingclient').modal('hide');
                },
            });
        }

        function validateCpf() {
            let cpfInput = document.getElementById('cpf_search');
            let cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

            let isValid = cpfIsValid(cpf);

            if (isValid) {
                cpfInput.classList.remove("is-invalid")
                cpfInput.classList.add("is-valid")
                return true;
            } else {
                cpfInput.classList.add("is-invalid")
                return false;
            }
        }

        function applyMasks() {
          $("#cpf_search").mask('000.000.000-00');
        }

        $(document).ready(function () {
          applyMasks();
          @if($message = Session::get('danger'))
          //CPF já cadastrado no sistema. Para acessar sua conta, faça login.
            Swal.fire({
              title: 'CPF já cadastrado',
              text: 'Este CPF já está cadastrado no sistema. Para acessar sua conta, faça login.',
              icon: 'info',
              showCancelButton: true,
              confirmButtonText: 'Fazer Login',
              cancelButtonText: 'Tentar outro CPF'
            }).then((result) => {
              if (result.isConfirmed) {
                window.location.href = "{{ route('login') }}";
              }
            });
          @endif
        });
      </script>
    @else
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-md-12" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex align-items-center"
                                 style="margin-top: 9px; margin-bottom:6px">
                                 <a  href="{{route('clients.publicRegister')}}"class="mx-2" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw "></i>
                                 </a>
                                <h5 class="card-title mb-0"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Cadastro</h5>
                            </div>
                            <div class="col-3">
                                <span style="color:red" class="table-legend">* Campos obrigatórios</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('clients.publicStore') }}" id="form-create-client" method="POST"
                              onsubmit="event.preventDefault();">
                            @csrf
                            @include('clients.public_form')
                            @if(isset($user))
                                <input type="hidden" name="user_exists" id="user_exists" value="1">
                            @else
                                <input type="hidden" name="user_exists" id="user_exists" value="0">
                            @endif
                            <div class="modal-footer d-flex flex-column align-items-start w-100">
                                <div style="padding: 0 0 10px 4px;" class="justify-content-start">
                                    <input type="checkbox" class="form-check-input" id="term-checkbox" name="term"
                                           required>
                                    <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os
                                        dados passados de acordo com o termo em anexo <a
                                            href="{{route('data-processing-agreement', ['user_type' => 'client'])}}"
                                            target="_blank">(clique aqui para ler o termo).</a></label>
                                </div>
                                <div class="d-flex justify-content-end w-100">
                                    <button id="submit-button" disabled type="button" class="btn btn-success"
                                            onclick="validateStore('form-create-client');">Continuar
                                    </button>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if($message = Session::get('danger'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>

    <script>
        $("#term-checkbox").click(showHideFormButton);

        function showHideFormButton() {
            if ($("#term-checkbox").is(":checked")) {
                $("#submit-button").prop("disabled", false);
            } else {
                $("#submit-button").prop("disabled", true);
            }
        }
    </script>
    @endif
@endsection
