{{-- Informações Básicas --}}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-user-alt mr-2"></i>Informações básicas</h5>
    </div>
    <div class="card-body">
        <div class="form-row">
            <div class="col-md-12">
                <h6 class="style_campo_titulo"><PERSON><PERSON> pessoais</h6>
            </div>
            <div class="col-md-12">
                <label for="">Nome completo</label>
                <input type="text" class="form-control style_campo_estatico"
                        value="{{ $client['name'] }}" disabled>
            </div>
            <div class="col-md-4" style="margin-top: 10px">
                <label for="">Gênero</label>
                <input type="text" class="form-control style_campo_estatico"
                        value="{{ StringMask::gender($client['gender']) }}" disabled>
            </div>
            <div class="col-md-4" style="margin-top: 10px">
                <label for="">Data de nascimento</label>
                <input type="text" class="form-control style_campo_estatico"
                        value="{{ StringMask::brDate($client['birth_date']) }}" disabled>
            </div>
            <div class="col-md-4" style="margin-top: 10px">
                <label for="">CPF</label>
                <input type="text" class="form-control style_campo_estatico"
                        value="{{ StringMask::cpf($client['cpf']) }}" disabled>
            </div>
    
            <div class="col-md-12" style="margin-top: 20px">
                <h6 class="style_campo_titulo">Contato</h6>
            </div>
            <div class="col-md-6" style="margin-top: 10px">
                <label for="">Email</label>
                <input type="text" class="form-control style_campo_estatico"
                        value="{{ $client['email']?? 'Não informado' }}" disabled>
            </div>
            <div class="col-md-{{ isset($client['phone_number_2']) ? 3 : 6 }}" style="margin-top: 10px">
                <label for="">Telefone 1</label>
                <input type="text" class="form-control style_campo_estatico"
                        value="{{ StringMask::phone($client['phone_number_1']) }}" disabled>
            </div>
            @if(isset($client['phone_number_2']))
                <div class="col-md-3" style="margin-top: 10px">
                    <label for="">Telefone 2</label>
                    <input type="text" class="form-control style_campo_estatico"
                            value="{{ StringMask::phone($client['phone_number_2']) }}" disabled>
                </div>
            @endif
            <div class="col-md-12" style="margin-top: 20px">
                <x-dropdown-section title="Endereço">
                    <div class="form-row">
                        <div class="col-md-12">
                            <label for="">CEP</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                    value="{{ StringMask::cep($client['address']['cep']) }}">
                        </div>
                        <div class="col-md-6" style="margin-top: 10px">
                            <label for="">Estado</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                    value="{{ $client['address']['state'] }}">
                        </div>
                        <div class="col-md-6" style="margin-top: 10px">
                            <label for="">Cidade</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                    value="{{ $client['address']['city'] }}">
                        </div>
                        <div class="col-md-6" style="margin-top: 10px">
                            <label for="">Bairro</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                    value="{{ $client['address']['neighborhood'] }}">
                        </div>
                        <div class="col-md-6" style="margin-top: 10px">
                            <label for="">Rua</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                    value="{{ $client['address']['street'] }}">
                        </div>
                        <div class="col-md-6" style="margin-top: 10px">
                            <label for="">Número</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                    value="{{ $client['address']['number'] }}">
                        </div>
                        <div class="col-md-6" style="margin-top: 10px">
                            <label for="">Complemento</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                    value="{{ $client['address']['complement'] }}">
                        </div>
                    </div>
                </x-dropdown-section>
            </div>
            <div class="col-md-12" style="margin-top: 20px">
                @php
                    $user = ApiUser::get();
                    $userBusiness = $user['businesses'] ?? [];
                    $clientBusiness = $client['businesses'] ?? [];
                    $canReleaseAllBusinesses = $user['authorizedFunction']['release_all_business'];

                    $userBusiness = array_map(function ($business) {
                        return $business['name'];
                    }, $userBusiness);

                    $clientBusiness = array_map(function ($business) {
                        return $business['name'];
                    }, $clientBusiness);

                    if ($canReleaseAllBusinesses) {
                        $businesses = $clientBusiness;
                    } else {
                        $businesses = array_intersect($clientBusiness, $userBusiness);
                    }
                @endphp
                <x-dropdown-section title="Negócios">
                    <ul class="list-group">
                        @if (count($businesses) > 0)
                            @foreach ($businesses as $business)
                                <li class="list-group-item">{{ $business }}</li>
                            @endforeach
                        @else
                            <li class="text-muted text-center fst-italic py-2 px-1">
                                Nenhum negócio associado a este cliente.
                            </li>
                        @endif
                    </ul>
                </x-dropdown-section>
            </div>
        </div>
    </div>
</div>