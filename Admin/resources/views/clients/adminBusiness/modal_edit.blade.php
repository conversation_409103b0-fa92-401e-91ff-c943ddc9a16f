<x-modal.modal_confirm_action 
    id="{{$modalId}}" 
    title="Editar cliente"
    submitButtonText="Editar"
    submitButtonClass="btn-primary"
    onSubmit="$('#client-edit-{{$client['id']}}').submit()"
>
    <form action="{{ route('clients.adminEdit')}}" id="client-edit-{{$client['id']}}" method="GET">
        @csrf
        <div class="row">
            <div class="col">
                @if (ApiUser::get()["business_selected"] != null && !$isAdminBusinessSelected)
                    Você deseja mesmo deletar <b>{{ $client['name'] }}</b> do negócio: 
                    <b>{{ ApiUser::get()["business_selected"]['name'] }}</b>?
                @elseif ($isAdminBusinessSelected)
                    @if(isset($client['client']))
                        <input type="hidden" id="client_id" name="client_id" value="{{$client['client']['id']}}">
                    @endif

                    @if(isset($client['employee']))
                        <input type="hidden" id="employee_id" name="employee_id" value="{{$client['employee']['id']}}">
                    @endif

                    @if(isset($client['seller']))
                        <input type="hidden" id="seller_id" name="seller_id" value="{{$client['seller']['id']}}">
                    @endif
                    <div class="mb-3">
                        <label for="client_type">Escolha o perfil a editar</label>
                        <select class="form-select form-control" name="client_type" id="client_type_select">
                            @php
                                // ordem de prioridade
                                $typesPriorityOrder = ['admin', 'client', 'employee', 'seller'];

                                $canSeeSellers = ApiUser::hasUserFunction('sellers') ?? false;

                                if (!$canSeeSellers) {
                                    unset($typesPriorityOrder['seller']);
                                }

                                // Filtra e ordena os tipos conforme a prioridade
                                $orderedTypes = collect($typesPriorityOrder)
                                    ->filter(fn($type) => isset($client['types'][$type]) && $client['types'][$type]['hasType'])
                                    ->mapWithKeys(fn($type) => [$type => $client['types'][$type]]);
                            @endphp
                            @foreach ($orderedTypes as $type => $details)
                                <option value="{{ $type }}" @if ($loop->first) selected @endif>{{ $details['name'] }}</option>
                            @endforeach
                        </select>
                    </div>
                @endif
            </div>
        </div>
    </form>
</x-modal.modal_confirm_action>
