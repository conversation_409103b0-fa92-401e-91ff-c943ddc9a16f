@php use App\Helpers\StringMask; @endphp
<div class="modal fade" id="{{ $modalId }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Dados do cliente</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto" style="text-align: left; max-height: calc(100vh - 200px); overflow-y: auto;">
                @include('clients.adminBusiness.modalShowParts.basic_info')
                @if(isset($client['client']))
                    @include('clients.adminBusiness.modalShowParts.client')
                @endif

                @if(isset($client['employee']))
                    @include('clients.adminBusiness.modalShowParts.employee')
                @endif

                @if(isset($client['seller']))
                    @include('clients.adminBusiness.modalShowParts.seller')
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" style="width:30%">Fechar</button>
            </div>
        </div>
    </div>
</div>
