{{-- Informações Básicas --}}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-user-alt mr-2"></i>Informações básicas</h5>
        <small class="text-muted">* O email ou o telefone devem ser informados</small>
    </div>
    <div class="card-body">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'name', 
                'label' => 'Nome', 
                'value' =>  old('name') ?? '', 
                'placeholder' => 'Digite o nome do cliente', 
                'withErrorText' => true
            ])
        </div>
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'email', 
                'required' => false, 
                'label' => 'Email', 
                'placeholder' => 'Digite o email do cliente', 
                'value' =>  old('email') ?? '', 
                'withErrorText' => true
            ])
        </div>
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'phone_number_1',
                'label' => 'Telefone/Celular',
                'value' => old('phone_number_1') ?? '',
                'placeholder' => 'Digite o número do telefone do cliente',
                'withErrorText' => true,
                'required' => false,
                'class' => 'phone-input'
            ])        
        </div>
    </div>
</div>

<script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
<script>
    $(document).ready(() => {
        applyMasks();
    })

    function applyMasks() {
        $("#phone_number_1").mask(getMask('phone_number_1')).keyup(function (event) {
            checkPhoneMask(event.target, event.originalEvent.key, counter_phone1);
        });
    }
</script>