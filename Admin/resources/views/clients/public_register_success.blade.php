@extends('layouts.guest', ['activePage' => 'clients'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12" style="margin-bottom: 2rem;">
              <div style="margin-bottom: 2rem;background-color: #d4edda; border-color: #c3e6cb; color: #155724; padding: .75rem 1.25rem; border-radius: .25rem;">
                  <h4 class="alert-heading">Cadastro realizado com sucesso!</h4>
                  @if(isset($isCreateNewClientAndUser) && $isCreateNewClientAndUser)
                      <p>O seu cadastro foi realizado com sucesso.</p>
                      <hr>
                      <p class="mb-2">Uma senha foi enviada para o seu email para acessar o sistema.</p>
                  @else
                      <p class="mb-2">Você pode acessar o sistema com o email e senha cadastrados.</p>
                  @endif
                  <p>Para entrar no sistema clique no botão abaixo:</p>
                  <a href="{{ route('login') }}" class="btn btn-primary">Entrar</a>
              </div>
            </div>
        </div>
    </div>
@endsection

@if(isset($isCreateNewClientAndUser) && $isCreateNewClientAndUser)
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            Swal.fire({
                icon: 'success',
                title: 'Cadastro realizado com sucesso!',
                text: 'Uma senha foi enviada para o seu email para acessar o sistema.',
                confirmButtonColor: '#3085d6'
            });
        });
    </script>
@endif
