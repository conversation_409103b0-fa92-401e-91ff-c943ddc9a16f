<div class="form-row">
    <div class="col-md-12"><p class="form-title"><PERSON><PERSON> pessoais</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'name',
                'label' => 'Nome completo',
                'value' => old('name') ?? ($client['name'] ?? $user['name']?? ''),
                'placeholder' => 'Digite o nome completo do cliente',
                'withErrorText' => true,
                'disabled' => isset($user)
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="gender">Gênero</label>
            <select id="gender" name="gender" class="form-select form-control @error('gender') is-invalid @enderror"
                    aria-label="Default select example" required @if(isset($user)) disabled @endif>
                <option value="other">-- Não Informar --</option>
                <option value="m"
                        @if(old('gender') == 'm' || isset($client) && $client['gender'] == 'm' || isset($user) && $user['gender'] == 'm') selected @endif >
                    Masculino
                </option>
                <option value="f"
                        @if(old('gender') == 'f' || isset($client) && $client['gender'] == 'f' || isset($user) && $user['gender'] == 'f') selected @endif >
                    Feminino
                </option>
            </select>
            @error('gender')
            <span class="invalid-feedback" role="alert">
                <strong>{{$message}}</strong>
            </span>
            @enderror
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.date', [
                'name' => 'birth_date',
                'label' => 'Data de nascimento',
                'value' => (old('birth_date') ?? ($client['birth_date'] ?? $user['birth_date'] ?? '')),
                'withErrorText' => true,
                'required' => false,
                'disabled' => isset($user)
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'cpf',
                'label' => 'CPF',
                'value' => old('cpf') ?? ($client['cpf'] ?? $user['cpf'] ?? $cpf ?? ''),
                'placeholder' => 'Digite o CPF',
                'onchange' =>'checkCPF()',
                'withErrorText' => true,
                'disabled' => true
            ])
            <input type="hidden" name="cpf" id="cpf" value="{{ old('cpf') ?? ($client['cpf'] ?? $user['cpf'] ?? $cpf ?? '') }}">
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Contato</p></div>
    @php
        $initialPhoneForValidationLogic = '';
        $initialPhoneValidated = false;
        if (isset($client['phone_number_1']) || isset($user['phone_number_1'])) {
            $rawPhone = $client['phone_number_1'] ?? $user['phone_number_1'] ?? '';
            $initialPhoneForValidationLogic = preg_replace('/\D/', '', $rawPhone);
            $initialPhoneValidated = !empty($rawPhone);
        }
        $isInitialPhoneActuallyValid = (!empty($initialPhoneForValidationLogic)) && preg_match('/^\d{10,11}$/', $initialPhoneForValidationLogic);
        $currentClientCpf = $client['cpf'] ?? $user['cpf'] ?? $cpf ?? '';
    @endphp
    <input type="hidden" id="initial_phone_number" value="{{ $initialPhoneForValidationLogic }}" name="initial_phone_number">
    <div class="col-md-6">
        @include('components.inputs.phone_number_with_validation', [
            'name' => 'phone_number_1',
            'id' => 'phone_number_1',
            'label' => 'Telefone/Celular 1',
            'class' => 'phone-input',
            'value' => old('phone_number_1') ?? ($client['phone_number_1'] ?? $user['phone_number_1'] ?? ''),
            'placeholder' => 'Digite o número do telefone do cliente',
            'disabled' => isset($user),
            'withHiddenField' => isset($user),
            'currentClientCpf' => $currentClientCpf,
            'validationFieldName' => 'phone_validated',
            'initialValidated' => ($isInitialPhoneActuallyValid && $initialPhoneValidated) ? '1' : '0'
        ])
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'phone_number_2',
                'label' => 'Telefone/Celular 2',
                'class' => 'phone-input',
                'value' => old('phone_number_2') ?? ($client['phone_number_2'] ?? $user['phone_number_2'] ?? ''),
                'placeholder' => 'Digite o número do telefone do cliente',
                'required' => false,
                'disabled' => isset($user)
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Login</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'email',
                'type' => 'email',
                'label' => 'E-mail',
                'value' => old('email') ?? ($client['email'] ?? $user['email'] ?? ''),
                'placeholder' => 'Digite o e-mail do cliente',
                'withErrorText' => true,
                'required' => true,
                'disabled' => isset($user)
            ])
            @if(isset($user))
                <input type="hidden" name="email" id="email" value="{{ old('email') ?? ($client['email'] ?? $user
                    ['email'] ?? '') }}">
            @endif
        </div>
    </div>
    @if(isset($client))
        <div class="col-md-12">
            <div class="form-group">
                <input type="checkbox" id="reset_password" name="reset_password">
                <label for="reset_password">Resetar a senha do cliente</label>
            </div>
        </div>
    @endif
    <div class="col-md-12"><p class="form-title">Empreendedor</p></div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="is_entrepreneur">É um Empreendedor? <span style="color: red">*</span></label>
            <select class="form-control" name="is_entrepreneur" id="selectIsEntreprenur" required>
                <option value="" selected disabled>Selecione se é empreendedor</option>
                <option
                    value="1" {{ isset($client['is_entrepreneur']) && $client['is_entrepreneur'] ? 'selected' : '' }}>
                    Sim
                </option>
                <option
                    value="0" {{ isset($client['is_entrepreneur']) && !$client['is_entrepreneur'] ? 'selected' : '' }}>
                    Não
                </option>
            </select>
            <small id="error-is_entrepreneur" class="text-danger"></small>
        </div>
    </div>

    <input type="hidden" name="email_validated" id="email_validated" value="0">

    @include('components.loading', ['message' => 'Aguardando confirmação de email'])
    @include('components.modal.modal_confirm_email', ['modalId' => 'modal-confirm-email'])

    @push('js')
        <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>

        <script type="text/javascript">
            // =======================================
            // VARIÁVEIS GLOBAIS
            // =======================================
            
            // CPF do cliente atual - disponível globalmente para o modal WhatsApp
            window.currentClientCpf = "{{ $currentClientCpf }}".replace(/\D/g, '');

            // =======================================
            // FUNÇÕES DE VALIDAÇÃO DE EMAIL E CPF
            // =======================================
            function validateEmailAddress(emailInput, errorElement) {
                let email = emailInput.val();
                const currentClientCpf = "{{$client['cpf'] ?? $user['cpf'] ?? $cpf ?? ''}}";

                emailInput.removeClass('is-invalid');
                errorElement.text('');

                if (email.length === 0) {
                    return; // Não valida se o campo estiver vazio
                }

                // Verifica se o email tem um formato básico válido antes de enviar a requisição
                if (!/\S+@\S+\.\S+/.test(email)) {
                    emailInput.addClass('is-invalid');
                    errorElement.text('Por favor, insira um endereço de e-mail válido.');
                    return;
                }

                $.ajax({
                    url: `<?= env('API_URL') ?>/api/user/check-email/${email}`,
                    type: 'GET',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                    },
                    success: function (response) {
                        // A API retorna 404 se o email não for encontrado, então success significa que foi encontrado
                        // Verifica se o email encontrado pertence a outro usuário com CPF completo
                        if (response && response.id && response.cpf != currentClientCpf && response.cpf_status === 'completo') {
                            emailInput.addClass('is-invalid');
                            errorElement.text('Este email já está associado a outro usuário.');
                        } else {
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Se a resposta for 404, significa que o email não foi encontrado, o que é OK
                        if (jqXHR.status === 404) {
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        } else {
                            console.error("Erro ao validar email:", textStatus, errorThrown);
                            // Para outros erros, remove a validação para não bloquear o usuário indevidamente
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        }
                    },
                });
            }

            function checkCPF() {
                var cpfInput = document.getElementById('cpf');
                var cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

                var isValid = cpfIsValid(cpf);

                if (isValid) {
                    cpfInput.classList.remove("is-invalid")
                    cpfInput.classList.add("is-valid")
                } else {
                    cpfInput.classList.add("is-invalid")
                }
            }

            // =======================================
            // MÁSCARAS E FORMATAÇÃO
            // =======================================
            function applyMasks() {
                $("#cpf").mask('000.000.000-00');
                $("#cpf_search").mask('000.000.000-00');
                $("#cep").mask('00000-000');
                $("#phone_number_2").mask(getMask('phone_number_2')).keyup(function (event) {
                    checkPhoneMask(event.target, event.originalEvent.key, counter_phone2);
                });
            }

            // =======================================
            // INICIALIZAÇÃO E EVENT LISTENERS
            // =======================================
            $(document).ready(function () {
                let debounceTimerEmail;

                // Event listener para input de email
                $('#email').on('input', function() {
                    clearTimeout(debounceTimerEmail);
                    const emailInput = $(this);
                    const errorElement = $('#error-email');
                    let email = emailInput.val();

                    // Verifica se o email tem um formato básico válido antes de enviar a requisição
                    if (email.length > 0 && /\S+@\S+\.\S+/.test(email)) {
                         debounceTimerEmail = setTimeout(() => {
                            validateEmailAddress(emailInput, errorElement);
                         }, 500); // 500ms debounce
                    } else {
                        // Limpa erros se o email for inválido ou vazio
                        emailInput.removeClass('is-invalid');
                        errorElement.text('');
                    }
                });

                // Aplicar máscaras
                applyMasks();
            });

            // =======================================
            // VARIÁVEIS GLOBAIS AUXILIARES
            // =======================================
            var counter_phone2 = {value: 0};

            // =======================================
            // INTEGRAÇÃO COM COMPONENTE DE VALIDAÇÃO
            // =======================================
            
            // Listener para evento de sucesso do componente de validação de telefone
            $(document).on('phoneValidation:success', function(event, data) {
                // Atualiza também o valor inicial para que a comparação funcione corretamente
                $('#initial_phone_number').val(data.phoneNumber);
                checkFormCompletionAndEnableSubmit();
            });

            // =======================================
            // FUNÇÕES AUXILIARES PARA CEP
            // =======================================
            $("#cep").focusout(function () {
                $.ajax({
                    url: 'https://viacep.com.br/ws/' + $(this).val().replaceAll('-', '') + '/json/',
                    dataType: 'json',
                    success: function (response) {
                        $("#complement").val(response.complemento);
                        $("#street").val(response.logradouro);
                        $("#neighborhood").val(response.bairro);
                        $("#city").val(response.localidade);
                        $("#state").val(response.uf);
                        $("#number").focus();
                    }
                });
            });

            // =======================================
            // FUNÇÕES DE SUBMISSÃO DO FORMULÁRIO
            // =======================================
            function validateStore(formId, emailChange = false) {
                const form = document.getElementById(formId);
                const formData = new FormData(form);
                const isPhoneValidated = $('#phone_validated').val() === '1';

                if (!isPhoneValidated) {
                    alert('É necessário validar o telefone pelo WhatsApp antes de continuar.');
                    $('#phone_number_1').focus();
                    return;
                }

                formData.forEach(function (value, key) {
                    if (['cpf', 'cep', 'phone_number_1', 'phone_number_2'].includes(key)) {
                        formData.set(key, value.replaceAll('.', '').replaceAll('-', '').replaceAll('(', '').replaceAll(')', '').replaceAll(' ', ''));
                    }
                    if (key == '_method') {
                        formData.delete(key);
                    }
                    if (key == 'user_exists') {
                        formData.delete(key);
                        @if(isset($user))
                            formData.append('user_exists', 1);
                        @else
                            formData.append('user_exists', 0);
                        @endif
                    }
                });

                let email = $('#email').val();
                let isSetUser = {{isset($user) ? 'true' : 'false'}};
                let isSetClient = {{isset($client) ? 'true' : 'false'}};
                let oldEmailUser = "{{isset($user) ?  $user['email'] : ''}}";

                if (form.reportValidity()) {
                    $('#loading').modal('show');
                    $.ajax({
                        url: "<?= env('API_URL') ?>/api/clients/publicValidateStore",
                        type: 'post',
                        data: formData,
                        cache: true,
                        contentType: false,
                        processData: false,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                        },
                        success: function (response) {
                            $('#loading').modal('hide');
                            removeErrorMessages();
                            if ((formId === 'form-create-client' &&
                                    !(isSetUser && oldEmailUser === email)) ||
                                ((emailChange && email !== "") && !(isSetUser && oldEmailUser === email)) ||
                                (isSetUser && oldEmailUser !== email)
                            ) {
                                confirmClientEmail(formId);
                            } else form.submit();
                        },
                        error: function (response, textStatus, msg) {
                            $('#loading').modal('hide');
                            if (response.status == 422) {
                                const errors = response.responseJSON.errors;
                                removeErrorMessages();
                                removeIsInvalidClass();

                                Object.entries(errors).forEach(([field, error]) => {
                                    const id = 'error-' + field;
                                    document.getElementById(id).innerHTML = error;
                                    errorsIdToRemove.push(id);
                                })

                                Object.keys(errors).forEach((field) => {
                                    document.getElementById(field).classList.add('is-invalid')
                                    removeIsInvalidClassId.push(field)
                                })
                            }
                            alert('Preencha todos os dados corretamente.');
                        }
                    });
                }
            }

            function confirmClientEmail(formId) {
                const email = document.getElementById('email').value;
                const name = document.getElementById('name').value;
                const form = document.getElementById(formId);

                if (form.reportValidity() && email) {
                    const formData = new FormData();
                    formData.append('email', email);
                    formData.append('name', name);

                    $('#loading').modal('show');

                    $.ajax({
                        url: "{{route('send.email.client.confirm')}}",
                        type: 'post',
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        beforeSend: function (xhr) {
                        },
                        success: function (response) {
                            const code = response.code;
                            const modal = document.getElementById('modal-confirm-email');
                            modal.dataset.code = code;
                            modal.dataset.formId = formId;
                            modal.dataset.email = email;
                            modal.dataset.name = name;

                            $('#loading').modal('hide');
                            $("#modal-confirm-email").modal('toggle');
                        },
                        error: function (response, textStatus, msg) {
                            $('#loading').modal('hide');
                            alert('Falha ao enviar email de confirmação!');
                        }
                    });
                }
            }

            // =======================================
            // FUNÇÕES DE VERIFICAÇÃO DE DUPLICAÇÃO
            // =======================================
            // NOTA: Esta função foi renomeada para evitar conflito com o componente
            function validatePhoneNumberDuplication(phoneInput, errorElement) {
                let phoneNumber = phoneInput.val().replace(/\D/g, '');
                const currentClientCpf = window.currentClientCpf;
                const phoneInputId = phoneInput.attr('id');

                // Limpeza inicial de elementos de validação por WhatsApp para este campo específico
                $(`#btn-validate-whatsapp-${phoneInputId}`).remove();
                $(`#whatsapp-validation-code-${phoneInputId}`).remove();
                $(`#btn-confirm-whatsapp-code-${phoneInputId}`).remove();
                $(`#whatsapp-validation-message-${phoneInputId}`).remove();

                phoneInput.removeClass('is-invalid');
                errorElement.text('');
                if (phoneNumber.length < 10) {
                    return;
                }

                $.ajax({
                    url: `<?= env('API_URL') ?>/api/user/check-phone/${phoneNumber}`,
                    type: 'GET',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                    },
                    success: function (response) {
                        let isDuplicate = false;
                        if (Array.isArray(response)) {
                            response.forEach(user => {
                                if (user.cpf_status === 'completo' && user.cpf != currentClientCpf) {
                                    isDuplicate = true;
                                }
                            });
                        }

                        // Limpa elementos de validação por WhatsApp de uma tentativa anterior antes de decidir o que mostrar
                        $(`#btn-validate-whatsapp-${phoneInputId}`).remove();
                        $(`#whatsapp-validation-code-${phoneInputId}`).remove();
                        $(`#btn-confirm-whatsapp-code-${phoneInputId}`).remove();
                        $(`#whatsapp-validation-message-${phoneInputId}`).remove();

                        if (isDuplicate) {
                            phoneInput.addClass('is-invalid');
                            errorElement.text('Este telefone já está associado a outro usuário.');

                            // Adiciona o botão para abrir o modal de validação do WhatsApp
                            $(`#btn-validate-whatsapp-${phoneInputId}`).remove();
                            const btnValidateWhatsApp = $(`<button type="button" id="btn-validate-whatsapp-${phoneInputId}" class="btn btn-sm btn-warning mt-2">Validar por WhatsApp</button>`);
                            errorElement.after(btnValidateWhatsApp);

                        } else {
                            phoneInput.removeClass('is-invalid');
                            errorElement.text('');
                            // Garante que o botão de validação por WhatsApp seja removido se o número não for duplicado
                            $(`#btn-validate-whatsapp-${phoneInputId}`).remove();
                        }
                        // Limpeza adicional para garantir que elementos de validação inline antigos sejam removidos
                        $(`#whatsapp-validation-code-${phoneInputId}`).remove();
                        $(`#btn-confirm-whatsapp-code-${phoneInputId}`).remove();
                        $(`#whatsapp-validation-message-${phoneInputId}`).remove();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        phoneInput.removeClass('is-invalid');
                        errorElement.text('');
                        // Garante que o botão de validação por WhatsApp seja removido em caso de erro na verificação inicial
                        $(`#btn-validate-whatsapp-${phoneInputId}`).remove();
                        // Limpeza adicional para garantir que elementos de validação inline antigos sejam removidos
                        $(`#whatsapp-validation-code-${phoneInputId}`).remove();
                        $(`#btn-confirm-whatsapp-code-${phoneInputId}`).remove();
                        $(`#whatsapp-validation-message-${phoneInputId}`).remove();
                    },
                });
            }

            // =======================================
            // INTERVALOS E INICIALIZAÇÕES FINAIS
            // =======================================
            setInterval(checkCPF, 1000);

            // =======================================
            // VERIFICAÇÃO DE SUBMISSÃO DO FORMULÁRIO
            // =======================================

            // Função para verificar se o formulário pode ser enviado
            function checkFormCompletionAndEnableSubmit() {
                const isPhoneValidated = $('#phone_validated').val() === '1';
                const areTermsAccepted = $('#term-checkbox').is(':checked');

                // Habilita o botão de submit apenas se o telefone estiver validado e os termos aceitos
                if (isPhoneValidated && areTermsAccepted) {
                    $('#submit-button').prop('disabled', false);
                } else {
                    $('#submit-button').prop('disabled', true);
                }
            }

            // Listener para mudanças no checkbox de termos
            $(document).on('change', '#term-checkbox', checkFormCompletionAndEnableSubmit);

            // Inicialização da verificação
            $(document).ready(function() {
                checkFormCompletionAndEnableSubmit();
            });
        </script>
@endpush
