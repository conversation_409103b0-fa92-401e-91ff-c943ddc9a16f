<div class="form-row">
    <div class="col-md-12"><p class="form-title"><PERSON><PERSON> pessoais</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'name',
                'label' => 'Nome completo',
                'value' => old('name') ?? ($client['name'] ?? $user['name']?? ''),
                'placeholder' => 'Digite o nome completo do cliente',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="gender">Gênero</label>
            <select id="gender" name="gender" class="form-select form-control @error('gender') is-invalid @enderror"
                    aria-label="Default select example" required>
                <option value="other">-- Não Informar --</option>
                <option value="m"
                        @if(old('gender') == 'm' || isset($client) && $client['gender'] == 'm' || isset($user) && $user['gender'] == 'm') selected @endif >
                    Masculino
                </option>
                <option value="f"
                        @if(old('gender') == 'f' || isset($client) && $client['gender'] == 'f' || isset($user) && $user['gender'] == 'f') selected @endif >
                    Feminino
                </option>
            </select>
            @error('gender')
            <span class="invalid-feedback" role="alert">
                <strong>{{$message}}</strong>
            </span>
            @enderror
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.date', [
                'name' => 'birth_date',
                'label' => 'Data de nascimento',
                'value' => (old('birth_date') ?? ($client['birth_date'] ?? $user['birth_date'] ?? '')),
                'withErrorText' => true,
                'required' => false
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'cpf',
                'label' => 'CPF',
                'value' => old('cpf') ?? ($client['cpf'] ?? $user['cpf'] ?? $cpf ?? ''),
                'placeholder' => 'Digite o CPF',
                'onchange' =>'checkCPF()',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Contato</p></div>
    @php
        $initialPhoneForValidationLogic = '';
        $initialPhoneValidated = false;
        if (isset($client['phone_number_1']) || isset($user['phone_number_1'])) {
            $rawPhone = $client['phone_number_1'] ?? ($user['phone_number_1'] ?? '');
            $initialPhoneForValidationLogic = preg_replace('/\D/', '', $rawPhone);
            $initialPhoneValidated = !empty($rawPhone);
        }
        $isInitialPhoneActuallyValid = (isset($client) || isset($user)) && preg_match('/^\d{10,11}$/', $initialPhoneForValidationLogic);
    @endphp
    <div class="col-md-6">
        @include('components.inputs.phone_number_with_validation', [
            'name' => 'phone_number_1',
            'id' => 'phone_number_1',
            'label' => 'Telefone/Celular 1',
            'class' => 'phone-input',
            'value' => old('phone_number_1') ?? ($client['phone_number_1'] ?? $user['phone_number_1'] ?? ''),
            'placeholder' => 'Digite o número do telefone do cliente',
            'required' => true,
            'currentClientCpf' => $client['cpf'] ?? $user['cpf'] ?? $cpf ?? '',
            'validationFieldName' => 'phone_validated',
            'initialValidated' => ($isInitialPhoneActuallyValid && $initialPhoneValidated) ? '1' : '0'
        ])
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'phone_number_2',
                'label' => 'Telefone/Celular 2',
                'value' => old('phone_number_2') ?? ($client['phone_number_2'] ?? $user['phone_number_2'] ?? ''),
                'placeholder' => 'Digite o número do telefone do cliente',
                'required' => false,
                'class' => 'phone-input'
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Login</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'email',
                'type' => 'email',
                'label' => 'E-mail',
                'value' => old('email') ?? ($client['email'] ?? $user['email'] ?? ''),
                'placeholder' => 'Digite o e-mail do cliente',
                'withErrorText' => true,
                'required' => true
            ])
        </div>
    </div>
    @if(isset($client))
        <div class="col-md-12">
            <div class="form-group">
                <input type="checkbox" id="reset_password" name="reset_password">
                <label for="reset_password">Resetar a senha do cliente</label>
            </div>
        </div>
    @endif
    <div class="col-md-12"><p class="form-title">Empreendedor</p></div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="is_entrepreneur">É um Empreendedor? <span style="color: red">*</span></label>
            <select class="form-control" name="is_entrepreneur" id="selectIsEntreprenur" required>
                <option value="" selected disabled>Selecione se é empreendedor</option>
                <option
                    value="1" {{ isset($client['is_entrepreneur']) && $client['is_entrepreneur'] ? 'selected' : '' }}>
                    Sim
                </option>
                <option
                    value="0" {{ isset($client['is_entrepreneur']) && !$client['is_entrepreneur'] ? 'selected' : '' }}>
                    Não
                </option>
            </select>
            <small id="error-is_entrepreneur" class="text-danger"></small>
        </div>
    </div>
    @include('components.business-table', ['businesses' => $client['businesses'] ?? [], 'isEdit' => isset($client), 'isClient' => true])
    
    <input type="hidden" name="email_validated" id="email_validated" value="0">

    @include('components.loading', ['message' => 'Aguardando confirmação de email'])
    @include('components.modal.modal_confirm_email', ['modalId' => 'modal-confirm-email'])
    @push('js')
        <script type="text/javascript"
                src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
        <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>
              
        <script type="text/javascript">
            // Variável global para o CPF atual (necessária para o modal WhatsApp)
            window.currentClientCpf = "{{ $client['cpf'] ?? $user['cpf'] ?? $cpf ?? '' }}";

            $(document).ready(function () {
                loadBusinessesFormEmployee();

                let debounceTimerEmail;
                $('#email').on('input', function() {
                    clearTimeout(debounceTimerEmail);
                    const emailInput = $(this);
                    const errorElement = $('#error-email');
                    let email = emailInput.val();

                    // Verifica se o email tem um formato básico válido antes de enviar a requisição
                    if (email.length > 0 && /\S+@\S+\.\S+/.test(email)) {
                         debounceTimerEmail = setTimeout(() => {
                            validateEmailAddress(emailInput, errorElement);
                         }, 500); // 500ms debounce
                    } else {
                        // Limpa erros se o email for inválido ou vazio
                        emailInput.removeClass('is-invalid');
                        errorElement.text('');
                    }
                });

               // Definir variáveis necessárias para validação de negócios
               let isAdmin = {{ ApiUser::getLoginType() == 'admin' ? 'true' : 'false' }};
               let isAuthorizedAllBusiness = {{ ApiUser::get()['authorizedFunction']['release_all_business'] ? 'true' : 'false' }};
           });
           $("#cep").focusout(function () {
                $.ajax({
                    url: 'https://viacep.com.br/ws/' + $(this).val().replaceAll('-', '') + '/json/',
                    dataType: 'json',
                    success: function (response) {
                        $("#complement").val(response.complemento);
                        $("#street").val(response.logradouro);
                        $("#neighborhood").val(response.bairro);
                        $("#city").val(response.localidade);
                        $("#state").val(response.uf);
                        $("#number").focus();
                    }
                });
            });

            $(document).ready(applyMasks());
            // counter_phone1 removido pois phone_number_1 agora é gerenciado pelo componente
            var counter_phone2 = {value: 0};

            function applyMasks() {
                $("#cpf").mask('000.000.000-00');
                $("#cpf_search").mask('000.000.000-00');
                $("#cep").mask('00000-000');
                // phone_number_1 agora é gerenciado pelo componente phone_number_with_validation
                $("#phone_number_2").mask(getMask('phone_number_2')).keyup(function (event) {
                    checkPhoneMask(event.target, event.originalEvent.key, counter_phone2);
                });
            }

            function checkCPF() {
                var cpfInput = document.getElementById('cpf');
                var cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

                var isValid = cpfIsValid(cpf);

                if (isValid) {
                    cpfInput.classList.remove("is-invalid")
                    cpfInput.classList.add("is-valid")
                } else {
                    cpfInput.classList.add("is-invalid")
                }
            }

            function getAjaxParams(employeeId = null) {
                if (employeeId != null) {
                    return {
                        url: "<?= env('API_URL') ?>/api/clients/validateUpdate/" + employeeId,
                        method: 'post',
                    };
                }

                return {
                    url: "<?= env('API_URL') ?>/api/clients/validateStore",
                    method: 'post',
                };
            }

            function validateStoreUpdate(formId, employeeId = null, emailChange = false) {
                const form = document.getElementById(formId);
                const formData = new FormData(form);

                // Verificar se o telefone está validado (obrigatório)
                if ($('#phone_validated').val() !== '1') {
                    alert('É necessário validar o telefone pelo WhatsApp antes de continuar.');
                    $('#phone_number_1').focus();
                    return false;
                }

                formData.forEach(function (value, key) {
                    if (['cpf', 'cep', 'phone_number_1', 'phone_number_2'].includes(key)) {
                        formData.set(key, value.replaceAll('.', '').replaceAll('-', '').replaceAll('(', '').replaceAll(')', '').replaceAll(' ', ''));
                    }
                    if (key == '_method') {
                        formData.delete(key);
                    }
                });

                if ($('#business_selected').children().length === 0 && !(isAdmin || isAuthorizedAllBusiness)) {
                    alert('Selecione pelo menos um negócio.');
                    return;
                }

                const ajaxData = getAjaxParams(employeeId);

                let email = $('#email').val();
                let isSetUser = {{isset($user) ? 'true' : 'false'}};
                let isSetClient = {{isset($client) ? 'true' : 'false'}};
                let oldEmailUser = "{{isset($user) ?  $user['email'] : ''}}";

                if (form.reportValidity()) {
                    $('#loading').modal('show');
                    $.ajax({
                        url: ajaxData.url,
                        type: ajaxData.method,
                        data: formData,
                        cache: true,
                        contentType: false,
                        processData: false,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                        },
                        success: function (response) {
                            $('#loading').modal('hide');
                            removeErrorMessages();
                            if ((formId === 'form-create-client' &&
                                    !(isSetUser && oldEmailUser === email)) ||
                                ((emailChange && email !== "") && !(isSetUser && oldEmailUser === email)) ||
                                (isSetUser && oldEmailUser !== email)
                            ) {
                                confirmClientEmail(formId);
                            } else form.submit();
                        },
                        error: function (response, textStatus, msg) {
                            $('#loading').modal('hide');
                            if (response.status == 422) {
                                const errors = response.responseJSON.errors;
                                removeErrorMessages();
                                removeIsInvalidClass();
    
                                Object.entries(errors).forEach(([field, error]) => {
                                    const id = 'error-' + field;
                                    document.getElementById(id).innerHTML = error;
                                    errorsIdToRemove.push(id);
                                })
    
                                Object.keys(errors).forEach((field) => {
                                    document.getElementById(field).classList.add('is-invalid')
                                    removeIsInvalidClassId.push(field)
                                })
                            }
                            alert('Preencha todos os dados corretamente.');
                        }
                    });
                }
            }

            function confirmClientEmail(formId) {
                const email = document.getElementById('email').value;
                const name = document.getElementById('name').value;
                const form = document.getElementById(formId);

                if (form.reportValidity() && email) {
                    const formData = new FormData();
                    formData.append('email', email);
                    formData.append('name', name);

                    $('#loading').modal('show');

                    $.ajax({
                        url: "{{route('send.email.client.confirm')}}",
                        type: 'post',
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        beforeSend: function (xhr) {},
                        success: function (response) {
                            const code = response.code;
                            const modal = document.getElementById('modal-confirm-email');
                            modal.dataset.code = code;
                            modal.dataset.formId = formId;
                            modal.dataset.email = email;
                            modal.dataset.name = name;

                            $('#loading').modal('hide');
                            $("#modal-confirm-email").modal('toggle');
                        },
                        error: function (response, textStatus, msg) {
                            $('#loading').modal('hide');
                            alert('Falha ao enviar email de confirmação!');
                        }
                    });
                }
            }

            setInterval(checkCPF, 1000);

            // Função para validar o email via AJAX
            function validateEmailAddress(emailInput, errorElement) {
                let email = emailInput.val();
                const currentClientCpf = "{{$client['cpf'] ?? $user['cpf'] ?? $cpf ?? ''}}";

                emailInput.removeClass('is-invalid');
                errorElement.text('');

                if (email.length === 0) {
                    return; // Não valida se o campo estiver vazio
                }

                // Verifica se o email tem um formato básico válido antes de enviar a requisição
                if (!/\S+@\S+\.\S+/.test(email)) {
                    emailInput.addClass('is-invalid');
                    errorElement.text('Por favor, insira um endereço de e-mail válido.');
                    return;
                }


                $.ajax({
                    url: `<?= env('API_URL') ?>/api/user/check-email/${email}`,
                    type: 'GET',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                    },
                    success: function (response) {
                        // A API retorna 404 se o email não for encontrado, então success significa que foi encontrado
                        // Verifica se o email encontrado pertence a outro usuário com CPF completo
                        if (response && response.id && response.cpf != currentClientCpf && response.cpf_status === 'completo') {
                            emailInput.addClass('is-invalid');
                            errorElement.text('Este email já está associado a outro usuário.');
                        } else {
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Se a resposta for 404, significa que o email não foi encontrado, o que é OK
                        if (jqXHR.status === 404) {
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        } else {
                            console.error("Erro ao validar email:", textStatus, errorThrown);
                            // Para outros erros, remove a validação para não bloquear o usuário indevidamente
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        }
                    },
                });
            }
        </script>
@endpush
