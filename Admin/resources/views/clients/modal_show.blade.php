@php use App\Helpers\StringMask; @endphp
<div class="modal fade" id="{{ $modalId }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Dados do cliente</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto" style="text-align: left; max-height: calc(100vh - 200px); overflow-y: auto;">
                <div class="form-row">
                    <div class="col-md-12">
                        <h6 class="style_campo_titulo">Dad<PERSON> pessoais</h6>
                    </div>
                    <div class="col-md-12">
                        <label for="">Nome completo</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ $client['name'] }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">Gênero</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::gender($client['gender']) }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">Data de nascimento</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::brDate($client['birth_date']) }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">CPF</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::cpf($client['cpf']) }}" disabled>
                    </div>

                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Contato</h6>
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Email</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ $client['email']?? 'Não informado' }}" disabled>
                    </div>
                    <div class="col-md-{{ isset($client['phone_number_2']) ? 3 : 6 }}" style="margin-top: 10px">
                        <label for="">Telefone 1</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::phone($client['phone_number_1']) }}" disabled>
                    </div>
                    @if(isset($client['phone_number_2']))
                        <div class="col-md-3" style="margin-top: 10px">
                            <label for="">Telefone 2</label>
                            <input type="text" class="form-control style_campo_estatico"
                                   value="{{ StringMask::phone($client['phone_number_2']) }}" disabled>
                        </div>
                    @endif
                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Empreendedor</h6>
                    </div>
                    <div class="col-md-3">
                        <label for="is_entrepreneur">É um Empreendedor?</label>
                        <select class="form-control" name="is_entrepreneur" id="selectIsEntreprenur" disabled>
                            <option value="1" {{ isset($client['is_entrepreneur']) && $client['is_entrepreneur'] ? 'selected' : '' }}>Sim</option>
                            <option value="0" {{ isset($client['is_entrepreneur']) && !$client['is_entrepreneur'] ? 'selected' : '' }}>Não</option>
                        </select>
                    </div>
                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Negócios</h6>
                        <ul class="list-group">
                            @php
                                $user = ApiUser::get();
                                $userBusiness = $user['businesses'] ?? [];
                                $clientBusiness = $client['businesses'] ?? [];
                                $canReleaseAllBusinesses = $user['authorizedFunction']['release_all_business'];

                                $userBusiness = array_map(function ($business) {
                                    return $business['name'];
                                }, $userBusiness);

                                $clientBusiness = array_map(function ($business) {
                                    return $business['name'];
                                }, $clientBusiness);

                                if ($canReleaseAllBusinesses) {
                                    $businesses = $clientBusiness;
                                } else {
                                    $businesses = array_intersect($clientBusiness, $userBusiness);
                                }
                            @endphp
                            @foreach ($businesses as $business)
                                <li class="list-group-item">{{ $business }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" style="width:30%">Fechar</button>
            </div>
        </div>
    </div>
</div>
