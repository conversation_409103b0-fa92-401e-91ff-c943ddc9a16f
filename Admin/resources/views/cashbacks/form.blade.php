<?php

use App\Helpers\ApiUser;
use Carbon\Carbon;

?>
<form id="form-add-edit-cashback" onsubmit="onSubmitForm(event)" method="post"
      action="{{ $cashback['id'] ?? false ? route('cashbacks.update', $cashback['id']) : route('cashbacks.store') }}">
    @csrf
    @if ($cashback['id'] ?? false)
        @method('put')
    @endif
    <input type="hidden" name="clients_id" id="clients_id" value="[]">
    <input type="hidden" name="business_id" id="business_id" value="{{ApiUser::get()['business_selected']['id'] ?? 'null'}}">
    <div class="form-group mb-2">
        <label for="expiration_date">Data de expiração</label>
        <div class="d-flex align-items-center ">
            <input 
                id="form_expiration_date"
                name="form_expiration_date"
                type="date" 
                class="form-control mr-3"
                value="{{ isset($cashback['expiration_date']) 
                            ? Carbon::parse($cashback['expiration_date'])->format('Y-m-d')
                            : Carbon::now()->addWeek()->format('Y-m-d')
                        }}"
                min="{{ Carbon::now()->format('Y-m-d') }}"
                style="flex: 1;"
                {{ isset($cashback['expiration_date']) ? 'disabled' : '' }}
            />
            <input 
                id="expiration_time"
                name="expiration_time"
                type="time" 
                class="form-control"
                value="{{ isset($cashback['expiration_date']) 
                        ? Carbon::parse($cashback['expiration_date'])->format('H:i') 
                        : '23:59' }}"
                style="flex: 1; max-width: 180px"
                {{ isset($cashback['expiration_date']) ? 'disabled' : '' }}
            />
            <input type="hidden" name="expiration_date" id="expiration_date">
        </div>
        <div>
            <small id="error-expiration_date" class="text-danger"></small>
        </div>
    </div>
    <div class="form-group mb-2">
        @include('components.inputs.number', array_merge([
                'name' => 'percentage', 
                'label' => 'Porcentagem', 
                'min' => 0.1,
                'max' => 20.0,
                'value' => $cashback['percentage'] ?? 0.1, 
                'withErrorText' => true, 'step' => '0.1',
                'oninput' => "if (this.value > 20.0) this.value = 20.0;
                             else if (this.value < 0.1) this.value = 0.1"
            ], isset($cashback['percentage']) ? ['readonly' => true] : []))
    </div>
    @if (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
        @if(isset($cashback['amount']))
            <div class="mb-2">
                <label for="">Valor do Cashback</label>
                <div class="input-group">
                    <span class="input-group-text" style="border-top-right-radius: 0; border-bottom-right-radius: 0">R$</span>
                    <input type="text" class="form-control style_campo_estatico"
                        value="{{$cashback['amount'] ?? 0.00}}" disabled>
                </div>
            </div>
        @endif
    @endif
    <div class="d-flex col-12 p-0 mb-2">
        <div class="form-group mb-2 col-6 pl-0 m-0">
            <label for="status">Status</label>
            <?php
                $isExpired = false;
                if (isset($cashback['expiration_date'])) {
                    $expirationDate = Carbon::createFromFormat('Y-m-d H:i:s', $cashback['expiration_date'], 'America/Sao_Paulo');
                    $now = Carbon::now()->setTimezone('America/Sao_Paulo');
                    $isExpired = $expirationDate->isBefore($now);
                }
            ?>
            <select 
                name="status" 
                id="statusSelect" 
                class="form-control"
                {{ $isExpired ? 'disabled' : '' }}
            >
                <option value="pending" {{ $cashback && $cashback['status'] == 'pending' ? 'selected' : '' }} >Pendente</option>
                <option value="completed" {{ $cashback && $cashback['status'] == 'completed' ? 'selected' : '' }}>Concluído</option>
                <option value="expired" {{ $cashback && $cashback['status'] == 'expired' ? 'selected' : 'hidden' }} >Expirado</option>
            </select>
        </div>
    </div>
    <div class="form-group mt-0">
        <hr id="clientsSeparator"/>
        <label for="name" id="clientsSelectedLabel">{{ ($cashback && isset($cashback['client_id'])) 
                                                        ? 'Cliente:' 
                                                        : 'Selecionados:'
                                                    }}
        </label>
        <div class="form-group mb-0">
            <ul class="list-group" id="clients_selected">
                <!-- clients selecionados serão carregados aqui -->
                @if($cashback && isset($cashback['client_id']))
                <li class="list-group list-group-item">
                    <div>
                        {{$cashback['client_name']}}
                        <div style="color: #909090">{{ StringMask::cpf($cashback['client_cpf']) }} </div>
                    </div>
                </li>
                @endif
            </ul>
        </div>
    </div>
    <div class="form-group mb-2 {{isset($cashback['client_id']) ? 'd-none' : ''}}">
        <hr id="clientsSeparator"/>
        <div>
            <input type="text" id="clientsSearch" class="form-control" placeholder="Pesquisar cliente...">
        </div>
        <div class="table-responsive-sm">
            <table class="table" style="white-space: nowrap;" id="clientsTable">
                <thead>
                    <tr>
                        <th style="width: 1%">Selecionar</th>
                        <th >Nome/CPF</th>
                    </tr>
                </thead>
                <tbody id="clientsTableBody">
                    <div class="d-flex justify-content-center loading pb-1 d-none">
                        <div class="spinner-border loading" role="status">
                            <span class="sr-only loading">Loading...</span>
                        </div>
                    </div>
                    <!-- Clientes serão carregados aqui -->
                </tbody>
            </table>
            <small id="error-clients_id" class="p-1 text-danger"></small>
        </div>
            <nav>
                <ul class="pagination" id="clientPagination">
                    <!-- Paginação será carregada aqui -->
                </ul>
            </nav>
    </div>
</form>

<script text="text/javascript">
    document.addEventListener('openCashbackForm', function() {
        loadClients()
    });
    let errorsList = [];
    showHideNoClientSelectedMessage()
    
    function fillErrors(errors) {
        errorsList.forEach((id) => {
            document.getElementById(id).innerHTML = '';
            document.getElementById(id).classList.add('d-none');
        });

        Object.keys(errors).forEach((key) => {
            let error = document.getElementById(`error-${key}`);
            console.log(error);
            if (error) {
                error.innerHTML = errors[key][0];
                error.classList.remove('d-none');
                errorsList.push(error.id);
            }
        });
    }

    function showHideNoClientSelectedMessage() {
        if ($('#clients_selected').children().length === 0) {
            $('#clientsSelectedLabel').addClass('d-none');
            $('#clientsSeparator').addClass('d-none');
        } else {
            $('#clientsSelectedLabel').removeClass('d-none');
            $('#clientsSeparator').removeClass('d-none');
        }
    }

    function removeClient(clientId) {
        const clientsInput = document.querySelector('#clients_id');
        let clientsIds = JSON.parse(clientsInput.value);
        clientsIds = clientsIds.filter(id => id !== clientId);
        $(`#client_button_${clientId}`).prop('checked', false);
        $(`#client_selected_${clientId}`).remove();
        clientsInput.value = JSON.stringify(clientsIds);
        showHideNoClientSelectedMessage()
    }
    
    function addClient(checkbox) {
        const clientId = Number($(checkbox).val());
        const clientsInput = document.querySelector('#clients_id');
        let clientsIds = JSON.parse(clientsInput.value);
        if ($(checkbox).is(':checked')) {
            let p = document.createElement('p');
            let texto = $(checkbox).parent().next().text().trim();
            let name = texto.slice(0, -14).trim();
            p.className = 'row justify-content-between m-0';
            p.innerHTML = `- ${name}
                                <span 
                                    class="badge badge-danger" 
                                    style="cursor: pointer; padding: 5px 10px; font-size: 14px;" 
                                    onclick="removeClient(${clientId})"
                                >X</span>`;
            let li = document.createElement('li');
            li.className = 'list-group list-group-item';
            li.id = `client_selected_${clientId}`;
            li.appendChild(p);
            $('#clients_selected').append(li);
            clientsIds.push(clientId);
            clientsInput.value = JSON.stringify(clientsIds);
            showHideNoClientSelectedMessage();
        }else {
            removeClient(clientId);
        }
    }

    function loadClients(page = 1, show_defaut_option = true) {
        const clients_id = [];
        const searchQuery = $('#clientsSearch').val();

        function formatCPF(cpf) {
            return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        }
        $.ajax({
            url: `<?= env('API_URL') ?>/api/clients/by-business?search=${searchQuery}&page==${page}&per_page=4`,
            type: 'get',
            data: {
                search: searchQuery,
                page: page,
                per_page: 4
            },
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#clientsTable').addClass('d-none');
                $('#clientPagination').addClass('d-none');
                $('.loading').removeClass('d-none');
            },
            success: function (response) {
                $('.loading').addClass('d-none');

                $('#clientsTableBody').empty();
                const clientsIds = JSON.parse(document.querySelector('#clients_id').value);
                const clients = response.clients.data;
                if (clients.length > 0) {
                    $.each(clients, function (index, client) {
                        const isSelected = clientsIds.includes(client.id);
                        $('#clientsTableBody').append(`
                        <tr>
                            <td style="width: 1%; text-align: center">
                                <input 
                                    type="checkbox" 
                                    id="client_button_${client.id}"
                                    value="${client.id}"
                                    onchange="addClient(this)"
                                />
                            </td>
                            <td>
                                ${client.name}    
                                <div style="color: #909090">
                                    ${formatCPF(client.cpf)}
                                </div>
                            </td>
                            <div>
                                <small id="error-clients_id${index}" class="p-1 text-danger"></small>
                            </div>
                        </tr>
                    `);
                        $(`#client_button_${client.id}`).prop('checked', isSelected);
                    });
                } else {
                    $('#clientsTableBody').append('<tr><td colspan="3">Nenhum cliente encontrado</td></tr>');
                }

                // Paginação
                $('#clientPagination').empty();
                if (response.clients.last_page > 1) {
                    for (let i = 1; i <= response.clients.last_page; i++) {
                        $('#clientPagination').append(`
                            <li class="page-item ${i === response.clients.current_page ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i}</a>
                            </li>
                        `);
                    }
                }

                $('#clientPagination a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadClients(page);
                });

                $('#clientsTable').removeClass('d-none');
                $('#clientPagination').removeClass('d-none');
            },
            error: function (response, textStatus, msg) {
                $('.loading').addClass('d-none');
            }
        });
    }

    function onSubmitForm(event) {
        event.preventDefault();

        const cashbackId = Number('<?= $cashback['id'] ?? 0 ?>');
        const formData = new FormData(event.target);
        const clients = JSON.parse(document.querySelector('#clients_id').value);
        formData.delete('clients_id');
        formData.set('clients_id', JSON.stringify(clients));

        formData.forEach(function (value, key) {
            if (key == '_method') {
                formData.delete(key);
            }
        });

        const formExpirationDate = document.querySelector('#form_expiration_date').value;
        const expirationTime = document.querySelector('#expiration_time').value;
        const inputExpirationDate = document.querySelector('#expiration_date');

        if (formExpirationDate && expirationTime) {
            const formattedDateTime = (`${formExpirationDate} ${expirationTime}:00`);
            inputExpirationDate.value = formattedDateTime;
            formData.set('expiration_date', formattedDateTime);
        }

        if (cashbackId) {
            const data = document.querySelector('#expiration_date').value;
            $.ajax({
                url: `<?= env('API_URL') ?>/api/cashbacks/validateUpdate/${cashbackId}`,
                type: 'post',
                cache: false,
                contentType: false,
                processData: false,
                data: formData,
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {
                    fillErrors([]);
                    event.target.submit();
                },
                error: function (response, textStatus, msg) {
                    if (response.status == 422) {
                        fillErrors(response.responseJSON.errors);
                    }
                }
            });
            return
        }

        $.ajax({
            url: `<?= env('API_URL') ?>/api/cashbacks/validateStore`,
            type: 'post',
            cache: false,
            contentType: false,
            processData: false,
            data: formData,
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
            },
            success: function (response) {
                fillErrors([]);
                event.target.submit();
            },
            error: function (response, textStatus, msg) {
                if (response.status == 422) {
                    fillErrors(response.responseJSON.errors);
                }
            }
        });
    }

    function debounce(func, wait, immediate) {
        let timeout;
        return function () {
            const context = this, args = arguments;
            const later = function () {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    $(document).ready(function () {
        $('#clientsSearch').on('keyup', debounce(function () {
            loadClients();
        }, 500));
        
    });
</script>
