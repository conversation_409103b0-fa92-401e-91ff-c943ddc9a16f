<?php
    use App\Helpers\StringMask;
?>

<div class="form-row">
    <div class="col-md-12">
        <label for="">Cliente</label>
        <input type="text" class="form-control style_campo_estatico"
                value="{{ $cashback['client_name'] }}" disabled>
    </div>
    <div class="col-md-12 mt-2">
        <label for="">CPF</label>
        <input type="text" class="form-control style_campo_estatico"
                value="{{ StringMask::cpf($cashback['client_cpf']) }}" disabled>
    </div>
    <div class="col-md-12 mt-2">
        <label for="">Negócio</label>
        <input type="text" class="form-control style_campo_estatico"
                value="{{ $cashback['business_name'] }}" disabled>
    </div>
    <div class="col-md-12 mt-2">
        <label for="">Data de expiração</label>
        <input type="text" class="form-control style_campo_estatico"
                value="{{ date('d/m/Y H:i', strtotime($cashback['expiration_date'])) }}" disabled>
    </div>
    <div class="col-md-12 mt-2">
        <label for="">Data de utilização</label>
        <input type="text" class="form-control style_campo_estatico"
                @if(isset($cashback['used_at'])) 
                    value="{{ date('d/m/Y H:i', strtotime($cashback['used_at'])) }}"
                @else
                    value="Não utilizado"
                @endif disabled>
    </div>
    <div class="col-md-12 mt-2">
        <label for="">Porcentagem</label>
        <input type="text" class="form-control style_campo_estatico"
                value="{{$cashback['percentage']}}%" disabled>
    </div>
    @if (isset(ApiUser::get()['business_selected']) && ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
        <div class="col-md-12 mt-2">
            <label for="">Valor do Cashback</label>
            <div class="input-group">
                <span class="input-group-text" style="border-top-right-radius: 0; border-bottom-right-radius: 0">R$</span>
                <input type="text" class="form-control style_campo_estatico"
                value="{{$cashback['amount'] ?? 0.00}}" disabled>
            </div>
        </div>
    @elseif (ApiUser::get()['login_type'] == 'client' && $cashback['business']['parameters']['can_set_cashback_value'])
        <div class="col-md-12 mt-2">
            <label for="">Valor do Cashback</label>
            <div class="input-group">
                <span class="input-group-text" style="border-top-right-radius: 0; border-bottom-right-radius: 0">R$</span>
                <input type="text" class="form-control style_campo_estatico"
                value="{{$cashback['amount'] ?? 0.00}}" disabled>
            </div>
        </div>
    @endif

    <div class="d-flex col-md-12 mt-2" >
        <div class="form-group col-6 pl-0 m-0">
            <label for="status">Status</label>
            <select name="status" id="statusSelect" class="form-control" disabled >
                <option value="pending" {{ $cashback && $cashback['status'] == 'pending' ? 'selected' : '' }} >Pendente</option>
                <option value="completed" {{ $cashback && $cashback['status'] == 'completed' ? 'selected' : '' }}>Concluído</option>
                <option value="expired" {{ $cashback && $cashback['status'] == 'expired' ? 'selected' : '' }}>Expirado</option>
            </select>
        </div>
    </div>
</div>
