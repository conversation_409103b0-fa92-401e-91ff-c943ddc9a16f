@extends('layouts.app', ['activePage' => 'cashbacks'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-1 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Cashback</h5>
                            </div>

                            <div class="col-lg-9 col-md-6 col-sm-6 col-2 mb-2 d-flex justify-content-end">
                                <div class="d-flex">
                                    @if(ApiUser::hasPermission('update_cashback'))
                                    <button class="btn btn-success mr-1" onclick="openModalAdd('modal-add-cashback')">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{url('icon/icon_plus.png')}}" width="15px"
                                             style="margin-top: -2px;"/>
                                        <label class="styleBotaoAdicionar">Adicionar cashback</label>
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{route('cashbacks.clients.index')}}" method="get" id="form_search_cashbacks">
                                    @csrf
                                    <div class="input-group " id="search_form_desktop">
                                        <select class="form-control mr-2" name="business_id"
                                                style="flex: 2;"
                                                aria-describedby="business_id" id="search_desktop" onchange="document.getElementById('form_search_cashbacks').submit()">
                                            <option value="">Todos os negócios</option>
                                            @foreach($businesses as $business)
                                                <option
                                                    value="{{ $business['id'] }}" {{ isset($business_id) && $business_id == $business['id'] ? 'selected' : '' }}>
                                                    {{ $business['name'] }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <select class="form-control mr-2" name="status"
                                                style="flex: 2;"
                                                aria-describedby="status" id="status_desktop">
                                            <option value="all"
                                                @if(isset($status) && $status == 'all') selected @endif>
                                                    Todos
                                            </option>
                                            <option value="completed"
                                                @if((isset($status) && $status == 'completed') || !isset($status)) selected @endif>
                                                    Concluidos
                                            </option>
                                            <option value="expired"
                                                @if(isset($status) && $status == 'expired') selected @endif>
                                                    Expirados
                                            </option>
                                            <option value="pending"
                                                @if((isset($status) && $status == 'pending') || !isset($status)) selected @endif>
                                                    Pendentes
                                            </option>
                                        </select>
                                    </div>
                                    <div class="input-group d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <select class="form-control" name="business_id"
                                                    aria-describedby="business_id" id="search_mobile" onchange="document.getElementById('form_search_cashbacks').submit()">
                                                <option value="">Todos os negócios</option>
                                                @foreach($businesses as $business)
                                                    <option
                                                        value="{{ $business['id'] }}" {{ isset($business_id) && $business_id == $business['id'] ? 'selected' : '' }}>
                                                        {{ $business['name'] }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="input-group mb-2">
                                            <select class="form-control" name="status"
                                                    aria-describedby="status" id="status_mobile">
                                                <option value="all"
                                                    @if(isset($status) && $status == 'all') selected @endif>
                                                        Todos
                                                </option>
                                                <option value="completed"
                                                    @if((isset($status) && $status == 'completed') || !isset($status)) selected @endif>
                                                        Concluidos
                                                </option>
                                                <option value="pending"
                                                    @if((isset($status) && $status == 'pending') || !isset($status)) selected @endif>
                                                        Pendentes
                                                </option>
                                                <option value="expired"
                                                    @if(isset($status) && $status == 'expired') selected @endif>
                                                        Expirados
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="cashbacks-table">
                            <thead>
                            <tr>
                                <th scope="col" style="text-align: center">Ações</th>
                                <th scope="col">Status</th>
                                <th scope="col">Valor do cashback</th>
                                <th scope="col">Porcentagem</th>
                                <th scope="col">Data de expiração</th>
                                <th scope="col">Negócio</th>
                                <th scope="col">Data de utilização</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach ($cashbacks as $cashback)
                            <tr class="cashback-row" data-status="{{ $cashback['status'] }}">
                                <td style="text-align: center">
                                    <button class="btn btn-secondary" type="button" onclick="openModalShow('modal-view-cashback', <?= $cashback['id'] ?>)" style="margin-right: 10px">
                                        <img class="card-img-left example-card-img-responsive" src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                    </button>
                                </td>
                                <td>
                                    @php
                                        $badgeClass = '';
                                        switch ($cashback['status']) {
                                            case 'completed':
                                                $status = "Concluído";
                                                $badgeClass = 'badge bg-success';
                                                break;
                                            case 'pending':
                                                $status = "Pendente";
                                                $badgeClass = 'badge bg-warning';
                                                break;
                                            case 'expired':
                                                $status = "Expirado";
                                                $badgeClass = 'badge bg-secondary';
                                                break;
                                            default:
                                                $badgeClass = 'badge bg-secondary';
                                        }
                                    @endphp
                                    <span class="{{ $badgeClass }} p-2 text-light" style="width:100px" >
                                        {{ ucfirst($status) }}
                                    </span>
                                </td>
                                @if ($cashback['business']['parameters']['can_set_cashback_value'])
                                    <td style="text-align: center;">
                                        {{ $cashback['amount'] ? 'R$ '.number_format($cashback['amount'], 2, ',', '') : 'Não infomado' }}
                                    </td>
                                @else
                                    <td style="text-align: center;">-</td>
                                @endif
                                <td>
                                    {{ $cashback['percentage'] }}%
                                </td>
                                <td class="text-truncate" style="max-width: 1rem;">
                                    {{ date('d/m/Y H:i', strToTime($cashback['expiration_date'])) }}
                                </td>
                                <td>
                                    <span title="{{ $cashback['business_name'] }}">
                                        {{ Str::limit($cashback['business_name'], 16, '...') }}
                                    </span>
                                </td>
                                <td class="text-truncate" style="max-width: 1rem;">
                                    @if(isset($cashback['used_at']))
                                        {{ date('d/m/Y H:i', strToTime($cashback['used_at'])) }}
                                    @else
                                        <p>Não utilizado</p>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>

                        {{ $cashbacks->onEachSide(0)->appends($queryParams)->links() }}
                    </div>
                    <div class="card-footer"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('cashbacks.legend')
                    </div>
                </div>
            </div>
        </div>
        @include('cashbacks.modal_show', ['modalId' => "modal-view-cashback"])

        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...'])
@endsection

@push('js')
<script type="text/javascript">
    $(document).ready(() => {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });
        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }
    });

let status_desktop = document.getElementById('status_desktop');
    let status_mobile = document.getElementById('status_mobile');

    status_desktop.addEventListener('change', function () {
        status_mobile.value = status_desktop.value;

        const form = document.getElementById('form_search_cashbacks');
        form.submit();
    });

    status_mobile.addEventListener('change', function () {
        status_desktop.value = status_mobile.value;
        const form = document.getElementById('form_search_cashbacks');
        form.submit();
    });

    let search_desktop = document.getElementById('search_desktop');
    let search_mobile = document.getElementById('search_mobile');

    status_desktop.addEventListener('change', function () {
        status_mobile.value = status_desktop.value;
    });

    status_mobile.addEventListener('change', function () {
        status_desktop.value = status_mobile.value;
    });

    window.addEventListener('resize', function () {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }
    });

    function addLoading(containerId) {
        $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
    }

    function openModalShow(modalId, cashbackId) {
        addLoading('cashback_show_modal_body')
        $(`#${modalId}`).modal('show');
        $.ajax({
            url: `client/${cashbackId}/show`,
            type: 'get',
            data: {},
            success: function(response) {
                $('#cashback_show_modal_body').html(response);
            },
            error: function(response, textStatus, msg) {
                $('#cashback_show_modal_body').html(msg);
            }
        });
    }
</script>
@endpush
