<?php
    use App\Helpers\ApiUser;
?>
<div class="d-flex justify-content-between align-items-start">
    <div class="table-legend">
        <div>
            <h6 style="font-weight: bold">Legenda:</h6>
        </div>
        <div class="form-row">
            <div style="margin:5px">
                <button class="btn btn-secondary" style="margin-right: 10px" disabled>
                    <img class="card-img-left" src="{{url('icon/icon_visualizar.svg')}}" width="24px"
                    alt="Visualizar cashback" />
                </button> Visualizar cashback
            </div>
        </div>        
    </div> 
    @if (ApiUser::get()['login_type'] != 'client' && isset(ApiUser::get()['business_selected']) && ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
        <div>
            <p><b>Total em Cashbacks: R$ {{number_format($totalAmount, 2, ',', '') ?? '0,00'}}</b></p>
        </div>
    @elseif (ApiUser::get()['login_type'] == 'client')
        <div>
            <p><b>Total em Cashbacks: R$ {{number_format($totalAmount, 2, ',', '') ?? '0,00'}}</b></p>
        </div>
    @endif
</div>