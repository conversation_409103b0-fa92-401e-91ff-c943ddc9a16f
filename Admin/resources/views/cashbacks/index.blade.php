<?php

use Carbon\Carbon;

?>
@extends('layouts.app', ['activePage' => 'cashbacks'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Cashback</h5>
                            </div>

                            <div class="col-lg-9 col-md-6 col-sm-6 col-2 mb-2 d-flex justify-content-end">
                                <div class="d-flex">
                                    {{-- @if (ApiUser::hasPermission('update_cashback'))
                                        @include('cashbacks.modal_add', ['modalId' => "modal-add-cashback"])
                                        <button class="btn btn-success mr-1" onclick="openModalAdd('modal-add-cashback')">
                                            <img class="card-img-left example-card-img-responsive"
                                                src="{{url('icon/icon_plus.png')}}" width="15px"
                                                style="margin-top: -2px;"/>
                                            <label class="styleBotaoAdicionar">Cadastrar cashback</label>
                                        </button>
                                        <script>
                                            function openModalAdd(modalId) {
                                                addLoading('cashback_add_modal_body')
                                                $(`#${modalId}`).modal('show');
                                                $.ajax({
                                                    url: `cashbacks/create`,
                                                    type: 'get',
                                                    data: {},
                                                    success: function(response) {
                                                        $('#cashback_add_modal_body').html(response);
                                                        const event = new Event('openCashbackForm');
                                                        document.dispatchEvent(event);
                                                    },
                                                    error: function(response, textStatus, msg) {
                                                        $('#cashback_add_modal_body').html(msg);
                                                    }
                                                });
                                            }
                                        </script>
                                    @endif --}}
                                    @if(ApiUser::hasPermission('update_cashback'))
                                        <button type="button" class="btn btn-success mr-1"
                                                onclick="openCashbackModal()">
                                            <i class="fas fa-download fa-fw"></i>
                                            <span class="d-none d-md-inline">Baixar Cashback</span>
                                        </button>
                                    @endif
                                    <button type="button" class="btn btn-secondary" onclick="showNegociosUser(false)">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{ url('icon/icon_filtro.png') }}" width="15px"
                                             style="margin-top: -2px;"/>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{ route('cashbacks.index') }}" method="get" id="form_search_cashbacks">
                                    @csrf
                                    <div class="input-group " id="search_form_desktop">
                                        <input type="text" class="form-control mr-2" name="search"
                                               aria-describedby="search" style="flex: 5;"
                                               placeholder="Buscar por Nome, CPF, E-mail ou Telefone"
                                               value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        <div class="input-group-append mr-2">
                                            <button class="btn btn-light" type="submit" id="btnSearch">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{ url('icon/icon_search.png') }}" width="20px"/>
                                            </button>
                                        </div>
                                        <select class="form-control mr-2" name="status" style="flex: 3;"
                                                aria-describedby="status" id="status_desktop">
                                            <option value="all"
                                                    @if (isset($status) && $status == 'all') selected @endif>
                                                Todos
                                            </option>
                                            <option value="completed"
                                                    @if ((isset($status) && $status == 'completed')) selected @endif>
                                                Concluidos
                                            </option>
                                            <option value="expired"
                                                    @if (isset($status) && $status == 'expired') selected @endif>
                                                Expirados
                                            </option>
                                            <option value="pending"
                                                    @if ((isset($status) && $status == 'pending') || !isset($status)) selected @endif>
                                                Pendentes
                                            </option>
                                        </select>
                                    </div>
                                    <div class="input-group d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="search"
                                                   aria-describedby="search"
                                                   placeholder="Buscar por Nome, CPF, E-mail ou Telefone"
                                                   value="{{ isset($search) ? $search : '' }}" id="search_mobile">
                                        </div>
                                        <div class="input-group mb-2">
                                            <button class="btn btn-light btn-block" type="submit">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{ url('icon/icon_search.png') }}" width="20px"/>
                                            </button>
                                        </div>
                                        <div class="input-group mb-2">
                                            <select class="form-control" name="status" aria-describedby="status"
                                                    id="status_mobile">
                                                <option value="all"
                                                        @if (isset($status) && $status == 'all') selected @endif>
                                                    Todos
                                                </option>
                                                <option value="completed"
                                                        @if ((isset($status) && $status == 'completed')) selected @endif>
                                                    Concluidos
                                                </option>
                                                <option value="expired"
                                                        @if (isset($status) && $status == 'expired') selected @endif>
                                                    Expirados
                                                </option>
                                                <option value="pending"
                                                        @if ((isset($status) && $status == 'pending') || !isset($status)) selected @endif>
                                                    Pendentes
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="cashbacks-table">
                                <thead>
                                <tr>
                                    @if (ApiUser::hasUserFunction('update_cashback'))
                                        <th scope="col" style="text-align: center">Ações</th>
                                    @endif
                                    <th scope="col">Status</th>
                                    @if (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
                                        <th scope="col">Cashback</th>
                                    @endif
                                    <th scope="col">Porcentagem</th>
                                    <th scope="col">Expiração</th>
                                    <th scope="col">Cliente</th>
                                    <th scope="col">Contato</th>
                                    <th scope="col">Utilização</th>
                                </tr>
                                </thead>
                                <tbody>
                                @forelse ($cashbacks as $cashback)
                                    <tr class="cashback-row" data-status="{{ $cashback['status'] }}">
                                        <td style="text-align: center">
                                            <button
                                                class="btn btn-secondary"
                                                type="button"
                                                onclick="openShowModal('modal-show-cashback', {{ $cashback['id'] }})"
                                                style="margin-right: 10px"
                                            >
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_visualizar.svg')}}" width="20px"/>
                                            </button>
                                        </td>
                                        <td>
                                            @php
                                                $badgeClass = '';
                                                switch ($cashback['status']) {
                                                    case 'completed':
                                                        $status = "Concluído";
                                                        $badgeClass = 'badge bg-success';
                                                        break;
                                                    case 'pending':
                                                        $status = "Pendente";
                                                        $badgeClass = 'badge bg-warning';
                                                        break;
                                                    case 'expired':
                                                        $status = "Expirado";
                                                        $badgeClass = 'badge bg-secondary';
                                                        break;
                                                    default:
                                                        $badgeClass = 'badge bg-secondary';
                                                }
                                            @endphp
                                            <span class="{{ $badgeClass }} p-2 text-light" style="width:100px">
                                        {{ ucfirst($status) }}
                                    </span>
                                        </td>
                                        @if (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
                                            <td>
                                                R$ {{ $cashback['amount'] ?? '0,00' }}
                                            </td>
                                        @endif
                                        <td>
                                            {{ $cashback['percentage'] }}%
                                        </td>
                                        <td class="text-truncate">
                                            {{ date('d/m/Y H:i', strtotime($cashback['expiration_date'])) }}
                                        </td>
                                        <td>
                                    <span title="{{ $cashback['client_name'] }}">
                                        {{ Str::limit($cashback['client_name'], 16, '...') }}
                                    </span><br>
                                            <span title="{{ $cashback['client_cpf'] }}">
                                        {{ StringMask::cpf($cashback['client_cpf']) }}
                                    </span>
                                        </td>
                                        <td>
                                    <span title="{{ StringMask::phone($cashback['client_phone']) }}">
                                        {{ StringMask::phone($cashback['client_phone'])}}
                                    </span><br>
                                            <span title="{{ $cashback['client_email'] }}">
                                        {{ Str::limit($cashback['client_email'] ?? '-', 22, '...') }}
                                    </span>
                                        </td>
                                        <td class="text-truncate">
                                            @if(isset($cashback['used_at']))
                                                {{ date('d/m/Y H:i', strToTime($cashback['used_at'])) }}
                                            @else
                                                <p>Não utilizado</p>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="{{ (ApiUser::hasUserFunction('update_cashback') ? 1 : 0) + (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'] ? 1 : 0) + 7 }}"
                                            class="text-center">
                                            <div
                                                class="empty-state py-3 py-md-5 px-2 px-md-3 bg-light rounded shadow-sm">
                                                <div class="empty-state-icon mb-2 mb-md-3">
                                                    <i class="fa fa-search d-block mx-auto"
                                                       style="font-size: calc(32px + 1vw); color: #0842A0;"></i>
                                                </div>
                                                <h4 class="empty-state-title font-weight-bold fs-5 fs-md-4"
                                                    style="color: #333;">Nenhum cashback encontrado</h4>
                                                <p class="empty-state-description mt-2 mt-md-3 px-2 px-md-4"
                                                   style="font-size: calc(0.9em + 0.2vw); color: #555; max-width: 600px; margin: 0 auto;">
                                                    @if(isset($search) && !empty($search))
                                                        Não encontramos nenhum cashback com o termo
                                                        <strong>"{{ $search }}"</strong>.
                                                    @else
                                                        Não há registros de cashback disponíveis no momento.
                                                    @endif
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>

                        {{ $cashbacks->onEachSide(0)->appends($queryParams)->links() }}
                    </div>
                    <div class="card-footer"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('cashbacks.legend')
                    </div>
                </div>
            </div>
        </div>
        @include('cashbacks.modal_show', ['modalId' => "modal-show-cashback"])
        @if(ApiUser::hasPermission('update_cashback'))
            @include('components.search-complete-cashback-modal')
        @endif

        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{ $message }}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{ $message }}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...'])
@endsection

@push('js')
    <script type="text/javascript">
        // Função centralizada para alternar formulários desktop/mobile
        function toggleSearchForms() {
            const isMobile = window.innerWidth <= 575;
            const desktopForm = $('#search_form_desktop');
            const mobileForm = $('#search_form_mobile');

            // Alterna visibilidade e estado dos formulários
            desktopForm.toggleClass('d-none', isMobile);
            desktopForm.find('input, select').prop('disabled', isMobile);

            mobileForm.toggleClass('d-none', !isMobile);
            mobileForm.find('input, select').prop('disabled', !isMobile);
        }

        // Função para sincronizar valores entre formulários desktop e mobile
        function syncFormValues(sourceElement, targetElement) {
            if (sourceElement && targetElement) {
                targetElement.value = sourceElement.value;
            }
        }

        // Função para submeter o formulário apenas se estiver ativo
        function submitActiveForm() {
            const form = document.getElementById('form_search_cashbacks');
            if (form) {
                form.submit();
            }
        }

        // Inicialização quando o documento estiver pronto
        $(document).ready(function() {
            // Configura formulários responsivos
            toggleSearchForms();
            $(window).on('resize', toggleSearchForms);

            // Elementos dos formulários
            const searchDesktop = document.getElementById('search_desktop');
            const searchMobile = document.getElementById('search_mobile');
            const statusDesktop = document.getElementById('status_desktop');
            const statusMobile = document.getElementById('status_mobile');

            // Sincronização de campos de busca
            if (searchDesktop && searchMobile) {
                searchDesktop.addEventListener('input', function() {
                    syncFormValues(searchDesktop, searchMobile);
                });

                searchMobile.addEventListener('input', function() {
                    syncFormValues(searchMobile, searchDesktop);
                });
            }

            // Sincronização e submissão automática para campos de status
            if (statusDesktop && statusMobile) {
                statusDesktop.addEventListener('change', function() {
                    syncFormValues(statusDesktop, statusMobile);
                    submitActiveForm();
                });

                statusMobile.addEventListener('change', function() {
                    syncFormValues(statusMobile, statusDesktop);
                    submitActiveForm();
                });
            }
        });

        // Funções utilitárias para modais
        function openShowModal(modalId, cashbackId) {
            addLoading('cashback_show_modal_body');
            $(`#${modalId}`).modal('show');
            
            $.ajax({
                url: `cashbacks/${cashbackId}/show`,
                type: 'GET',
                success: function(response) {
                    $('#cashback_show_modal_body').html(response);
                },
                error: function(xhr, textStatus, errorThrown) {
                    $('#cashback_show_modal_body').html('<p class="text-danger">Erro ao carregar dados. Tente novamente.</p>');
                }
            });
        }

        function openCashbackModal() {
            $('#buscarCashbackModal').modal('show');
        }

        function addLoading(containerId) {
            $(`#${containerId}`).html(
                '<div class="d-flex justify-content-center py-4">' +
                    '<div class="spinner-border text-primary" role="status">' +
                        '<span class="sr-only">Carregando...</span>' +
                    '</div>' +
                '</div>'
            );
        }
    </script>
@endpush
