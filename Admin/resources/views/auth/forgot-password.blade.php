@php
    use App\Helpers\SystemHelper;
@endphp

@extends('layouts.guest')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 card card-body shadow-lg">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="card-title custom-title mb-0 mx-auto">Recuperação de senha</h5>
                </div>

                @if (session('status'))
                    <div class="alert alert-success" role="alert">
                        {{ session('status') }}
                    </div>
                    <div class="row d-flex justify-content-center mb-4">
                        <div class="col-8">
                            <div class="d-flex justify-content-center align-items-center mb-3" style="gap: 8px">
                                <i class="fas fa-clock fa-fw"></i>
                                <span id="resendTimer"></span>
                                <button class="btn btn-outline-secondary disabled" id="resendButton" onclick="resendPasswordResetEmail()" style="padding: 0.375rem 0.75rem;">
                                    <span id="resendButtonText">Reenviar e-mail</span>
                                    <div class="spinner-border text-secondary spinner-border-sm" style="display: none;" role="status" id="resendSpinner">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </button>
                            </div>
                            <div class="text-center">
                                <a href="{{ route('login') }}" class="btn btn-primary w-100">Continuar</a>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="row d-flex justify-content-center mb-4">
                        <div class="col-8">
                            <form class="row" action="{{ route('password.email') }}" method="post">
                                @csrf
                                @include('components.inputs.text', ['name' => 'email', 'label' => 'E-Mail', 'placeholder' => 'Insira seu email', 'value' => old('email')])
                                <button class="btn btn-success mt-4 w-100" onclick="showLoading()">Confirmar</button>
                            </form>
                            <div class="mt-2">
                                <p class="text-center">Precisando de ajuda?
                                    <br>
                                    <a href="{{  SystemHelper::get()['SUPPORT_TAB_LINK'] ?? env('NOTION_APP_LINK') }}" target="_blank" rel="noopener noreferrer">
                                        Entre em contato com o nosso suporte
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                    @include('components.loading',['message' => 'Enviando email ...'])
                @endif
            </div>
        </div>
    </div>
    @php
        $resendEmailTimeSeconds = App\Helpers\SystemHelper::getAuthCodeResendTimeSeconds();
    @endphp

    <script>
        function showLoading() {
            if ($('input[name="email"]').val() != '') {
                $('#loading').modal('show');
            }
        }

        @if (session('status'))
        const RESEND_EMAIL_TIME_SECONDS = {{ $resendEmailTimeSeconds }};
        const resetEmail = '{{ session('reset_email') }}';
        
        $(document).ready(function () {
            resetTimer();
        });

        let interval;
        function resetTimer() {
            let timeLeft = RESEND_EMAIL_TIME_SECONDS;
            const timer = document.getElementById('resendTimer');
            const resendButton = document.getElementById('resendButton');

            // Desabilitar botão e adicionar classe disabled
            resendButton.disabled = true;
            resendButton.classList.add('disabled');

            if (interval){
                clearInterval(interval);
            }

            // Definir exibição inicial do timer
            const initialMinutes = String(Math.floor(timeLeft / 60)).padStart(2, '0');
            const initialSeconds = String(timeLeft % 60).padStart(2, '0');
            timer.textContent = `${initialMinutes}:${initialSeconds}`;

            interval = setInterval(() => {
                timeLeft -= 1;
                
                if (timeLeft <= 0) {
                    console.log('Timer expirado, habilitando botão');
                    clearInterval(interval);
                    resendButton.classList.remove('disabled');
                    resendButton.disabled = false;
                    timer.textContent = '00:00';
                } else {
                    const minutes = String(Math.floor(timeLeft / 60)).padStart(2, '0');
                    const seconds = String(timeLeft % 60).padStart(2, '0');
                    timer.textContent = `${minutes}:${seconds}`;
                }
            }, 1000);
        }

        function resendPasswordResetEmail() {
            const resendButton = document.getElementById('resendButton');
            
            // Verificar se o botão está desabilitado
            if (resendButton.disabled || resendButton.classList.contains('disabled')) {
                return false;
            }
            
            if (resetEmail) {
                const formData = new FormData();
                formData.append('email', resetEmail);
                formData.append('_token', '{{ csrf_token() }}');

                $('#resendSpinner').show();
                $('#resendButtonText').hide();
                $('#resendButton').prop('disabled', true);

                $.ajax({
                    url: "{{ route('password.email') }}",
                    type: 'post',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (response) {
                        $('#resendButton').prop('disabled', false);
                        $('#resendButtonText').show();
                        $('#resendSpinner').hide();
                        resetTimer();
                        
                        // Mostrar mensagem de sucesso temporária
                        showTemporaryMessage('E-mail reenviado com sucesso!', 'success');
                    },
                    error: function (response, textStatus, msg) {
                        $('#resendButton').prop('disabled', false);
                        $('#resendButtonText').show();
                        $('#resendSpinner').hide();
                        resetTimer();
                        
                        // Mostrar mensagem de erro temporária
                        showTemporaryMessage('Erro ao reenviar e-mail. Tente novamente.', 'danger');
                    }
                });
            }
        }

        function showTemporaryMessage(message, type) {
            const alertDiv = $(`
                <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `);
            
            $('body').append(alertDiv);
            
            // Remover automaticamente após 5 segundos
            setTimeout(() => {
                alertDiv.fadeOut(() => alertDiv.remove());
            }, 5000);
        }
        @endif
    </script>
@endsection
