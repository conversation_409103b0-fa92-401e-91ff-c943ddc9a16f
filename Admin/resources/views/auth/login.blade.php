@extends('layouts.guest')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-5">
            <div class="card" style="margin-top:3rem; border-radius: 12px; box-shadow: 2px 0px 31px -12px rgba(0,0,0,0.29);
            -webkit-box-shadow: 2px 0px 31px -12px rgba(0,0,0,0.29);
            -moz-box-shadow: 2px 0px 31px -12px rgba(0,0,0,0.29); border-width: 0px;">

                <div class="container" style="text-align: center; margin-top:3rem">
                    <img class="" src="{{ SystemHelper::get()['MAIN_LOGO_THEME']?? '/icon/logo_visao_negocio.svg' }}" alt="Logo Visão Negócio" width="60%">
                </div>
                <div class="row">
                    <div class="col">
                        @include('components.alerts.error')
                        @include('components.alerts.errors')
                        @include('components.alerts.success')
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" onsubmit="onSignIn(event)" action="{{ route('auth.api.login') }}">
                        @csrf
                        <div class="form-group row">
                            <label for="email" class="col-md-12 col-form-label">{{ __('E-mail/CPF') }}</label>

                            <div class="col-md-12">
                                <input id="email" onkeyup="cpfMask()" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email', session('remember_email')) }}" required autocomplete="email" autofocus>
                                @error('email')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="password" class="col-md-12 col-form-label">{{ __('Senha') }}</label>
                            <div class="col-md-12">

                                <div class="input-group">
                                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" value="{{ old('password', session('remember_password')) }}" required autocomplete="current-password">
                                    @include('components.buttons.visible_password_button')
                                </div>

                                @error('password')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-md-12 offset-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="remember" id="remember" {{ old('remember', session('remember_login')) ? 'checked' : '' }}>

                                    <label class="form-check-label" for="remember">
                                        {{ __('Lembre de mim') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row mb-0">
                            <div class="col-md-12 offset-md-12" style="text-align: center">
                                <button type="submit" class="btn btn-success" style="width: 100%" id="loginSubmitButton">
                                    <span id="loginButtonText">{{ __('Entrar') }}</span>
                                    <div class="spinner-border text-secondary d-none" role="status" id="loginSpinner">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </button>
                                <a href="{{ route('clients.publicRegister') }}" class="btn btn-primary mt-2" style="width: 100%">
                                    Cadastrar-se
                                </a>
                                @if (Route::has('password.request'))
                                    <a class="btn btn-link" style="text-decoration: none;" href="{{ route('password.request') }}">
                                        {{ __('Esqueceu sua senha?') }}
                                    </a>
                                @endif
                            </div>
                        </div>

                        <div style="margin-left: 20px; margin-right: 20px; margin-top: 10px; margin-bottom: 5px" style="text-align: center">
                            <label style="text-align: center" class="form-check-label" for="term">Para acessar nossa política de privacidade e termos de uso <a
                                    href="{{route('data-processing-agreement')}}"
                                    target="_blank">clique aqui</a></label>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
    @if ($message = Session::get('success'))
        <div class="alert alert-success alert-block shadow fade show" style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
            <strong>{{$message}}</strong>
        </div>
    @elseif($message = Session::get('danger'))
        <div class="alert alert-danger alert-block shadow fade show" style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
            <strong>{{$message}}</strong>
        </div>
    @endif
</div>
<script>
    //timeout
    window.setTimeout(function() {
        $(".alert").fadeTo(500, 0).slideUp(500, function(){
            $(this).remove();
        });
    }, 10000);

    function cpfMask() {
        var valueInput = $("#email").val().replaceAll(".", "").replaceAll("-", "");

        if (valueInput.length == 11 && verifyNumber(valueInput)) {
            $("#email").val(cpf(valueInput));
        }
    }

    function cpf(v){
        v=v.replace(/\D/g,"")                    //Remove tudo o que não é dígito
        v=v.replace(/(\d{3})(\d)/,"$1.$2")       //Coloca um ponto entre o terceiro e o quarto dígitos
        v=v.replace(/(\d{3})(\d)/,"$1.$2")       //Coloca um ponto entre o terceiro e o quarto dígitos
        //de novo (para o segundo bloco de números)
        v=v.replace(/(\d{3})(\d{1,2})$/,"$1-$2") //Coloca um hífen entre o terceiro e o quarto dígitos
        return v
    }

    function verifyNumber(value){
        var er = /^[0-9]+$/;
        return (er.test(value));
    }

    function onSignIn(event) {
        event.preventDefault();
        $('#loginSubmitButton').prop('disabled', true);
        $('#loginButtonText').addClass('d-none');
        $('#loginSpinner').removeClass('d-none')
        event.target.submit();
    }

</script>
@endsection
