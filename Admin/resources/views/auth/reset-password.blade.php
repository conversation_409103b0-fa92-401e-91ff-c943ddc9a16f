@extends('layouts.guest', ['activePage' => 'reports'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="card card-body shadow-lg">
                <div class="row row-cols-lg-2 row-cols-md-2 row-cols-sm-1 mb-2">
                    <div class="col-lg-5 col-md-12 col-sm-12">
                        <h5 class="card-title custom-title mb-2">Defina sua nova senha senha</h5>
                    </div>
                    <div class="col-lg-7 col-md-12 col-sm-12 d-flex"></div>
                </div>
                <div class="row d-flex justify-content-center mb-4">
                    <div class="col-6">
                        <form class="row" action="{{ route('password.update') }}" method="post">
                            @csrf
                            <input type="hidden" name="token" value="{{ $request->route('token') }}">
                            <div class="col">
                                <div class="mb-2">
                                    @include('components.inputs.text', ['name' => 'email', 'label' => 'E-mail', 'value' => old('email', $request->email), 'readonly' => true])
                                </div>
                                <div class="mb-2">
                                    @include('components.inputs.text', ['input_type' => 'password','name' => 'password', 'label' => 'Nova Senha', 'placeholder' => 'Nova Senha', 'value' => old('password')])
                                </div>
                                <div class="">
                                    @include('components.inputs.text', ['input_type' => 'password','name' => 'password_confirmation', 'label' => 'Confirme a Senha', 'placeholder' => 'Repita a Senha', 'value' => old('password_confirmation')])
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button class="btn btn-success mt-4" type="submit">Resetar Senha</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
