<div class="modal fade" id="showTopicModal" tabindex="-1" role="dialog" aria-labelledby="showTopicModalLabel" 
aria-hidden="true" data-backdrop="static" data-keyboard="false" style="padding-bottom: 1%; padding-top: 1%;">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" >
            <div class="modal-header">
                <h5 class="modal-title" id="showTopicModalLabel">Visualizar Categoria</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-0">
                <div id="loadingIndicator" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Carregando...</span>
                    </div>
                    <p class="mt-2">Carregando conteúdo...</p>
                </div>
                <iframe id="showTopicFrame" style="width:100%; border: none; display: none; max-height: 100vh; overflow: auto;"></iframe>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let isClosingConfirmed = false;
    let topicWasUpdated = false;

    window.markTopicAsUpdated = function() {
        topicWasUpdated = true;
    };

    // Oculta o loading e exibe o iframe quando o conteúdo terminar de carregar
    // Função para receber a altura do iframe do conteúdo filho
    window.addEventListener('message', function(e) {
        if (e.data && e.data.type === 'frameHeight') {
            const iframe = document.getElementById('showTopicFrame');
            if (iframe) {
                const maxHeight = window.innerHeight * 0.86;
                const height = Math.min(e.data.height, maxHeight);
                iframe.style.height = height + 'px';
            }
        }
    });

    $('#showTopicFrame').on('load', function() {
        $('#loadingIndicator').hide();
        $(this).show();

        isClosingConfirmed = false;
    });

    $('#showTopicModal').on('hidden.bs.modal', function () {
        $('#showTopicFrame').attr('src', 'about:blank').hide();
        $('#loadingIndicator').show();
        isClosingConfirmed = false;

        if (topicWasUpdated) {
            if (typeof loadTopicsFormEmployee === 'function') {
                loadTopicsFormEmployee();
            }
            topicWasUpdated = false;
        } 
    });

    $('#showTopicModal').on('show.bs.modal', function () {
        $('#loadingIndicator').show();
        $('#showTopicFrame').hide();
        isClosingConfirmed = false;
        topicWasUpdated = false;
    });

    $('#showTopicModal').on('hide.bs.modal', async function (e) {
        // Se o fechamento já foi confirmado pela flag isClosingConfirmed, permite fechar
        if (isClosingConfirmed) {
            return; // Sai do handler, permitindo o fechamento
        }

        const iframe = document.getElementById('showTopicFrame');
        if (iframe && iframe.contentWindow) {
            let editChanged = false;
            if (typeof iframe.contentWindow.isTopicEditFormChanged === 'function') {
                editChanged = iframe.contentWindow.isTopicEditFormChanged();
            }

            let editDefaultChanged = false;
            if (typeof iframe.contentWindow.isTopicEditDefaultFormChanged === 'function') {
                editDefaultChanged = iframe.contentWindow.isTopicEditDefaultFormChanged();
            }

            let createChanged = false;
            if (typeof iframe.contentWindow.isTopicCreateFormChanged === 'function') {
                createChanged = iframe.contentWindow.isTopicCreateFormChanged();
            }

            const hasChanges = editChanged || editDefaultChanged || createChanged;

            if (hasChanges) {
                e.preventDefault();

                const result = await Swal.fire({
                    title: 'Sair sem salvar?',
                    text: "Você tem alterações não salvas. Deseja realmente sair?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#28a745',
                    confirmButtonText: 'Sair sem salvar',
                    cancelButtonText: 'Salvar e fechar'
                });

                // Se o usuário confirmar que quer sair sem salvar
                if (result.isConfirmed) {
                    isClosingConfirmed = true;
                    $(this).modal('hide');
                }
                // Se o usuário clicar em "Salvar e fechar"
                else if (result.dismiss === Swal.DismissReason.cancel) {
                    const iframe = document.getElementById('showTopicFrame');
                    if (iframe && iframe.contentWindow) {
                        const submitButton = iframe.contentWindow.document.getElementById('submit-button');
                        if (submitButton) {
                            submitButton.click();
                        }
                    }
                }
            }
        }
    });

    // Previne que o botão close (X) feche o modal diretamente
    $('.close').on('click', function(e) {
        e.preventDefault();
        $('#showTopicModal').modal('hide');
    });
});
</script>
