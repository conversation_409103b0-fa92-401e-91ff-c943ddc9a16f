@extends('layouts.modals')

@section('content')
    <div class="form-row px-3 mx-0" style="padding-bottom: 80px;">
        <form action="{{ $topic['default'] ?? false ?
                route('topics.default.update', $topic['id']):
                route('topics.update', $topic['id']) }}"
              id="form-topic"
              method="POST" onsubmit="event.preventDefault();">
            @csrf
            @method('put')
            @include('topic.form')
            <div class="modal-footer d-flex justify-content-end w-100"
                 style="position: fixed; bottom: 0; left: 0; width: 100%; background-color: #f8f9fa; z-index: 10; border-top: 1px solid #dee2e6; padding-right: 1.5rem;">
                <button id="submit-button" type="submit" class="btn btn-success"><PERSON>var e fechar</button>
            </div>
        </form>
    </div>
@endsection
