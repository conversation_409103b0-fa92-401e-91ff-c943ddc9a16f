@extends('layouts.modals')

@section('content')
    <div class="form-row px-3 mx-0" style="padding-bottom: 80px;">
        <form action="{{ route('topics.store') }}" id="form-topic" method="POST">
            @csrf
            @include('topic.form', ['isStore' => true]) {{-- Conteúdo do formulário --}}

            {{-- Botão de submissão e rodapé do modal --}}
            <div class="modal-footer d-flex justify-content-end w-100" style="position: fixed; bottom: 0; left: 0; width: 100%; background-color: #f8f9fa; z-index: 10; border-top: 1px solid #dee2e6; padding-right: 1.5rem;">
                <button id="submit-button" type="submit" class="btn btn-success">Salvar e fechar</button>
            </div>
        </form>
    </div>
@endsection
@push('js')
<script>
    $(document).ready(function() {
        // Guarda o estado inicial do formulário dentro do iframe
        window.initialTopicCreateFormData = $('#form-topic').serialize();

        // Função global para a página pai verificar se houve mudanças
        window.isTopicCreateFormChanged = function() {
            return $('#form-topic').serialize() !== window.initialTopicCreateFormData;
        }
    });
</script>
@endpush
