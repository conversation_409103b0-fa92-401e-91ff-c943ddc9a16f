@php use App\Helpers\ApiUser; @endphp
@extends('layouts.app', ['activePage' => 'topics'])

@php
    $isOnlyTopicsDefault = ApiUser::get()['business_selected'] == null;
@endphp

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Categorias</h5>
                            </div>

                            <div class="col-lg-9 col-md-6 col-sm-6 col-2 mb-2 d-flex justify-content-end">
                                <div class="d-flex">
                                    @if(($isOnlyTopicsDefault &&
                                        ApiUser::hasPermissionOrIsAdmin('update_topics') &&
                                        ApiUser::hasUserFunction('default_topics')) ||
                                        (!$isOnlyTopicsDefault && ApiUser::hasPermissionOrIsAdmin('update_topics'))
                                    )
                                        <a href="{{ route('topics.create') }}" type="button"
                                            class="btn btn-success mr-1">
                                                <img class="card-img-left example-card-img-responsive"
                                                    src="{{url('icon/icon_plus.png')}}" width="15px"
                                                    style="margin-top: -2px;"/>
                                                <label class="styleBotaoAdicionar">Adicionar categoria</label>
                                        </a>
                                    @endif
                                    <button type="button" class="btn btn-secondary" onclick="showNegociosUser(false)">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{url('icon/icon_filtro.png')}}" width="15px"
                                             style="margin-top: -2px;"/>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{route('topics.search')}}" method="get" id="form_search_topics">
                                    @csrf
                                    <div class="input-group " id="search_form_desktop">
                                        <input type="text" class="form-control mr-2" name="search"
                                               aria-describedby="search" style="flex: 5;"
                                               placeholder="Pesquisar categoria"
                                               value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        <div class="input-group-append mr-2">
                                            <button class="btn btn-light" type="submit" id="btnSearch">
                                                <img class="card-img-left example-card-img-responsive"
                                                        src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <select class="form-control mr-2" name="filter_default"
                                                style="flex: 4;"
                                                aria-describedby="filter_default" id="filter_default_desktop"
                                                @if ($isOnlyTopicsDefault) disabled @endif>
                                            <option value="all"
                                                    @if(isset($filter_default) && $filter_default == 'all') selected @endif>
                                                Todos
                                            </option>
                                            <option value="default"
                                                    @if((isset($filter_default) && $filter_default == 'default') || $isOnlyTopicsDefault) selected @endif>
                                                Categorias Padrão
                                            </option>
                                            <option value="not_default"
                                                    @if(isset($filter_default) && $filter_default == 'not_default') selected @endif>
                                                Categorias não padrão
                                            </option>
                                        </select>
                                    </div>
                                    <div class="input-group d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="search"
                                                   aria-describedby="search" placeholder="Pesquisar categoria"
                                                   value="{{ isset($search) ? $search : '' }}" id="search_mobile">
                                        </div>
                                        <div class="input-group mb-2">
                                            <button class="btn btn-light btn-block" type="submit">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <div class="input-group mb-2">
                                            <select class="form-control" name="filter_default"
                                                    aria-describedby="filter_default" id="filter_default_mobile"
                                                    @if ($isOnlyTopicsDefault) disabled @endif>
                                                <option value="all"
                                                        @if(isset($filter_default) && $filter_default == 'all') selected @endif>
                                                    Todos
                                                </option>
                                                <option value="default"
                                                        @if((isset($filter_default) && $filter_default == 'default') || $isOnlyTopicsDefault) selected @endif>
                                                    Categorias Padrão
                                                </option>
                                                <option value="not_default"
                                                        @if(isset($filter_default) && $filter_default == 'not_default') selected @endif>
                                                    Categorias não padrão
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="topics-table">
                            <thead>
                                <tr>
                                    <th scope="col" style="text-align: center">Ações</th>
                                    <th scope="col">Nome</th>
                                    <th scope="col">Descrição</th>
                                    <th scope="col">Negócios</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Obrigatoriedade</th>
                                </tr>
                            </thead>
                            <tbody>
                            @foreach ($topics as $topic)
                                <tr>
                                    <td style="text-align: center">
                                        <div class="btn-group">
                                            <a href="{{ route('topics.show', $topic['id']) }}"
                                               class="btn btn-secondary" style="margin-right: 10px">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                            </a>

                                            @if(!$isOnlyTopicsDefault &&
                                                ApiUser::hasPermissionOrIsAdmin('update_topics') &&
                                                !ApiUser::hasUserFunction('default_topics') &&
                                                $topic['default'])
                                                <a href="{{ route('topics.edit.default', $topic['id']) }}"
                                                   class="btn btn-primary" style="margin-right: 10px">
                                                    <img class="card-img-left example-card-img-responsive"
                                                         src="{{url('icon/icon_editar.png')}}" width="20px"/>
                                                </a>
                                            @endif
                                            @if((ApiUser::hasPermissionOrIsAdmin('update_topics') && !$topic['default']) ||
                                                (ApiUser::hasUserFunction('default_topics') && $topic['default']))
                                                <div>
                                                    <a class="btn btn-primary" style="margin-right: 10px"
                                                       href="{{ route('topics.edit', $topic['id']) }}">
                                                        <img class="card-img-left example-card-img-responsive"
                                                             src="{{url('icon/icon_editar.png')}}" width="20px"/>
                                                    </a>
                                                </div>
                                                @include('topic.modal_delete',['modalId' => 'delete-topic-'.($topic['id']), 'topic' => $topic])
                                                <div>
                                                    <button class="btn btn-danger" data-toggle="modal"
                                                            data-target="#delete-topic-{{ $topic['id'] }}">
                                                        <img class="card-img-left example-card-img-responsive"
                                                             src="{{url('icon/icon_lixeira.png')}}" width="18px"/>
                                                    </button>
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-form">
                                            <div>{{ $topic['name'] }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-form">
                                            <div>{{ $topic['description'] }}</div>
                                        </div>
                                    </td>
                                    @php
                                        if ($topic['default']) {
                                            $businesses = 'Todos';
                                            $allBusinesses = 'Todos';
                                        } else {
                                            $businesses = $topic['businesses'];
                                            usort($businesses, function ($a, $b) {
                                                return $a['name'] <=> $b['name'];
                                            });
                                            $allBusinesses = array_map(function ($business) {
                                                return $business['name'];
                                            }, $businesses);
                                            $allBusinesses = implode(', ', $allBusinesses);
                                            $businessesCount = count($businesses);
                                            $businessesLimit = 3;

                                            if ($businessesCount > $businessesLimit) {
                                                $businesses = array_slice($businesses, 0, $businessesLimit);
                                            }

                                            $businesses = array_map(function ($business) {
                                                return $business['name'];
                                            }, $businesses);

                                            $businesses = implode(', ', $businesses);

                                            if ($businessesCount > $businessesLimit) {
                                                $businesses .= '...';
                                            }
                                        }
                                    @endphp
                                    <td style="max-width: 12rem; text-overflow: ellipsis; overflow: hidden;"
                                        title="{{ $allBusinesses }}">
                                        {{ $businesses }}
                                    </td>
                                    <td>{{$topic['active'] ? 'Ativo' : 'Inativo'}}</td>
                                    <td>{{$topic['required'] ? 'Obrigatório' : 'Opcional'}}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>

                        {{ $topics->onEachSide(0)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('topic.subtitle')
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    <script>
        $(document).ready(() => {
            if (window.innerWidth <= 575) {
                $('#search_form_desktop').addClass('d-none');
                $('#search_form_desktop').find('input, select').each(function () {
                    $(this).prop('disabled', true);
                });

                $('#search_form_mobile').removeClass('d-none');
                $('#search_form_mobile').find('input, select').each(function () {
                    if ($(this).is('select')) {
                        if (!isOnlyTopicsDefault)
                            $(this).prop('disabled', false);
                    } else $(this).prop('disabled', false);
                });

            } else {
                $('#search_form_desktop').removeClass('d-none');
                $('#search_form_desktop').find('input, select').each(function () {
                    if ($(this).is('select')) {
                        if (!isOnlyTopicsDefault)
                            $(this).prop('disabled', false);
                    } else $(this).prop('disabled', false);
                });

                $('#search_form_mobile').addClass('d-none');
                $('#search_form_mobile').find('input, select').each(function () {
                    $(this).prop('disabled', true);
                });
            }
        });
        let filter_default_desktop = document.getElementById('filter_default_desktop');
        let filter_default_mobile = document.getElementById('filter_default_mobile');

        filter_default_desktop.addEventListener('change', function () {
            filter_default_mobile.value = filter_default_desktop.value;
            const form = document.getElementById('form_search_topics');
            form.submit();
        });

        filter_default_mobile.addEventListener('change', function () {
            filter_default_desktop.value = filter_default_mobile.value;
            const form = document.getElementById('form_search_topics');
            form.submit();
        });

        let search_desktop = document.getElementById('search_desktop');
        let search_mobile = document.getElementById('search_mobile');
        let isOnlyTopicsDefault = {{ $isOnlyTopicsDefault ? 'true' : 'false'}};

        search_desktop.addEventListener('keyup', function () {
            search_mobile.value = search_desktop.value;
        });

        search_mobile.addEventListener('keyup', function () {
            search_desktop.value = search_mobile.value;
        });
        window.addEventListener('resize', function () {
            if (window.innerWidth <= 575) {
                $('#search_form_desktop').addClass('d-none');
                $('#search_form_desktop').find('input, select').each(function () {
                    $(this).prop('disabled', true);
                });

                $('#search_form_mobile').removeClass('d-none');
                $('#search_form_mobile').find('input, select').each(function () {
                    if ($(this).is('select')) {
                        if (!isOnlyTopicsDefault)
                            $(this).prop('disabled', false);
                    } else $(this).prop('disabled', false);
                });

            } else {
                $('#search_form_desktop').removeClass('d-none');
                $('#search_form_desktop').find('input, select').each(function () {
                    if ($(this).is('select')) {
                        if (!isOnlyTopicsDefault)
                            $(this).prop('disabled', false);
                    } else $(this).prop('disabled', false);
                });

                $('#search_form_mobile').addClass('d-none');
                $('#search_form_mobile').find('input, select').each(function () {
                    $(this).prop('disabled', true);
                });
            }
        });
    </script>
@endsection
