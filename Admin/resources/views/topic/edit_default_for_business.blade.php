@extends('layouts.modals')

@section('content')
    <form action="{{ route('topics.default.update.for.business', $topic['id']) }}" id="form-topic"
          method="POST">
        @csrf
        @method('put')
        <div class="form-row px-3 mx-0" style="padding-bottom: 80px;">
            <div class="col-md-12">
                <label for="">Nome</label>
                <input type="text" class="form-control style_campo_estatico"
                       value="{{ $topic['name'] }}" disabled>
            </div>
            <div class="col-md-12 mt-2">
                <label for="">Descrição</label>
                <textarea class="form-control style_campo_estatico"
                          disabled>{{ $topic['description'] ?? 'Não informado' }}</textarea>
            </div>

            <div style="margin-top: 20px; display: flex; flex-direction: row">
                <div class="@if(!ApiUser::get()['authorizedFunction']['default_topics']) d-none @endif">
                    <div class="form-check mb-3 ml-1">
                        <input class="form-check form-check-input" type="checkbox" disabled
                               @if($topic['default'] ?? false) checked @endif
                               id="default" name="default" onchange="defaultTopicChanged(this, false)">
                        <label class="form-check-label" for="default">
                            Categoria padrão
                        </label>
                    </div>
                </div>
                <div
                    class="@if(!ApiUser::get()['authorizedFunction']['default_topics'] || !$topic['default']) d-none @endif">
                    <div class="form-check mb-3" style="margin-left: 20px;">
                        <input class="form-check form-check-input" type="checkbox" disabled
                               @if($topic['active'] ?? false) checked @endif
                               id="active" name="active">
                        <label class="form-check-label" for="active" id="active_label">
                            Ativo
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-md-12" style="margin-top: 20px">
                <h6 class="style_campo_titulo">Perguntas Padrão</h6>
            </div>
            <div class="col-md-12">
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <th scope="col">Ordem</th>
                        <th scope="col">Descrição</th>
                        <th scope="col">Tipo de avaliação</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $topic['questions_default'] = collect($topic['questions_default'])->sortBy('order')->toArray();

                        $topic['questions_default'] = array_map(function($question){
                            $question['evaluation_type'] = $question['evaluation_type'] == 'yes_no' ? 'Sim/Não' : '5 estrelas';
                            return $question;
                        }, $topic['questions_default']);

                    @endphp
                    @foreach($topic['questions_default'] as $question)
                        <tr>
                            <td>{{ $question['order'] }}</td>
                            <td>{{ $question['description'] }}</td>
                            <td>{{ $question['evaluation_type'] }}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            @include('components.topics-questions', [
                        'topic' => $topic,
                        'title'=> 'Lista de Questões do Negócio',
                        'withBusiness' => true,
                        ])
            @include('components.loading', ['id' => 'loadingUpdate', 'message' => 'Atualizando...'])
            <div class="modal-footer d-flex justify-content-end w-100" style="position: fixed; bottom: 0; left: 0; width: 100%; background-color: #f8f9fa; z-index: 10; border-top: 1px solid #dee2e6; padding-right: 1.5rem;">
                <button id="submit-button" type="submit" class="btn btn-success">Salvar e fechar
                </button>
            </div>
        </div>
    </form>
    <script>
      $(document).ready(function () {
          initQuestions();
          window.initialTopicEditDefaultFormDATA = $('#form-topic').serialize();

          window.isTopicEditDefaultFormChanged = function() {
              return $('#form-topic').serialize() !== window.initialTopicEditDefaultFormDATA;
          }

          const form = document.getElementById('form-topic');
          form.addEventListener('submit', function (event) {
              event.preventDefault();
              $('#loadingUpdate').modal('show');
              $('#submit-button').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Atualizando...');
              form.submit();
          });
      });
    </script>
@endsection
