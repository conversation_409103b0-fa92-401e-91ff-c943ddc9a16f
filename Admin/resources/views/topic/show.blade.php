@extends('layouts.modals')

@php
    use App\Helpers\ApiUser;
    $isOnlyTopicsDefault = ApiUser::get()['business_selected'] == null;
@endphp

@section('content')
    <div class="form-row px-3 mx-0">
        <header class="col-md-12">
            <h6 class="style_campo_titulo">Dados da categoria</h6>
        </header>
        <div class="col-md-12">
            <label for="">Nome</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ $topic['name'] }}" disabled>
        </div>
        <div class="col-md-12 mt-2">
            <label for="">Descrição</label>
            <textarea class="form-control style_campo_estatico"
                      disabled>{{ $topic['description'] ?? 'Não informado' }}</textarea>
        </div>

        <div style="margin-top: 20px; display: flex; flex-direction: row">
            <div class="@if(!ApiUser::get()['authorizedFunction']['default_topics']) d-none @endif">
                <div class="form-check mb-3 ml-1">
                    <input class="form-check form-check-input" type="checkbox" disabled
                           @if($topic['default'] ?? false) checked @endif
                           id="default" name="default" onchange="defaultTopicChanged(this, false)">
                    <label class="form-check-label" for="default">
                        Categoria padrão
                    </label>
                </div>
            </div>
            <div
                class="@if(!ApiUser::get()['authorizedFunction']['default_topics'] || !$topic['default']) d-none @endif">
                <div class="form-check mb-3" style="margin-left: 20px;">
                    <input class="form-check form-check-input" type="checkbox" disabled
                           @if($topic['active'] ?? false) checked @endif
                           id="active" name="active">
                    <label class="form-check-label" for="active" id="active_label">
                        Ativo
                    </label>
                </div>
            </div>
            <div
                class="@if(!ApiUser::get()['authorizedFunction']['default_topics'] || !$topic['default']) d-none @endif">
                <div class="form-check mb-3" style="margin-left: 20px;">
                    <input class="form-check form-check-input" type="checkbox" disabled
                           @if($topic['required'] ?? false) checked @endif
                           id="required" name="required">
                    <label class="form-check form-check-label p-0" for="required" id="required_label">
                        Categoria Obrigatória
                    </label>
                </div>
            </div>
        </div>

        <div class="col-md-12" style="margin-top: 20px">
            <h6 class="style_campo_titulo">Perguntas Padrão</h6>
        </div>
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <th scope="col">Descrição</th>
                        <th scope="col">Tipo de avaliação</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $topic['questions'] = collect($topic['questions'])->sortBy('order')->toArray();

                        $topic['questions'] = array_map(function($question){
                            $question['evaluation_type'] = $question['evaluation_type'] == 'yes_no' ? 'Sim/Não' : '5 estrelas';
                            return $question;
                        }, $topic['questions']);

                    @endphp
                    @foreach($topic['questions'] as $question)
                        <tr>
                            <td>{{ $question['description'] }}</td>
                            <td>{{ $question['evaluation_type'] }}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @if(!$isOnlyTopicsDefault && isset($topic['questions_business']) &&  $topic['questions_business'] != null && count($topic['questions_business']) > 0)
            <div class="col-md-12" style="margin-top: 20px">
                <h6 class="style_campo_titulo">Perguntas do Negócio</h6>
            </div>
            <div class="col-md-12">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th scope="col">Ordem</th>
                            <th scope="col">Descrição</th>
                            <th scope="col">Tipo de avaliação</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php
                            $topic['questions_business'] = collect($topic['questions_business'])->sortBy('order')->toArray();

                            $topic['questions_business'] = array_map(function($question){
                                $question['evaluation_type'] = $question['evaluation_type'] == 'yes_no' ? 'Sim/Não' : '5 estrelas';
                                return $question;
                            }, $topic['questions_business']);

                        @endphp
                        @foreach($topic['questions_business'] as $question)
                            <tr>
                                <td>{{ $question['order'] }}</td>
                                <td>{{ $question['description'] }}</td>
                                <td>{{ $question['evaluation_type'] }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
        @if(count($topic['businesses']) > 0)
            <div class="col-md-12" style="margin-top: 20px">
                <h6 class="style_campo_titulo">Negócios associados</h6>
            </div>
            <div class="col-md-12">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <tbody>
                        @foreach($topic['businesses'] as $business)
                            <tr>
                                <td>{{ $business['name'] }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
    </div>
@endsection
