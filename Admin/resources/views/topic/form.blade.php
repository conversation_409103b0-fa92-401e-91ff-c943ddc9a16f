@php
use App\Helpers\ApiUser;

@endphp
<div class="form-row">
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'name', 'label' => 'Nome', 'value' => $topic['name'] ?? old('name'), 'placeholder' => 'Digite o nome da categoria', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text_area', [
    'name' => 'description',
     'label' => 'Descrição',
      'value' => $topic['description'] ?? old('description'),
       'placeholder' => 'Digite a descrição da categoria',
        'withErrorText' => true,
         'required' => false
         ])
        </div>
    </div>
    <div class="@if(!ApiUser::hasUserFunction('default_topics')) d-none @endif">
        <div class="form-check mb-3 ml-1">
            <input class="form-check form-check-input" type="checkbox"
                   @if(($topic['default'] ?? false) || ($is_default_from_url ?? false)) checked @endif
                   id="default" name="default" onchange="defaultTopicChanged(this, false)" @if(!empty($topic) || ($is_default_from_url ?? false)) disabled @endif
            >
            <label class="form-check-label" for="default">
                Categoria padrão
            </label>
            @if(!empty($topic) || ($is_default_from_url ?? false))
                @if(($topic['default'] ?? false) || ($is_default_from_url ?? false))
                    <input type="hidden" name="default" value="true">
                @endif
            @endif
        </div>
    </div>
    <div class="@if(!ApiUser::hasUserFunction('default_topics')) d-none @endif">
        <div class="form-check mb-3" style="margin-left: 20px;">
            <input class="form-check form-check-input" type="checkbox"
                   @if($topic['active'] ?? false || ($is_default_from_url ?? false)) checked @endif
                   id="active" name="active">
            <label class="form-check-label" for="active" id="active_label">
                Ativo
            </label>
        </div>
    </div>

    <div class="@if(!ApiUser::hasUserFunction('default_topics')) d-none @endif">
        <div class="form-check mb-3" style="margin-left: 20px;">
            <input class="form-check form-check-input" type="checkbox"
                   @if($topic['required'] ?? false) checked @endif
                   id="required" name="required">
            <label class="form-check form-check-label p-0" for="required" id="required_label">
                Categoria Obrigatória
            </label>
        </div>

    </div>

    @include('components.business-table', ['businesses' => $topic['businesses'] ?? [], 'isEdit' => isset($topic)])
    @include('components.topics-questions', ['topic' => $topic ?? null])

    @include('components.loading', ['message' => 'Aguardando confirmação de email'])
    @include('components.loading', ['id' => 'loadingCreateUpdate', 'message' => 'Enviando...'])
    @push('js')
        <script>
            $(document).ready(function () {
                defaultTopicChanged(document.getElementById('default'), true);
                loadBusinessesFormEmployee();
                initQuestions();
                // Guarda o estado inicial do formulário dentro do iframe
                window.initialTopicEditFormData = $('#form-topic').serialize();
                // Função global para a página pai verificar se houve mudanças
                window.isTopicEditFormChanged = function() {
                    return $('#form-topic').serialize() !== window.initialTopicEditFormData;
                }
            });

            function defaultTopicChanged(checkbox, isfirstLoad = false) {
                if ($(checkbox).is(':checked')) {
                    $('#business_selected').empty();
                    $('#businessesInputs').empty();
                    $('#businessTableFormEmployee').addClass('d-none');
                    $('#businessSelectedLabel').addClass('d-none');
                    $('#businessesSeparator').addClass('d-none');
                    $('#businessPaginationFormEmployee').addClass('d-none');
                    $('#businessSearchFormEmployee').addClass('d-none');
                    $('#businessesTopic').addClass('d-none');
                    $('.loading').addClass('d-none');
                    businessesSelectedIds = [];
                    showHideNoBusinessSelectedMessage();
                    $('#active').removeClass('d-none');
                    $('#required').removeClass('d-none');
                    $('#active_label').removeClass('d-none');
                    $('#required_label').removeClass('d-none');

                    if (!isfirstLoad) {
                        $('#active').prop('checked', true);
                        $('#required').prop('checked', false);
                    }
                } else {
                    //$('#businessesTopic').removeClass('d-none');
                    $('#businessesTopic').addClass('d-none'); // Por enquanto vamos deixar sempre oculto e gravando apenas para o negócio selecionado.
                    $('#businessTableFormEmployee').removeClass('d-none');
                    $('#businessSelectedLabel').removeClass('d-none');
                    $('#businessesSeparator').removeClass('d-none');
                    $('#businessPaginationFormEmployee').removeClass('d-none');
                    $('#businessSearchFormEmployee').removeClass('d-none');

                    showHideNoBusinessSelectedMessage();
                    $('#active').addClass('d-none');
                    $('#required').addClass('d-none');
                    $('#active_label').addClass('d-none');
                    $('#required_label').addClass('d-none');
                    if (!isfirstLoad) {
                        loadBusinessesFormEmployee();
                        $('#active').prop('checked', true);
                        $('#required').prop('checked', true);
                        selectActualBusiness();
                    }
                }
            }

            form.addEventListener('submit', function (event) {
                event.preventDefault();
                if (verifyBusinesses() && verifyQuestions()) {
                    $('#submit-button').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Adicionando...');
                    $('#loadingCreateUpdate').modal('show');
                    form.submit();
                }
            });
        </script>

        <script type="text/javascript"
                src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
@endpush
