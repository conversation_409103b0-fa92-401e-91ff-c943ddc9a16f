@php
use App\Helpers\ApiUser;
use App\Helpers\StringMask;
@endphp
@extends('layouts.app', ['activePage' => 'parameters'])

@section('content')
    <div class="container mt-4">
        @php
            $user = ApiUser::get();
            $business = $user['business_selected'];
            $parameters = $business['parameters'] ?? [];
            $plan = $business['plan'];
        @endphp

        @php
            // Opções predefinidas para Tempo de Expiração da Avaliação e Cashback
            $predefined_hours = [1, 12, 24, 48, 168, 720];

            // Determinar a opção selecionada para Tempo de Expiração da Avaliação
            if (in_array($parameters['evaluation_expiration_hours'] ?? null, $predefined_hours)) {
                $evaluation_expiration_option = $parameters['evaluation_expiration_hours'];
                $custom_evaluation_expiration_days = null;
            } else {
                $evaluation_expiration_option = 'other';
                $custom_evaluation_expiration_days = isset($parameters['evaluation_expiration_hours']) ? intval($parameters['evaluation_expiration_hours'] / 24) : old('custom_evaluation_expiration_days');
            }

            // Determinar a opção selecionada para Tempo de Expiração do Cashback
            if (in_array($parameters['cashback_expiration_hours'] ?? null, $predefined_hours)) {
                $cashback_expiration_option = $parameters['cashback_expiration_hours'];
                $custom_cashback_expiration_days = null;
            } else {
                $cashback_expiration_option = 'other';
                $custom_cashback_expiration_days = isset($parameters['cashback_expiration_hours']) ? intval($parameters['cashback_expiration_hours'] / 24) : old('custom_cashback_expiration_days');
            }
        @endphp
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10 col-sm-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h4 class="mb-0"
                            style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                            Parâmetros - {{ $business['name'] }}
                        </h4>

                        <span style="color:red" class="table-legend">* Campos obrigatórios</span>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ route('businesses.parameters.update', $business['id']) }}"
                              onsubmit="return validateForm()">
                            @csrf

                            <!-- Campo: Tempo de Expiração da Avaliação -->
                            <div class="form-group">
                                <label for="evaluation_expiration_option">
                                    Tempo de Expiração da Avaliação <span style="color:red">*</span>
                                </label>
                                <select class="form-control @error('evaluation_expiration_hours') is-invalid @enderror"
                                        id="evaluation_expiration_option" name="evaluation_expiration_option" required>
                                    <option value=""
                                            disabled {{ $evaluation_expiration_option === null ? 'selected' : '' }}>
                                        Selecione o tempo de expiração
                                    </option>
                                    <option
                                        value="720" {{ ($evaluation_expiration_option == 720) ? 'selected' : '' }}>
                                        30 dias
                                    </option>
                                    <option
                                        value="168" {{ ($evaluation_expiration_option == 168) ? 'selected' : '' }}>
                                        7 dias
                                    </option>
                                    <option
                                        value="48" {{ ($evaluation_expiration_option == 48) ? 'selected' : '' }}>
                                        2 dias
                                    </option>
                                    <option
                                        value="24" {{ ($evaluation_expiration_option == 24) ? 'selected' : '' }}>
                                        24 h
                                    </option>
                                    <option
                                        value="12" {{ ($evaluation_expiration_option == 12) ? 'selected' : '' }}>
                                        12 h
                                    </option>
                                    <option
                                        value="1" {{ ($evaluation_expiration_option == 1) ? 'selected' : '' }}>
                                        1 h
                                    </option>
                                    <option
                                        value="other" {{ ($evaluation_expiration_option == 'other') ? 'selected' : '' }}>
                                        Outro
                                    </option>
                                </select>
                                @error('evaluation_expiration_hours')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror

                                <!-- Campo Personalizado para Tempo de Expiração da Avaliação -->
                                <div class="custom-field mt-2" id="customEvaluationExpirationField"
                                     style="{{ ($evaluation_expiration_option == 'other') ? 'display: block;' : 'display: none;' }}">
                                    <label for="custom_evaluation_expiration_days" class="sr-only">Informe o tempo em
                                        dias <span style="color:red">*</span></label>
                                    <div class="input-group">
                                        <input type="number"
                                               class="form-control @error('custom_evaluation_expiration_days') is-invalid @enderror"
                                               id="custom_evaluation_expiration_days"
                                               name="custom_evaluation_expiration_days" min="1"
                                               value="{{ old('custom_evaluation_expiration_days', $custom_evaluation_expiration_days) }}"
                                               placeholder="Informe o tempo em dias">
                                        <div class="input-group-append">
                                            <span class="input-group-text">dias</span>
                                        </div>
                                        @error('custom_evaluation_expiration_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Campo: Tempo de Expiração do Cashback -->
                            <div class="form-group">
                                <label for="cashback_expiration_option">
                                    Tempo de Expiração do Cashback <span style="color:red">*</span>
                                </label>
                                <select class="form-control @error('cashback_expiration_hours') is-invalid @enderror"
                                        id="cashback_expiration_option" name="cashback_expiration_option" required>
                                    <option value=""
                                            disabled {{ $cashback_expiration_option === null ? 'selected' : '' }}>
                                        Selecione o tempo de expiração
                                    </option>
                                    <option
                                        value="720" {{ ($cashback_expiration_option == 720) ? 'selected' : '' }}>
                                        30 dias
                                    </option>
                                    <option
                                        value="168" {{ ($cashback_expiration_option == 168) ? 'selected' : '' }}>
                                        7 dias
                                    </option>
                                    <option
                                        value="48" {{ ($cashback_expiration_option == 48) ? 'selected' : '' }}>
                                        2 dias
                                    </option>
                                    <option
                                        value="24" {{ ($cashback_expiration_option == 24) ? 'selected' : '' }}>
                                        24 h
                                    </option>
                                    <option
                                        value="12" {{ ($cashback_expiration_option == 12) ? 'selected' : '' }}>
                                        12 h
                                    </option>
                                    <option
                                        value="1" {{ ($cashback_expiration_option == 1) ? 'selected' : '' }}>
                                        1 h
                                    </option>
                                    <option
                                        value="other" {{ ($cashback_expiration_option == 'other') ? 'selected' : '' }}>
                                        Outro
                                    </option>
                                </select>
                                @error('cashback_expiration_hours')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror

                                <!-- Campo Personalizado para Tempo de Expiração do Cashback -->
                                <div class="custom-field mt-2" id="customCashbackExpirationField"
                                     style="{{ ($cashback_expiration_option == 'other') ? 'display: block;' : 'display: none;' }}">
                                    <label for="custom_cashback_expiration_days" class="sr-only">Informe o tempo em
                                        dias <span style="color:red">*</span></label>
                                    <div class="input-group">
                                        <input type="number"
                                               class="form-control @error('custom_cashback_expiration_days') is-invalid @enderror"
                                               id="custom_cashback_expiration_days"
                                               name="custom_cashback_expiration_days" min="1"
                                               value="{{ old('custom_cashback_expiration_days', $custom_cashback_expiration_days) }}"
                                               placeholder="Informe o tempo em dias">
                                        <div class="input-group-append">
                                            <span class="input-group-text">dias</span>
                                        </div>
                                        @error('custom_cashback_expiration_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Campo: Formulário Padrão de Envio -->
                            <div class="form-group">
                                <label for="form_id">Formulário Padrão de Envio</label>
                                <select class="form-control @error('form_id') is-invalid @enderror" id="form_id"
                                        name="form_id">
                                    <option value="">Não selecionar agora</option>
                                    @foreach($forms as $form)
                                        <option
                                            value="{{ $form['id'] }}" {{ (old('form_id', $parameters['form_id'] ?? '') == $form['id']) ? 'selected' : '' }}>
                                            {{ $form['name'] }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('form_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Terceira Linha: Porcentagem do Cashback (%) e Intervalo entre Avaliações -->
                            <div class="form-row">
                                <!-- Porcentagem do Cashback (%) -->
                                <div class="form-group col-md-12">
                                    <label for="cashback_percentage">
                                        Porcentagem do Cashback <span style="color:red">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number"
                                               class="form-control @error('cashback_percentage') is-invalid @enderror"
                                               id="cashback_percentage" name="cashback_percentage"
                                               value="{{ old('cashback_percentage', $parameters['cashback_percentage'] ?? 5.00) }}"
                                               min="0" max="100" step="0.01" required>
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>
                                        @error('cashback_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Intervalo entre Avaliações -->
                                <!-- <div class="form-group col-md-6">
                                    <label for="evaluation_interval_day">
                                        Intervalo entre Avaliações <span style="color:red">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number"
                                               class="form-control @error('evaluation_interval_day') is-invalid @enderror"
                                               id="evaluation_interval_day" name="evaluation_interval_day"
                                               value="{{ old('evaluation_interval_day', $parameters['evaluation_interval_day'] ?? 0) }}"
                                               min="0" required>
                                        <div class="input-group-append">
                                            <span class="input-group-text">dias</span>
                                        </div>
                                        @error('evaluation_interval_day')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div> -->

                                <!--
                                <div class="form-group col-md-6">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox"
                                            class="custom-control-input @error('can_set_cashback_value') is-invalid @enderror"
                                            id="can_set_cashback_value" name="can_set_cashback_value"
                                            value="1" {{ old('can_set_cashback_value', $parameters['can_set_cashback_value'] ?? 0) ? 'checked' : '' }}>
                                        @error('can_set_cashback_value')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <label class="custom-control-label" for="can_set_cashback_value">
                                            Adicionar Valor do Produto/Serviço na Avaliação
                                        </label>
                                    </div>
                                </div> -->
                            </div>

                            <!-- Botão de Submit -->
                            <div class="d-flex justify-content-end mt-3">
                                <button type="submit" class="btn btn-success">Salvar</button>
                            </div>
                        </form>
                    </div>
                </div>
                @include("parameters.whatsapp_config")
            </div>
        </div>
        @endsection

        @push('js')
        <script>
            $(document).ready(function() {
                handleEvaluationExpirationOption();
                handleCashbackExpirationOption();
                handleCashbackPercentageInput();
            });

            function handleEvaluationExpirationOption() {
                const evaluationExpirationOption = document.getElementById('evaluation_expiration_option');
                const customEvaluationExpirationField = document.getElementById('customEvaluationExpirationField');
                const customEvaluationExpirationDays = document.getElementById('custom_evaluation_expiration_days');

                evaluationExpirationOption.addEventListener('change', function () {
                    if (this.value === 'other') {
                        customEvaluationExpirationField.style.display = 'block';
                        customEvaluationExpirationDays.required = true;
                    } else {
                        customEvaluationExpirationField.style.display = 'none';
                        customEvaluationExpirationDays.required = false;
                        customEvaluationExpirationDays.value = '';
                    }
                });

                // Executa a função no carregamento inicial para manter o estado correto
                evaluationExpirationOption.dispatchEvent(new Event('change'));
            }

            function handleCashbackExpirationOption() {
                const cashbackExpirationOption = document.getElementById('cashback_expiration_option');
                const customCashbackExpirationField = document.getElementById('customCashbackExpirationField');
                const customCashbackExpirationDays = document.getElementById('custom_cashback_expiration_days');

                cashbackExpirationOption.addEventListener('change', function () {
                    if (this.value === 'other') {
                        customCashbackExpirationField.style.display = 'block';
                        customCashbackExpirationDays.required = true;
                    } else {
                        customCashbackExpirationField.style.display = 'none';
                        customCashbackExpirationDays.required = false;
                        customCashbackExpirationDays.value = '';
                    }
                });

                // Executa a função no carregamento inicial para manter o estado correto
                cashbackExpirationOption.dispatchEvent(new Event('change'));
            }

            function handleCashbackPercentageInput() {
                const cashbackPercentage = document.getElementById('cashback_percentage');

                cashbackPercentage.addEventListener('input', function () {
                    let value = parseFloat(this.value);
                    if (value > 100) {
                        alert('A porcentagem máxima permitida é 100%.');
                        this.value = 100;
                    } else if (value < 0) {
                        this.value = 0;
                    }
                });
            }

            function validateForm() {
                const cashbackPercentage = parseFloat(document.getElementById('cashback_percentage').value);
                if (cashbackPercentage < 0 || cashbackPercentage > 100) {
                    alert('A porcentagem do cashback deve estar entre 0 e 100.');
                    return false;
                }

                // Verifica se "Outro" foi selecionado e se o campo personalizado está preenchido
                const evaluationExpirationOption = document.getElementById('evaluation_expiration_option').value;
                if (evaluationExpirationOption === 'other') {
                    const customDays = document.getElementById('custom_evaluation_expiration_days').value;
                    if (!customDays || customDays < 1) {
                        alert('Por favor, informe um valor válido para o tempo de expiração da avaliação.');
                        return false;
                    }
                }

                const cashbackExpirationOption = document.getElementById('cashback_expiration_option').value;
                if (cashbackExpirationOption === 'other') {
                    const customDays = document.getElementById('custom_cashback_expiration_days').value;
                    if (!customDays || customDays < 1) {
                        alert('Por favor, informe um valor válido para o tempo de expiração do cashback.');
                        return false;
                    }
                }

                return true;
            }
        </script>
        <style>
            /* Estilização da primeira opção do select "Formulário Padrão de Envio" */
            #form_id.form-control option:first-child {
                font-style: italic;
                color: gray;
            }
        </style>

@endpush
