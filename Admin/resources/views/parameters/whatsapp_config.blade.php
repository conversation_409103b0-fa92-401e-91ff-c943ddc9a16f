@if($plan['authorizedFunctions']['whatsapp_sending'])
    <div class="card shadow-sm mt-1">
        <div class="card-body">
            <input type="hidden" id="whatsappInstanceKey" value="{{ $parameters['whatsapp_instance_key'] ?? '' }}">
            <input type="hidden" id="whatsappPhoneNumber" value="{{ $parameters['whatsapp_phone_number'] ?? '' }}">

            <div id="whatsappDisconnected" class="{{ !empty($parameters['whatsapp_instance_key']) && !empty($parameters['whatsapp_phone_number']) ? 'd-none' : '' }}">
                <div class="text-center">
                    <button type="button" class="btn btn-success" id="btnConnectWhatsapp">
                        <i class="fab fa-whatsapp me-2"></i> Conectar WhatsApp
                    </button>
                </div>
                <!-- Modal do WhatsApp -->
                <div class="modal fade" id="whatsappModal" tabindex="-1" aria-labelledby="whatsappModalLabel"
                     aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fab fa-whatsapp text-success me-2"></i>
                                    Conectar WhatsApp
                                </h5>
                            </div>
                            <div class="modal-body">
                                <p class="mb-2">Para configurar o WhatsApp:</p>
                                <ol class="mb-0 mb-4">
                                    <li>Abra o WhatsApp no seu celular</li>
                                    <li>Toque em Menu <i class="fas fa-ellipsis-v"></i> e selecione <strong>Dispositivos
                                            conectados</strong></li>
                                    <li>Aponte a câmera do seu celular para o código QR</li>
                                </ol>
                                <!-- Loading do WhatsApp -->
                                <div id="whatsappLoading" class="text-center">
                                    <div class="d-flex flex-column align-items-center">
                                        <div class="spinner-border text-success mb-3" role="status">
                                        </div>
                                        <p id="loadingMessage" class="text-muted">Preparando código QR...</p>
                                    </div>
                                </div>
                                <!-- Container do QR Code -->
                                <div id="qrCodeContainer" class="d-none text-center animate__animated animate__fadeIn">
                                    <img id="qrCode" src="" alt="QR Code WhatsApp" class="img-fluid mb-3">
                                    <div class="d-flex justify-content-center gap-2">
                                        <button type="button" class="btn btn-outline-secondary" id="btnRefreshQrCode">
                                            <i class="fas fa-sync-alt me-1"></i> Atualizar QR Code
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" id="btnCheckStatus">
                                            <i class="fas fa-check-circle me-1"></i> Verificar Conexão
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" id="btnCancelConnection"
                                                data-bs-dismiss="modal">
                                            <i class="fas fa-times me-1"></i> Cancelar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="whatsappConnected" class="{{ !empty($parameters['whatsapp_instance_key']) && !empty($parameters['whatsapp_phone_number']) ? 'animate__animated animate__fadeIn' : 'd-none animate__animated animate__fadeIn' }}">
                <div class="card border-success mb-4">
                    <div class="card-body py-2">
                        <div class="d-flex align-items-center">
                            <div class="me-4">
                                <div class="rounded-circle bg-success d-flex align-items-center justify-content-center"
                                     style="width: 48px; height: 48px;">
                                    <i class="fab fa-whatsapp fa-2x text-white"></i>
                                </div>
                            </div>
                            <div class="ml-2">
                                <h5 class="card-title mb-1 text-success">WhatsApp Conectado</h5>
                                <p class="card-text mb-0" id="connectedPhoneNumber" style="font-size: 1.1rem;">
                                    {{ StringMask::phone($parameters['whatsapp_phone_number'] ?? '') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <button type="button" class="btn btn-danger" id="btnDisconnectWhatsapp">
                        <i class="fas fa-unlink me-1"></i> Desconectar
                    </button>
                </div>
            </div>
        </div>
    </div>

    @push('js')
        <script>
            function formatPhoneNumber(value) {
                let cleaned = value.replace(/\D/g, '');
                // Remove o 55
                if (cleaned.length > 11) {
                    cleaned = cleaned.slice(2);
                }
                let part1 = cleaned.slice(0, 2);
                let part2 = '';
                let part3 = '';
                if (cleaned.length > 2) {
                    if (cleaned.length > 10) {
                        part2 = cleaned.slice(2, 7);
                        part3 = cleaned.slice(7, 11);
                    } else {
                        part2 = cleaned.slice(2, 6);
                        part3 = cleaned.slice(6, 10);
                    }
                }
                let formatted = part1 ? '(' + part1 : '';
                formatted += part1.length === 2 ? ') ' : '';
                formatted += part2;
                formatted += part3 ? '-' + part3 : '';
                return formatted;
            }
            const btnConnectWhatsapp = $('#btnConnectWhatsapp');
            const btnDisconnectWhatsapp = $('#btnDisconnectWhatsapp');
            const btnRefreshQrCode = $('#btnRefreshQrCode');
            const btnCancelConnection = $('#btnCancelConnection');
            const qrCodeContainer = $('#qrCodeContainer');
            const whatsappLoading = $('#whatsappLoading');
            const loadingMessage = $('#loadingMessage');
            const qrCode = $('#qrCode');
            const whatsappConnected = $('#whatsappConnected');
            const whatsappDisconnected = $('#whatsappDisconnected');
            const connectedPhoneNumber = $('#connectedPhoneNumber');
            const whatsappInstanceKey = $('#whatsappInstanceKey').val();

            const whatsappPhoneNumber = $('#whatsappPhoneNumber').val();
            if (whatsappInstanceKey && whatsappPhoneNumber) {
                // Se tiver instância e número, assume diretamente que está conectado
                whatsappDisconnected.addClass('d-none');
                whatsappConnected.removeClass('d-none');
                connectedPhoneNumber.text(formatPhoneNumber(whatsappPhoneNumber));
            } else {
                // Se não tiver ambos, mostra como desconectado
                whatsappConnected.addClass('d-none');
                whatsappDisconnected.removeClass('d-none');
            }

            $(document).ready(function () {
                let checkStatusInterval;
                let qrCodeRetryCount = 0;
                let qrCodeRetryLimit = 5;
                let qrCodeRetryDelay = 2000; // 2 segundos

                function showLoading(message = "Preparando código QR...") {
                    whatsappLoading.removeClass('d-none');
                    qrCodeContainer.addClass('d-none');
                    loadingMessage.text(message);
                }

                function hideLoading() {
                    whatsappLoading.addClass('d-none');
                }

                function showQrCode() {
                    hideLoading();
                    qrCodeContainer.removeClass('d-none');
                    getQrCode();

                    // Inicia verificação automática a cada 7 segundos
                    checkStatusInterval = setInterval(() => {
                        checkConnectionStatus(false);
                    }, 7000);
                }

                function hideQrCode() {
                    qrCodeContainer.addClass('d-none');
                    hideLoading();
                    btnConnectWhatsapp.prop('disabled', false);
                    qrCodeRetryCount = 0;
                    whatsappModal.hide();

                    // Limpa o intervalo de verificação automática
                    if (checkStatusInterval) {
                        clearInterval(checkStatusInterval);
                    }
                }

                function getQrCode() {
                    showLoading("Obtendo código QR...");

                    $.ajax({
                        url: '{{ route("businesses.parameters.whatsapp.qrcode", $business["id"]) }}',
                        method: 'GET',
                        success: function (response) {
                            if (response.success && response.qrcode && response.qrcode.trim() !== '') {
                                qrCode.attr('src', response.qrcode);
                                hideLoading();
                                qrCodeContainer.removeClass('d-none');
                                qrCodeRetryCount = 0;
                            } else {
                                // QR code vazio, tenta novamente após um delay
                                qrCodeRetryCount++;
                                if (qrCodeRetryCount < qrCodeRetryLimit) {
                                    loadingMessage.text("Carregando código QR...");
                                    setTimeout(getQrCode, qrCodeRetryDelay);
                                } else {
                                    hideLoading();
                                    hideQrCode();
                                    alert('Não foi possível obter o código QR após várias tentativas. Tente novamente mais tarde.');
                                    btnConnectWhatsapp.prop('disabled', false);
                                }
                            }
                        },
                        error: function (response) {
                            console.error('Erro ao obter QR code:', response);
                            hideLoading();
                            hideQrCode();
                            alert('Erro ao gerar QR code. Tente novamente.');
                            btnConnectWhatsapp.prop('disabled', false);
                        }
                    });
                }

                function checkConnectionStatus(showToast = true) {
                    // Exibe um indicador de carregamento durante a verificação
                    const checkingToast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000
                    });

                    if (showToast) {
                        checkingToast.fire({
                            icon: 'info',
                            title: 'Verificando conexão...',
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                    }

                    $.ajax({
                        url: '{{ route("businesses.parameters.whatsapp.status", $business["id"]) }}',
                        method: 'GET',
                        success: function (response) {
                            let isConnected = false;
                            let phoneNumber = '';

                            if (response.success) {
                                isConnected = response.connected === true ||
                                    (response.data && response.data.instance_data && response.data.instance_data.phone_connected === true);

                                phoneNumber = response.phone_number ||
                                    (response.data && response.data.phone_number) || '';

                                if (isConnected) {
                                    hideQrCode();
                                    whatsappDisconnected.addClass('d-none');
                                    whatsappConnected.removeClass('d-none');
                                    if (phoneNumber) {
                                        connectedPhoneNumber.text(formatPhoneNumber(phoneNumber));
                                    }

                                    whatsappConnected.removeClass('animate__fadeIn').addClass('animate__animated animate__fadeIn');
                                    if (showToast) {
                                        Swal.close();
                                        const Toast = Swal.mixin({
                                            toast: true,
                                            position: 'top-end',
                                            showConfirmButton: false,
                                            timer: 3000,
                                            timerProgressBar: true
                                        });
                                        Toast.fire({
                                            icon: 'success',
                                            title: 'WhatsApp conectado com sucesso!'
                                        });
                                    }
                                } else if (!qrCodeContainer.hasClass('d-none')) {
                                    // Se o QR code estiver visível, mantem a mensagem de instruções
                                    whatsappConnected.addClass('d-none');
                                    whatsappDisconnected.removeClass('d-none');

                                    if (showToast) {
                                        Swal.close();
                                        const Toast = Swal.mixin({
                                            toast: true,
                                            position: 'top-end',
                                            showConfirmButton: false,
                                            timer: 3000,
                                            timerProgressBar: true
                                        });
                                        Toast.fire({
                                            icon: 'info',
                                            title: 'Aguardando leitura do QR code...'
                                        });
                                    }
                                }
                            } else {
                                console.error('Erro ao verificar status:', response.message);
                                if (showToast) {
                                    Swal.close();
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Erro',
                                        text: 'Erro ao verificar status da conexão.'
                                    });
                                }
                            }
                        },
                        error: function (xhr) {
                            console.error('Erro ao verificar status:', xhr);
                            if (showToast) {
                                Swal.close();
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Erro',
                                    text: 'Não foi possível verificar o status da conexão.'
                                });
                            }
                        }
                    });
                }
                const whatsappModal = new bootstrap.Modal(document.getElementById('whatsappModal'));

                // listener para quando o modal for fechado
                $('#whatsappModal').on('hidden.bs.modal', function () {
                    hideQrCode();
                });

                btnConnectWhatsapp.click(function () {
                    whatsappModal.show();
                    showLoading("Iniciando conexão...");

                    $.ajax({
                        url: '{{ route("businesses.parameters.whatsapp.connect", $business["id"]) }}',
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function (response) {
                            showQrCode();
                        },
                        error: function (xhr) {
                            console.error('Erro ao iniciar conexão:', xhr);
                            hideLoading();
                            whatsappModal.hide();
                            alert('Erro ao iniciar conexão com WhatsApp. Tente novamente.');
                        }
                    });
                });

                btnRefreshQrCode.click(function () {
                    qrCodeRetryCount = 0;
                    getQrCode();
                });

                function disconectWhatsapp(showToast = true) {
                    $.ajax({
                        url: '{{ route("businesses.parameters.whatsapp.disconnect", $business["id"]) }}',
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function (response) {
                            whatsappConnected.addClass('d-none');
                            whatsappDisconnected.removeClass('d-none');

                            if (showToast) {
                                const Toast = Swal.mixin({
                                    toast: true,
                                    position: 'top-end',
                                    showConfirmButton: false,
                                    timer: 3000,
                                    timerProgressBar: true
                                });
                                Toast.fire({
                                    icon: 'success',
                                    title: 'WhatsApp desconectado com sucesso!'
                                });
                            }
                        },
                        error: function (xhr) {
                            console.error('Erro ao desconectar:', xhr.responseJSON);
                            let messageError = 'Não foi possível desconectar o WhatsApp. Tente novamente.'
                            if(xhr.responseJSON && xhr.responseJSON.error) {
                                messageError = xhr.responseJSON.error;
                            }
                            if (showToast) {
                                Swal.close();
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Erro',
                                    text: messageError
                                });
                            }
                        }
                    });
                }

                // Adiciona o evento para o novo botão de verificação de status
                const btnCheckStatus = $('#btnCheckStatus');
                btnCheckStatus.click(function() {
                    checkConnectionStatus(true);
                });

                btnCancelConnection.click(function () {
                    disconectWhatsapp(false);
                    hideQrCode();
                });

                btnDisconnectWhatsapp.click(function () {
                    Swal.fire({
                        title: 'Confirmação',
                        text: 'Tem certeza que deseja desconectar o WhatsApp?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Sim, desconectar',
                        cancelButtonText: 'Cancelar'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            Swal.fire({
                                title: 'Desconectando...',
                                didOpen: () => {
                                    Swal.showLoading();
                                }
                            });
                            disconectWhatsapp();
                        }
                    });
                });

            });
        </script>
    @endpush
@endif
