@php use App\Helpers\ApiUser;

    $user = ApiUser::get();
    $business = $user['business_selected'];
    $parameters = $business['parameters'] ?? [];
    $plan = $business['plan'];
@endphp
<div class="row justify-content-center mb-4">
    <div class="col-lg-12 col-md-12 col-sm-12">
        <div class="card shadow">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-info-circle text-primary me-2 mr-2"></i>Informações Gerais</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            @if($plan['authorizedFunctions']['email_sending'])
                            <!-- Envios por Email -->
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-envelope text-primary fa-2x me-2 mr-2"></i>
                                                    @if($plan['authorizedFunctions']['email_sends_type'] === 'monthly')
                                                        <h6 class="text-muted mb-0">Envios por Email do mês atual:</h6>
                                                    @else
                                                        @php
                                                            $messageEmailDays = '';
                                                            if ($plan['authorizedFunctions']['email_sends_days'] > 1) {
                                                                $messageEmailDays = 'Envios por Email dos últimos ' . $plan['authorizedFunctions']['email_sends_days'] . ' dias:';
                                                            } else {
                                                                $messageEmailDays = 'Envios por Email do último dia:';
                                                            }
                                                        @endphp
                                                        <h6 class="text-muted mb-0">{{ $messageEmailDays }}</h6>
                                                    @endif
                                                </div>
                                                <h2 class="mb-0 counter-value" id="totalEmailEnviosValue" data-bs-toggle="tooltip" data-bs-placement="top">0</h2>
                                            </div>
                                        </div>
                                        <div id="emailShipmentsInfo" class="mt-3" style="display: none;">
                                            <div class="progress mb-2" style="height: 10px;">
                                                <div id="emailShipmentsProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted">
                                                <span id="emailShipmentsDetails"></span>
                                            </small>
                                        </div>
                                        <div id="emailShipmentsLoader" class="text-center">
                                            <div class="spinner-border text-primary" role="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            @if($plan['authorizedFunctions']['whatsapp_sending'])
                            <!-- Envios por WhatsApp -->
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fab fa-whatsapp text-success fa-2x me-2 mr-2"></i>
                                                    @if($plan['authorizedFunctions']['whatsapp_sends_type'] === 'monthly')
                                                        <h6 class="text-muted mb-0">Envios por WhatsApp do mês atual:</h6>
                                                    @else
                                                        @php
                                                            $messageWhatsappDays = '';
                                                            if ($plan['authorizedFunctions']['whatsapp_sends_days'] > 1) {
                                                                $messageWhatsappDays = 'Envios por WhatsApp dos últimos ' . $plan['authorizedFunctions']['whatsapp_sends_days'] . ' dias:';
                                                            } else {
                                                                $messageWhatsappDays = 'Envios por WhatsApp do último dia:';
                                                            }
                                                        @endphp
                                                        <h6 class="text-muted mb-0">{{ $messageWhatsappDays }}</h6>
                                                    @endif
                                                </div>
                                                <h2 class="mb-0 counter-value" id="totalWhatsappEnviosValue" data-bs-toggle="tooltip" data-bs-placement="top">0</h2>
                                            </div>
                                        </div>
                                        <div id="whatsappShipmentsInfo" class="mt-3" style="display: none;">
                                            <div class="progress mb-2" style="height: 10px;">
                                                <div id="whatsappShipmentsProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted">
                                                <span id="whatsappShipmentsDetails"></span>
                                            </small>
                                        </div>
                                        <div id="whatsappShipmentsLoader" class="text-center">
                                            <div class="spinner-border text-success" role="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- Total de Funcionários -->
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-users text-info fa-2x me-2 mr-2"></i>
                                                    <h6 class="text-muted mb-0">Total de funcionários cadastrados:</h6>
                                                </div>
                                                <h2 id="totalFuncionarios" class="mb-0 counter-value" data-bs-toggle="tooltip" data-bs-placement="top">0</h2>
                                            </div>
                                        </div>
                                        <div id="employeesInfo" class="mt-3" style="display: none;">
                                            <div class="progress mb-2" style="height: 10px;">
                                                <div id="employeesProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted">
                                                <span id="employeesDetails"></span>
                                            </small>
                                        </div>
                                        <div id="employeesLoader" class="text-center">
                                            <div class="spinner-border text-info" role="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Total de Clientes -->
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-user-circle text-success fa-2x me-2 mr-2"></i>
                                                    <h6 class="text-muted mb-0">Total de clientes cadastrados:</h6>
                                                </div>
                                                <h2 id="totalClientes" class="mb-0 counter-value" data-bs-toggle="tooltip" data-bs-placement="top">0</h2>
                                            </div>
                                        </div>
                                        <div id="clientsInfo" class="mt-3" style="display: none;">
                                            <small class="text-muted">
                                                <span id="clientsDetails"></span>
                                            </small>
                                        </div>
                                        <div id="clientsLoader" class="text-center">
                                            <div class="spinner-border text-success" role="status"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js')
<script>
    async function checkClients() {
        try {
            $('#clientsInfo').hide();
            $('#clientsLoader').show();
            
            const response = await fetch("{{ env('API_URL') }}/api/user/validate-clients", {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Erro na requisição');
            }

            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.msg || 'Erro ao verificar clientes');
            }
            
            $('#clientsLoader').hide();

            const totalClients = data.data.total_clients;
            window.animateValue('totalClientes', totalClients);
            
            let details = `<div class="d-flex justify-content-between align-items-center w-100">
                <div><i class="fas fa-check-circle me-1"></i> Limite de clientes para seu plano: <strong>Ilimitado</strong></div>
                </div>`;
            
            $('#clientsDetails').html(details);
            $('#clientsInfo').fadeIn(500);
        } catch (error) {
            console.error('Erro:', error);
            $('#clientsLoader').hide();
            $('#clientsInfo').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Erro ao carregar dados. Tente novamente.</div>').show();
        }
    }

    const EMAIL_SENDING_ENABLED = {{ $plan['authorizedFunctions']['email_sending'] ? 'true' : 'false' }};
    const WHATSAPP_SENDING_ENABLED = {{ $plan['authorizedFunctions']['whatsapp_sending'] ? 'true' : 'false' }};

    $(document).ready(() => {
        // Inicializa tooltips do Bootstrap
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Carrega os dados
        checkShipments();
        checkEmployees();
        checkClients();
        
        // Adiciona animações aos contadores
        function animateCounter(elementId, finalValue) {
            const duration = 1000; // duração da animação em ms
            const frameDuration = 1000/60; // ~60fps
            const totalFrames = Math.round(duration / frameDuration);
            let frame = 0;
            const element = document.getElementById(elementId);
            const initialValue = 0;
            const increment = Math.max(1, Math.floor(finalValue / totalFrames));
            
            const counter = setInterval(() => {
                frame++;
                const value = Math.min(initialValue + (increment * frame), finalValue);
                element.textContent = value;
                
                if (frame === totalFrames) {
                    clearInterval(counter);
                    element.textContent = finalValue;
                }
            }, frameDuration);
        }
        
        // Override para expor as funções animateCounter para uso global
        window.animateValue = animateCounter;
    });

    async function checkShipments() {
        try {
            if (EMAIL_SENDING_ENABLED) {
                $('#emailShipmentsInfo').hide();
                $('#emailShipmentsLoader').show();
            }
            if (WHATSAPP_SENDING_ENABLED) {
                $('#whatsappShipmentsInfo').hide();
                $('#whatsappShipmentsLoader').show();
            }
            
            const response = await fetch("{{ env('API_URL') }}/api/evaluations/business/validate-shipments", {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Erro na requisição');
            }

            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.msg || 'Erro ao verificar envios');
            }

            if (EMAIL_SENDING_ENABLED) {
                $('#emailShipmentsLoader').hide();
                // Email shipments
                const totalEmailShipments = data.data.total_email_shipments;
                const totalEmailShipmentsPlan = data.data.total_email_sends_plan;
                const remainingEmailShipments = data.data.remaining_email_shipments;
                const emailPercentage = (totalEmailShipments / totalEmailShipmentsPlan) * 100;
                
                window.animateValue('totalEmailEnviosValue', totalEmailShipments);
                
                $('#emailShipmentsProgress').css('width', `${Math.min(emailPercentage, 100)}%`);
                
                if (emailPercentage >= 90) {
                    $('#emailShipmentsProgress').removeClass('bg-success bg-warning').addClass('bg-danger');
                } else if (emailPercentage >= 70) {
                    $('#emailShipmentsProgress').removeClass('bg-success bg-danger').addClass('bg-warning');
                } else {
                    $('#emailShipmentsProgress').removeClass('bg-warning bg-danger').addClass('bg-success');
                }

                let emailDetails = `<div class="d-flex justify-content-between align-items-center w-100">
                    <div><i class="fas fa-check-circle me-1"></i> Limite de envios para seu plano: <strong>${totalEmailShipmentsPlan}</strong> | Restantes: <strong>${remainingEmailShipments}</strong></div>`;
                
                if (data.data.email_sends_type === 'monthly') {
                    const nextMonth = new Date();
                    nextMonth.setMonth(nextMonth.getMonth() + 1);
                    nextMonth.setDate(1);
                    const formattedDate = nextMonth.toLocaleDateString('pt-BR');
                    emailDetails += `<div><i class="fas fa-calendar-alt me-1"></i> Reinicia em <strong>${formattedDate}</strong></div>`;
                }
                
                emailDetails += '</div>';
                $('#emailShipmentsDetails').html(emailDetails);
                $('#emailShipmentsInfo').fadeIn(500);
            }

            if (WHATSAPP_SENDING_ENABLED) {
                $('#whatsappShipmentsLoader').hide();
                // WhatsApp shipments
                const totalWhatsappShipments = data.data.total_whatsapp_shipments;
                const totalWhatsappShipmentsPlan = data.data.total_whatsapp_sends_plan;
                const remainingWhatsappShipments = data.data.remaining_whatsapp_shipments;
                const whatsappPercentage = (totalWhatsappShipments / totalWhatsappShipmentsPlan) * 100;
                
                window.animateValue('totalWhatsappEnviosValue', totalWhatsappShipments);
                
                $('#whatsappShipmentsProgress').css('width', `${Math.min(whatsappPercentage, 100)}%`);
                
                if (whatsappPercentage >= 90) {
                    $('#whatsappShipmentsProgress').removeClass('bg-success bg-warning').addClass('bg-danger');
                } else if (whatsappPercentage >= 70) {
                    $('#whatsappShipmentsProgress').removeClass('bg-success bg-danger').addClass('bg-warning');
                } else {
                    $('#whatsappShipmentsProgress').removeClass('bg-warning bg-danger').addClass('bg-success');
                }

                let whatsappDetails = `<div class="d-flex justify-content-between align-items-center w-100">
                    <div><i class="fas fa-check-circle me-1"></i> Limite de envios para seu plano: <strong>${totalWhatsappShipmentsPlan}</strong> | Restantes: <strong>${remainingWhatsappShipments}</strong></div>`;
                
                if (data.data.whatsapp_sends_type === 'monthly') {
                    const nextMonth = new Date();
                    nextMonth.setMonth(nextMonth.getMonth() + 1);
                    nextMonth.setDate(1);
                    const formattedDate = nextMonth.toLocaleDateString('pt-BR');
                    whatsappDetails += `<div><i class="fas fa-calendar-alt me-1"></i> Reinicia em <strong>${formattedDate}</strong></div>`;
                }
                
                whatsappDetails += '</div>';
                $('#whatsappShipmentsDetails').html(whatsappDetails);
                $('#whatsappShipmentsInfo').fadeIn(500);
            }

        } catch (error) {
            console.error('Erro:', error);
            if (EMAIL_SENDING_ENABLED) {
                $('#emailShipmentsLoader').hide();
                $('#emailShipmentsInfo').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Erro ao carregar dados. Tente novamente.</div>').show();
            }
            if (WHATSAPP_SENDING_ENABLED) {
                $('#whatsappShipmentsLoader').hide();
                $('#whatsappShipmentsInfo').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Erro ao carregar dados. Tente novamente.</div>').show();
            }
        }
    }

    async function checkEmployees() {
        try {
            $('#employeesInfo').hide();
            $('#employeesLoader').show();
            
            const response = await fetch("{{ env('API_URL') }}/api/user/validate-employees", {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Erro na requisição');
            }

            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.msg || 'Erro ao verificar funcionários');
            }
            
            $('#employeesLoader').hide();

            const totalEmployees = data.data.total_employees;
            const totalEmployeesPlan = data.data.total_employees_plan;
            window.animateValue('totalFuncionarios', totalEmployees);
            
            const remainingEmployees = data.data.remaining_employees;
            const percentage = (totalEmployees / totalEmployeesPlan) * 100;

            // Atualiza a barra de progresso
            $('#employeesProgress').css('width', `${Math.min(percentage, 100)}%`);
            
            // Define a cor da barra baseado na porcentagem
            if (percentage >= 90) {
                $('#employeesProgress').removeClass('bg-success bg-warning').addClass('bg-danger');
            } else if (percentage >= 70) {
                $('#employeesProgress').removeClass('bg-success bg-danger').addClass('bg-warning');
            } else {
                $('#employeesProgress').removeClass('bg-warning bg-danger').addClass('bg-success');
            }

            // Atualiza o texto de detalhes
            let details = `<div class="d-flex justify-content-between align-items-center w-100">
                <div><i class="fas fa-check-circle me-1"></i> Limite de funcionários para seu plano: <strong>${totalEmployeesPlan}</strong> | Restantes: <strong>${remainingEmployees}</strong></div>
                </div>`;
            
            $('#employeesDetails').html(details);
            $('#employeesInfo').fadeIn(500);
        } catch (error) {
            console.error('Erro:', error);
            $('#employeesLoader').hide();
            $('#employeesInfo').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Erro ao carregar dados. Tente novamente.</div>').show();
        }
    }
</script>
@endpush
