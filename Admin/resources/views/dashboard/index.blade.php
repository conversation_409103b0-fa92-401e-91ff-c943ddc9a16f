@php
    use App\Helpers\ApiUser;
    use App\Helpers\StringMask;
    use Carbon\Carbon;
@endphp
@extends('layouts.app', ['activePage' => 'dashboard'])

@section('content')
    <div class="container">
        @include('dashboard.general_information')

        <!-- Container de Gráficos -->
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-12 mb-2 row align-items-center flex-nowrap">
                                @if(isset($isSellerDashboard) && $isSellerDashboard)
                                  <a href="{{route('businesses.seller.index')}}" class="mx-2" style="cursor: pointer">
                                      <i class="fas fa-chevron-left fa-fw "></i>
                                  </a>
                                @endif
                                <h5 class="card-title mb-0 mr-2 overflow-hidden flex-grow-1"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                    @if(isset($isSellerDashboard) && $isSellerDashboard && $business)
                                        {{$business['name']}} - Gráficos
                                    @else
                                        Gráficos
                                    @endif
                                    </h5>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12" id="searchForm">
                                <div class="form-row">
                                    <div class="col-md-6 mb-2">
                                        <select class="form-control" name="period" aria-describedby="period" id="period_desktop">
                                            <option value="TODAY" @if(isset($period) && $period == 'TODAY' || !isset($period)) selected @endif>Hoje</option>
                                            <option value="LAST_30DAYS" @if(isset($period) && $period == 'LAST_30DAYS') selected @endif>Últimos 30 dias</option>
                                            <option value="ESPECIFIC" @if(isset($period) && $period == 'ESPECIFIC') selected @endif>Escolher um período</option>
                                            <option value="ALL" @if((isset($period) && $period == 'ALL')) selected @endif>Todo período</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <select class="form-control" name="form_id" aria-describedby="form_id" id="forms_desktop" disabled>
                                            <option value="" @if(!isset($form)) selected @endif>Carregando</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="" onsubmit="handleFilterDashboard(event)" method="get" id="form_filter_dashboard">
                                    @csrf
                                    <input type="hidden" name="generated_at" id="generated_at" value="{{$generated_at ?? Carbon::now()}}">
                                    <input type="hidden" name="period" id="periodInput" @if(isset($period)) value="{{$period}}" @endif>
                                    <input type="hidden" name="form_id" id="formIdInput" @if(isset($form)) value="{{$form}}" @endif>
                                    <div class="form-row dateInputFilterWrapper" style="display: {{ !isset($startDate)  ? 'none !important' : 'flex !important' }}">
                                        <div class="col-md-5 mb-2 d-flex align-items-center ">
                                            <input
                                                id="startDateInput"
                                                name="startDate"
                                                type="date"
                                                class="form-control"
                                                value="{{ isset($startDate) ? Carbon::parse($startDate)->format('Y-m-d') : Carbon::now()->sub('1 day')->format('Y-m-d') }}"
                                                @if (!isset($startDate)) disabled @endif
                                            />
                                        </div>
                                        <div class="col-md-5 mb-2 d-flex align-items-center">
                                            <input
                                                id="endDateInput"
                                                name="endDate"
                                                type="date"
                                                class="form-control"
                                                value="{{ isset($endDate) ? Carbon::parse($endDate)->format('Y-m-d') : Carbon::now()->format('Y-m-d') }}"
                                                @if (!isset($endDate)) disabled @endif
                                            />
                                        </div>
                                        <div class="col-md-2 mb-2">
                                            <button class="btn btn-light w-100" type="submit" id="searchFilterButton">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @php
                            $hasTopicDefault = !empty($topicsDefault['labels']);
                            $hasTopicNonDefault = !empty($topicsNonDefault['labels']);
                        @endphp
                        <div class="form-row">
                            <div class="col-12 d-flex justify-content-end mb-3">
                                <a href="{{ isset($isSellerDashboard) && $isSellerDashboard ? route('businesses.seller.dashboard.details', ['id' => $business['id'], 'period' => $period, 'form_id' => $form]) : route('dashboard.details', ['period' => $period, 'form_id' => $form]) }}" 
                                    class="btn btn-sm btn-outline-primary">
                                    <i class="fa fa-star fa-fw"></i>
                                    Detalhes
                                </a>
                            </div>
                        </div>
                        <div class="form-row">
                            @if (!$hasTopicDefault && !$hasTopicNonDefault)
                                <div class="d-flex align-items-center justify-center justify-content-center flex-column w-100 h-100 mt-5">
                                    <img src="/icon/chart_not_found.svg" width="350" height="350" style="opacity: 0.2" />
                                    <p class="text-center text-muted font-weight-bold" style="width: 100%; max-width: 15rem">Não há dados suficientes para gerar os gráficos!</p>
                                </div>
                            @endif
                            @if ($hasTopicDefault)
                                <div class="{{$hasTopicNonDefault ? 'col-md-6' : 'col-md-12'}} mt-4">
                                        @include('components.dashboard.pieChart', [
                                            'chartId' => 'defaultPie',
                                            'baseDetailUrl' => '/topics',
                                            'ids' => $topicsDefault['ids'],
                                            'labels' => $topicsDefault['labels'],
                                            'data' => $topicsDefault['data'],
                                            'datasetLabel' => "Média",
                                        ])
                                </div>
                            @endif
                            @if ($hasTopicNonDefault)
                                <div class="{{$hasTopicDefault ? 'col-md-6' : 'col-md-12'}}">
                                    <h6 class="style_campo_titulo text-center">Questões customizadas</h6>
                                        @include('components.dashboard.pieChart', [
                                            'chartId' => 'nonDefaultPie',
                                            'baseDetailUrl' => '/topics',
                                            'ids' => $topicsNonDefault['ids'],
                                            'labels' => $topicsNonDefault['labels'],
                                            'data' => $topicsNonDefault['data'],
                                            'datasetLabel' => "Média",
                                        ])
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff;
                                border-bottom-left-radius: 12px;
                                border-bottom-right-radius: 12px;">
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...', 'id' => 'loadingDashboard'])
    @include('dashboard.modal_show', ['modalId' => "modal-details-topic"])
@endsection

@push('js')
<script>
    $(document).ready(() => {
        loadForms();
    })

    function handleFilterDashboard(event) {
        event.preventDefault();
        const inputValue = $('#period_desktop').val();
        if (inputValue !== 'ESPECIFIC') {
            $('#loadingDashboard').modal('show');
            event.target.submit();
        }
        const startDateInput = document.getElementById('startDateInput');
        const endDateInput = document.getElementById('endDateInput');

        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        if (startDate > endDate) return alert('A data de início não pode ser após a data de fim!');
        $('#loadingDashboard').modal('show');
        event.target.submit();
    }

    function resetSelect(element, defaultValue) {
        const defaultOption = document.createElement('option');
        element.innerHTML = "";
        defaultOption.value = "";
        defaultOption.textContent = defaultValue;
        defaultOption.selected = true;
        element.appendChild(defaultOption);
    }

    function loadForms() {
        const formsSelect = document.getElementById('forms_desktop');
        const formSelected = "{{$form}}";
        $.ajax({
            url: "<?= env('API_URL') ?>/api/dashboard/forms",
            type: 'get',
            data: {
                is_paginate: false
            },
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#loadingDashboard').modal('hide');
            },
            success: function (response) {
                $('#loadingDashboard').modal('hide');
                formsSelect.innerHTML = "";
                const forms = response.forms ?? [];
                resetSelect(formsSelect, 'Todos');
                forms.forEach(form => {
                    const option = document.createElement('option');
                    option.value = form.id;
                    option.textContent = form.name;
                    if (parseInt(form.id) === parseInt(formSelected)) {
                        option.selected = true;
                    }
                    formsSelect.appendChild(option);
                });
                formsSelect.disabled = false;
            },
            error: function (response, textStatus, msg) {
                $('#loadingDashboard').modal('hide');
            }
        });
    }
    $('#forms_desktop').on('change', function() {
        const inputValue = $('#forms_desktop').val();
        const form = $('#form_filter_dashboard')
        const hiddenInput = document.getElementById('formIdInput');
        hiddenInput.value = inputValue;
        form.submit();
    })

    $('#period_desktop').on('change', function() {
        const inputValue = $('#period_desktop').val();
        const form = $('#form_filter_dashboard')
        const hiddenInput = document.getElementById('periodInput');
        const startDateInput = document.getElementById('startDateInput');
        const endDateInput = document.getElementById('endDateInput');
        hiddenInput.value = inputValue;

        if (inputValue !== 'ESPECIFIC') {
            $('#loadingDashboard').modal('show');
            startDateInput.value = null;
            endDateInput.value = null;
            form.submit();
            return;
        }

        startDateInput.disabled = false;
        endDateInput.disabled = false;
        $('.dateInputFilterWrapper').each(function () {
            $(this).show();
        })
        $('#searchFilterButton').show();
    })

    function addLoading(containerId) {
        $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
    }

    function openTopicDetails(modalId, topicId, topicName) {
        const period = "{{$period}}";
        const startDate = "{{$startDate}}";
        const endDate = "{{$endDate}}";
        const generated_at = "{{$generated_at}}";
        addLoading('topic_detail_modal_body')
        $(`#${modalId}`).modal('show');
        $.ajax({
            url: `/dashboard/${topicId}/show`,
            type: 'get',
            data: {period, startDate, endDate, generated_at},
            success: function(response) {
                $('#topic_detail_modal_body').html(response);
                const title = document.getElementById('exampleModalLongTitle');
                const event = new Event('openDetailTopic');
                title.textContent = `Detalhes da categoria ${topicName}`;
                document.dispatchEvent(event);
            },
            error: function(response, textStatus, msg) {
                $('#topic_detail_modal_body').html(msg);
            }
        });
    }
</script>
@endpush
