<?php
    use App\Helpers\StringMask;
    use Carbon\Carbon;
?>

<div class="form-row">
    <div class="col-md-12 mb-4">
        <h6 class="style_campo_titulo">Questões</h6>
    </div>
    <div class="col-md-12 mb-2">
        <h6 class="style_campo_titulo">5 Estrelas</h6>
    </div>
    @if (empty($fiveStarQuestions))
        <div class="col-md-12 mb-2">
            <p class="text-muted text-center" >Essa categoria não possui questões desse tipo.</p>
        </div>
    @else
        @foreach ($fiveStarQuestions as $fiveStarQuestion)
            <div class="form-group col-md-12 mb-2 border-bottom">
                <div class="d-flex w-100 justify-content-between align-items-center">
                    <div>
                        <h6>Descrição</h6>
                        <p style="flex: 1">{{ $fiveStarQuestion['description'] }}</p>
                    </div>
                    <div class="d-flex align-items-center h-100">                    
                        <p style="font-size: 18px">{{ number_format($fiveStarQuestion['average_response'], 2, '.', '') }} </p>
                        <p class="text-warning ml-2" style="font-size: 18px">★</p>
                    </div>
                </div>
            </div>
        @endforeach
    @endif
    <div class="col-md-12 mt-4">
        <h6 class="style_campo_titulo">Sim ou não</h6>
    </div>
    <div class="form-group my-2 col-md-12">
        @if (empty($yesNoQuestions))
            <p class="text-muted text-center" >Essa categoria não possui questões desse tipo.</p>
        @else
        <div class="table-responsive-sm">
            <table class="table" style="white-space: nowrap;" id="clientsTable">
                <thead>
                    <tr>
                        <th style="width: 60%">Descrição</th>
                        <th style="width: 20%">Sim</th>
                        <th style="width: 20%">Não</th>
                    </tr>
                </thead>
                <tbody id="clientsTableBody">
                    @foreach ($yesNoQuestions as $yesNoQuestion)
                        <tr>
                            <td>{{ $yesNoQuestion['description'] }}</td>
                            <td>{{ $yesNoQuestion['yes_count'] }}</td>
                            <td>{{ $yesNoQuestion['no_count'] }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <small id="error-clients_id" class="p-1 text-danger"></small>
        </div>
            <nav>
                <ul class="pagination" id="clientPagination">
                    <!-- Paginação será carregada aqui -->
                </ul>
            </nav>
        @endif
    </div>
</div>
<script text="text/javascript">
</script>