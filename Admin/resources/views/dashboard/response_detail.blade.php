<div class="modal fade" id="responsesModal-{{ $response['id'] }}" tabindex="-1" role="dialog"
     aria-labelledby="responsesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title mr-1" id="exampleModalLongTitle" style="max-width: 35rem; text-overflow: ellipsis; overflow: hidden;">Detalhes da categoria</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body pt-0">
                <div class="form-row">
                    <div class="col-md-12 mt-2">
                        <label for="">Data de Reposta</label>
                        <input type="text" class="form-control style_campo_estatico"
                                value="{{ date('d/m/Y H:i', strtotime($response['created_at'])) }}" disabled>
                    </div>
                    <div class="col-md-12 mt-2">
                        <label for="">Formulário</label>
                        <input type="text" class="form-control style_campo_estatico"
                                value="{{$response['form_name'] ?? 'Não informado'}}" disabled>
                    </div>
                    <div class="col-md-12 mt-2">
                        <label for="">Categoria</label>
                        <input type="text" class="form-control style_campo_estatico"
                                value="{{$response['question']['topic']['name'] ?? 'Não informado'}}" disabled>
                    </div>
                    <div class="col-md-12 mt-2">
                        <label for="">Pergunta</label>
                        <input type="text" class="form-control style_campo_estatico"
                                value="{{$response['question']['description'] ?? 'Não informado'}}" disabled>
                    </div>
                    <div class="col-md-6 mt-2">
                        <label for="">Tipo Resposta</label>
                        <input type="text" class="form-control style_campo_estatico"
                                value="{{$response['question']['evaluation_type'] === 'five_star' ? '5 Estrelas' : 'Sim ou não' }}" disabled>
                    </div>
                    @if ($response['question']['evaluation_type'] === 'five_star')
                        <div class="col-md-6 mt-2">
                            <label for="">Resposta</label>
                            <input type="text" class="form-control style_campo_estatico"
                                    value="{{ $response['response'] }} ⭐" disabled>
                        </div>
                    @else
                        <div class="col-md-6 mt-2">
                            <label for="">Resposta</label>
                            <input type="text" class="form-control style_campo_estatico"
                                    value="{{ strtolower($response['response']) === 'yes' ? 'Sim' : 'Não' }}" disabled>
                        </div>
                    @endif
                    <div class="col-md-12 mt-2">
                        <label for="">Justificativa</label>
                        <textarea class="form-control style_campo_estatico" rows="7" name="justification" maxlength="1000"
                                              disabled>{{ $response['justification'] ?? 'Não informado'}}</textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
