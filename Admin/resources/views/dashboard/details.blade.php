@php
    use App\Helpers\ApiUser;
    use App\Helpers\StringMask;
    use Carbon\Carbon;
@endphp
@extends('layouts.app', ['activePage' => 'dashboard'])


@section('content')
<style>
    .filtersCard:not(:disabled):hover span {
        color: #fff !important;
    }
    .filters-bar {
        visibility: hidden;
        border: none;
        transform:  translateX(100%);
        transition: transform 0.25s;
    }

    .filters-bar.show {
        display: block !important;
        visibility: visible;
        transform:  translateX(0);
    }

    .skeleton-card {
        width: 120px;
        height: 30px;
        border-radius: 5px;
        background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
    }

    /* Skeleton animation */
    @keyframes skeleton-loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    @media(max-width: 900px) {
        .filters-bar {
            width: 100vw !important;
            max-width: 100vw !important;
            z-index: 999;
        }
    }
</style>
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1 align-items-center">
                            <div class="d-flex align-items-center col-lg-10 col-md-10 col-sm-10 col-10 mb-2">
                                <a href="{{ isset($isSellerDashboard) && $isSellerDashboard ? route('businesses.seller.dashboard', ['id' => $business['id']]) : route('dashboard.index') }}" class="mx-2" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw "></i>
                                 </a>
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    @if(isset($business) && !empty($business))
                                        {{ $business['name'] }} - Respostas
                                    @else
                                        Respostas
                                    @endif
                                    </h5>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-2 col-2 mb-2 d-flex justify-content-end">
                                <button id="open-filters-bar" class="btn btn-secondary d-flex align-items-center justify-content-center">
                                    <i class="fas fa-filter fa-fw" ></i>
                                    <span class="d-none d-md-inline ml-2">Filtrar</span>
                                </button>
                            </div>
                        </div>
                        <div class="row col-md-12">
                            @if(isset($filters))
                                <div class="d-flex align-items-center flex-wrap" style="gap: 8px">
                                    <b class="text-muted">Filtrando por: </b>
                                    <div id="filtersSkeleton" class="align-items-center flex-wrap" style="display: flex; gap: 8px">
                                        @for ($i = 0; $i < 3; $i++)
                                            <div class="skeleton-card"></div>
                                        @endfor
                                    </div>
                                    @php
                                        $filtersNameCast = [
                                            "period" => "Período",
                                            "topic_id" => "Categoria",
                                            "question_id" => "Questão",
                                            "form_id" => "Formulário",
                                            "evaluation_type" => "Tipo de Avaliação",
                                            "answer_type" => "Tipo de Resposta"
                                        ];

                                        $filtersConstantValuesCast = [
                                            "ALL" => [
                                                "name" => "Todo período",
                                                "default" => false
                                            ],
                                            "TODAY" => [
                                                "name" => "Hoje",
                                                "default" => true
                                            ],
                                            "ESPECIFIC" => [
                                                "name" => "Específico",
                                                "default" => false
                                            ],
                                            "LAST_30DAYS" => [
                                                "name" => "Últimos 30 dias",
                                                "default" => false
                                            ],
                                            "yes_no" => [
                                                "name" => "Sim/Não",
                                                "default" => false
                                            ],
                                            "five_star" => [
                                                "name" => "5 Estrelas",
                                                "default" => true
                                            ]
                                        ];

                                        $filterYesNo = ["yes" => "Sim", "no" => "Não"]

                                    @endphp
                                    <div class="d-none align-items-center flex-wrap" id="showFiltersContainer" style="display: flex;  gap: 8px">
                                        @foreach ($filters as $key => $value)
                                            @if(isset($filtersNameCast[$key]) && !empty($value))
                                                @php
                                                    $isDefaultSelected = isset($filtersConstantValuesCast[$value]['name']) && $filtersConstantValuesCast[$value]['default'];
                                                @endphp
                                                <button 
                                                    class="btn btn-outline-dark px-2 py-1 rounded-sm d-flex align-items-center flex-wrap filtersCard" 
                                                    style="font-size: 0.8rem" 
                                                    id="{{$key}}_card"
                                                    onclick="removeFilter('{{$key}}')"
                                                    @if ($isDefaultSelected) disabled  @endif
                                                >
                                                    <span>
                                                        {{ $filtersNameCast[$key] }}:
                                                        <span class="text-muted font-weight-bold" id="{{$key}}_description">
                                                            {{ $filtersConstantValuesCast[$value]['name'] ?? $filterYesNo[$value] ?? Str::limit($value, 11, '...') }}
                                                        </span>
                                                        @if ($key == 'answer_type' && $filters['evaluation_type'] === 'five_star') ⭐ @endif
                                                    </span>
                                                    @if(!$isDefaultSelected)
                                                        <i class="fas fa-times ml-2"></i>
                                                    @endif
                                                </button>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="cashbacks-table">
                                <thead>
                                    <tr>
                                        <th scope="col" style="text-align: center">Ações</th>
                                        <th scope="col">Resposta</th>
                                        <th scope="col">Pergunta</th>
                                        <th scope="col">Categoria</th>
                                        <th scope="col">Formulário</th>
                                        <th scope="col">Justificativa</th>
                                        <th scope="col">Data de Resposta</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($responses as $response)
                                    <tr>
                                        <td style="text-align: center">
                                            <button type="button" class="btn btn-secondary" data-toggle="modal"
                                            data-target="#responsesModal-{{ $response['id'] }}"
                                            style="margin-right: 10px">
                                                <img class="card-img-left example-card-img-responsive" src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                            </button>
                                        </td>
                                        <td>
                                            @if ($response['question']['evaluation_type'] == 'five_star')
                                            {{ $response['response'] }} ⭐
                                            @else
                                            {{ strtolower($response['response']) === "yes" ? 'Sim' : 'Não'}}
                                            @endif
                                        </td>
                                        <td title="{{$response['question']['description']}}">{{ Str::limit($response['question']['description'], 25, '...') ?? "Não informado" }}</td>
                                        <td title="{{$response['question']['topic']['name']}}">{{ Str::limit($response['question']['topic']['name'], 16, '...') ?? "Não informado" }}</td>
                                        <td title="{{$response['form_name']}}">{{ Str::limit($response['form_name'], 16, '...') ?? "Não informado" }}</td>
                                        <td title="{{$response['justification']}}">{{ Str::limit($response['justification'], 16, '...') ?? "Não informado" }}</td>
                                        <td class="text-truncate" style="max-width: 1rem;">
                                            {{ isset($response['created_at']) ? date('d/m/Y H:i', strtotime($response['created_at'])) : 'Não informado' }}
                                        </td>
                                    </tr>

                                        @include('dashboard.response_detail', ['response' => $response])
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        {{ $responses->onEachSide(0)->appends($queryParams)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff;
                                border-bottom-left-radius: 12px;
                                border-bottom-right-radius: 12px;">
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...', 'id' => 'loadingDashboard'])
    @include('dashboard.modal_show', ['modalId' => "modal-details-topic"])

    <div class="filters-bar bg-white border" style="position:fixed; top: 0; right: 0; width: 25vw; height: 100dvh" id="filters-bar">
        <div class="filters-container h-100 w-100" style="overflow-x: hidden;overflow-y: auto; padding-bottom: 48px">
            <div class="filters-bar-header px-4 pt-4 pb-2 mb-2 border-bottom">
                <div class="d-flex align-items-center justify-content-between mb-2">
                    <h5 class="card-title mb-0 mr-2 text-dark"
                        style="font-size: 1.5rem; font-family:Arial, Helvetica, sans-serif;">
                        <i class="fas fa-filter fa-fw" style="font-size: 1.25rem"></i>
                        Filtrar
                    </h5>
                    <button class="btn btn-outline-dark px-2" style="cursor: pointer" id="close-filters-bar">
                        <i class="fas fa-times fa-fw"></i>
                    </button>
                </div>
                <p class="text-muted mt-1">Utilize os filtros para ajustar os resultados de acordo com suas preferências.</p>
            </div>
            <div class="filters-bar-body px-4 pt-2 pb-4">
                <form action="" method="get" id="form_filter">
                    @csrf
                    <input type="hidden" name="filters" id="filters_input" value="{{ !isset($filters) ? $filters : null }}">
                    <div class="form-group">
                        <label for="period" class="text-dark">Selecione o período</label>
                        <select class="form-control" name="period" aria-describedby="period" id="period_desktop">
                            <option value="TODAY" @if(isset($filters['period']) && $filters['period'] == 'TODAY' || !isset($filters['period'])) selected @endif>Hoje</option>
                            <option value="LAST_30DAYS" @if(isset($filters['period']) && $filters['period'] == 'LAST_30DAYS') selected @endif>Últimos 30 dias</option>
                            <option value="ESPECIFIC" @if(isset($filters['period']) && $filters['period'] == 'ESPECIFIC') selected @endif>Escolher um período</option>
                            <option value="ALL" @if((isset($filters['period']) && $filters['period'] == 'ALL')) selected @endif>Todo período</option>
                        </select>
                    </div>
                    <div class="form-row dateInputFilterWrapper" style="display: {{ !isset($filters['startDate'])  ? 'none !important' : 'flex !important' }}">
                        <div class="col-md-6 form-group">
                            <label for="startDate" class="text-dark">Data início</label>
                            <input
                                id="startDateInput"
                                name="startDate"
                                type="date"
                                class="form-control"
                                value="{{ isset($filters['startDate']) ? Carbon::parse($filters['startDate'])->format('Y-m-d') : Carbon::now()->sub('1 day')->format('Y-m-d') }}"
                                @if (!isset($filters['startDate'])) disabled @endif
                            />
                        </div>
                        <div class="col-md-6 form-group">
                            <label for="endDate" class="text-dark">Data fim</label>
                            <input
                                id="endDateInput"
                                name="endDate"
                                type="date"
                                class="form-control"
                                value="{{ isset($filters['endDate']) ? Carbon::parse($filters['endDate'])->format('Y-m-d') : Carbon::now()->format('Y-m-d') }}"
                                @if (!isset($filters['endDate'])) disabled @endif
                            />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="period" class="text-dark">Selecione o formulário</label>
                        <select class="form-control" name="form_id" aria-describedby="forms" id="forms_desktop" disabled>
                            <option value="ALL" @if(isset($filters['form_id']) && $filters['form_id'] == '' || !isset($filters['topics'])) selected @endif>Carregando</option>
                            {{-- Tópicos vão ser carregados aqui --}}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="period" class="text-dark">Selecione a categoria</label>
                        <select class="form-control" name="topic_id" aria-describedby="topics" id="topics_desktop" disabled>
                            <option value="ALL" @if(isset($filters['topics']) && $filters['topics'] == 'ALL' || !isset($filters['topics'])) selected @endif>Carregando</option>
                            {{-- Tópicos vão ser carregados aqui --}}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="period" class="text-dark">Selecione a questão</label>
                        <select class="form-control" name="question_id" aria-describedby="questions" id="questions_desktop">
                            <option value="" @if(isset($filters['questions']) && $filters['questions'] == '' || !isset($filters['questions'])) selected @endif>Todas</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="period" class="text-dark">Selecione o tipo de avaliação</label>
                        <select class="form-control" name="evaluation_type" aria-describedby="evaluation_type" id="evaluation_type_desktop">
                            <option value="five_star" @if(!empty($filters['evaluation_type']) && $filters['evaluation_type'] == 'five_star' || empty($filters['evaluation_type'])) selected @endif>5 estrelas</option>
                            <option value="yes_no" @if(!empty($filters['evaluation_type']) && $filters['evaluation_type'] == 'yes_no') selected @endif>Sim ou não</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="period" class="text-dark">Selecione a reposta</label>
                        <select class="form-control" name="answer_type" aria-describedby="answer_type" id="answer_type_desktop">
                            <option value="" @if(isset($filters['answer_type']) && empty($filters['answer_type']) || !isset($filters['answer_type'])) selected @endif>Todas</option>
                            @if (isset($filters['evaluation_type']) && $filters['evaluation_type'] == 'yes_no')
                                <option value="yes" @if(isset($filters['answer_type']) && strtolower($filters['answer_type']) == 'yes') selected @endif>Sim</option>
                                <option value="no" @if(isset($filters['answer_type']) && strtolower($filters['answer_type']) == 'no') selected @endif>Não</option>
                            @else
                                @for ($i = 1; $i <= 5; $i++)
                                    <option value="{{ $i }}" @if(isset($filters['answer_type']) && $filters['answer_type'] == $i) selected @endif>{{ $i }} ⭐</option>
                                @endfor
                            @endif
                        </select>
                    </div>
                    <div class="form-row mt-4" style="gap: 0.85rem">
                        <a href="{{ route('dashboard.details') }}" class="btn btn-outline-dark" style="flex: 1">
                            Limpar
                        </a>
                        <button class="btn btn-primary" style="flex: 1">
                            Filtrar
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
    function removeFilter(key) {
        const input = document.querySelector(`select[name="${key}"]`);
        if(input) {
            input.value = "";
            const event = new Event('change');
            input.dispatchEvent(event);
            $('#form_filter').submit();
        }
    }
    $('document').ready(() => {
        let business_selected_id = {{ ApiUser::get()['business_selected']['id'] ?? -1 }};
        loadForms();
        
        function showDynamicFilters() {
            const topicIdSelected = $('#topics_desktop').val();
            const questionIdSelected = $('#questions_desktop').val();
            const formIdSelected = $('#forms_desktop').val();
            const topicShowFilterText = document.getElementById('topic_id_description');
            const questionShowFilterText = document.getElementById('question_id_description');
            const formShowFilterText = document.getElementById('form_id_description');

            if (topicShowFilterText) {
                const topicSelected = allTopics.find((topic) => parseInt(topic.id) === parseInt(topicIdSelected));
                let textToDisplay = "Não encontrado";
                if (topicSelected){
                    textToDisplay = topicSelected.name.length > 13 ? topicSelected.name.slice(0,13).concat('...') : topicSelected.name;
                }
                topicShowFilterText.textContent = `${textToDisplay}`;
            }

            if (questionShowFilterText) {
                const questionSelected = allQuestions.find((question) => parseInt(question.id) === parseInt(questionIdSelected));
                let textToDisplay = "Não encontrada";
                if (questionSelected){
                    textToDisplay = questionSelected.description.length > 13 ? questionSelected.description.slice(0,13).concat('...') : questionSelected.description;
                }
                questionShowFilterText.textContent = `${textToDisplay}`;
            }

            if (formShowFilterText) {
                const formSelected = allForms.find((form) => parseInt(form.id) === parseInt(formIdSelected));
                let textToDisplay = "Não encontrado";
                if (formSelected){
                    textToDisplay = formSelected.name.length > 13 ? formSelected.name.slice(0,13).concat('...') : formSelected.name;
                }
                formShowFilterText.textContent = `${textToDisplay}`;
            }

            $('#filtersSkeleton').hide();
            $('#showFiltersContainer').removeClass('d-none');
        }

        $('#open-filters-bar').on('click', function() {
            const filtersBar = $('#filters-bar');
            const content = $('#content');
            filtersBar.toggleClass('show');
            if (window.innerWidth > 900) {
                content.toggleClass('collapsed-right');
            }
        });

        $('#close-filters-bar').on('click', function() {
            const filtersBar = $('#filters-bar');
            const content = $('#content');
            filtersBar.removeClass('show');
            if (window.innerWidth > 900) {
                content.removeClass('collapsed-right');
            }
        });

        $('#period_desktop').on('change', function() {
            const inputValue = $('#period_desktop').val();
            const startDateInput = document.getElementById('startDateInput');
            const endDateInput = document.getElementById('endDateInput');
            if (inputValue === 'ESPECIFIC') {
                startDateInput.disabled = false;
                endDateInput.disabled = false;
                $('.dateInputFilterWrapper').each(function () {
                    $(this).show();
                })
            } else {
                startDateInput.disabled = true;
                endDateInput.disabled = true;
                $('.dateInputFilterWrapper').each(function () {
                    $(this).hide();
                })
            }
        })

        $('#forms_desktop').on('change', function() {
            const formId = document.getElementById('forms_desktop').value ?? null;
            loadTopics(formId)
        });

        $('#topics_desktop').on('change', function() {
            const topicsSelect = document.getElementById('topics_desktop');
            const topicId = topicsSelect.value;
            const questionsSelect = document.getElementById('questions_desktop');
            let questions = [];

            resetSelect(questionsSelect, 'Todas');
            if (!topicId) {
                allTopics.forEach((topic) => {
                    if (topic.questions) {
                        questions = questions.concat(topic.questions);
                    }
                });
            } else {
                const topicSelected = allTopics.find((topic) => topic.id.toString() === topicId);
                questions = topicSelected?.questions ?? [];
            }

            questions = questions.filter((question) => (question.business_id === business_selected_id || question.business_id === null))
            questions.sort((a, b) => a.description.localeCompare(b.description));
            const filtersQuestionId = "{{ !empty($filters['question_id']) ? $filters['question_id'] : -1 }}";
            let isSelected = false;
            questions.forEach(question => {
                const option = document.createElement('option');
                option.value = question.id;
                option.textContent = question.description;
                if (parseInt(question.id) === parseInt(filtersQuestionId)) {
                    isSelected = true;
                    option.selected = true;
                }
                questionsSelect.appendChild(option);
            });
            allQuestions = questions;
            const event = new Event('change');
            questionsSelect.dispatchEvent(event);
        });

        $('#questions_desktop').on('change', function() {
            const questionsSelect = document.getElementById('questions_desktop');
            const questionId = questionsSelect.value;
            const evaluationSelect = document.getElementById('evaluation_type_desktop');
            const evaluationTypes = new Set([]);

            evaluationSelect.innerHTML = "";

            if (!questionId) {
                allQuestions.forEach((question) => {
                    evaluationTypes.add(question.evaluation_type);
                });
            } else {
                const questionSelected = allQuestions.find((question) => question.id.toString() === questionId);
                evaluationTypes.add(questionSelected.evaluation_type);
            }
            const filtersEvaluationType = "{{ !empty($filters['evaluation_type']) ? $filters['evaluation_type'] : -1 }}";
            evaluationTypes.forEach((evType) => {
                const option = document.createElement('option');
                option.value = evType;
                option.textContent = evType === "five_star" ? "5 estrelas" : "Sim ou não";
                option.selected = evType === filtersEvaluationType;
                evaluationSelect.appendChild(option);
            });

            const event = new Event('change');
            evaluationSelect.dispatchEvent(event);
        });

        $('#evaluation_type_desktop').on('change', function() {
            const answerTypeSelect = document.getElementById('answer_type_desktop');
            const evaluationSelect = document.getElementById('evaluation_type_desktop');
            const evaluationType = evaluationSelect.value;
            let answerTypes = [];

            resetSelect(answerTypeSelect, 'Todas');

            if (evaluationType == 'yes_no') {
                answerTypes = ['yes', 'no'];
            } else {
                answerTypes = [1, 2, 3, 4, 5];
            }
            const filtersAnswerType = "{{ !empty($filters['answer_type']) ? $filters['answer_type'] : -1 }}";
            answerTypes.forEach((answer) => {
                const option = document.createElement('option');
                option.value = answer;
                if (typeof answer === 'string') {
                    option.textContent = answer === 'yes' ? 'Sim' : 'Não';
                    option.selected = filtersAnswerType === answer;
                } else {
                    option.textContent = `${answer} ⭐`;
                    option.selected = parseInt(filtersAnswerType) === answer;
                }
                answerTypeSelect.appendChild(option);
            });
        });

        let allTopics = [];
        let allQuestions = [];
        let allForms = [];

        function resetSelect(element, defaultValue) {
            const defaultOption = document.createElement('option');
            element.innerHTML = "";
            defaultOption.value = "";
            defaultOption.textContent = defaultValue;
            defaultOption.selected = true;
            element.appendChild(defaultOption);
        }

        function loadForms() {
            const formsSelect = document.getElementById('forms_desktop');
            const filtersFormsSelected = "{{ !empty($filters['form_id']) ? $filters['form_id'] : null }}";
            $.ajax({
                url: "<?= env('API_URL') ?>/api/dashboard/forms",
                type: 'get',
                data: {
                    is_paginate: false
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                    $('#loadingDashboard').modal('hide');
                },
                success: function (response) {
                    $('#loadingDashboard').modal('hide');
                    formsSelect.innerHTML = "";
                    const forms = response.forms ?? [];
                    let isSelected = false;
                    resetSelect(formsSelect, 'Todos');

                    forms.forEach(form => {
                        const option = document.createElement('option');
                        option.value = form.id; 
                        option.textContent = form.name;
                        if (parseInt(form.id) === parseInt(filtersFormsSelected)) {
                            isSelected = true 
                            option.selected = true;
                        }
                        formsSelect.appendChild(option);
                    });
                    allForms = forms;
                    formsSelect.disabled = false;
                    loadTopics(filtersFormsSelected, true);
                },
                error: function (response, textStatus, msg) {
                    $('#loadingDashboard').modal('hide');
                }
            });
        }

        function loadTopics(formId = null, updateLegendFilter = false) {
            const topicsSelect = document.getElementById('topics_desktop');
            const filtersTopicSelected = "{{ !empty($filters['topic_id']) ? $filters['topic_id'] : -1 }}";
            $.ajax({
                url: "<?= env('API_URL') ?>/api/topics/getTopicsByForm",
                type: 'get',
                data: {
                    form_id: formId
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                    $('#loadingDashboard').modal('hide');
                    resetSelect(topicsSelect, 'Carregando');
                    topicsSelect.disabled = true;
                },
                success: function (response) {
                    $('#loadingDashboard').modal('hide');
                    topicsSelect.innerHTML = "";
                    const topics = response.topics ?? [];
                    let isSelected = false;
                    resetSelect(topicsSelect, 'Todos');
                    topics.forEach(topic => {
                        const option = document.createElement('option');
                        option.value = topic.id;
                        option.textContent = topic.name;
                        if (parseInt(topic.id) === parseInt(filtersTopicSelected)) {
                            isSelected = true
                            option.selected = true;
                        }
                        topicsSelect.appendChild(option);
                    });
                    topicsSelect.disabled = false;
                    allTopics = topics;
                    const event = new Event('change');
                    topicsSelect.dispatchEvent(event);
                    if (updateLegendFilter) showDynamicFilters();
                },
                error: function (response, textStatus, msg) {
                    $('#loadingDashboard').modal('hide');
                }
            });
        }

        $('#form_filter').on('submit', function (e) {
            e.preventDefault();
            const periodSelected = $('#period_desktop').val();
            if (periodSelected === 'ESPECIFIC') {
                const startDateInput = document.getElementById('startDateInput');
                const endDateInput = document.getElementById('endDateInput');
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                if (startDate > endDate) return alert('A data de início não pode ser após a data de fim!');
            }
            const hiddenFiltersInput = document.getElementById('filters_input');
            let filtersData = $(this).serializeArray();
            filtersDataObject = {};
            $.each(filtersData, function () {
                if (this.name !== 'filters')
                    filtersDataObject[this.name] = this.value;
            });
            delete filtersDataObject['_token'];
            hiddenFiltersInput.value = JSON.stringify(filtersDataObject);
            e.target.submit();
        });

        function addLoading(containerId) {
            $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
        }
    })
</script>
@endpush
