@php use App\Helpers\ApiUser; @endphp
@extends('layouts.app', ['ocultMenu'=> true])

@section('content')
    @include('components.loading', ['message' => 'Aguarde...'])

    <div class="container">
        <div class="row justify-content-center">
            <div class="container" style="margin-bottom: 1rem;">

            </div>
            <div class="col-md-9" style="margin-bottom:3rem">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="d-flex justify-content-between align-items-center"
                             style="margin-top: 5px; margin-bottom:4px">
                            <h5 class="mb-0 card-title"
                                style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                Validar E-mail</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="col-md-12">
                            <div class="form-group">
                                <form action="{{ route('update_email_verified_at') }}" method="post" id="form-update-email-verified-at">
                                    @csrf
                                    <input type="hidden" name="email_validated" id="email_validated" value="0">
                                    @include('components.inputs.text', ['name' => 'email', 'type' => 'email', 'label' => 'E-mail', 'value' => (ApiUser::get()['email'] ?? old('email')), 'placeholder' => 'Digite o e-mail do funcionário', 'withErrorText' => true, 'required' => true])
                                </form>
                            </div>
                            <div class="form-group text-center">
                                <button type="button" class="btn btn-success btn-block" onclick="validateEmail()">
                                    Validar
                                </button>
                            </div>
                            <p class="text-center" style="margin-top: 1rem">Ajuste o e-mail para o desejado</p>
                            <p class="text-center">Você receberá um código de validação no e-mail que você preencheu
                                para poder seguir com o seu login no sistema</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('components.modal.modal_confirm_email', ['modalId' => 'check-code-modal'])

    <script type="text/javascript">
        let oldEmail = '<?= ApiUser::get()['email'] ?>';

        function validateEmail() {
            $('#loading').modal('show')

            let newFormData = new FormData();
            let emailInput = document.getElementById('email');
            newFormData.append('email', emailInput.value);
            if (oldEmail != emailInput.value) {
                $.ajax({
                    url: "<?= env('API_URL') ?>/api/user/validate/email",
                    type: 'post',
                    data: newFormData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>')
                    },
                    success: function (response) {
                        $('#loading').modal('hide')
                        removeErrorMessages()
                        confirmEmail()
                    },
                    error: function (response, textStatus, msg) {
                        $('#loading').modal('hide')
                        if (response.status == 422) {
                            const errors = response.responseJSON.errors
                            removeErrorMessages()
                            removeIsInvalidClass()

                            Object.entries(errors).forEach(([field, error]) => {
                                const id = 'error-' + field
                                document.getElementById(id).innerHTML = error
                                errorsIdToRemove.push(id)
                            })

                            Object.keys(errors).forEach((field) => {
                                document.getElementById(field).classList.add('is-invalid')
                                removeIsInvalidClassId.push(field)
                            })
                        }
                        alert('Preencha todos os dados corretamente.')
                    }
                })
            }else{
                $('#loading').modal('hide');
                removeErrorMessages();
                confirmEmail();
            }
        }

        function confirmEmail() {
            cleanErrors()
            let email = document.getElementById('email').value;

            const formData = new FormData();
            formData.append('email', email);
            formData.append('name', '<?= ApiUser::get()['name'] ?>');
            $('#loading').modal('show');

            $.ajax({
                url: "/api/send/email/confirm-email",
                type: 'post',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (response) {
                    const code = response.code;
                    const modal = document.getElementById('check-code-modal');
                    modal.dataset.code = code;
                    modal.dataset.formId = 'form-update-email-verified-at';
                    modal.dataset.email = email;
                    modal.dataset.name = '<?= ApiUser::get()['name'] ?>';

                    $('#loading').modal('hide');
                    $("#check-code-modal").modal('show');
                },
                error: function (response) {
                    $('#loading').modal('hide');
                    alert('Falha ao enviar email de confirmação!');
                }
            });
        }
    </script>

@endsection
