@php
    use App\Helpers\SystemHelper;
@endphp
    <!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap">

    <!-- Styles -->
    {{-- <link rel="stylesheet" href="{{ asset('css/app.css') }}"> --}}
    <link rel="stylesheet" href="{{ asset('css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css"
          integrity="sha512-YWzhKL2whUzgiheMoBFwW8CKV4qpHQAEuvilg9FAn5VJUDwKZZxkJNuGM4XkWuk94WCrrwslk8yWNGmY1EduTA=="
          crossorigin="anonymous" referrerpolicy="no-referrer"/>

    <!-- Tab icons -->
    <link rel="shortcut icon" type="image/x-icon" sizes="32x32" href="{{ asset('favicon.ico') }}">
    <link rel="icon" href="{{ SystemHelper::get()['TAB_LOGO'] ?? '/icon/favicon/favicon.ico'  }}">

    <!-- Scripts -->
    {{-- <script src="{{ asset('js/app.js') }}" defer></script> --}}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"
            integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script src="{{ asset('js/iframe-resizer.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
    <script src="{{asset('js/script.js')}}"></script>

    {{-- Charts.js --}}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>

    {{-- SweetAlert2 --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js"
            integrity="sha384-b/U6ypiBEHpOf/4+1nzFpr53nxSS+GLCkfwBdFNTxtclqqenISfwAzpKaMNFNmj4"
            crossorigin="anonymous"></script>

    <script src="{{asset('js/bootstrap.js')}}"></script>

    @stack('head_js')
</head>

<body>
<div id="app" class="d-flex">
    <div class="content" id="content">
        <main class="py-4">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col">
                        @include('components.alerts.success')
                        @include('components.alerts.error')
                        @include('components.alerts.errors')
                    </div>
                </div>
            </div>
            @yield('content')
            <script>
                function debounce(func, wait, immediate) {
                    var timeout;
                    return function () {
                        var context = this, args = arguments;
                        var later = function () {
                            timeout = null;
                            if (!immediate) func.apply(context, args);
                        };
                        var callNow = immediate && !timeout;
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                        if (callNow) func.apply(context, args);
                    };
                };
            </script>
            @stack('js')
        </main>
    </div>
</div>
@if(session('topic_action_success'))
    <script>
        @php $topicData = session('topic_action_data'); @endphp
        if (window.parent) {
            @if($topicData)
                if (typeof window.parent.handleTopicUpdate === 'function') {
                    window.parent.handleTopicUpdate(@json($topicData));
                }
            @endif
            if (typeof window.parent.markTopicAsUpdated === 'function') {
                window.parent.markTopicAsUpdated();
            } 
            try {
                if (window.parent.$('#showTopicModal').length) {
                     window.parent.$('#showTopicModal').modal('hide');
                }
            } catch (e) {
                console.error('Erro ao tentar fechar o modal pai:', e);
            }
        }
    </script>
@endif
</body>
</html>
