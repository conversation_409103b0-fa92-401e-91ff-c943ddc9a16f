<style>
    .floatingClose {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        bottom: 2%;
        right: 25px;
        border-radius: 0.2rem;
        background-color: #fff !important;
        font-weight: bold;
        opacity: 0;
        font-size: 1rem;
        transform: translateY(15px);
        transition: all 0.7s ease;
    }

    .modal.show .floatingClose {
        opacity: 1;
        transform: translateY(0); 
    }

    .floatingClose > span {
        padding: 0.5rem;
    }

</style>

<div class="modal fade" id="floating-button-modal" tabindex="-1" role="dialog" aria-labelledby="floating-button-modal" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" style="width: 95vw !important; max-width: 90rem !important; height: 80vh !important;" role="document">
        <div class="modal-content">
            @php
                $hasLink = isset(SystemHelper::get()['FLOATING_BUTTON_LINK']) && SystemHelper::get()['FLOATING_BUTTON_LINK'] !== "";
            @endphp
            <div class="modal-body p-0" style="max-height: calc(100dvh - 50px);">
                <button type="button" class="close floatingClose" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">
                        Fechar
                        <i class="fas fa-times fa-fw"></i>
                    </span>
                </button>
                <div id="loadingIndicatorSupport" class="text-center py-5" @if (!$hasLink) style="display: none" @endif>
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Carregando...</span>
                    </div>
                    <p class="mt-2">Carregando conteúdo...</p>
                </div>
                @if ($hasLink)
                    <iframe id="floating-button-iframe" style="width:100%; height: 95vh; border: none; display: none;"></iframe>
                @else
                    <div class="text-center my-5">
                        <p class="text-muted">A tela de suporte não está disponível.</p>
                        <p class="text-muted">Tente novamente mais tarde.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(() => {
        const iframe = document.getElementById('floating-button-iframe');
        const link = @json(SystemHelper::get()['FLOATING_BUTTON_LINK'] ?? null);

        if (iframe && link) {
            iframe.onload = function() {
                $('#loadingIndicatorSupport').hide();
                $('#floating-button-iframe').show();
            };

            iframe.src = link;
        }
    })
</script>