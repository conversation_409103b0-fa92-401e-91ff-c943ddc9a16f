@php use App\Helpers\ApiUser; use App\Helpers\SystemHelper; @endphp
@php
    $activePage = $activePage ?? '';
    $adminBusinessesId = (int) SystemHelper::get()['ADMIN_BUSINESS_ID'];
    $isAdminBusinessSelected = isset(ApiUser::get()['business_selected']) && ApiUser::get()['business_selected']['id'] === $adminBusinessesId;

@endphp
<style>
    .sectionSidebar > .sectionSidebarTitle:only-child {
        display: none; /* Esconde o título se for o único filho */
    }
    .limited-text {
        display: inline-block;
        max-width: 140px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
    }
</style>

@if ((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin'))
<div class="w-100 mb-3 sectionSidebar notShowOnCollapse">
    <span class="sectionSidebarTitle text-muted font-weight-bold ml-1">Negócio Selecionado</span>
        @if (ApiUser::get()['business_selected'] != null)
            <div class="text-dark d-flex align-items-center" style="padding: 10px 15px; font-size: 20px;" >
                <div class="d-flex align-items-center" onclick="showNegociosUser()" style="cursor: pointer;">
                    <i class="fa fa-suitcase fa-fw mr-2"></i>
                    <span class="limited-text text-dark font-weight-bold">
                        {{ApiUser::get()['business_selected']['name']}}
                    </span>
                    <span class="ml-2" style="font-size: 0.65rem; vertical-align: middle">▼</span>
                </div>
                <a href="/businesses/{{ApiUser::get()['business_selected']['id']}}" class="ml-2 text-dark" style="cursor: pointer; font-size: 20px">
                    @if(ApiUser::hasPermission('manage_business_profile') || ApiUser::hasUserFunction('update_business'))
                    <i class="fas fa-edit fa-fw"></i>
                    @else
                    <i class="fas fa-eye fa-fw"></i>
                    @endif
                </a>
            </div>
        @elseif(ApiUser::get()['business_selected'] == null)
            <div class="text-dark d-flex align-items-center" style="padding: 10px 15px; font-size: 20px;" >
                <div class="d-flex align-items-center" onclick="showNegociosUser()" style="cursor: pointer;">
                    <i class="fa fa-suitcase fa-fw mr-2"></i>
                    <span class="limited-text text-dark font-weight-bold">
                        Nenhum
                    </span>
                    <span class="ml-2" style="font-size: 0.65rem; vertical-align: middle">▼</span>
                </div>
            </div>
        @endif
    </div>
@endif

<div class="w-100 mb-3 sectionSidebar">
    <span class="notShowOnCollapse sectionSidebarTitle text-muted font-weight-bold ml-1">Conteúdos</span>
    @if(ApiUser::get() != null)
        <x-menu-item route="informative" page="informative" textClass="{{ $text_class ?? '' }}"
        :activePage="$activePage">
            <i class="fas fa-home fa-fw mr-2"></i>
            <span class="notShowOnCollapse">
            Tela Inicial
            </span>
        </x-menu-item>
        @if((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin'))
            @if((ApiUser::hasPermission('dashboard') || ApiUser::hasPermission('evaluations') || ApiUser::hasPermission('cashback')) && ApiUser::get()['business_selected'] != null)
                <x-menu-item route="quick_actions" page="quick_actions" textClass="{{ $text_class ?? '' }}"
                             :activePage="$activePage">
                    <i class="fas fa-paper-plane fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                        Disparos
                    </span>
                </x-menu-item>
            @endif
            {{-- ROTAS DA SEÇÃO CONTEÚDO RELACIONADAS A FUNCIONÁRIO/ADMIN --}}
            
            @if(ApiUser::hasPermission('dashboard') && ApiUser::get()['business_selected'] != null)
                <x-menu-item route="dashboard.index" page="dashboard" textClass="{{ $text_class ?? '' }}"
                             :activePage="$activePage">
                    <i class="fas fa-pie-chart fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                        Gráficos
                    </span>
                </x-menu-item>
            @endif

            @if(ApiUser::hasPermission('evaluations') && ApiUser::get()['business_selected'] != null)
                <x-menu-item route="evaluations.index" page="evaluations" textClass="{{ $text_class ?? '' }}"
                             :activePage="$activePage">
                    <i class="fas fa-star fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                        Avaliações
                    </span>
                </x-menu-item>
            @endif

            @if(ApiUser::hasPermission('cashback'))
                <x-menu-item route="cashbacks.index" page="cashbacks" textClass="{{ $text_class ?? '' }}"
                             :activePage="$activePage">
                    <i class="fas fa-money-bill-wave fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                        Cashbacks
                    </span>
                </x-menu-item>
            @endif

            @if(ApiUser::hasPermissionOrIsAdmin('forms'))
                <x-menu-item route="forms.index" page="forms" textClass="{{ $text_class ?? '' }}"
                             :activePage="$activePage">
                    <i class="fas fa-file-alt fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                        Formulários
                    </span>
                </x-menu-item>
            @endif

            @if(ApiUser::hasPermissionOrIsAdmin('notifications'))
                <x-menu-item route="notifications.index" page="notifications" textClass="{{ $text_class ?? '' }}"
                             :activePage="$activePage">
                    <i class="fas fa-flag fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                        Campanhas
                    </span>
                </x-menu-item>
            @endif

            @if(ApiUser::hasPermissionOrAdminBusinessSelected('site_notifications'))
                <x-menu-item route="siteNotifications.index" page="siteNotifications" textClass="{{ $text_class ?? '' }}"
                             :activePage="$activePage">
                    <i class="fas fa-bell fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                        Notificações
                    </span>
                </x-menu-item>
            @endif

            @if (ApiUser::hasPermission('parameters'))
                <x-menu-item route="businesses.parameters" page="parameters" textClass="{{ $text_class ?? '' }}"
                            :activePage="$activePage">
                    <i class="fas fa-sliders-h fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">
                                Parâmetros do Negócio
                                </span>
                </x-menu-item>
            @endif

            <x-menu-item route="{{isset(SystemHelper::get()['SUPPORT_TAB_LINK']) ? SystemHelper::get()['SUPPORT_TAB_LINK'] : env('NOTION_APP_LINK')}}" isExternal page="support" target="blank" submenu textClass="text-dark dropdown-item"
                         :activePage="$activePage">
                <i class="fas fa-headset fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Suporte
                </span>
            </x-menu-item>

            {{-- ROTAS DA SEÇÃO CONTEÚDO RELACIONADAS A FUNCIONÁRIO/ADMIN --}}
        @elseif(ApiUser::getLoginType() == 'seller')
            {{-- ROTAS DA SEÇÃO CONTEÚDO RELACIONADAS A VENDEDORES --}}
            
            <x-menu-item route="businesses.seller.index" page="businesses" submenu textClass="text-dark dropdown-item"
                         :activePage="$activePage">
                <i class="fas fa-store fa-fw mr-2"></i>
                <span class="notShowOnCollapse">Negócios</span>
            </x-menu-item>
            @if(ApiUser::get()['register_sellers'])
                <x-menu-item route="sellers.seller.index" page="sellers" submenu textClass="text-dark dropdown-item"
                             :activePage="$activePage">
                    <i class="fas fa-user-tag fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">Vendedores</span>
                </x-menu-item>
            @endif

            <x-menu-item route="{{isset(SystemHelper::get()['SUPPORT_TAB_LINK']) ? SystemHelper::get()['SUPPORT_TAB_LINK'] : env('NOTION_APP_LINK')}}" isExternal page="support" target="blank" submenu textClass="text-dark dropdown-item"
                         :activePage="$activePage">
                <i class="fas fa-headset fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Suporte
                </span>
            </x-menu-item>
            
            {{-- ROTAS DA SEÇÃO CONTEÚDO RELACIONADAS A VENDEDORES --}}
        @elseif(ApiUser::getLoginType() == 'client')
            {{-- ROTAS DA SEÇÃO CONTEÚDO RELACIONADAS A CLIENTES --}}

            <x-menu-item route="cashbacks.clients.index" page="cashbacks" textClass="{{ $text_class ?? '' }}"
                         :activePage="$activePage">
                <i class="fas fa-money-bill-wave fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Cashbacks
                </span>
            </x-menu-item>

            <x-menu-item route="notifications.clients.index" page="notifications" textClass="{{ $text_class ?? '' }}"
                         :activePage="$activePage">
                <i class="fas fa-flag fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Campanhas
                </span>
            </x-menu-item>

            <x-menu-item route="evaluations.clients.index" page="evaluations" textClass="{{ $text_class ?? '' }}"
                         :activePage="$activePage">
                <i class="fas fa-star fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Avaliações
                </span>
            </x-menu-item>


            <x-menu-item route="{{isset(SystemHelper::get()['SUPPORT_TAB_LINK']) ? SystemHelper::get()['SUPPORT_TAB_LINK'] : env('NOTION_APP_LINK')}}" isExternal page="support" target="blank" submenu textClass="text-dark dropdown-item"
                         :activePage="$activePage">
                <i class="fas fa-headset fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Suporte
                </span>
            </x-menu-item>

            {{-- ROTAS DA SEÇÃO CONTEÚDO RELACIONADAS A CLIENTES --}}
        @endif

        {{-- ROTAS DA SEÇÃO CONTEÚDO PADRÕES VÃO SER EXIBIDAS SEMPRE --}}
        {{-- <x-menu-item route="app" page="app" submenu textClass="text-dark dropdown-item" :activePage="$activePage">
            <i class="fas fa-mobile-alt fa-fw mr-2"></i>
            <span class="notShowOnCollapse">
                Aplicativo
            </span>
        </x-menu-item> --}}

    @endif
</div>
@if((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin'))
    <div class="w-100 mb-3 sectionSidebar">
        <span class="notShowOnCollapse sectionSidebarTitle text-muted font-weight-bold ml-1">Cadastro</span>
        @if(ApiUser::hasPermission('employees') || (ApiUser::hasUserFunction('employees') && ApiUser::hasUserFunction('release_all_business') && ApiUser::get()['business_selected'] == null))
            <x-menu-item route="employees.index" page="employees" textClass="{{ $text_class ?? '' }}"
                        :activePage="$activePage">
                <i class="fas fa-user-tie fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Funcionários
                </span>
            </x-menu-item>
        @endif
        @if(ApiUser::hasPermissionOrIsAdmin('clients') && ApiUser::get()['business_selected'] != null)
            <x-menu-item route="clients.index" page="clients" textClass="{{ $text_class ?? '' }}"
                        :activePage="$activePage">
                <i class="fas fa-users fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                    Clientes
                </span>
            </x-menu-item>
        @endif
    </div>
@endif

@if((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin'))
    {{-- ROTAS DA SEÇÃO ADMINISTRATIVO (SÓ É EXIBIDA PARA FUNCIONÁRIOS) --}}
    <div class="w-100 mb-3 sectionSidebar">
        <span class="notShowOnCollapse sectionSidebarTitle text-muted font-weight-bold ml-1">Administrativo</span>
        @if(ApiUser::hasUserFunction('update_business'))
            <x-menu-item route="businesses.index" page="businesses" submenu textClass="text-dark dropdown-item"
                         :activePage="$activePage">
                <i class="fas fa-store fa-fw mr-2"></i>
                <span class="notShowOnCollapse">Negócios</span>
            </x-menu-item>
        @endif
        @if(ApiUser::hasUserFunction('plans'))
            <x-menu-item route="plans.index" page="plans" submenu textClass="text-dark dropdown-item"
                        :activePage="$activePage">
                <i class="fas fa-th-list fa-fw mr-2"></i>
                <span class="notShowOnCollapse">Planos</span>
            </x-menu-item>
        @endif
        @if (ApiUser::hasUserFunction('settings'))
            <x-menu-item route="settings" page="settings" textClass="{{ $text_class ?? '' }}"
                         :activePage="$activePage">
                <i class="fas fa-cog fa-fw mr-2"></i>
                <span class="notShowOnCollapse">
                            Configurações
                        </span>
            </x-menu-item>
        @endif
        @if(ApiUser::hasUserFunction('sellers'))
            <x-menu-item route="sellers.index" page="sellers" submenu textClass="text-dark dropdown-item"
                         :activePage="$activePage">
                <i class="fas fa-user-tag fa-fw mr-2"></i>
                <span class="notShowOnCollapse">Vendedores</span>
            </x-menu-item>
        @endif
    </div>
    {{-- ROTAS DA SEÇÃO ADMINISTRATIVO (SÓ É EXIBIDA PARA FUNCIONÁRIOS) --}}
@endif
