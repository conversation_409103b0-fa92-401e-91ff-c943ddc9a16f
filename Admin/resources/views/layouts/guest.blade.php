@php use App\Helpers\SystemHelper; @endphp

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap">

    <!-- Styles -->
    {{-- <link rel="stylesheet" href="{{ asset('css/app.css') }}"> --}}
    <link rel="stylesheet" href="{{ asset('css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css"
          integrity="sha512-YWzhKL2whUzgiheMoBFwW8CKV4qpHQAEuvilg9FAn5VJUDwKZZxkJNuGM4XkWuk94WCrrwslk8yWNGmY1EduTA=="
          crossorigin="anonymous" referrerpolicy="no-referrer"/>

    <!-- Tab icons -->
    <link rel="shortcut icon" type="image/x-icon" sizes="32x32" href="{{ asset('favicon.ico') }}">
    <link rel="icon" href="{{ SystemHelper::get()['TAB_LOGO'] ?? '/icon/favicon/favicon.ico'  }}">

    <!-- Scripts -->
    {{-- <script src="{{ asset('js/app.js') }}" defer></script> --}}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"
            integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>

    <script src="{{asset('js/bootstrap.js')}}"></script>
    <script src="{{asset('js/script.js')}}"></script>

    {{-- SweetAlert2 --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    {{-- Script para formatar telefone colado --}}
    <script src="{{asset('js/phone-paster.js')}}"></script>
    
    @stack('head_js')

    <style>
        body {
            background-color: {{ SystemHelper::get()['BACKGROUND_COLOR'] ?? "#fff" }};
        }
        #backgroundImage {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .nav-link:hover {
            text-decoration: underline;
        }
        @media(max-width: 575px) {
            .menu_principal {
                top: 0;
                z-index: 99;
            }
        }

        @media(max-width: 768px) {
            #backgroundImage {
                object-fit: cover;
            }
        }
    </style>
</head>
<body>
<div id="app">
    @if (isset(SystemHelper::get()['BACKGROUND_IMAGE']))
        <img src="{{ SystemHelper::get()['BACKGROUND_IMAGE'] }}" id="backgroundImage" >
    @endif
    {{-- <div class="menu_principal position-sticky" style="background-color: {{ SystemHelper::get()['MAIN_COLOR'] }};">
        <nav class="navbar navbar-expand navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="{{route('home')}}">
                    <img src="{{ SystemHelper::get()['MAIN_LOGO'] ?? '/icon/logo_visao_negocio.svg' }}" alt="Logo"
                         style="width: 100%; max-height: 50px;"/>
                </a>
                        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                <div class="d-flex">
                    <a class="nav-link text-dark" href="{{ route('login') }}">{{ __('Entrar') }}</a>
                    <a class="nav-link text-dark" href="{{ route('support') }}">{{ __('Suporte') }}</a>
                </div>
            </div>
        </nav>
    </div> --}}
    <main class="py-4">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col">
                    @include('components.alerts.success')
                    @include('components.alerts.error')
                    @include('components.alerts.errors')
                </div>
            </div>
        </div>
        @yield('content')
        @stack('js')
    </main>
</div>
</body>
</html>
