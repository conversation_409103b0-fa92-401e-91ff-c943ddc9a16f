<style>
    .sidebar {
        display: block !important;
        height: 100vh;
        height: 100dvh;
        width: 300px;
        position: fixed;
        padding-top: 20px;
        transition: width 0.3s;
        border-right: 1px solid {{ SystemHelper::get()['MAIN_COLOR'] ?? '#FBBC05' }};
        z-index: 999;
    }
    .sidebar.collapsed {
        width: 100px;
    }
    .sidebar .menu-item {
        width: 100%;
        padding: 10px 15px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 1.10rem;
        color: white;
        display: block;
        transition: all 0.15s;
    }
    .sidebar-content {
        max-height: calc(100vh - 60px);
        overflow-y: auto;
    }
    .sidebar.collapsed .menu-item {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0px !important;
        margin-left: 0px !important;
        text-align: center;
        font-size: 12px;
    }
    .sidebar.collapsed .menu-item i {
        text-align: center;
        font-size: 24px;
        margin: 0 !important;
        padding: 0px;
    }
    .sidebar.collapsed .notShowOnCollapse {
        display: none !important;
    }
    .sidebar.collapsed .sidebarProfile {
        justify-content: center;
    }
    .sidebar.collapsed #sidebarIconColapse {
        display: none;
    }
    .sidebar.collapsed #sidebarIconExpand {
        display: inline !important;
    }
    .sidebar .menu-item:hover {
        font-weight: bold;
        background: #F4F4F5;
    }
    .collapse-btn {
        position: absolute;
        top: 50%;
        right: -24px;
        z-index: 99;
        outline: none;
        background-color: white;
        border: 1px solid {{ SystemHelper::get()['MAIN_COLOR'] ?? '#FBBC05' }};
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .sideBarSeparator {
        width: 100%;
        height: 2px;
        background-color: #000;
    }
    .footer {
        width: 100%;
        position: sticky;
        bottom: 0;
        background-color: #fff;
        z-index: 100;
        padding: 10px;
        box-shadow: 0px -2px 5px rgba(0, 0, 0, 0.1);
    }
    .footer a {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-decoration: none;
    }

    .sidebar .sidebar-content::-webkit-scrollbar {
        width: 4px;
    }

    .sidebar.collapsed .sidebar-content::-webkit-scrollbar {
        display: none;
    }

    .sidebar .sidebar-content::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.1); 
        border-radius: 6px;
    }

    .sidebar .sidebar-content::-webkit-scrollbar-thumb {
        background-color: rgb(63 63 70); 
        border-radius: 6px; 
    }

    .sidebar .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: darken({{ SystemHelper::get()['MAIN_COLOR'] ?? '#FBBC05' }}, 10%);
    }

    .sidebar.collapsed .popover {
        left: 100px !important;
    }

    @media(max-width: 900px) {
        .footer {
            position: fixed;
            padding: 10px 10px 5px 10px;
            z-index: 1000;
        }
        .sidebar {
            display: none;
            border: none;
            padding-bottom: 30px;
            transform:  translateX(-100%);
            transition: transform 0.25s;
        }
        .sidebar.show {
            display: block !important;
            visibility: visible;
            transform:  translateX(0);
        }
        .sidebar .sidebar-content::-webkit-scrollbar {
            width: 4px;
        }
        #hamburguerMenu {
            display: inline !important;
        }
        #toggleSidebar {
            display: none;
        }
        .content, .content.collapsed {
            margin-left: 0px;
            width: 100vw;
            padding: 0px
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }
        .overlay.show {
            display: block;
        }
    }
    .notification-menu-button {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        position: relative;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        font-size: 1.5em;
    }

    .notification-badge {
        position: absolute;
        top: -10px;
        left: 50%;
        background-color: red;
        color: white;
        border-radius: 50%;
        width: 22px;
        height: 22px;
        font-size: 0.5em;
        font-weight: bold;
        display: flex;
        justify-content: center;
        align-items: center;
    }

</style>
<div>
    <div class="overlay" id="overlay"></div>
    <div class="sidebar bg-white" id="sidebar">
        <button class="collapse-btn bg-white" id="toggleSidebar">
            <i class="fas fa-chevron-left fa-fw" id="sidebarIconColapse"></i>
            <i class="fas fa-chevron-right fa-fw" id="sidebarIconExpand" style="display: none"></i>
        </button>
        <div class="sidebar-content d-flex flex-column h-100">
            <a href="{{ route('home') }}" class="d-flex sidebarLogo justify-content-center my-3">
                <img src="{{ SystemHelper::get()['MAIN_LOGO'] ?? '/icon/logo_visao_negocio.svg' }}" 
                alt="Logo" 
                class="w-100 px-2 img-fluid" 
                style="max-height: 40px; max-width: 150px; object-fit: contain;">
            </a>
            @include('layouts.menu')
        </div>

        <div class="footer w-100 d-flex justify-content-center align-items-center" style="position: absolute; bottom: 0;">
            <div class="border text-center" style="width: 90%">
                <a class="menu-item text-dark" href="{{ route('auth.api.logout') }}"
                    onclick="event.preventDefault();document.getElementById('logout-form').submit();">
                    <i class="fas fa-sign-out-alt fa-fw mr-2"></i>
                    <span class="notShowOnCollapse">{{ __('Sair') }}</span>
                </a>
                <form id="logout-form" action="{{ route('auth.api.logout') }}" method="POST" class="d-none">
                    @csrf
                </form>
            </div>
        </div>
    </div>
</div>

@include('components.loading', ['message' => 'Carregando...', 'id' => 'changeProfileLoading'])

<script>
    $(document).ready(function() {
        $('#hamburguerMenu').on('click', function() {
            $('#sidebar').removeClass('collapsed');
            $('#sidebar').addClass('show');
            $('#overlay').addClass('show');
        });
        
        $('#overlay').on('click', function() {
            $('#sidebar').removeClass('show');
            $('#overlay').removeClass('show');
        });

        $('#toggleSidebar').on('click', function() {
            const sidebar = $('#sidebar');
            const content = $('#content');
            const notificationButton = $('#notification-menu-button');
            notificationButton.toggleClass('collapsed');
            sidebar.toggleClass('collapsed');
            content.toggleClass('collapsed');
        });
    });

    function changeLoginType(type) {
        $('#changeProfileLoading').modal('show');
        $.ajax({
            url: "/changeLoginType",
            type: 'post',
            data: {
                _token: '<?= csrf_token() ?>',
                login_type: type
            },
            beforeSend: function (xhr) {
                $('#changeProfileLoading').modal('show');
            },
            success: function (response) {
                refreshUser(true);
            },
            error: function (response, textStatus, msg) {
                $('#changeProfileLoading').modal('hide');
            }
        });
    }
</script>