@php use App\Helpers\ApiUser; @endphp
@if (ApiUser::get())
    <style>
        @media(max-width: 900px) {
            #headerBusinessLogo {
                object-fit: contain;
                width: 65px !important;
                height: 65px !important;
            }
        }
    </style>
    <div class="container d-flex align-items-center justify-content-beetwen" style="width: 100%">
        <div>
            {{-- Left side --}}
            <button class="btn btn-white border bg-white" id="hamburguerMenu" style="display: none; width: 50px;">
                <i class="fas fa-bars" id="sidebarIcon"></i>
            </button>
        </div>
        <div class="d-flex justify-content-center" style="object-fit: cover">
            <a href="{{ route('home') }}" class="d-flex sidebarLogo justify-content-center my-3">
                <img src="{{ SystemHelper::get()['MAIN_LOGO'] ?? '/icon/logo_visao_negocio.svg' }}"
                     alt="Logo"
                     class="w-100 px-2 img-fluid"
                     style="max-height: 40px; max-width: 150px; object-fit: contain;">
            </a>
        </div>
        <div>
            {{-- right side --}}
            <button type="button" class="notification-menu-button" id="notification-menu-button-mobile">
                <div class="notification-badge d-none" id="unreadNotificationBadgeMobile">
                    <span id="unreadNotificationsCountMobile">0</span>
                </div>
                <i class="fas fa-bell fa-fw text-dark"></i>
            </button>
        </div>
    </div>
    <script>
    @if(ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin')
        let releaseAllBusinesses = {{ ApiUser::get()['authorizedFunction']['release_all_business'] != null && ApiUser::get()['authorizedFunction']['release_all_business'] ? 'true' : 'false' }};

        function showNegociosUser(show_defaut_option = true) {
            $('#modalNegociosUser').modal('show');
            loadBusinesses(1, show_defaut_option);
        }

        function addBusinessToTable(business) {
            let isSelected = business.id === <?= ApiUser::get()['business_selected']['id'] ?? 'null' ?>;
            let hasManageBusinessProfilePermission = business.plan.authorizedFunctions.manage_business_profile;
            let hasManageBusinessProfilePermissionUser = '1' === '<?= ApiUser::get()['authorizedFunction']['manage_business_profile'] ?? '0' ?>';
            let hasUpdateBusinessPermissionUser = '1' === '<?= ApiUser::get()['authorizedFunction']['update_business'] ?? '0' ?>';
            if ((hasManageBusinessProfilePermission && hasManageBusinessProfilePermissionUser) || hasUpdateBusinessPermissionUser) {
                $('#businessTableBody').append(`
            <tr>
                <td>
                    ${business.name}
                </td>
                <td>
                    <a href="/businesses/${business.id}" class="btn btn-warning">
                        <img class="card-img-left example-card-img-responsive"
                            src="{{url('icon/icon_editar.png')}}" width="20px"/>
                    </a>
                    <button class="btn ${isSelected ? 'btn-success' : 'btn-white'} border" onclick=" ${isSelected ? '' : 'selectBusiness(' + business.id + ');'}"><span class="fa fa-check"></span></button>
                </td>
            </tr>
            }

        `);
            } else {
                $('#businessTableBody').append(`
            <tr>
                <td>
                    ${business.name}
                </td>
                <td>
                    <a href="/businesses/${business.id}" class="btn btn-secondary"><span class="fa fa-eye"></span></a>
                    <button class="btn ${isSelected ? 'btn-success' : 'btn-primary'}" onclick=" ${isSelected ? '' : 'selectBusiness(' + business.id + ');'}"><span class="fa fa-check"></span></button>
                </td>
            </tr>
            }

        `);
            }
        }

        function loadBusinesses(page = 1, show_defaut_option = true) {
            const searchQuery = $('#businessSearch').val();
            $.ajax({
                url: "<?= env('API_URL') ?>/api/business/getUserBusiness?search=" + searchQuery + "&page=" + page + "&per_page=5",
                type: 'get',
                data: {
                    search: searchQuery,
                    page: page,
                    per_page: 5,
                    priorize_admin: true
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                    $('#businessTable').addClass('d-none');
                    $('#businessPagination').addClass('d-none');
                    $('.loading').removeClass('d-none');
                },
                success: function (response) {
                    $('.loading').addClass('d-none');

                    $('#businessTableBody').empty();
                    const businesses = response.businesses.data;
                    if (show_defaut_option && releaseAllBusinesses) {
                        $('#businessTableBody').append(`
                        <tr>
                            <td>
                                Nenhum
                            </td>
                            <td>
                                <button class="btn w-auto {{ApiUser::get() != null && ApiUser::get()['business_selected'] == null ? 'btn-success' : 'btn-white'}} border" onclick="selectAllBusinesses();"><span class="fa fa-check"></span></button>
                            </td>
                        </tr>
                    `);
                    }

                    // adiciona o negócio admin na primeira opção (se houver)
                    if (response.adminBusiness) {
                        addBusinessToTable(response.adminBusiness);
                    }

                    // Exibe os outros negócios
                    if (businesses.length > 0) {
                        $.each(businesses, function (index, business) {
                            addBusinessToTable(business);
                        });
                    }  else if (!response.adminBusiness) {
                            $('#businessTableBody').append(`
                                <tr>
                                    <td colspan="2" style="text-align: center; padding: 20px; color: #666;">
                                        <div style="display: flex; flex-direction: column; align-items: center;">
                                            <i class="fas fa-store-slash" style="font-size: 24px; margin-bottom: 8px;"></i>
                                            <span style="font-style: italic;">Nenhum negócio encontrado</span>
                                        </div>
                                    </td>
                                </tr>
                            `);
                    }

                    // Paginação
                    $('#businessPagination').empty();
                    if (response.businesses.last_page > 1) {
                        for (let i = 1; i <= response.businesses.last_page; i++) {
                            $('#businessPagination').append(`
                                <li class="page-item ${i === response.businesses.current_page ? 'active' : ''}">
                                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                                </li>
                            `);
                        }
                    }

                    $('#businessPagination a').on('click', function (e) {
                        e.preventDefault();
                        const page = $(this).data('page');
                        loadBusinesses(page);
                    });

                    $('#businessTable').removeClass('d-none');
                    $('#businessPagination').removeClass('d-none');
                },
                error: function (response, textStatus, msg) {
                    $('.loading').addClass('d-none');
                }
            });
        }

        function selectAllBusinesses() {
            $('.loading').removeClass('d-none');
            $('#businessTable').addClass('d-none');
            $('#businessPagination').addClass('d-none');
            $.ajax({
                url: "<?= env('API_URL') ?>/api/business/selectAllBusinesses",
                type: 'post',
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: async function (response) {
                    $('#modalNegociosUser').modal('hide');
                    refreshUser(true);
                },
                error: function (response, textStatus, msg) {
                    $('.loading').addClass('d-none');
                    $('#businessTable').removeClass('d-none');
                    $('#businessPagination').removeClass('d-none');
                }
            });
        }

        function debounce(func, wait, immediate) {
            let timeout;
            return function () {
                const context = this, args = arguments;
                const later = function () {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }

        function selectBusiness(businessId) {
            $('.loading').removeClass('d-none');
            $('#businessTable').addClass('d-none');
            $('#businessPagination').addClass('d-none');
            $.ajax({
                url: "<?= env('API_URL') ?>/api/business/selectBusiness?business_id=" + businessId,
                type: 'post',
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: async function (response) {
                    $('#modalNegociosUser').modal('hide');
                    refreshUser(true);
                },
                error: function (response, textStatus, msg) {
                    $('.loading').addClass('d-none');
                    $('#businessTable').removeClass('d-none');
                    $('#businessPagination').removeClass('d-none');
                }
            });
        }
        @endif

        function refreshUser(goToHome = false) {
            $.ajax({
                url: "/refresh_user",
                type: 'post',
                data: {
                    _token: '<?= csrf_token() ?>'
                },
                success: function (response) {
                    if (goToHome) {
                        window.location.href = "/";
                    } else {
                        location.reload();
                    }
                },
                error: function (response, textStatus, msg) {
                    //console.log(response);
                }
            });
        }
        @if(ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin')
            $(document).ready(function () {
                $('#business_dropdown').on('click', function () {
                    showNegociosUser();
                });
                $('#businessSearch').on('keyup', debounce(function () {
                    loadBusinesses();
                }, 500));
            });
        @endif
    </script>
@else
    <li class="nav-item">
        <a class="nav-link" href="{{ route('login') }}">{{ __('Entrar') }}</a>
    </li>
@endif



