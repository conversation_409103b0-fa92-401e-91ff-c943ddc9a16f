@php use App\Helpers\ApiUser; @endphp
@php
    $name = explode(' ', ApiUser::get()['name']);
    $name = $name[0];
    $name = strlen($name) > 13 ? substr($name, 0, 13) . '...' : $name;
    $hasAvatar = ApiUser::get()['avatar'] && ApiUser::get()['avatar'] != null;
@endphp
<div class="d-flex px-2">
    <nav class="navbar navbar-expand-md justify-content-between flex-column w-100 px-2 py-1">
        <div style="width: 100%" class="mb-3">
            <div class="w-100 d-flex justify-content-between align-items-center">
                <span 
                    class="notShowOnCollapse w-100 text-muted font-weight-bold ml-1 d-flex justify-content-between align-items-center"
                    id="sidebarSectionTitle">
                    Perfil
                </span>
                <button type="button" class="notification-menu-button" id="notification-menu-button">
                    <div class="notification-badge d-none" id="unreadNotificationBadge">
                        <span id="unreadNotificationsCount">0</span>
                    </div>
                    <i class="fas fa-bell fa-fw text-dark"></i>
                </button>
            </div>
            <div class="d-flex align-items-center my-2 sidebarProfile">
                <a href="{{ route('profile') }}" class="text-dark">
                    @if ($hasAvatar)
                        <img
                            src="{{ ApiUser::get()['avatar'] }}"
                            alt="Avatar" id="profile_menu" class="img-fluid rounded-circle d-block"
                            style="cursor: pointer;  width: 45px; height: 45px; object-fit: cover;">
                    @else
                        <i class="fas fa-user-circle fa-fw" style="font-size: 2.5rem"></i>
                    @endif
                </a>
                <div class="ml-2 notShowOnCollapse">
                    <a href="{{ route('profile') }}" class="text-dark">
                        <b>{{Str::limit($name, 18, '...')}}</b>
                        <i class="fas fa-edit fa-fw" style="font-size: 1rem"></i>
                    </a>
                    @php
                    $countTypesAvailable = 0;
                    foreach(ApiUser::get()['types'] as $type => $details) {
                        if($details['hasType']) {
                            $countTypesAvailable++;
                        }
                    }
                    $countTypesAvailableAux = 0;
                @endphp
                @if($countTypesAvailable > 0)
                    <div class="nav-item dropdown">
                        <a id="profileDropdown" class="@if($countTypesAvailable > 1) dropdown-toggle @endif text-dark" href="#" role="button" data-toggle="dropdown"
                           aria-haspopup="true" aria-expanded="false" v-pre
                           @if($countTypesAvailable == 1) style="pointer-events: none" @endif>
                            {{ ApiUser::get()['types'][ApiUser::get()['login_type']]['name'] }}
                        </a>
                        <div class="dropdown-menu dropdown-menu-left" aria-labelledby="profileDropdown">
                            @foreach(ApiUser::get()['types'] as $type => $details)
                                @if($details['hasType'])
                                    @php
                                        $countTypesAvailableAux++;
                                    @endphp
                                    <a class="dropdown-item" href="#" onclick="changeLoginType('{{ $type }}')">
                                        {{ $details['name'] }}
                                    </a>
                                    @if($countTypesAvailableAux < $countTypesAvailable)
                                        <div class="dropdown-divider"></div>
                                    @endif
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif
                </div>
            </div>
        </div>
        <div class="w-100 mb-3">
            <div class="d-flex flex-column" id="menu_options">
                @if(ApiUser::get()['email_verified_at'] != null)
                    @include('layouts.menu_options')
                @endif
            </div>
        </div>
    </nav>
</div>

@push('js')
<script type="text/javascript">
    window.setTimeout(function () {
        $(".alert").fadeTo(500, 0).slideUp(500, function () {
            $(this).remove();
        });
    }, 5000);

    function ajust_menu() {
        if ($(window).width() < 768) {
            $('#titulo-cidade').removeClass('w-100');
            $('#titulo-cidade').addClass('w-100');
        } else if ($(window).width() < 576) {
            $('#titulo-cidade').removeClass('w-100');
            $('#titulo-cidade').addClass('w-100');
        } else {
            $('#titulo-cidade').removeClass('w-100');
            $('#titulo-cidade').addClass('w-100');
        }
    }

    ajust_menu();
    $(window).resize(function () {
        ajust_menu();
    });
</script>
@endpush
