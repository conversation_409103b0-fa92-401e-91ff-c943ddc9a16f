@php
    use App\Helpers\SystemHelper;
@endphp


    <!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap">

    <!-- Styles -->
    {{-- <link rel="stylesheet" href="{{ asset('css/app.css') }}"> --}}
    <link rel="stylesheet" href="{{ asset('css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css"
          integrity="sha512-YWzhKL2whUzgiheMoBFwW8CKV4qpHQAEuvilg9FAn5VJUDwKZZxkJNuGM4XkWuk94WCrrwslk8yWNGmY1EduTA=="
          crossorigin="anonymous" referrerpolicy="no-referrer"/>

    <!-- Tab icons -->
    <link rel="shortcut icon" type="image/x-icon" sizes="32x32" href="{{ asset('favicon.ico') }}">
    <link rel="icon" href="{{ SystemHelper::get()['TAB_LOGO'] ?? '/icon/favicon/favicon.ico'  }}">

    <!-- Scripts -->
    {{-- <script src="{{ asset('js/app.js') }}" defer></script> --}}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"
            integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
    <script src="{{asset('js/script.js')}}"></script>

    {{-- Charts.js --}}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>

    {{-- SweetAlert2 --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    {{-- Script para formatar telefone colado --}}
    <script src="{{asset('js/phone-paster.js')}}"></script>


    @stack('head_js')
    <style>
        body {
            background-color: {{ SystemHelper::get()['BACKGROUND_COLOR'] ?? "#fff" }};
        }

        #backgroundImage {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        #map {
            width: 100%;
            height: 400px;
            background-color: grey;
        }

        .content {
            padding: 0px 20px 20px 20px;
            margin-left: 300px;
            width: calc(100vw - 300px);
            transition: width 0.3s, margin 0.3s;
        }

        .content.collapsed {
            margin-left: 100px;
            width: calc(100vw - 100px)
        }

        .content.collapsed-right {
            margin-right: 25vw;
            width: calc(100vw - 25vw)
        }

        @media (max-width: 575px) {
            .menu_principal {
                top: 0;
                z-index: 99;
            }
        }


        @media (min-width: 901px) {
            #menu_principal {
                display: none;
            }
        }

        @media(max-width: 768px) {
            #backgroundImage {
                object-fit: cover;
            }
        }
    </style>
</head>

<body>
<div id="app" class="d-flex">
    @if (isset(SystemHelper::get()['BACKGROUND_IMAGE']))
        <img src="{{ SystemHelper::get()['BACKGROUND_IMAGE'] }}" id="backgroundImage">
    @endif
    @include('layouts.sidebar')
    <div class="content" id="content">
        <div class="menu_principal " id="menu_principal"
             style="background-color: {{ SystemHelper::get()['MAIN_COLOR'] ?? '#FBBC05'}};">
            <nav class="navbar navbar-expand navbar-light bg-white" style="max-height: 60px">
                @include('layouts.header')
            </nav>
        </div>
        <main class="py-4">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col">
                        @include('components.alerts.success')
                        @include('components.alerts.error')
                        @include('components.alerts.errors')
                    </div>
                </div>
            </div>
            @if(session('alert_incomplete_data') && ApiUser::get()['email_verified_at'] != null)
                <div style="
                  background-color: #fff9db;
                  border: 1px solid #ffe8a1;
                  border-radius: 6px;
                  padding: 16px;
                  margin-left: 15px;
                  margin-right: 15px;
                  margin-bottom: 24px;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                  font-family: 'Nunito', sans-serif;
                  color: #6b4e14;
                  line-height: 1.5;

                ">{!! session('alert_incomplete_data') !!}</div>
            @endif

            @yield('content')

            @stack('js')
        </main>
    </div>
</div>

@include('components.floating-button.index')

@include('siteNotifications.bellNotification.modal')

<div class="modal fade" id="modalNegociosUser" tabindex="-1" role="dialog" aria-labelledby="modalNegociosUser">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Negócios</h5>
                <button type="button" class="close closeModalNegociosUser" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="text-align: left">
                @if(ApiUser::getLoginType() != null && (ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin') && ApiUser::get()['business_selected'] != null)
                    <div class="alert-success" role="alert"
                         style="padding: 10px;border-radius: 5px;margin-bottom: 10px;">
                        Negócio selecionado: <b>{{ ApiUser::get()['business_selected']['name'] }}</b>
                    </div>
                @endif
                <div class="form-group mb-0">
                    <input type="text" id="businessSearch" class="form-control" placeholder="Pesquisar negócio...">
                </div>
                <div class="d-flex justify-content-center loading pb-1 d-none">
                    <div class="spinner-border loading" role="status">
                        <span class="sr-only loading">Loading...</span>
                    </div>
                </div>
                <table class="table table-bordered d-none" id="businessTable">
                    <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Ações</th>
                    </tr>
                    </thead>
                    <tbody id="businessTableBody">
                    <!-- Negócios serão carregados aqui -->
                    </tbody>
                </table>
                <nav>
                    <ul class="pagination" id="businessPagination">
                        <!-- Paginação será carregada aqui -->
                    </ul>
                </nav>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary closeModalNegociosUser" data-dismiss="modal"
                        style="width:30%">Fechar
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js"
        integrity="sha384-b/U6ypiBEHpOf/4+1nzFpr53nxSS+GLCkfwBdFNTxtclqqenISfwAzpKaMNFNmj4"
        crossorigin="anonymous"></script>
<script src="{{asset('js/bootstrap.js')}}"></script>

<script>
</script>
</body>

</html>
