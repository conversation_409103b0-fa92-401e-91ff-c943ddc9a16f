@php use App\Helpers\StringMask; @endphp
    <!-- <PERSON><PERSON> de Detalhes da Avaliação -->
<div class="modal fade" id="evaluationDetailModal-{{ $evaluation['id'] }}" tabindex="-1" role="dialog"
     aria-labelledby="evaluationDetailModalLabel-{{ $evaluation['id'] }}" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header"
                 style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                <h5 class="modal-title" id="evaluationDetailModalLabel-{{ $evaluation['id'] }}"
                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                    Detalhes da Avaliação
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="col-md-6">
                        <label>Avaliação Anônima</label>
                        <input type="text" class="form-control"
                               value="{{ $evaluation['anonymous'] ? 'Sim' : 'Não' }}"
                               disabled>
                    </div>

                    @if (!$evaluation['anonymous'])
                        @php
                            $clientFromEval = $evaluation['client'] ?? null;
                            $clientUser = null;
                            if ($clientFromEval && isset($clientFromEval['user']) && is_array($clientFromEval['user'])) {
                                $clientUser = $clientFromEval['user'];
                            }

                            $cpf = $clientUser['cpf'] ?? null;
                            // Se client e client.user existirem e tiverem email, usa o email do user. Senão, fallback para o email da avaliação.
                            $email = ($clientFromEval && $clientUser && isset($clientUser['email'])) ? $clientUser['email'] : $evaluation['email'];
                            $clientName = $clientUser['name'] ?? null;
                        @endphp
                        @if($clientFromEval && $clientUser && $clientName) {{-- Mostra o nome apenas se client, client.user e clientName existirem --}}
                            <div class="col-md-6">
                                <label>Nome do Cliente</label>
                                <input type="text" class="form-control" value="{{ $clientName }}"
                                       disabled>
                            </div>
                        @endif
                        <div class="col-md-6 @if($evaluation['client']) mt-3 @endif">
                            <label>CPF do Cliente</label>
                            <input type="text" class="form-control" value="{{ StringMask::cpf($cpf) }}" disabled>
                        </div>
                        @if($evaluation['sending_type'] === 'EMAIL')
                            <div class="col-md-6 mt-3">
                                <label>Email</label>
                                <input type="email" class="form-control" value="{{ $email }}" disabled>
                            </div>
                        @else
                            <div class="col-md-6 mt-3">
                                <label>Número de Contato</label>
                                <input type="text" class="form-control"
                                       value="{{ StringMask::phone($evaluation['number_phone'])  }}"
                                       disabled>
                            </div>
                        @endif
                    @endif
                    <div class="col-md-6 @if(!$evaluation['anonymous']) mt-3 @endif">
                        <label>Tipo de Envio</label>
                        <input type="text" class="form-control" value="{{ $evaluation['sending_type'] }}" disabled>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label>Data de Envio</label>
                        <input type="text" class="form-control"
                               value="{{ \Carbon\Carbon::parse($evaluation['shipping_date'])->format('d/m/Y H:i') }}"
                               disabled>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label>Formulário</label>
                        @if (\App\Helpers\ApiUser::hasPermission('forms') && $evaluation['form']['deleted_at'] === null)
                            <a href="{{ route('forms.show', $evaluation['form_id']) }}" target="_blank"
                               class="form-control"
                               style="color: #0842A0; text-decoration: none;
                               background-color: #e9ecef; border-radius: 5px;
                               padding-left: 12px;">
                                {{ $evaluation['form']['name'] }}
                            </a>
                        @else
                            <input type="text" class="form-control" value="{{ $evaluation['form']['name'] }}" disabled>
                        @endif
                    </div>
                    <div class="col-md-6 mt-3">
                        <label>Negócio</label>
                        <input type="text" class="form-control" value="{{ $evaluation['business']['name'] }}" disabled>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label>Status</label>
                        <input type="text" class="form-control"
                               value="{{ $statuses[$evaluation['shipping_status']] ?? $evaluation['shipping_status'] }}"
                               disabled>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label>Data de Expiração da Avaliação</label>
                        <input type="text" class="form-control"
                               value="{{ $evaluation['expiration_date'] ? \Carbon\Carbon::parse($evaluation['expiration_date'])->format('d/m/Y H:i') : '-' }}"
                               disabled>
                    </div>
                    @if (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
                        <div class="col-md-6 mt-3">
                            <label>Valor Produto/Serviço</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control"
                                    value="{{ $evaluation['amount'] ? number_format($evaluation['amount'], 2, ',', '') : '-' }}"
                                    disabled>
                            </div>
                        </div>
                    @endif
                    <div class="col-md-6 mt-3">
                        <label>Avaliação com CashBack?</label>
                        <input type="text" class="form-control"
                               value="{{ $evaluation['with_cashback'] ? 'Sim' : 'Não' }}"
                               disabled>
                    </div>
                    @if($evaluation['with_cashback'])
                        <div class="col-md-6 mt-3">
                            <label>% CashBack</label>
                            <div class="input-group">
                                <span class="input-group-text">%</span>
                                <input type="text" class="form-control"
                                        value="{{ $evaluation['cashback_percentage'] }}"
                                        disabled>
                            </div>
                        </div>
                    @endif
                    @if (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'] && $evaluation['with_cashback'])
                        @php
                            $cashbackAmount = floatval($evaluation['amount']) * (floatval($evaluation['cashback_percentage']) / 100);
                        @endphp
                        <div class="col-md-6 mt-3">
                            <label>Valor CashBack</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control"
                                        value="{{ $cashbackAmount ? number_format($cashbackAmount, 2, ',', '') : '0,00' }}"
                                        disabled>
                            </div>
                        </div>
                    @endif
                    @if($evaluation['with_cashback'])
                      <div class="col-md-6 mt-3">
                          <label>Data de Expiração do CashBack</label>
                          <input type="text" class="form-control"
                              value="{{ $evaluation['cashback_expiration_date'] ? \Carbon\Carbon::parse($evaluation['cashback_expiration_date'])->format('d/m/Y H:i') : '-' }}"
                              disabled>
                      </div>
                    @endif
                </div>
            </div>
            <div class="modal-footer"
                 style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
