@extends('layouts.app', ['activePage' => 'evaluations'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Avaliações
                                </h5>
                            </div>
                            <div class="col-md-6 d-flex justify-content-end">
                                <div class="d-flex">
                                    <!-- <button type="button" class="btn btn-success mr-1" data-toggle="modal"
                                            data-target="#sendEvaluationModal">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{url('icon/icon_plus.png')}}" width="15px"
                                             style="margin-top: -2px;"/>
                                        <label class="styleBotaoAdicionar">Enviar Avaliação</label>
                                    </button> -->
                                    <button type="button" class="btn btn-secondary" onclick="showNegociosUser(false)">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{url('icon/icon_filtro.png')}}" width="15px"
                                             style="margin-top: -2px;"/>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{ route('evaluations.index') }}" method="get"
                                      id="form_search_evaluations">
                                    @csrf
                                    <div class="form-row">
                                        <div class="col-md-4 mb-2">
                                            <select class="form-control" name="form_id" onchange="document.getElementById('form_search_evaluations').submit()">
                                                <option value="">Todos os Formulários</option>
                                                @foreach($forms as $form)
                                                    <option
                                                        value="{{ $form['id'] }}" {{ isset($form_id) && $form_id == $form['id'] ? 'selected' : '' }}>
                                                        {{ $form['name'] }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <select class="form-control" name="shipping_status" onchange="document.getElementById('form_search_evaluations').submit()">
                                                <option value="">Todos os Status</option>
                                                @foreach($statuses as $key => $status)
                                                    <option
                                                        value="{{ $key }}" {{ isset($shipping_status) && $shipping_status == $key ? 'selected' : '' }}>
                                                        {{ $status }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <select class="form-control" name="cashback_filter" onchange="document.getElementById('form_search_evaluations').submit()">
                                                <option value="">Todas as Avaliações</option>
                                                <option value="with_cashback" {{ isset($cashback_filter) && $cashback_filter == 'with_cashback' ? 'selected' : '' }}>Avaliações com Cashback</option>
                                                <option value="without_cashback" {{ isset($cashback_filter) && $cashback_filter == 'without_cashback' ? 'selected' : '' }}>Avaliações sem Cashback</option>
                                            </select>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="" id="evaluation-table">
                                <thead>
                                <tr>
                                    <th scope="col" style="text-align: center">Ações</th>
                                    <th scope="col">Envio</th>
                                    <th scope="col">Data</th>
                                    <th scope="col">Formulário</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">CashBack</th>
                                    @if (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
                                        <th scope="col">Valor</th>
                                    @endif
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($evaluations as $evaluation)
                                    <tr>
                                        <td style="text-align: center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-secondary" data-toggle="modal"
                                                        data-target="#responsesModal-{{ $evaluation['id'] }}"
                                                        style="margin-right: 10px">
                                                    <span class="fas fa-comments"></span>
                                                </button>
                                                <button type="button" class="btn btn-primary" data-toggle="modal"
                                                        data-target="#evaluationDetailModal-{{ $evaluation['id'] }}"
                                                        style="margin-right: 10px">
                                                    <img class="card-img-left example-card-img-responsive"
                                                         src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                                </button>
                                            </div>
                                        </td>
                                        <td>{{ $evaluation['sending_type'] }}</td>
                                        <td>{{ \Carbon\Carbon::parse($evaluation['shipping_date'])->format('d/m/Y H:i') }}</td>
                                        <td>
                                            @if (\App\Helpers\ApiUser::hasPermission('forms') && $evaluation['form']['deleted_at'] === null)
                                                <a href="{{ route('forms.show', $evaluation['form_id']) }}"
                                                   target="_blank">
                                                    {{ $evaluation['form']['name'] }}
                                                </a>
                                            @else
                                                {{ $evaluation['form']['name'] }}
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $badgeClass = '';
                                                switch ($evaluation['shipping_status']) {
                                                    case 'RESPONDED':
                                                        $badgeClass = 'badge bg-success';
                                                        break;
                                                    case 'SENT':
                                                        $badgeClass = 'badge bg-warning';
                                                        break;
                                                    case 'EXPIRED':
                                                        $badgeClass = 'badge bg-secondary';
                                                        break;
                                                    default:
                                                        $badgeClass = 'badge bg-secondary';
                                                }
                                            @endphp
                                            <span class="{{ $badgeClass }} p-2 text-light" style="width:100px">
                                                {{ ucfirst($statuses[$evaluation['shipping_status']] ?? $evaluation['shipping_status']) }}
                                            </span>
                                        </td>
                                        @if($evaluation['with_cashback'])
                                            <td>{{ $evaluation['cashback_percentage'] ? $evaluation['cashback_percentage'].'%' : '-' }}</td>
                                        @else
                                            <td>Sem CashBack</td>
                                        @endif
                                        @if (ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
                                            <td>{{ $evaluation['amount'] ? 'R$ '.number_format($evaluation['amount'], 2, ',', '') : '-' }}</td>
                                        @endif
                                    </tr>

                                    <!-- Modal de Respostas -->
                                    @include('evaluations.responses_modal', ['evaluation' => $evaluation])

                                    <!-- Modal de Detalhes da Avaliação -->
                                    @include('evaluations.evaluation_detail_modal', ['evaluation' => $evaluation, 'statuses' => $statuses])
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        {{ $evaluations->onEachSide(0)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('evaluations.subtitle')
                    </div>
                </div>
            </div>
        </div>

        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>

@endsection
