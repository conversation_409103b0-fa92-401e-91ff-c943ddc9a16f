<!-- <PERSON><PERSON><PERSON> de Avaliação -->
<div id="evaluationCarousel" class="carousel slide" data-ride="carousel" data-interval="false">
    <!-- Slides -->
    <div class="carousel-inner">
        @include('evaluations.respond.slides.welcome')
        @include('evaluations.respond.slides.anonymity')

        @foreach($evaluation['form']['topics'] as $topicIndex => $topic)
            @include('evaluations.respond.slides.topics', ['topic' => $topic])
        @endforeach

        @include('evaluations.respond.slides.thank_you')
    </div>
</div>

<script>
    $(document).ready(function () {
        $('#evaluationCarousel').carousel({
            touch: false
        });

        // Desativar o evento de arrastar no carrossel (touch e mouse)
        $('#evaluationCarousel').on('dragstart', function (e) {
            e.preventDefault();
        });

    });
</script>
