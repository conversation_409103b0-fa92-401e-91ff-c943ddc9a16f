<div class="modal fade" id="confirmar-envio-email" tabindex="-1" role="dialog"
     aria-labelledby="confirmar-envio-email-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmar-envio-email-label">Confirmar Envio de E-mail</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Deseja validar o email agora?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="pular-envio">Não
                </button>
                <button type="button" class="btn btn-primary" id="confirmar-envio">Sim</button>
            </div>
        </div>
    </div>
</div>

<div class="modal" tabindex="-1" role="dialog" id="modal-confirm-email" data-code=""
     data-formId="form-create-employee">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar email</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto">
                <div class="row">
                    <div class="col">
                        <input class="form-control" name="code" id="code" type="text"
                               placeholder="Insira o codigo enviado">
                        <div id="alert-code-confirm-email"></div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col">
                        <b style="font-size: 15px">Foi enviado um código de confirmação para seu e-mail,
                            caso não esteja na sua caixa de entrada, verifique sua caixa de span.</b>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-success" id="confirm-code">Confirmar</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        $('#confirmar-envio').on('click', function () {
            $('#confirmar-envio-email').modal('hide');
            confirmClientEmail();
        });

$('#pular-envio').on('click', function () {
    $('#confirmar-envio-email').modal('hide');
    let dataToSend = window.pendingClientData || {};
    sendRegistrationAndEvaluation(dataToSend);
    delete window.pendingClientData;
});

        function confirmClientEmail() {
            const email = document.getElementById('email').value;
            const name = document.getElementById('name').value;

            if (email) {
                const formData = new FormData();
                formData.append('email', email);
                formData.append('name', name);

                $('#loading').modal('show');

                $.ajax({
                    url: "{{route('send.email.client.confirm')}}",
                    type: 'post',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (response) {
                        const code = response.code;
                        const modal = document.getElementById('modal-confirm-email');
                        modal.dataset.code = code;
                        modal.dataset.email = email;
                        modal.dataset.name = name;

                        $('#loading').modal('hide');
                        $("#modal-confirm-email").modal('show');
                    },
                    error: function (response, textStatus, msg) {
                        $('#loading').modal('hide');
                        alert('Falha ao enviar email de confirmação!');
                    }
                });
            }
        }

        $('#confirm-code').on('click', function () {
            checkCode();
        });

function checkCode() {
    const modal = document.getElementById('modal-confirm-email');
    const code = document.getElementById('code').value;
    let dataToSend = window.pendingClientData || {};
    if (code == modal.dataset.code) {
        $('#email_validated').val('1');
        $('#modal-confirm-email').modal('hide');
        dataToSend.email_validated = '1';
        sendRegistrationAndEvaluation(dataToSend);
        delete window.pendingClientData;
    } else {
        const alert = document.getElementById('alert-code-confirm-email');
        alert.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show">
                    <button type="button" aria-hidden="true" class="close" data-dismiss="alert" aria-label="Close">
                        <i class="nc-icon nc-simple-remove"></i>
                    </button>
                    <span>Código inválido</span>
                </div>
            `;
    }
}

        function removeAlert() {
            const alert = document.getElementById('alert-code-confirm-email');
            alert.innerHTML = '';
        }

        setInterval(removeAlert, 5000);
    });
</script>
