@php use App\Helpers\StringMask; @endphp

<div class="modal fade" id="{{ $modalId }}" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white text-center">
                <h4 class="modal-title w-100"><strong>Deixe sua opinião sobre nós</strong></h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            
            <div class="modal-body text-center" style="max-height: calc(100vh - 200px); overflow-y: auto;">
                <p class="lead">Sua opinião é muito importante para nós!</p>
                <p>Ajude-nos a melhorar deixando sua avaliação no Google.</p>
                <div class="star-rating my-3" style="display: block">
                    @for($i = 5; $i >= 1; $i--)
                        <input type="radio" id="star{{ $i }}-g-review" name="rating" value="{{ $i }}" @if($i === 5) checked @endif>
                        <label for="star{{ $i }}-g-review">&#9733;</label>
                    @endfor
                </div>
                <a href="{{$url_google}}" target="_blank" class="btn btn-lg btn-primary mt-3 px-4 rounded-lg">
                    <strong>⭐ Avaliar Agora no Google</strong>
                </a>
            </div>
        </div>
    </div>
</div>
