@php
    use App\Helpers\ApiUser;
    use App\Helpers\SystemHelper;
    $layout = ApiUser::get() != null ? 'layouts.app' : 'layouts.guest';
@endphp

@extends($layout)

@section('content')
    <div class="container mt-0">
        @if(isset($errorMessage))
            <div class="alert-danger text-center" style="padding: 20px; border-radius: 5px;">
                {{ $errorMessage }}
                @if($errorMessage == 'A avaliação já foi respondida.' && ApiUser::get() == null)
                    <p class="mt-1">Para entrar no sistema clique no botão abaixo:</p>
                    <a href="{{ route('login') }}" class="btn btn-primary mt-1">Entrar</a>
                @endif
            </div>
        @else
            @include('evaluations.respond.modals.confirm_envio_email')
            @include('evaluations.respond.components.carousel')
            @include('evaluations.respond.respond_css')
            @include('evaluations.respond.modals.loading')
            <script>
                @if(!isset($errorMessage))

                    window.evaluationData = {};
                    window.evaluationData.anonymous = 1; //mesmo valor default do input
                    window.evaluationData.responses = {};
                    window.evaluationData.justifications = {};

                    function sendRegistrationAndEvaluation(clientDataObject) {
                      let dataToSend = {
                          ...window.evaluationData,
                          client: clientDataObject,
                          _token: '{{ csrf_token() }}'
                      };

                      showLoading('Enviando seus dados...');

                      $.ajax({
                          url: "{{ route('evaluations.respond', ['token' => $evaluation['token']]) }}",
                          method: 'POST',
                          data: dataToSend,
                          success: function (data) {
                              if (data.error) {
                                  alert(data.msg);
                                  hideLoading();
                                  return;
                              }

                              hideLoading();
                              $('.carousel-item').removeClass('active');

                              $('#evaluationCarousel .carousel-item').each(function (index, element) {
                                  if (element.id !== 'thankYouSlide') {
                                      $(element).remove();
                                  }
                              });
                              if(window.evaluationData.anonymous == 1){
                                $("#thankYouMessage").hide();
                              }
                              $('#thankYouSlide').addClass('active');
                              $('#evaluationCarousel').carousel('to', $('#thankYouSlide').index());
                          },
                          error: function (error) {
                              hideLoading();
                              alert('Ocorreu um erro ao enviar sua avaliação.');
                          }
                      });
                  }

                $(document).ready(function () {
                    $('.toggle-justification').on('click', function (e) {
                        e.preventDefault();
                        let $button = $(this);
                        let questionId = $button.data('question-id');
                        let justificationField = $('#justification-field-' + questionId);

                        if (justificationField.is(':visible')) {
                            $button.text('Adicionar Justificativa');
                        } else {
                            $button.text('Ocultar Justificativa');
                        }

                        justificationField.slideToggle(function () {
                            if (justificationField.is(':visible')) {
                                $button.text('Ocultar Justificativa');
                            } else {
                                $button.text('Adicionar Justificativa');
                            }
                        });
                    });

                    let clientRegistered = {{ isset($evaluation['client']) ? 'true' : 'false' }}; // Declare clientRegistered
                    window.clientDataFetched = false;

                    $('#startEvaluation').on('click', function () {
                        $('#evaluationCarousel').carousel('next');
                    });

                    $('#startEvaluation').prop('disabled', false);

                    $('#evaluationCarousel').on('slid.bs.carousel', function () {
                        let activeSlide = $('#evaluationCarousel .carousel-item.active').attr('id');
                        if (activeSlide === 'registrationSlide') {
                            if (!window.clientDataFetched) {
                                window.clientDataFetched = true;
                                let cpf = $('#cpf').val();
                                cpf = cpf.replace(/\D/g, '');

                                showLoading('Verificando CPF...');

                                $.ajax({
                                    url: '<?= env('API_URL') ?>/api/user/show/by-cpf',
                                    method: 'GET',
                                    data: {cpf: cpf},
                                    beforeSend: function (xhr) {
                                        xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                                    },
                                    success: function (data) {
                                        hideLoading();
                                        fillClientFormWithData(data);
                                        disableClientFormFields();
                                        alert('Usuário já existe com outro perfil de login. Por favor, complete o cadastro.');
                                    },
                                    error: function (xhr) {
                                        hideLoading();
                                        if (xhr.status === 404) {
                                            $('#user_exists').val('0');
                                        } else {
                                            alert('Erro ao verificar CPF.');
                                        }
                                    }
                                });
                            }
                        }
                    });

                    function fillClientFormWithData(data) {
                        if (data.user) {
                            $('#name').val(data.user.name);
                            $('#email').val(data.user.email);
                            $('#birth_date').val(data.user.birth_date);
                            $('#gender').val(data.user.gender);
                            $('#phone_number_1').val(data.user.phone_number_1);
                            $('#phone_number_2').val(data.user.phone_number_2);
                            $('#user_exists').val('1');
                            $('#existing_user_cpf').val(data.user.cpf);
                        }
                    }

                    function disableClientFormFields() {
                        $('#name, #email, #birth_date, #gender, #phone_number_1, #phone_number_2, #password, #password_confirmation')
                            .prop('disabled', true);
                    }

                    $('#nextToTopics').on('click', function () {
                        $('#evaluationCarousel').carousel('next');
                    });

                    $('.next-topic').on('click', function () {
                        let currentItem = $(this).closest('.carousel-item');
                        let unansweredQuestions = [];
                        let questionIds = [];

                        currentItem.find('input[name^="responses"]').each(function () {
                            let name = $(this).attr('name');
                            let questionId = name.match(/\d+/)[0];
                            if ($.inArray(questionId, questionIds) === -1) {
                                questionIds.push(questionId);
                            }
                        });

                        questionIds.forEach(function (id) {
                            if (currentItem.find('input[name="responses[' + id + ']"]:checked').length === 0) {
                                unansweredQuestions.push(id);
                            }
                        });

                        if (unansweredQuestions.length > 0) {
                            alert('Por favor, responda todas as perguntas antes de prosseguir.');
                            return;
                        }

                        let totalTopics = {{ count($evaluation['form']['topics']) }};
                        let currentCarouselIndex = $('#evaluationCarousel .carousel-item').index(currentItem) - 2;

                        if (currentCarouselIndex === (totalTopics - 1)) {
                            const avgStarRating = submitEvaluation();
                            let businessUrlGoogle = "{{ $evaluation['business']['url_google'] ?? '' }}";
                            let EVALUATION_AVG_STAR_RATING = {{ SystemHelper::get()['EVALUATION_AVG_STAR_RATING'] ?? 4.5 }};
                            let shouldOpenGooglePage = avgStarRating >= EVALUATION_AVG_STAR_RATING && businessUrlGoogle != '';

                            if (shouldOpenGooglePage) {
                                const link = document.createElement('a');
                                link.href = businessUrlGoogle;
                                link.target = '_self';
                                link.rel = 'noopener noreferrer';
                                link.style.display = 'none';
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                            }
                        } else {
                            $('#evaluationCarousel').carousel('next');
                        }
                    });

                    $('.back-topic').on('click', function () {
                        $('#evaluationCarousel').carousel('prev');
                    });

                    $('.back-registration').on('click', function () {
                        if (window.evaluationData.anonymous != null && window.evaluationData.anonymous === 0 && !clientRegistered) {
                            let totalTopics = {{ count($evaluation['form']['topics']) }};
                            $('#evaluationCarousel').carousel(totalTopics + 1);
                        } else {
                            $('#evaluationCarousel').carousel('prev');
                        }
                    });

                    $('#submitEvaluation').on('click', submitEvaluation);

                    function submitEvaluation() {
                        let responses = {};
                        let justifications = {};
                        let starSum = 0;
                        let starQuestionsCount = 0;
                        
                        $('input[name^="responses"]:checked').each(function () {
                            let questionId = $(this).attr('name').match(/\d+/)[0];
                            let inputId = $(this).attr('id');
                            let value = $(this).val();
                            if (inputId.includes('star')) {
                                starSum = starSum + Number(value);
                                starQuestionsCount++;
                            }
                            responses[questionId] = value;
                        });

                        $('textarea[name^="justifications"]').each(function () {
                            let questionId = $(this).attr('name').match(/\d+/)[0];
                            justifications[questionId] = $(this).val();
                        });

                        let totalQuestions = {{ array_sum(array_map(function($topic) { return count($topic['questions']); }, $evaluation['form']['topics'])) }};

                        if (Object.keys(responses).length < totalQuestions) {
                            alert('Por favor, responda todas as perguntas antes de enviar.');
                            return;
                        }

                        const avgStarRating = starSum / starQuestionsCount;

                        window.evaluationData = {
                            anonymous: window.evaluationData.anonymous,
                            responses: responses,
                            justifications: justifications,
                        };

                        sendEvaluationData();
                        return avgStarRating;
                    }

                    $('#clientRegistrationForm').on('submit', function (e) {
                        e.preventDefault();
                        let clientData = $(this).serializeArray();
                        let clientDataObject = {};
                        
                        $.each(clientData, function () {
                            clientDataObject[this.name] = this.value;
                        });

                        let dataToValidate = {
                            ...window.evaluationData,
                            client: clientDataObject,
                            _token: '{{ csrf_token() }}'
                        };

                        showLoading('Validando seus dados...');

                        $.ajax({
                            url: "{{ route('evaluations.respond.validate.store', ['token' => $evaluation['token']]) }}",
                            method: 'POST',
                            data: dataToValidate,
                            beforeSend: function (xhr) {
                                clearValidationErrors();
                            },
                            success: function (data) {
                                if (data.error) {
                                    alert(data.msg);
                                    hideLoading();
                                    return;
                                }
                                hideLoading();
                                let userExists = $('#user_exists').val();
                                if (userExists === '1') {
                                    sendRegistrationAndEvaluation(clientDataObject);
                                    return;
                                }
                                window.pendingClientData = clientDataObject;
                                $('#confirmar-envio-email').modal('show');
                            },
                            error: function (xhr) {
                                hideLoading();
                                if (xhr.status === 422) {
                                    displayValidationErrors(xhr.responseJSON.errors);
                                } else {
                                    alert('Ocorreu um erro ao validar seus dados.');
                                }
                            }
                        });
                    });

                    function sendEvaluationData() {
                        let dataToSend = {
                            ...window.evaluationData,
                            _token: '{{ csrf_token() }}'
                        };

                        showLoading('Enviando sua avaliação...');

                        $.ajax({
                            url: "{{ route('evaluations.respond', ['token' => $evaluation['token']]) }}",
                            method: 'POST',
                            data: dataToSend,
                            success: function (data) {
                                hideLoading();
                                $('.carousel-item').removeClass('active');

                                // Remove todos os slides exceto o de agradecimento
                                $('#evaluationCarousel .carousel-item').each(function (index, element) {
                                    if (element.id !== 'thankYouSlide') {
                                        $(element).remove();
                                    }
                                });
                                
                                // Ir direto para o slide de agradecimento
                                $('#thankYouSlide').addClass('active');
                                $('#evaluationCarousel').carousel('to', $('#thankYouSlide').index());
                                
                                
                                let promptMessage = '';
                                // Corrigido: Comparar com o número 1 em vez da string '1'
                                let isAnonymous = window.evaluationData.anonymous === 1;
                                let hasCashback = {{ $evaluation['with_cashback'] ? 'true' : 'false' }};

                                $('#anonymousCashbackMessage').show();
                                
                                if (isAnonymous) {
                                    if (hasCashback) {
                                        promptMessage = 'Para registrarmos o seu Cashback, precisamos que você se cadastre no sistema, mas não se preocupe, sua avaliação já foi concluída e continuará sendo anônima.';
                                    } else {
                                        promptMessage = 'Cadastre-se para receber atualizações do sistema Visão Negócio. Não se preocupe, sua avaliação continuará anônima.';
                                    }
                                } else {
                                    if (hasCashback) {
                                        promptMessage = 'Para registrarmos o seu Cashback, precisamos que você se cadastre no sistema.';
                                    } else {
                                        promptMessage = 'Cadastre-se para receber atualizações do sistema Visão Negócio.';
                                    }
                                }
                                
                                
                                $('#anonymousCashbackMessage p').text(promptMessage);
                                $('#anonymousCashbackMessage').show();
                            },
                            error: function (error) {
                              if (error.responseJSON){
                                if(error.responseJSON['message'] !== undefined && error.responseJSON['message'] !== null){
                                  alert(error.responseJSON['message']);
                                  hideLoading();
                                  return;
                                }
                              }
                              alert('Ocorreu um erro ao enviar seus dados.');
                              hideLoading();
                            }
                        });
                    }

                    $("#term-checkbox").click(showHideFormButton);

                    function showHideFormButton() {
                        if ($("#term-checkbox").is(":checked")) {
                            $("#submit-button").prop("disabled", false);
                        } else {
                            $("#submit-button").prop("disabled", true);
                        }
                    }

                    function displayValidationErrors(errors) {
                        clearValidationErrors();
                        $.each(errors, function (fieldName, errorMessages) {
                            fieldName = fieldName.replace('client.', '');
                            let field = $('#clientRegistrationForm').find('[name="' + fieldName + '"]');
                            field.addClass('is-invalid');
                            $("#error-" + fieldName).text(errorMessages[0]);
                        });
                    }

                    function clearValidationErrors() {
                        $('#clientRegistrationForm .invalid-feedback').remove();
                        $('#clientRegistrationForm .is-invalid').removeClass('is-invalid');
                        $('#clientRegistrationForm .form-control').each(function () {
                            let fieldName = $(this).attr('name');
                            $("#error-" + fieldName).text('');
                        });
                    }
                });
                @endif
            </script>
        @endif
    </div>
@endsection
