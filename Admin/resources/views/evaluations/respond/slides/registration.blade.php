@php use App\Helpers\StringMask; @endphp
<div class="carousel-item" id="registrationSlide">
    <div class="slide-container">
        <h3>Cadastro de Cliente</h3>
        <form id="clientRegistrationForm">
            <div class="form-row">
                <div class="col-md-12"><p class="form-title">Dad<PERSON> pessoais</p></div>
                <div class="col-md-12">
                    <div class="form-group">
                        @include('components.inputs.text', [
                            'name' => 'name',
                            'label' => 'Nome completo',
                            'value' => old('name') ?? '',
                            'placeholder' => 'Digite o nome completo do cliente',
                            'withErrorText' => true
                        ])
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="gender">Gênero</label>
                        <select id="gender" name="gender"
                                class="form-select form-control @error('gender') is-invalid @enderror"
                                aria-label="Default select example" required>
                            <option value="other">-- Não Informar --</option>
                            <option value="m">Masculino</option>
                            <option value="f">Feminino</option>
                        </select>
                        @error('gender')
                        <span class="invalid-feedback" role="alert">
                <strong>{{$message}}</strong>
            </span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        @include('components.inputs.date', [
                            'name' => 'birth_date',
                            'label' => 'Data de nascimento',
                            'value' => old('birth_date') ?? '',
                            'withErrorText' => true,
                            'required' => false
                        ])
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        @include('components.inputs.text', [
                            'name' => 'cpf',
                            'label' => 'CPF',
                            'value' => StringMask::cpf($evaluation['client_cpf']),
                            'placeholder' => 'Digite o CPF',
                            'withErrorText' => true,
                            'readonly' => true
                        ])
                    </div>
                </div>
                <div class="col-md-12"><p class="form-title">Contato</p></div>
                <div class="col-md-6">
                    <div class="form-group">
                        @include('components.inputs.text', [
                            'name' => 'phone_number_1',
                            'label' => 'Telefone/Celular 1',
                            'value' => old('phone_number_1') ?? '',
                            'placeholder' => 'Digite o número do telefone do cliente',
                            'withErrorText' => true,
                            'class' => 'phone-input'
                        ])
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        @include('components.inputs.text', [
                            'name' => 'phone_number_2',
                            'label' => 'Telefone/Celular 2',
                            'value' => old('phone_number_2') ?? '',
                            'placeholder' => 'Digite o número do telefone do cliente',
                            'required' => false,
                            'class' => 'phone-input'
                        ])
                    </div>
                </div>
                <div class="col-md-12"><p class="form-title">Login</p></div>
                <div class="col-md-6">
                    <div class="form-group">
                        @include('components.inputs.text', [
                            'name' => 'email',
                            'type' => 'email',
                            'label' => 'E-mail',
                            'value' => old('email') ?? '',
                            'placeholder' => 'Digite o e-mail do cliente',
                            'withErrorText' => true,
                            'required' => true
                        ])
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        @include('components.inputs.password', ['name' => 'password', 'label' => 'Senha', 'placeholder' => 'Digite a senha do cliente', 'required' => true , 'withErrorText' => true])
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        @include('components.inputs.password', ['name' => 'password_confirmation', 'label' => 'Confirme a Senha', 'placeholder' => 'Confirme a senha', 'required' => true, 'withErrorText' => true])
                    </div>
                </div>
                <div class="col-md-12"><p class="form-title">Empreendedor</p></div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="is_entrepreneur">Você é um Empreendedor? <span style="color: red">*</span></label>
                        <select class="form-control" name="is_entrepreneur" id="selectIsEntreprenur" required>
                            <option value="" selected disabled>Selecione se é empreendedor</option>
                            <option value="1">Sim</option>
                            <option value="0">Não</option>
                        </select>
                        <small id="error-is_entrepreneur" class="text-danger"></small>
                    </div>
                </div>
                <input type="hidden" name="user_exists" id="user_exists" value="0">
                <input type="hidden" name="existing_user_cpf" id="existing_user_cpf" value="">
                <input type="hidden" name="email_validated" id="email_validated" value="0">
                <div class="modal fade" id="confirmar-envio-email" tabindex="-1" role="dialog"
                     aria-labelledby="confirmar-envio-email-label" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="confirmar-envio-email-label">Confirmar Envio de E-mail</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p>Deseja validar o email agora?</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="pular-envio">
                                    Não
                                </button>
                                <button type="button" class="btn btn-primary" id="confirmar-envio">Sim</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-left: 20px; margin-top: 8px" class="mb-4 justify-content-start">
                <input type="checkbox" class="form-check-input" id="term-checkbox" name="term"
                       required>
                <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os
                    dados passados de acordo com o termo em anexo <a
                        href="{{route('data-processing-agreement')}}"
                        target="_blank">(clique aqui para ler o termo).</a></label>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <a class="btn btn-secondary back-registration">Voltar</a>
                <button id="submit-button" disabled class="btn btn-primary" type="submit">Cadastrar</button>
            </div>
        </form>

    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        applyMasksPhones();
    });

    function applyMasksPhones() {
        // Selecionar os campos de telefone
        const phone1 = document.getElementById('phone_number_1');
        const phone2 = document.getElementById('phone_number_2');

        // Função para aplicar a máscara
        function maskPhone(event) {
            const input = event.target;
            let value = input.value;

            // Remover todos os caracteres que não são números
            value = value.replace(/\D/g, '');

            // Limitar a 11 dígitos
            if (value.length > 11) {
                value = value.slice(0, 11);
            }

            // Aplicar a máscara conforme o número de dígitos
            if (value.length > 10) {
                // Formato: (99) 99999-9999
                value = value.replace(/^(\d{2})(\d{5})(\d{4}).*/, '($1) $2-$3');
            } else if (value.length > 5) {
                // Formato: (99) 9999-9999
                value = value.replace(/^(\d{2})(\d{4})(\d{0,4}).*/, '($1) $2-$3');
            } else if (value.length > 2) {
                value = value.replace(/^(\d{2})(\d{0,5})/, '($1) $2');
            } else {
                value = value.replace(/^(\d{0,2})/, '$1');
            }

            input.value = value;
        }

        function handlePaste(event) {
            const paste = (event.clipboardData || window.clipboardData).getData('text');
            const input = event.target;
            let value = paste.replace(/\D/g, '');

            if (value.length > 11) {
                value = value.slice(0, 11);
            }

            if (value.length > 10) {
                value = value.replace(/^(\d{2})(\d{5})(\d{4}).*/, '($1) $2-$3');
            } else if (value.length > 5) {
                value = value.replace(/^(\d{2})(\d{4})(\d{0,4}).*/, '($1) $2-$3');
            } else if (value.length > 2) {
                value = value.replace(/^(\d{2})(\d{0,5})/, '($1) $2');
            } else {
                value = value.replace(/^(\d{0,2})/, '($1');
            }

            input.value = value;

            event.preventDefault();
        }

        if (phone1) {
            phone1.addEventListener('input', maskPhone);
            phone1.addEventListener('paste', handlePaste);
        }

        if (phone2) {
            phone2.addEventListener('input', maskPhone);
            phone2.addEventListener('paste', handlePaste);
        }
    }

    $("#term-checkbox").click(showHideFormButton);

    function showHideFormButton() {
        if ($("#term-checkbox").is(":checked")) {
            $("#submit-button").prop("disabled", false);
        } else {
            $("#submit-button").prop("disabled", true);
        }
    }

    function displayValidationErrors(errors) {
        // Limpar mensagens de erro anteriores
        clearValidationErrors();

        // Iterar sobre os erros e exibir as mensagens
        $.each(errors, function (fieldName, errorMessages) {
            //remover "client." do nome do campo
            fieldName = fieldName.replace('client.', '');
            // Procurar o campo correspondente
            let field = $('#clientRegistrationForm').find('[name="' + fieldName + '"]');

            // Adicionar a classe 'is-invalid'
            field.addClass('is-invalid');

            $("#error-" + fieldName).text(errorMessages[0]);
        });
    }

    function clearValidationErrors() {
        $('#clientRegistrationForm .invalid-feedback').remove();
        $('#clientRegistrationForm .is-invalid').removeClass('is-invalid');

        $('#clientRegistrationForm .form-control').each(function () {
            let fieldName = $(this).attr('name');
            $("#error-" + fieldName).text('');
        });
    }
</script>
