<div class="carousel-item">
    <div class="slide-container text-center">
        <h4>Deseja responder essa avaliação de forma anônima?</h4>
        <div class="form-check form-check-inline mt-3">
            <input class="form-check-input" type="radio" name="anonymous" id="anonymousYes" value="1" checked>
            <label class="form-check-label" for="anonymousYes">Sim</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="anonymous" id="anonymousNo" value="0">
            <label class="form-check-label" for="anonymousNo">Não</label>
        </div>
        <div class="mt-4">
            <button class="btn btn-primary" id="nextToTopics">Próximo</button>
        </div>
    </div>
</div>
<script>
    let clientRegistered = {{ isset($evaluation['client']) ? 'true' : 'false' }};

    $('input[name="anonymous"]').on('change', function () {
        if (this.value === '1') {
          window.evaluationData.anonymous = 1;
        } else {
          window.evaluationData.anonymous = 0;
        }
    });
</script>
