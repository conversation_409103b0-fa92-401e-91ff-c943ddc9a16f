<div class="carousel-item" id="finalizationSlide">
    <div class="slide-container text-center">
        <h2>Obrigado por sua participação!</h2>

        <div id="anonymousCashbackMessage" style="display: none;">
            <p></p>
        </div>
        @if($evaluation['client_id'] && ApiUser::get() == null)
            <p>Para entrar no sistema clique no botão abaixo:</p>
            <a href="{{ route('login') }}" class="btn btn-primary mt-1">Entrar</a>
        @else
            <div id="loginButtonContainer" style="display: none;">
                <p>Para entrar no sistema clique no botão abaixo:</p>
                <a href="{{ route('login') }}" class="btn btn-primary mt-1">Entrar</a>
            </div>
        @endif
    </div>
    <div id="registrationFormContainer" class="mt-4">
        <div class="slide-container">
            <form id="clientRegistrationForm">
                <div class="form-row">
                    <div class="col-md-12"><p class="form-title">Dados pessoais</p></div>
                    <div class="col-md-6">
                        <div class="form-group">
                            @include('components.inputs.text', [
                                'name' => 'cpf',
                                'label' => 'CPF',
                                'value' => old('cpf') ?? '',
                                'placeholder' => 'Digite o CPF',
                                'withErrorText' => true,
                                'readonly' => false,
                                'required' => true
                            ])
                        </div>
                    </div>
                    <div id="cpfCheckStatusMessage" class="col-md-12 mb-3" style="min-height: 20px;"></div> <!-- Status Message Div -->
                    
                    <!-- Container for all fields except CPF -->
                    <div id="otherFields" style="display: none;" class="col-md-12">
                        <div class="form-group">
                            @include('components.inputs.text', [
                                'name' => 'name',
                                'label' => 'Nome completo',
                                'value' => old('name') ?? '',
                                'placeholder' => 'Digite o nome completo do cliente',
                                'withErrorText' => true
                            ])
                        </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="gender">Gênero</label>
                            <select id="gender" name="gender"
                                    class="form-select form-control @error('gender') is-invalid @enderror"
                                    aria-label="Default select example" required>
                                <option value="other">-- Não Informar --</option>
                                <option value="m">Masculino</option>
                                <option value="f">Feminino</option>
                            </select>
                            @error('gender')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{$message}}</strong>
                            </span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            @include('components.inputs.date', [
                                'name' => 'birth_date',
                                'label' => 'Data de nascimento',
                                'value' => old('birth_date') ?? '',
                                'withErrorText' => true,
                                'required' => false
                            ])
                        </div>
                    </div>
                    {{-- CPF field moved to the top --}}
                    <div class="col-md-12"><p class="form-title">Contato</p></div>
                    <div class="col-md-6">
                        <div class="form-group">
                            @include('components.inputs.text', [
                                'name' => 'phone_number_1',
                                'label' => 'Telefone/Celular 1',
                                'value' => old('phone_number_1') ?? '',
                                'placeholder' => 'Digite o número do telefone do cliente',
                                'withErrorText' => true,
                                'class' => 'phone-input'
                            ])
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            @include('components.inputs.text', [
                                'name' => 'phone_number_2',
                                'label' => 'Telefone/Celular 2',
                                'value' => old('phone_number_2') ?? '',
                                'placeholder' => 'Digite o número do telefone do cliente',
                                'required' => false,
                                'class' => 'phone-input'
                            ])
                        </div>
                    </div>
                    <div class="col-md-12"><p class="form-title">Login</p></div>
                    <div class="col-md-6">
                        <div class="form-group">
                            @include('components.inputs.text', [
                                'name' => 'email',
                                'type' => 'email',
                                'label' => 'E-mail',
                                'value' => old('email') ?? '',
                                'placeholder' => 'Digite o e-mail do cliente',
                                'withErrorText' => true,
                                'required' => true
                            ])
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            @include('components.inputs.password', ['name' => 'password', 'label' => 'Senha', 'placeholder' => 'Digite a senha do cliente', 'required' => true , 'withErrorText' => true])
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            @include('components.inputs.password', ['name' => 'password_confirmation', 'label' => 'Confirme a Senha', 'placeholder' => 'Confirme a senha', 'required' => true, 'withErrorText' => true])
                        </div>
                    </div>
                    <div class="col-md-12"><p class="form-title">Empreendedor</p></div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="is_entrepreneur">Você é um Empreendedor? <span style="color: red">*</span></label>
                            <select class="form-control" name="is_entrepreneur" id="selectIsEntreprenur" required>
                                <option value="" selected disabled>Selecione se é empreendedor</option>
                                <option value="1">Sim</option>
                                <option value="0">Não</option>
                            </select>
                            <small id="error-is_entrepreneur" class="text-danger"></small>
                        </div>
                    </div>
                    {{-- Removed old hidden fields --}}
                    <input type="hidden" name="cpf_check_status" id="cpf_check_status" value="not_checked">
                    <input type="hidden" name="evaluation_sending_type" id="evaluation_sending_type" value="{{ $evaluation['sending_type'] ?? '' }}">
                    <input type="hidden" name="evaluation_email" id="evaluation_email" value="{{ $evaluation['email'] ?? '' }}">
                    <input type="hidden" name="evaluation_phone" id="evaluation_phone" value="{{ $evaluation['number_phone'] ?? '' }}">
                    <input type="hidden" name="email_validated" id="email_validated" value="0"> {{-- Keep this for now, might be used in email validation flow --}}
                </div>

                <div id="termContainer" style="display: none; margin-left: 20px; margin-top: 8px" class="mb-4 justify-content-start">
                    <input type="checkbox" class="form-check-input" id="term-checkbox" name="term"
                           required>
                    <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os
                        dados passados de acordo com o termo em anexo <a
                            href="{{route('data-processing-agreement')}}"
                            target="_blank">(clique aqui para ler o termo).</a></label>
                </div>

                <div id="submitContainer" style="display: none" class="d-flex justify-content-between mt-4">
                    <button id="submit-button" disabled class="btn btn-primary" type="submit">Cadastrar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        applyMasksPhones();
        $("#cpf").mask('000.000.000-00');

        // --- Início: Lógica de Verificação de CPF ---

        // Adiciona listener ao campo CPF
        $('#cpf').on('input', function() {
            let cpfValue = $(this).val();
            let cleanCpf = cpfValue.replace(/\D/g, ''); // Remove non-digits

            // Limpa a mensagem de status se o CPF for modificado e não tiver 11 dígitos
            if (cleanCpf.length !== 11) {
                $('#cpfCheckStatusMessage').html('');
                $('#cpf_check_status').val('not_checked');
                // Reseta e esconde o formulário se o CPF for limpo ou incompleto
                resetFormFields();
            }

            // Verifica se o CPF tem 11 dígitos para iniciar a verificação
            if (cleanCpf.length === 11) {
                handleCpfCheck(cleanCpf);
            }
        });

        function handleCpfCheck(cpf) {
            const apiUrl = '{{ env('API_URL') }}/api/user/check-cpf/' + cpf;
            const statusMessageDiv = $('#cpfCheckStatusMessage');
            const cpfStatusInput = $('#cpf_check_status');

            statusMessageDiv.html('<span class="text-info"><i class="fas fa-spinner fa-spin"></i> Verificando CPF...</span>');
            cpfStatusInput.val('checking');
            // Desabilitar o botão de submit enquanto verifica
            $("#submit-button").prop("disabled", true);

            $.ajax({
                url: apiUrl,
                method: 'GET',
                // Não precisa de token para este endpoint público
                success: function(data) {
                    cpfStatusInput.val(data.status);
                    updateRegistrationFormUI(data.status, data.userData);
                    // Re-habilita o botão de submit APÓS a verificação e atualização da UI
                    // A função showHideFormButton será chamada dentro de updateRegistrationFormUI se necessário
                },
                error: function(xhr) {
                    console.error("Erro ao verificar CPF:", xhr);
                    cpfStatusInput.val('error');
                    updateRegistrationFormUI('error', null);
                     // Re-habilita o botão de submit em caso de erro também
                    showHideFormButton(); // Chama a função para verificar o estado do botão baseado no checkbox
                }
            });
        }

        function resetFormFields() {
            const form = $('#clientRegistrationForm');
            $('#otherFields').hide();
            $('#termContainer').hide();
            $('#submitContainer').hide();
            form.find('input[type="text"]:not([name="cpf"]), input[type="email"], input[type="date"], input[type="password"], select').each(function() {
                const field = $(this);
                field.removeClass('is-invalid').val('');
                // Limpa mensagens de erro específicas do campo (se houver)
                $("#error-" + field.attr('name')).text('');
            });
            // Resetar selects para a opção padrão (exceto gênero que tem 'other')
            form.find('select').not('#gender').prop('selectedIndex', 0);
            $('#gender').val('other'); // Valor padrão para gênero
            $('#selectIsEntreprenur').val(''); // Resetar select de empreendedor
            // Garantir que os campos de senha estejam visíveis por padrão
            $('#clientRegistrationForm').find('[name="password"]').closest('.form-group').parent().show();
            $('#clientRegistrationForm').find('[name="password_confirmation"]').closest('.form-group').parent().show();
            // Limpar mensagem de status do CPF
            $('#cpfCheckStatusMessage').html('');
        }

        function updateRegistrationFormUI(status, userData) {
            resetFormFields(); // Começa resetando o formulário
            const form = $('#clientRegistrationForm');
            const statusMessageDiv = $('#cpfCheckStatusMessage');
            const sendingType = $('#evaluation_sending_type').val();
            const evaluationEmail = $('#evaluation_email').val();
            const evaluationPhone = $('#evaluation_phone').val().replace(/\D/g, ''); // Pega telefone limpo da avaliação

            // Seletores dos campos
            const nameField = form.find('[name="name"]');
            const genderField = form.find('[name="gender"]');
            const birthDateField = form.find('[name="birth_date"]');
            const cpfField = form.find('[name="cpf"]'); // Já preenchido, mas pode precisar ser desabilitado
            const phone1Field = form.find('[name="phone_number_1"]');
            const phone2Field = form.find('[name="phone_number_2"]');
            const emailField = form.find('[name="email"]');
            const passwordField = form.find('[name="password"]');
            const passwordConfirmationField = form.find('[name="password_confirmation"]');
            const isEntrepreneurField = form.find('[name="is_entrepreneur"]');
            const passwordDiv = passwordField.closest('.form-group').parent();
            const passwordConfirmationDiv = passwordConfirmationField.closest('.form-group').parent();


            // Show/hide other fields based on CPF status
            $('#otherFields').hide();
            
            switch (status) {
                case 'not_found':
                    statusMessageDiv.html('<span class="text-info">CPF não encontrado. Por favor, preencha o formulário para se cadastrar.</span>');
                    $('#otherFields').show();
                    break;

                case 'deleted':
                    statusMessageDiv.html('<span class="text-warning">Encontramos um cadastro inativo para este CPF. Preencha o formulário para reativá-lo.</span>');
                    $('#otherFields').show();
                    if (userData) {
                        nameField.val(userData.name || '');
                        genderField.val(userData.gender || 'other');
                        birthDateField.val(userData.birth_date || '');
                        phone1Field.val(userData.phone_number_1 || '');
                        phone2Field.val(userData.phone_number_2 || '');
                        emailField.val(userData.email || '');
                        // Não preenche is_entrepreneur, deixa o usuário selecionar
                    }
                     applyMasksPhones(); // Reaplicar máscara após preencher telefone
                    break;

                case 'found_client':
                    statusMessageDiv.html('<span class="text-success">CPF já cadastrado como cliente. Verifique e atualize seu contato, se necessário.</span>');
                    $('#otherFields').show();
                    if (userData) {
                        nameField.val(userData.name || '');
                        genderField.val(userData.gender || 'other');
                        birthDateField.val(userData.birth_date || '');
                        phone2Field.val(userData.phone_number_2 || '');
                        isEntrepreneurField.val(userData.client?.is_entrepreneur ? '1' : '0');

                        // Habilita email ou telefone 1 baseado no envio da avaliação
                        if (sendingType === 'EMAIL' && evaluationEmail) {
                            emailField.val(evaluationEmail).prop('readonly', false).prop('disabled', false);
                            phone1Field.val(userData.phone_number_1 || ''); // Não bloqueia telefone 1
                        } else if (sendingType === 'WHATSAPP' && evaluationPhone) {
                            phone1Field.val(evaluationPhone).prop('readonly', false).prop('disabled', false); // Mantém editável se veio por WhatsApp
                            emailField.val(userData.email || ''); // Não bloqueia email
                        } else {
                             // Caso não tenha vindo de avaliação ou falte info, preenche mas não bloqueia
                             emailField.val(userData.email || '');
                             phone1Field.val(userData.phone_number_1 || '');
                        }
                         applyMasksPhones(); // Reaplicar máscara
                    }
                    // Esconde campos de senha
                    passwordDiv.hide();
                    passwordConfirmationDiv.hide();
                    passwordField.prop('required', false); // Remove required se escondido
                    passwordConfirmationField.prop('required', false);
                    break;

                case 'found_employee':
                case 'found_seller':
                    const profileType = status === 'found_employee' ? 'funcionário' : 'vendedor';
                    statusMessageDiv.html(`<span class="text-warning">CPF já cadastrado como ${profileType}. Complete seu cadastro como cliente.</span>`);
                    $('#otherFields').show();
                    if (userData) {
                        nameField.val(userData.name || '');
                        genderField.val(userData.gender || 'other');
                        birthDateField.val(userData.birth_date || '');
                        phone2Field.val(userData.phone_number_2 || '');

                        // Habilita email ou telefone 1 baseado no envio da avaliação
                        if (sendingType === 'EMAIL' && evaluationEmail) {
                            emailField.val(evaluationEmail).prop('readonly', false).prop('disabled', false);
                            phone1Field.val(userData.phone_number_1 || ''); // Não bloqueia
                        } else if (sendingType === 'WHATSAPP' && evaluationPhone) {
                            phone1Field.val(evaluationPhone).prop('readonly', false).prop('disabled', false); // Mantém editável se veio por WhatsApp
                            emailField.val(userData.email || ''); // Não bloqueia
                        } else {
                             emailField.val(userData.email || ''); // Não bloqueia
                             phone1Field.val(userData.phone_number_1 || ''); // Não bloqueia
                        }
                        isEntrepreneurField.prop('disabled', false); // Habilita campo empreendedor
                        applyMasksPhones(); // Reaplicar máscara
                    }
                     // Esconde campos de senha
                    passwordDiv.hide();
                    passwordConfirmationDiv.hide();
                    passwordField.prop('required', false);
                    passwordConfirmationField.prop('required', false);
                    break;

                case 'error':
                default:
                     statusMessageDiv.html('<span class="text-danger">Ocorreu um erro ao verificar o CPF. Tente novamente.</span>');
                     // Todos os campos já estão habilitados pelo reset
                     break;
            }

            // Após atualizar a UI, verifica se o botão de submit deve ser habilitado
            // (depende apenas do checkbox de termos agora, já que a verificação foi feita)
            // Exibe ou esconde termo e botão com base na visibilidade dos campos
            if ($('#otherFields').is(':visible')) {
                $('#termContainer').show();
                $('#submitContainer').show();
            } else {
                $('#termContainer').hide();
                $('#submitContainer').hide();
            }
            showHideFormButton();
        }


        // --- Fim: Lógica de Verificação de CPF ---

    }); // Fim do $(document).ready

    function applyMasksPhones() {
        // Selecionar os campos de telefone
        const phone1 = document.getElementById('phone_number_1');
        const phone2 = document.getElementById('phone_number_2');

        // Função para aplicar a máscara
        function maskPhone(event) {
            const input = event.target;
            let value = input.value;
            value = value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            if (value.length > 10) {
                value = value.replace(/^(\d{2})(\d{5})(\d{4}).*/, '($1) $2-$3');
            } else if (value.length > 5) {
                value = value.replace(/^(\d{2})(\d{4})(\d{0,4}).*/, '($1) $2-$3');
            } else if (value.length > 2) {
                value = value.replace(/^(\d{2})(\d{0,5})/, '($1) $2');
            } else {
                value = value.replace(/^(\d{0,2})/, '$1');
            }
            input.value = value;
        }

        function handlePaste(event) {
            const paste = (event.clipboardData || window.clipboardData).getData('text');
            const input = event.target;
            let value = paste.replace(/\D/g, '');
            if (value.length > 11) value = value.slice(0, 11);
            if (value.length > 10) {
                value = value.replace(/^(\d{2})(\d{5})(\d{4}).*/, '($1) $2-$3');
            } else if (value.length > 5) {
                value = value.replace(/^(\d{2})(\d{4})(\d{0,4}).*/, '($1) $2-$3');
            } else if (value.length > 2) {
                value = value.replace(/^(\d{2})(\d{0,5})/, '($1) $2');
            } else {
                value = value.replace(/^(\d{0,2})/, '$1');
            }
            input.value = value;
            event.preventDefault();
        }

        if (phone1) {
            phone1.addEventListener('input', maskPhone);
            phone1.addEventListener('paste', handlePaste);
        }

        if (phone2) {
            phone2.addEventListener('input', maskPhone);
            phone2.addEventListener('paste', handlePaste);
        }
    }

    $("#term-checkbox").click(showHideFormButton);

    function showHideFormButton() {
        if ($("#term-checkbox").is(":checked")) {
            $("#submit-button").prop("disabled", false);
        } else {
            $("#submit-button").prop("disabled", true);
        }
    }

    function displayValidationErrors(errors) {
        clearValidationErrors();
        $.each(errors, function (fieldName, errorMessages) {
            fieldName = fieldName.replace('client.', '');
            let field = $('#clientRegistrationForm').find('[name="' + fieldName + '"]');
            field.addClass('is-invalid');
            $("#error-" + fieldName).text(errorMessages[0]);
        });
    }

    function clearValidationErrors() {
        $('#clientRegistrationForm .invalid-feedback').remove();
        $('#clientRegistrationForm .is-invalid').removeClass('is-invalid');
        $('#clientRegistrationForm .form-control').each(function () {
            let fieldName = $(this).attr('name');
            $("#error-" + fieldName).text('');
        });
    }
</script>
