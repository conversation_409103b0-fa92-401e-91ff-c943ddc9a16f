<div class="carousel-item">
    <div class="topic-container">
        <h3 class="text-primary">{{ $topic['name'] }}</h3>
        @if(!empty($topic['description']))
            <p class="text-muted" style="font-size: 0.9em;">{{ $topic['description'] }}</p>
        @endif
        <hr>
        <div class="questions-wrapper">
            @foreach($topic['questions'] as $question)
                @include('evaluations.respond.slides.question', ['question' => $question])
            @endforeach
        </div>
        <div class="d-flex justify-content-between mt-4">
            <button class="btn btn-secondary back-topic">Voltar</button>
            <button class="btn btn-primary next-topic">Próximo</button>
        </div>
    </div>
</div>
