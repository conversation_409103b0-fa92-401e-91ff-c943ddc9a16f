<div class="card mb-4 shadow-sm">
    <div class="card-body">
        <h6 class="question-description">{{ $question['description'] }}</h6>
        <!-- Renderizar o tipo de resposta conforme o tipo de questão -->
        @if($question['evaluation_type'] === 'five_star')
            <div class="star-rating mt-0">
                @for($i = 5; $i >= 1; $i--)
                    <input type="radio" id="star{{ $i }}-{{ $question['id'] }}"
                           name="responses[{{ $question['id'] }}]" value="{{ $i }}">
                    <label class="mb-0" for="star{{ $i }}-{{ $question['id'] }}">&#9733;</label>
                @endfor
            </div>
        @elseif($question['evaluation_type'] === 'yes_no')
            <div class="form-check form-check-inline mt-2">
                <input class="form-check-input" type="radio"
                       name="responses[{{ $question['id'] }}]"
                       id="yes-{{ $question['id'] }}" value="Yes">
                <label class="form-check-label" for="yes-{{ $question['id'] }}">
                    Sim
                </label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio"
                       name="responses[{{ $question['id'] }}]" id="no-{{ $question['id'] }}"
                       value="No">
                <label class="form-check-label" for="no-{{ $question['id'] }}">
                    Não
                </label>
            </div>
        @endif

        <!-- Botão para mostrar justificativa -->
        <div class="mt-2">
            <a href="#" class="toggle-justification" data-question-id="{{ $question['id'] }}">Adicionar Justificativa</a>
        </div>

        <!-- Justificativa Opcional -->
        <div class="form-group mt-2 justification-field" id="justification-field-{{ $question['id'] }}" style="display: none;">
            <label for="justification-{{ $question['id'] }}">Justificativa (Opcional)</label>
            <textarea class="form-control" id="justification-{{ $question['id'] }}" name="justifications[{{ $question['id'] }}]" rows="2" placeholder="Digite sua justificativa aqui..."></textarea>
        </div>
    </div>
</div>
