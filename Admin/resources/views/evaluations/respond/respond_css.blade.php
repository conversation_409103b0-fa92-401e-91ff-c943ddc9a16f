<link href="https://fonts.googleapis.com/css2?family=Roboto+Slab&display=swap" rel="stylesheet">
<style>
    .star-rating {
        direction: rtl;
        display: inline-block;
        font-size: 2em;
    }

    .star-rating input[type=radio] {
        display: none;
    }

    .star-rating label {
        color: #ddd;
        cursor: pointer;
        transition: color 0.2s;
    }

    .star-rating input[type=radio]:checked ~ label,
    .star-rating label:hover,
    .star-rating label:hover ~ label {
        color: #f5b301;
    }

    .slide-container, .topic-container {
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 8px;
    }

    .questions-wrapper {
        max-height: 60vh;
        overflow-y: auto;
    }

    .card {
        border: none;
    }

    .btn-primary, .btn-secondary, .btn-success {
        min-width: 120px;
    }

    @media (max-width: 768px) {
        .star-rating {
            font-size: 1.5em;
        }
    }

    .question-description {
        font-family: 'Roboto Slab', serif;
        font-size: 1.1em;
    }

    .toggle-justification {
        color: #007bff;
        cursor: pointer;
        text-decoration: none;
        transition: color 0.2s;
    }

    .toggle-justification:hover {
        text-decoration: none;
        color: #0056b3;
    }
</style>
