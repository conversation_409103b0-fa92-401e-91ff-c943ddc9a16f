@php
    use App\Helpers\ApiUser;

    $user = ApiUser::get();
    $parameters = $user['business_selected']['parameters'];
    $formId = $parameters['form_id'] ?? null;

    $modalId = $modalId ?? 'sendEvaluationModal';
@endphp

<div class="modal fade" id="{{ $modalId }}" tabindex="-1" role="dialog" aria-labelledby="{{ $modalId }}Label"
     aria-hidden="true"
     data-form-id="{{ $formId }}">
    <div class="modal-dialog modal-lg" role="document">
        <form action="{{ route('evaluations.store') }}" method="POST" id="sendEvaluationForm">
            @csrf
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Enviar Avaliação <span id="cashbackText"></span></h5>
                    <button id="close_send" type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Avaliação Anônima -->
                    <div class="form-group form-check" style='display: none'>
                        <input type="checkbox" class="form-check-input" id="anonymous" name="anonymous" checked>
                        <label class="form-check-label" for="anonymous">Avaliação Anônima</label>
                    </div>

                    <!-- Tipo de Envio -->
                    <div class="form-group">
                        <label for="sending_type">Tipo de Envio <span style="color: red">*</span></label>
                        <select class="form-control" id="sending_type" name="sending_type" required disabled>
                            <option value="" disabled>Selecione o tipo de envio</option>
                            @php
                                $options = [];
                                if (ApiUser::get()['business_selected']['plan']['authorizedFunctions']['email_sending']) {
                                    $options[] = 'EMAIL';
                                }
                                if (ApiUser::get()['business_selected']['plan']['authorizedFunctions']['whatsapp_sending']) {
                                    $options[] = 'WHATSAPP';
                                }

                                $selectedOption = 'EMAIL';
                                if (in_array('WHATSAPP', $options)) {
                                    $selectedOption = 'WHATSAPP';
                                }
                            @endphp

                            @if(in_array('EMAIL', $options))
                                <option value="EMAIL" {{ $selectedOption == 'EMAIL' ? 'selected' : '' }}>Email</option>
                            @endif
                            @if(in_array('WHATSAPP', $options))
                                <option value="WHATSAPP" {{ $selectedOption == 'WHATSAPP' ? 'selected' : '' }}>WhatsApp</option>
                            @endif
                            <!-- <option value="SMS">SMS</option> -->
                        </select>
                    </div>
                    <!-- Email ou Número de Contato -->
                    <div class="form-group" id="emailField" style="display: none;">
                        <label for="email">Email <span style="color: red">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" disabled>
                    </div>
                    <div class="form-group" id="phoneField" style="display: none;">
                        <label for="number_phone">Número de Contato <span style="color: red">*</span></label>
                        <input type="text" class="phone-input form-control" id="number_phone" name="number_phone" disabled>
                    </div>
                    <div class="form-group" id="nameField" style="display: none;">
                        <label for="name">Nome <span style="color: red">*</span></label>
                        <div id="nameInputContainer">
                            <input type="text" class="form-control" id="name" name="name" required disabled>
                        </div>
                        <div>
                        <span id="nameLoadingIndicator" style="display: none; color: #000;">
                                <style>
                                    @keyframes spin {
                                        0% { transform: rotate(0deg); }
                                        100% { transform: rotate(360deg); }
                                    }
                                    .spinner {
                                        display: inline-block;
                                        width: 1em;
                                        height: 1em;
                                        border: 0.15em solid currentColor;
                                        border-right-color: transparent;
                                        border-radius: 50%;
                                        animation: spin .75s linear infinite;
                                        vertical-align: -0.125em;
                                    }
                                </style>
                                <span class="spinner"></span>
                                Carregando...
                            </span>
                        </div>
                        
                    </div>
                    <!-- Formulário de Avaliação -->
                    <div class="form-group">
                        <label for="form_id">Formulário de Avaliação <span style="color: red">*</span></label>
                        <select class="form-control" id="form_id" name="form_id" required disabled>
                            <option value="" disabled {{ is_null($formId) ? 'selected' : '' }}>Selecione o formulário
                            </option>
                            @foreach($forms as $form)
                                <option
                                    value="{{ $form['id'] }}" {{ (!is_null($formId) && $form['id'] == $formId) ? 'selected' : '' }}>
                                    {{ $form['name'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    @if(ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'])
                        <div class="form-group" id="priceField" style="display: none;">
                            <label for="amount">Valor do Produto/Serviço <span style="color: red">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text border-right-0"
                                      style="border-top-right-radius: 0px; border-bottom-right-radius: 0px">R$</span>
                                <input
                                    type="number"
                                    placeholder="Ex: 30.00"
                                    oninput="handleInput(this)"
                                    onblur="handleBlur(this)"
                                    min="0"
                                    step="0.01"
                                    class="form-control"
                                    id="amount"
                                    name="amount"
                                    required
                                >
                            </div>
                        </div>
                        <div class="form-row" id="cashbackFields" style="display: none;">
                            <div class="form-group col-md-6">
                                <label for="price">% Cashback</label>
                                <div class="input-group">
                                    <span class="input-group-text border-right-0"
                                          style="border-top-right-radius: 0px; border-bottom-right-radius: 0px">%</span>
                                    <input type="number" value="{{ $parameters['cashback_percentage'] ?? '0,00' }}"
                                           class="form-control" disabled readonly>
                                </div>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="price">Valor do Cashback</label>
                                <div class="input-group">
                                    <span class="input-group-text border-right-0"
                                          style="border-top-right-radius: 0px; border-bottom-right-radius: 0px">R$</span>
                                    <input type="number" placeholder="0,00" class="form-control" id="cashback_amount"
                                           disabled readonly>
                                </div>
                            </div>
                        </div>
                    @else
                        <input type="hidden" name="amount" value="0">
                    @endif
                </div>
                <input type="hidden" name="with_cashback" id="withCashbackInput" value="0">
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary" disabled>Enviar</button>
                    <button id="cancel_send" type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@include('components.loading', ['message' => 'Enviando avaliação...', 'id' => 'sending_loading'])
@include('components.loading', ['message' => 'Carregando...', 'id' => 'checking_loading'])

<script>
    const emailEnabled = {{ ApiUser::get()['business_selected']['plan']['authorizedFunctions']['email_sending'] ? 'true' : 'false' }};
    const whatsappEnabled = {{ ApiUser::get()['business_selected']['plan']['authorizedFunctions']['whatsapp_sending'] ? 'true' : 'false' }};
    const whatsappPhoneNumber = '{{ $parameters["whatsapp_phone_number"] ?? "" }}';
    const whatsappInstanceKey = '{{ $parameters["whatsapp_instance_key"] ?? "" }}';

    // Funções auxiliares
    /** 
     * Valida e controla a exibição do modal de envio de avaliação com base nas permissões do plano,
     * configuração do WhatsApp e limites de envio disponíveis.
     * 
     * Cenários de validação implementados:
     * 1. Plano sem permissão de WhatsApp -> Modal de avaliação apenas com email
     * 2. Plano sem permissão de email, WhatsApp desconfigurado -> Modal de configurar WhatsApp
     * 3. Plano sem permissão de email, WhatsApp configurado -> Modal de avaliação apenas com WhatsApp
     * 4. Plano sem permissão de WhatsApp, email esgotado -> Modal de limite esgotado
     * 5. Plano sem permissão de email, WhatsApp desconfigurado e limite esgotado -> Modal de limite esgotado
     * 6. Plano sem permissão de email, WhatsApp configurado e limite esgotado -> Modal de limite esgotado
     * 7. Plano com ambas permissões, WhatsApp desconfigurado, email com limite -> Modal de avaliação com email
     * 8. Plano com ambas permissões, WhatsApp configurado, ambos com limite -> Modal de avaliação com ambas opções
     * 9. WhatsApp desconfigurado, email sem limite -> Modal de configurar WhatsApp
     * 10. WhatsApp configurado, email sem limite, WhatsApp com limite -> Modal de avaliação apenas WhatsApp
     * 11. Ambos sem limite -> Modal de limite esgotado
     * 12. Ambos sem limite -> Modal de limite esgotado
     */

    /**
     * Retorna as permissões do plano e status da configuração do WhatsApp
     * @returns {Object} Objeto com as permissões e status da configuração
     */
    const validatePermissions = () => {
        return {
            emailPermission: emailEnabled,
            whatsappPermission: whatsappEnabled,
            hasWhatsappConfig: whatsappPhoneNumber && whatsappInstanceKey
        };
    };

    /**
     * Verifica a quantidade de envios disponíveis para email e WhatsApp
     * @returns {Promise<Object>} Objeto com os limites restantes de envios
     * @throws {Error} Se houver erro na requisição ou resposta inválida
     */
    const validateShipments = async () => {
        const response = await fetch("{{ env('API_URL') }}/api/evaluations/business/validate-shipments", {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Erro na requisição: ' + response.status);
        }

        const data = await response.json();

        if (data.error) {
            throw new Error(data.msg || 'Erro ao verificar disponibilidade de envios');
        }

        if (!data.data) {
            throw new Error('Resposta inválida da API');
        }

        return {
            remainingEmail: emailEnabled ? data.data.remaining_email_shipments : 0,
            remainingWhatsapp: whatsappEnabled ? data.data.remaining_whatsapp_shipments : 0
        };
    };

    /**
     * Processa o resultado das validações e determina qual ação tomar com base nas permissões e limites
     * @param {Object} permissions - Objeto com as permissões e configurações do plano
     * @param {Object} shipments - Objeto com os limites de envios restantes
     * @returns {Object} Objeto com o tipo de ação ('SHOW_MODAL', 'SHOW_WHATSAPP_CONFIG', 'SHOW_LIMIT_EXCEEDED') 
     *                   e opções disponíveis para o modal quando aplicável
     */
    const handleValidationResult = async (permissions, shipments) => {
        const {emailPermission, whatsappPermission, hasWhatsappConfig} = permissions;
        const {remainingEmail, remainingWhatsapp} = shipments;

        const availableOptions = [];

        // Se tiver permissão de WhatsApp, tiver limite disponível e estiver configurado, adiciona opção de WhatsApp
        if (whatsappPermission && remainingWhatsapp > 0 && hasWhatsappConfig) {
            availableOptions.push('WHATSAPP');
        }

        // Se tiver permissão de Email e tiver limite disponível, adiciona opção de Email
        if (emailPermission && remainingEmail > 0) {
            availableOptions.push('EMAIL');
        }

        // Se tiver pelo menos uma opção, abre o modal de avaliação
        if (availableOptions.length > 0) {
            return { type: 'SHOW_MODAL', options: availableOptions }
        } else {
            // Se não tiver nenhuma opção disponível, verifica se é por causa da configuração do WhatsApp
            if (whatsappPermission && remainingWhatsapp > 0 && !hasWhatsappConfig) {
                return { type: 'SHOW_WHATSAPP_CONFIG' };
            } else { // Se não for, é porque não tem mais limite
                return { type: 'SHOW_LIMIT_EXCEEDED' };
            }
        }
    };

    const showWhatsAppConfigModal = async () => {
        await Swal.fire({
            icon: 'warning',
            title: 'WhatsApp Não Configurado',
            text: 'O WhatsApp não está configurado. Por favor, configure o número e a chave de instância do WhatsApp.',
            confirmButtonText: 'Ok',
            @if(ApiUser::hasPermission('parameters'))
            showCancelButton: true,
            cancelButtonText: 'Configurar WhatsApp',
            cancelButtonColor: '#3085d6'
            @endif
        }).then((result) => {
            @if(ApiUser::hasPermission('parameters'))
            if (result.dismiss === Swal.DismissReason.cancel) {
                window.location.href = "{{ route('businesses.parameters') }}";
            }
            @endif
        });
    };

    const showLimitExceededModal = async () => {
        await Swal.fire({
            icon: 'warning',
            title: 'Limite de Envios Atingido',
            text: 'Você atingiu o limite de envios disponíveis.',
            confirmButtonText: 'Ok',
            @if(ApiUser::hasPermission('dashboard'))
            showCancelButton: true,
            cancelButtonText: 'Ver Limites de Envio',
            cancelButtonColor: '#3085d6'
            @endif
        }).then((result) => {
            @if(ApiUser::hasPermission('dashboard'))
            if (result.dismiss === Swal.DismissReason.cancel) {
                window.location.href = "{{ route('dashboard.index') }}";
            }
            @endif
        });
    };

    const configureModalOptions = (options) => {
        const sendingTypeSelect = $('#sending_type');
        sendingTypeSelect.empty();

        if (options.length === 1) {
            sendingTypeSelect.append(`<option value="${options[0]}">${options[0] === 'EMAIL' ? 'Email' : 'WhatsApp'}</option>`);
            sendingTypeSelect.val(options[0]).trigger('change');
        } else {
            sendingTypeSelect.append('<option value="" disabled selected>Selecione o tipo de envio</option>');
            options.forEach(opt => {
                sendingTypeSelect.append(`<option value="${opt}">${opt === 'EMAIL' ? 'Email' : 'WhatsApp'}</option>`);
            });
            if (options.includes('WHATSAPP')) {
                sendingTypeSelect.val('WHATSAPP').trigger('change');
            } else {
                sendingTypeSelect.val('EMAIL').trigger('change');
            }
        }
    };

    /**
     * Abre o modal de envio de avaliação após validar permissões e limites
     * Exibe diferentes modais baseado nas validações:
     * - Modal de envio com opções disponíveis (email e/ou whatsapp)
     * - Modal de configuração do WhatsApp
     * - Modal de limite excedido
     * 
     * @param {boolean} withCashback - Indica se deve incluir campos de cashback
     * @throws {Error} Se houver erro ao verificar disponibilidade de envios
     */
    async function openEvaluationModal(withCashback) {
        try {
            $('#checking_loading').modal('show');

            const permissions = validatePermissions();
            const shipments = await validateShipments();
            const result = await handleValidationResult(permissions, shipments);

            $('#checking_loading').modal('hide');

            switch (result.type) {
                case 'SHOW_WHATSAPP_CONFIG':
                    await showWhatsAppConfigModal();
                    return;
                case 'SHOW_LIMIT_EXCEEDED':
                    await showLimitExceededModal();
                    return;
                case 'SHOW_MODAL':
                    clearInputFields();
                    $('#cashbackText').text(withCashback ? 'com Cashback' : 'sem Cashback');
                    $('#withCashbackInput').val(withCashback ? '1' : '0');

                    $('#sending_type').prop('disabled', false);
                    $('#form_id').prop('disabled', false);
                    $('#sendEvaluationForm button[type="submit"]').prop('disabled', false);

                    if (withCashback && {{ ApiUser::get()['business_selected']['parameters']['can_set_cashback_value'] ? 'true' : 'false' }}) {
                        $('#priceField').show();
                        $('#cashbackFields').show();
                        $('#amount').prop('required', true);
                        $('#amount').val('');
                    } else {
                        $('#priceField').hide();
                        $('#cashbackFields').hide();
                        $('#amount').prop('required', false);
                        $('#amount').val('0');
                    }

                    configureModalOptions(result.options);
                    $('#sendEvaluationModal').modal('show');
                    break;
            }
        } catch (error) {
            $('#checking_loading').modal('hide');
            console.error('Erro ao verificar envios:', error);
            await Swal.fire({
                icon: 'error',
                title: 'Erro',
                text: 'Erro ao verificar disponibilidade de envios. Por favor, tente novamente.',
                confirmButtonText: 'Ok'
            });
        } finally {
            $('#checking_loading').modal('hide');
        }
    }

    $(document).ready(function () {
        applyPhoneMask();
        handleSendingTypeChange();
        preSelectFormId();

        // Função para verificar o contato (email ou telefone) no backend
        async function checkUserContact(contactValue, contactType) {
            const nameInput = $('#name');
            const nameLoadingIndicator = $('#nameLoadingIndicator');
            const nameInputContainer = $('#nameInputContainer');

            if (!contactValue) {
                 if (nameInput.is(':disabled')) {
                    nameInput.prop('disabled', false).val('');
                 }
                nameInputContainer.show();
                nameLoadingIndicator.hide();
                return;
            }

            nameInputContainer.hide();
            nameLoadingIndicator.show();
            nameInput.val('');
            
            try {
                const response = await fetch("{{ env('API_URL') }}/api/users/check-contact", {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contact_value: contactValue,
                        contact_type: contactType
                    })
                });

                nameLoadingIndicator.hide();
                nameInputContainer.show();

                if (!response.ok) {
                    console.error('Erro na requisição da API:', response.status, response.statusText);
                    nameInput.prop('disabled', false);
                    return;
                }

                const data = await response.json();

                if (data.error) {
                    console.error('Erro retornado pela API:', data.error);
                    nameInput.prop('disabled', false);
                    return;
                }

                if (data.found) {
                    nameInput.val(data.name).prop('disabled', true);
                } else {
                    nameInput.val('').prop('disabled', false);
                }

            } catch (error) {
                nameLoadingIndicator.hide();
                nameInputContainer.show();
                nameInput.prop('disabled', false);
                console.error('Erro ao verificar contato:', error);
            }
        }

        // Função auxiliar para validar formato básico de email
        function isValidEmail(email) {
            // Regex simples para verificar a presença de '@' e '.'
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        let debounceTimerEmail;
        $('#email').on('input', function() {
            clearTimeout(debounceTimerEmail);
            const emailValue = $(this).val();
            const nameInput = $('#name');
            const nameLoadingIndicator = $('#nameLoadingIndicator');
            const nameInputContainer = $('#nameInputContainer');

            if ($(this).is(':enabled') && $('#nameField').is(':visible')) {
                if (isValidEmail(emailValue)) {
                    debounceTimerEmail = setTimeout(() => {
                        checkUserContact(emailValue, 'email');
                    }, 500); // 500ms debounce
                } else {
                    // Email inválido, reseta o campo nome se não estiver carregando
                    if (nameLoadingIndicator.is(':hidden')) { // Evita resetar se já estiver carregando
                         nameInput.val('').prop('disabled', false);
                         nameInputContainer.show();
                         nameLoadingIndicator.hide();
                    }
                }
            } else {
                 clearTimeout(debounceTimerEmail);
            }
        });

        let debounceTimerPhone;
        $('#number_phone').on('input', function() {
            clearTimeout(debounceTimerPhone);
            const nameInput = $('#name');
            const nameLoadingIndicator = $('#nameLoadingIndicator');
            const nameInputContainer = $('#nameInputContainer');

            // Aplica a máscara primeiro para a exibição
            let unmaskedValue = this.value.replace(/\D/g, '');
            if (unmaskedValue.length > 11) {
                 unmaskedValue = unmaskedValue.substring(0, 11); // Limita a 11 dígitos
            }
            let maskedValue = formatPhone(unmaskedValue);
            // Evita redefinir o valor se for o mesmo, prevenindo problemas com o cursor
            if (this.value !== maskedValue) {
                this.value = maskedValue;
            }

            const phoneValue = unmaskedValue; // Usa valor sem máscara para validação

            if ($(this).is(':enabled') && $('#nameField').is(':visible')) {
                 // Verifica se tem 10 ou 11 dígitos
                 if (phoneValue.length === 10 || phoneValue.length === 11) {
                     debounceTimerPhone = setTimeout(() => {
                        checkUserContact(phoneValue, 'phone');
                     }, 500); // 500ms debounce
                 } else {
                     // Telefone com tamanho inválido, reseta o campo nome se não estiver carregando
                     if (nameLoadingIndicator.is(':hidden')) { // Evita resetar se já estiver carregando
                         nameInput.val('').prop('disabled', false);
                         nameInputContainer.show();
                         nameLoadingIndicator.hide();
                     }
                 }
            } else {
                 clearTimeout(debounceTimerPhone);
            }
        });
    });

    const defaultSendingType = $('#sending_type').val();

    function clearInputFields() {
        // Limpa campos de contato
        $('#email').val('');
        $('#number_phone').val('');
        $('#name').val('');
        
        // Reseta valor do formulário para o padrão ou selecionado
        let modal = $('#sendEvaluationModal');
        let defaultFormId = modal.data('form-id');
        if (defaultFormId) {
            $('#form_id').val(defaultFormId);
        } else {
            $('#form_id').prop('selectedIndex', 0);
        }
        
        // Limpa valor do cashback se aplicável
        $('#amount').val('0');
        $('#cashback_amount').val('0');

        // Esconde campos de contato inicialmente
        $('#emailField').hide();
        $('#phoneField').hide();
        $('#nameField').hide();
        
        // Garante que campos de contato estejam desabilitados e sem required inicialmente
        $('#email').prop('disabled', true).removeAttr('required');
        $('#number_phone').prop('disabled', true).removeAttr('required');
        $('#name').prop('disabled', true).removeAttr('required');

        // Desabilita botão de submit e campos principais inicialmente
        // (serão habilitados em openEvaluationModal após validações)
        $('#sendEvaluationForm button[type="submit"]').prop('disabled', true);
        $('#sending_type').prop('disabled', true);
        $('#form_id').prop('disabled', true);
    }

    function handleInput(input) {
        const value = parseFloat(input.value);
        const cashbackAmountInput = document.getElementById('cashback_amount');
        const cashbackPercentage = parseFloat("{{$parameters['cashback_percentage'] ?? 0}}");
        if (!isNaN(value) && value !== '') {
            cashbackAmountInput.value = (value * (cashbackPercentage / 100)).toFixed(2);
        } else {
            cashbackAmountInput.value = '';
        }
    }

    function handleBlur(input) {
        const value = parseFloat(input.value);
        if (!isNaN(value)) {
            input.value = value.toFixed(2);
        }
    }

    function applyPhoneMask() {
        // Aplicando máscara no campo Número de Contato
        $('#number_phone').on('input', function () {
            let value = this.value.replace(/\D/g, ''); // Remove todos os caracteres que não são dígitos
            if (value.length > 11) {
                value = value.substring(0, 11); // Limita a entrada para no máximo 11 dígitos
            }
            this.value = formatPhone(value); // Aplica a máscara apropriada
        });
    }

    function formatPhone(value) {
        // Remove todos os caracteres não numéricos
        value = value.replace(/\D/g, '');

        if (value.length <= 10) {
            // Formato para 10 dígitos: (99) 9999-9999
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2')      // (99) 9
                .replace(/(\d{4})(\d{1,4})$/, '$1-$2');  // (99) 9999-9999
        } else {
            // Formato para 11 dígitos: (99) 99999-9999
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2')      // (99) 9
                .replace(/(\d{5})(\d{1,4})$/, '$1-$2');  // (99) 99999-9999
        }
    }

    function handleSendingTypeChange() {
        $('#sending_type').on('change', function () {
            $('#name').val(''); // Sempre limpa o campo nome ao trocar o tipo de envio

            // Verifica se o select está habilitado antes de executar a lógica
            if (!$(this).is(':enabled')) {
                $('#emailField').hide();
                $('#phoneField').hide();
                // Limpa ambos os campos de contato se o select estiver desabilitado
                $('#email').val('');
                $('#number_phone').val('');
                return;
            };

            var sendingType = $(this).val();

            if (sendingType === 'EMAIL') {
                $('#emailField').show();
                $('#email').prop('disabled', false).attr('required', true);

                $('#phoneField').hide();
                $('#number_phone').val('').prop('disabled', true).removeAttr('required'); // Limpa e esconde o campo de telefone

                $('#nameField').show();
                $('#name').prop('disabled', false).attr('required', true);
            } else if (sendingType === 'WHATSAPP' /* || sendingType === 'SMS' */) {
                $('#emailField').hide();
                $('#email').val('').prop('disabled', true).removeAttr('required'); // Limpa e esconde o campo de email

                $('#phoneField').show();
                $('#number_phone').prop('disabled', false).attr('required', true);

                $('#nameField').show();
                $('#name').prop('disabled', false).attr('required', true);
            } else {
                // Caso nenhum tipo seja selecionado (embora o select tenha um valor padrão)
                $('#emailField').hide();
                $('#email').val('').prop('disabled', true).removeAttr('required');

                $('#phoneField').hide();
                $('#number_phone').val('').prop('disabled', true).removeAttr('required');

                $('#nameField').hide();
                $('#name').prop('disabled', true).removeAttr('required');
            }
        });
    }

    function preSelectFormId() {
        // Pré-seleciona o form_id se estiver definido
        let formId = '{{ $formId ?? '' }}';
        if (formId) {
            $('#form_id').val(formId).trigger('change');
        }
    }

    $('#sendEvaluationForm').on('submit', function () {
        $('#{{ $modalId }}').modal('hide');
        $('#loading').modal('show');
    });
</script>
