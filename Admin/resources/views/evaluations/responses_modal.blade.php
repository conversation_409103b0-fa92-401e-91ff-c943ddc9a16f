<div class="modal fade" id="responsesModal-{{ $evaluation['id'] }}" tabindex="-1" role="dialog"
     aria-labelledby="responsesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="responsesModalLabel">{{ $evaluation['form']['name'] }} - Respostas</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Fechar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body pt-0">
                @if($evaluation['shipping_status'] == "RESPONDED")
                    @php
                        $groupedResponses = [];
                        foreach ($evaluation['responses'] as $response) {
                            $topicName = $response['question']['topic']['name'];
                            $groupedResponses[$topicName][] = $response;
                        }
                    @endphp
                    @if(empty($groupedResponses))
                        <div class="text-center my-5">
                            <p class="text-muted">Avaliação sem respostas…</p>
                        </div>
                    @endif
                    @foreach($groupedResponses as $topicName => $responses)
                        <h4 class="mt-3"
                            style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#2494c7">
                            {{ $topicName }}
                        </h4>
                        <hr class="mt-1 mb-3">
                        @foreach($responses as $response)
                            <div class="card mb-2 shadow-sm">
                                <div class="card-body p-3">
                                    <h6 class="font-weight-bold">
                                        {{ $response['question']['description'] }}
                                    </h6>
                                    @if($response['question']['evaluation_type'] == 'five_star')
                                        @php
                                            $rating = (int)$response['response'];
                                        @endphp
                                        <div>
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="{{ $i <= $rating ? 'fas' : 'far' }} fa-star text-warning"></i>
                                            @endfor
                                        </div>
                                    @elseif($response['question']['evaluation_type'] == 'yes_no')
                                        <p class="mb-0">
                                            @if($response['response'] == 'S' || strtolower($response['response']) == 'yes' || strtolower($response['response']) == 'sim')
                                                <span class="text-success"><i class="fas fa-check"></i> Sim</span>
                                            @else
                                                <span class="text-danger"><i class="fas fa-times"></i> Não</span>
                                            @endif
                                        </p>
                                    @else
                                        <p>{{ $response['response'] }}</p>
                                    @endif

                                    {{-- Exibir a justificativa, se existir --}}
                                    @if(!empty($response['justification']))
                                        <div class="mt-1">
                                            <strong>Justificativa:</strong>
                                            <p class="mb-0">{{ $response['justification'] }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    @endforeach
                @else
                    <div class="text-center my-5">
                        <p class="text-muted">A avaliação ainda não foi respondida…</p>
                        <p class="text-muted">Aguarde o cliente responder para visualizar as respostas.</p>
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>
