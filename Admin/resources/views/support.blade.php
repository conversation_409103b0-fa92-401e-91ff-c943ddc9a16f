@php
    use App\Helpers\ApiUser;use App\Helpers\SystemHelper;
    $user = ApiUser::get();
    $businessSelected = $user && isset($user['business_selected']) ? $user['business_selected'] : null;

    // Verifica se o usuário é admin do negócio selecionado (compare o ID do usuário com o campo "admin" do negócio selecionado).
    $isAdminOfSelectedBusiness = false;
    if ($user && $businessSelected && isset($businessSelected['admin'])) {
        $isAdminOfSelectedBusiness = $businessSelected['admin']['id'] == $user['id'];
    }

    $layout = $user !== null ? 'layouts.app' : 'layouts.guest';
@endphp

@extends($layout, ['activePage' => 'support'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="container">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div>
                            <div class="d-flex form-row justify-content-between align-items-center"
                                 style="margin-top: 5px; margin-bottom:4px">
                                <div class="form-group">
                                    <h5 class="mb-0 card-title"
                                        style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                        Suporte
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        @if ($email != null || $phone_number != null)
                            @if($email != null)
                                <div>
                                    <h6><b>Email</b></h6>
                                    <h6><a href="mailto:{{$email}}?subject=Contato Site Visão Negócio&body=Olá, espero que esteja bem. Poderia me ajudar com o site visão negócio?" target="_blank">{{$email}}</a></h6>
                                </div>
                            @endif
                            @if($phone_number != null && $email != null)
                                <div style="margin-top: 20px"></div>
                            @endif
                            @if($phone_number != null)
                                <div>
                                    <h6><b>Telefone/Celular</b></h6>
                                    <h6><a id="phone_number" href="https://api.whatsapp.com/send?phone=55{{str_replace(["(", ")", " ", "-"], "", $phone_number)}}&text=Olá, espero que esteja bem. Poderia me ajudar com o site+%2Bluz?" target="_blank">{{$phone_number}}</a></h6>
                                </div>
                            @endif
                        @else
                            <div class="d-flex justify-content-center">
                                <h6>NÃO HÁ INFORMAÇÕES DE SUPORTE CADASTRADAS</h6>
                            </div>
                        @endif

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(() => {
            // Formata o telefone
            let phone_number = document.getElementById('phone_number');
            if (phone_number) {
                phone_number.innerHTML = phone_number.innerHTML.replace(/(\d{2})(\d{4,5})(\d{4})/, '($1) $2-$3');
            }
        });
    </script>
@endsection
