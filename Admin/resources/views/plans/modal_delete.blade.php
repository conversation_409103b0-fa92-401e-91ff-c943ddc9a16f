<x-modal.modal_confirm_action 
    id="{{$modalId}}" 
    title="Deletar plano"
    onSubmit="$('#plan-delete-{{$plan['id']}}').submit()"
>
    <form action="{{ route('plans.destroy', $plan['id']) }}" id="plan-delete-{{$plan['id']}}" method="POST">
        @csrf
        @method('delete')
        <div class="row">
            <div class="col">
                {{-- Spinner de verificação --}}
                <div id="deletePlanVerificationLoading_{{$plan['id']}}" class="text-center mb-2 d-none">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="sr-only">Verificando...</span>
                    </div>
                    <span class="ml-2">Verificando se o plano pode ser excluído...</span>
                </div>

                {{-- Mensagem de confirmação padrão --}}
                <div id="deletePlanConfirmMessage_{{$plan['id']}}">
                    Você deseja mesmo deletar o plano <b>{{ $plan['name'] }}</b>?
                </div>

                {{-- Mensagem de erro (inicialmente oculta) --}}
                <div id="deletePlanErrorMessage_{{$plan['id']}}" style="color: #721c24; padding: 12px; margin-bottom: 10px; border: 1px solid #f5c6cb; border-radius: 4px; background-color: #f8d7da;" class="d-none" role="alert">
                    <!-- Mensagem de erro será inserida aqui -->
                </div>

                {{-- Mensagem antiga (será removida ou adaptada no JS) - Mantida por enquanto para referência --}}
                <p id="has-bussiness-message_{{$plan['id']}}" class="alert alert-warning d-none" style="white-space: normal; text-align: center;">Não é possível excluir esse plano, pois existem negócios vinculados a ele.</p>
            </div>
        </div>
    </form>
</x-modal.modal_confirm_action>