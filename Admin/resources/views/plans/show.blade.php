<div class="form-row">
    <div class="col-md-12">
        <label for="">Nome</label>
        <input type="text" class="form-control style_campo_estatico"
                value="{{ $plan['name'] }}" disabled>
    </div>
    <div class="col-md-12 mt-4">
        <label for="">Descrição</label>
        <textarea class="form-control text-left" disabled>{{ $plan['description'] }}</textarea>
    </div>
    <div class="col-md-12 mt-4">
        <label for="">Valor</label>
        <input type="text" class="form-control style_campo_estatico"
                value="{{ StringMask::money($plan['value']) }}" disabled>
    </div>

    <div class="d-flex col-md-12 mt-4" >
        <div class="form-group col-6 pl-0 m-0">
            <label for="active">Ativo</label>
            <select name="active" id="active" class="form-control" disabled>
                <option value="1" {{ $plan && $plan['active']  ? 'selected' : '' }}>Sim</option>
                <option value="0" {{ $plan && !$plan['active']  ? 'selected' : '' }}>Não</option>
            </select>
        </div>

        <div class="form-group col-6 pr-0 m-0">
            <label for="publicly_visible">Visível Publicamente</label>
            <select name="publicly_visible" id="publicly_visible" class="form-control" disabled>
                <option value="1" {{ $plan && $plan['publicly_visible']  ? 'selected' : '' }}>Sim</option>
                <option value="0" {{ $plan && !$plan['publicly_visible']  ? 'selected' : '' }}>Não</option>
            </select>
        </div>
    </div>

    <div class="col-md-12 mt-4">
        <label for="">Tag do Sistema de Pagamento</label>
        <input type="text" class="form-control style_campo_estatico"
               value="{{ $plan['payment_system_tag'] ?? 'Não Informado' }}" disabled>
    </div>

    <div class="col-md-12 mt-4">
        <h3 class="text-primary">Módulos autorizados</h3>

        <ul class="list-group">
            @if($plan['authorizedFunctions']['evaluations'])
                <li class="list-group-item">Avaliações</li>
            @endif
            @if($plan['authorizedFunctions']['parameters'])
                <li class="list-group-item">Parâmetros do Negócio</li>
            @endif
            @if($plan['authorizedFunctions']['employees'])
                <li class="list-group-item">Funcionários</li>
            @endif
            @if($plan['authorizedFunctions']['manage_business_profile'])
                <li class="list-group-item">Gerenciar Perfil do Negócio</li>
            @endif
            @if($plan['authorizedFunctions']['dashboard'])
                <li class="list-group-item">Painel (Visualizar gráficos)</li>
            @endif
            @if($plan['authorizedFunctions']['topics'])
                <ul class="list-group-item">Perguntas (Visualização)
                    @if($plan['authorizedFunctions']['update_topics'])
                        <li class="list-group-item">Gerenciar Perguntas</li>
                    @endif
                </ul>
            @endif
            @if($plan['authorizedFunctions']['forms'])
                <ul class="list-group-item">Formulários (Apenas Visualização)
                    @if($plan['authorizedFunctions']['update_forms'])
                        <li class="list-group-item">Gerenciar Formulários</li>
                    @endif
                </ul>
            @endif
            @if($plan['authorizedFunctions']['cashback'])
                <ul class="list-group-item">Cashback (Apenas Visualização)
                    @if($plan['authorizedFunctions']['update_cashback'])
                        <li class="list-group-item">Gerenciar Cashbacks</li>
                    @endif
                </ul>
            @endif
            @if($plan['authorizedFunctions']['clients'])
                <ul class="list-group-item">Clientes (Apenas Visualização)
                    @if($plan['authorizedFunctions']['update_clients'])
                        <li class="list-group-item">Gerenciar Clientes</li>
                    @endif
                </ul>
            @endif
            @if($plan['authorizedFunctions']['notifications'])
                <ul class="list-group-item">Campanhas (Apenas Visualização)
                    @if($plan['authorizedFunctions']['update_notifications'])
                        <li class="list-group-item">Gerenciar Campanhas</li>
                    @endif
                </ul>
            @endif
            @if($plan['authorizedFunctions']['site_notifications'])
                <ul class="list-group-item">Notificações (Apenas Visualização)
                    @if($plan['authorizedFunctions']['update_site_notifications'])
                        <li class="list-group-item">Gerenciar Notificações</li>
                    @endif
                </ul>
            @endif
        </ul>
    </div>
</div>
