<form id="{{ $id_form }}" onsubmit="onSubmitForm(event)" method="post"
      action="{{ $plan['id'] ?? false ? route('plans.update', $plan['id']) : route('plans.store') }}">
    @csrf
    @if ($plan['id'] ?? false)
        @method('put')
    @endif

    @include('plans.form.basic_info')
    @include('plans.form.user_limits')
    @include('plans.form.permissions')
    @include('plans.form.sending_settings')
</form>

<script type="text/javascript">

var errorsList = [];

function fillErrors(errors) {
    errorsList.forEach((id) => {
        document.getElementById(id).innerHTML = '';
    });

    Object.keys(errors).forEach((key) => {
        let error = document.getElementById(`error-${key}`);
        if (error) {
            error.innerHTML = errors[key][0];
            errorsList.push(error.id);
        }
    });
}

function onSubmitForm(event) {
    event.preventDefault();

    const emailSending = document.querySelector('input[name="email_sending"]').checked;
    const whatsappSending = document.querySelector('input[name="whatsapp_sending"]').checked;

    if (!emailSending && !whatsappSending) {
        alert('Selecione pelo menos um tipo de envio (E-mail ou WhatsApp)');
        return;
    }

    const planId = Number('<?= $plan['id'] ?? 0 ?>');
    const formData = new FormData(event.target);

    formData.forEach(function (value, key) {
        if (key == '_method') {
            formData.delete(key);
        }
    });

    const validateUrl = planId 
        ? `<?= env('API_URL') ?>/api/plans/validate/update/${planId}`
        : `<?= env('API_URL') ?>/api/plans/validate/store`;

    $.ajax({
        url: validateUrl,
        type: 'post',
        cache: false,
        contentType: false,
        processData: false,
        data: formData,
        beforeSend: function (xhr) {
            xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
        },
        success: function (response) {
            fillErrors([]);
            event.target.submit();
        },
        error: function (response, textStatus, msg) {
            if (response.status == 422) {
                fillErrors(response.responseJSON.errors);
            }
        }
    });
}

// Inicialização
$(document).ready(function() {
    if (document.getElementById('email_sends_type')) {
        toggleEmailSendsDays();
    }
    if (document.getElementById('whatsapp_sends_type')) {
        toggleWhatsappSendsDays();
    }
});

// Evento para modal
$(document).on('shown.bs.modal', function(e) {
    if ($(e.target).find('#email_sends_type').length) {
        toggleEmailSendsDays();
    }
    if ($(e.target).find('#whatsapp_sends_type').length) {
        toggleWhatsappSendsDays();
    }
});
</script>
