@extends('layouts.app', ['activePage' => 'plans'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-6 col-md-12 col-sm-12 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Planos</h5>
                            </div>
                            <div class="col-lg-6 col-md-12 col-sm-12 mb-2">
                                <div class="d-flex justify-content-between">
                                    @include('components.forms.search', ['route' => route('plans.index'),'placeholder' => 'Pesquisar plano', 'value' => $search ?? '', 'method' => 'get'])
                                    <div>
                                        <button class="btn btn-success" onclick="openModalAdd('modal-add-plan')">
                                            <img class="card-img-left example-card-img-responsive"
                                                 src="{{url('icon/icon_plus.png')}}" width="15px"
                                                 style="margin-top: -2px;"/>
                                            <label class="styleBotaoAdicionar">Adicionar plano</label>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="plans-table">
                            <thead>
                            <tr>
                                <th scope="col" style="text-align: center">Ações</th>
                                <th scope="col" style="width:25%">Nome</th>
                                <th scope="col" style="width:50%">Descrição</th>
                                <th scope="col" style="width:50%">Valor</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach ($plans as $plan)
                            <tr>
                                <td>
                                    <!--<button class="btn btn-secondary" type="button" onclick="openModalShow('modal-view-plan', <?= $plan['id'] ?>)" style="margin-right: 10px">
                                        <img class="card-img-left example-card-img-responsive" src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                    </button>-->

                                    <button class="btn btn-primary" type="button" onclick="openEditModal('modal-edit-plan', <?= $plan['id'] ?>)" style="margin-right: 10px">
                                        <img class="card-img-left example-card-img-responsive" src="{{url('icon/icon_editar.png')}}" width="20px"/>
                                    </button>

                                    @include('plans.modal_delete', ['modalId' => "modal-delete-plan-".($plan['id']), 'plan' => $plan])
                                    <button class="btn btn-danger" style="margin-right: 10px" onclick="openModalDelete('modal-delete-plan-{{$plan['id']}}', {{$plan['id']}})">
                                        <img class="card-img-left example-card-img-responsive" src="{{url('icon/icon_lixeira.png')}}" width="18px"/>
                                    </button>
                                </td>   
                                <td>
                                    {{ $plan['name'] }}
                                </td>
                                <td class="text-truncate" style="max-width: 1rem;">
                                    {{ $plan['description'] }}
                                </td>
                                <td>
                                    {{ StringMask::money($plan['value']) }}
                                </td>
                            </tr>

                            @endforeach
                            </tbody>
                        </table>
                    </div>

                        {{ $plans->onEachSide(0)->appends($queryParams)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('plans.legend')
                    </div>
                </div>
            </div>
        </div>
        @include('plans.modal_show', ['modalId' => "modal-view-plan"])
        @include('plans.modal_add', ['modalId' => "modal-add-plan"])
        @include('plans.modal_edit', ['modalId' => "modal-edit-plan"])

        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...'])
@endsection

@push('js')
<script type="text/javascript">
    let planCheckDeletionUrlTemplate = "{{ env('API_URL') }}/api/plans/:id/check-deletion"; // Nova URL para verificação
    // Limpa conteúdo dos modais quando são fechados
    $('#modal-add-plan').on('hidden.bs.modal', function() {
        // Limpa o conteúdo e remove quaisquer event listeners
        const addModalBody = $('#plan_add_modal_body');
        addModalBody.empty();
        addModalBody.off();
    });

    $('#modal-edit-plan').on('hidden.bs.modal', function() {
        // Limpa o conteúdo e remove quaisquer event listeners
        const editModalBody = $('#plan_modal_body');
        editModalBody.empty();
        editModalBody.off();
    });

    function openModalDelete(modalId, planId) {
        let url = planCheckDeletionUrlTemplate.replace(':id', planId);
        let modalElement = $(`#${modalId}`);
        let loadingSpinner = modalElement.find(`#deletePlanVerificationLoading_${planId}`);
        let confirmMessageDiv = modalElement.find(`#deletePlanConfirmMessage_${planId}`);
        let errorMessageDiv = modalElement.find(`#deletePlanErrorMessage_${planId}`);
        let deleteButton = modalElement.find('#submit-button-delete'); // Assumindo que o botão de confirmação tem este ID dentro do componente x-modal

        // Reset inicial e mostrar loading
        loadingSpinner.removeClass('d-none');
        confirmMessageDiv.addClass('d-none');
        errorMessageDiv.addClass('d-none');
        deleteButton.prop('disabled', true);
        modalElement.modal('show');

        $.ajax({
            url: url,
            type: 'GET',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
            },
            success: function (response) {
                loadingSpinner.addClass('d-none');

                if (response.can_delete) {
                    confirmMessageDiv.removeClass('d-none');
                    errorMessageDiv.addClass('d-none');
                    deleteButton.prop('disabled', false);
                } else {
                    confirmMessageDiv.addClass('d-none');
                    errorMessageDiv.text(response.message || 'Este plano não pode ser excluído pois está vinculado a negócios ativos.');
                    errorMessageDiv.removeClass('d-none');
                    deleteButton.prop('disabled', true);
                }
            },
            error: function (xhr, textStatus, errorThrown) {
                loadingSpinner.addClass('d-none'); 
                confirmMessageDiv.addClass('d-none'); 

                let errorMessage = 'Erro ao verificar a possibilidade de exclusão.';
                 if (xhr.responseJSON && xhr.responseJSON.message) {
                     errorMessage += ' Detalhes: ' + xhr.responseJSON.message;
                 }
                errorMessageDiv.text(errorMessage);
                errorMessageDiv.removeClass('d-none');
                deleteButton.prop('disabled', true); 
            }
        });
    }

    function addLoading(containerId) {
        $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
    }

        $('#modal-view-plan').on('hidden.bs.modal', function() {
            // Limpa o conteúdo e remove quaisquer event listeners
            const showModalBody = $('#plan_show_modal_body');
            showModalBody.empty();
            showModalBody.off();
        });

        function openModalShow(modalId, planId) {
            // Limpa o conteúdo dos outros modais para evitar conflitos
            $('#plan_add_modal_body').empty();
            $('#plan_modal_body').empty();
            
            addLoading('plan_show_modal_body')
            $(`#${modalId}`).modal('show');
            $.ajax({
            url: `plans/${planId}`,
            type: 'get',
            data: {},
            success: function(response) {
                $('#plan_show_modal_body').html(response);
            },
            error: function(response, textStatus, msg) {
                $('#plan_show_modal_body').html(msg);
            }
        });
    }

    function openModalAdd(modalId) {
        // Limpa o conteúdo do modal de edição para evitar conflitos
        $('#plan_modal_body').empty();
        
        addLoading('plan_add_modal_body')
        $(`#${modalId}`).modal('show');
        $.ajax({
            url: `plans/create`,
            type: 'get',
            data: {},
            success: function(response) {
                $('#plan_add_modal_body').html(response);
                // Inicializar configurações de envio após carregar o conteúdo
                if (typeof initializeSendingSettings === 'function') {
                    initializeSendingSettings();
                }
            },
            error: function(response, textStatus, msg) {
                $('#plan_add_modal_body').html(msg);
            }
        });
    }

    function openEditModal(modalId, planId) {
        // Limpa o conteúdo do modal de adicionar para evitar conflitos
        $('#plan_add_modal_body').empty();
        
        addLoading('plan_modal_body')
        $(`#${modalId}`).modal('show');

        $.ajax({
            url: `plans/${planId}/edit`,
            type: 'get',
            data: {},
            success: function(response) {
                $('#plan_modal_body').html(response);
                // Inicializar configurações de envio após carregar o conteúdo
                if (typeof initializeSendingSettings === 'function') {
                    initializeSendingSettings();
                }
            },
            error: function(response, textStatus, msg) {
                $('#plan_modal_body').html(msg);
            }
        });
    }

    // Adicionar listener para resetar modais de exclusão ao fechar
    $(document).ready(function() {
        $('div[id^="modal-delete-plan-"]').on('hidden.bs.modal', function () {
            let planId = this.id.split('-').pop();
            let modalElement = $(this);
            let loadingSpinner = modalElement.find(`#deletePlanVerificationLoading_${planId}`);
            let confirmMessageDiv = modalElement.find(`#deletePlanConfirmMessage_${planId}`);
            let errorMessageDiv = modalElement.find(`#deletePlanErrorMessage_${planId}`);
            let deleteButton = modalElement.find('#submit-button-delete');

            // Resetar para o estado inicial
            loadingSpinner.addClass('d-none');
            confirmMessageDiv.removeClass('d-none'); 
            errorMessageDiv.addClass('d-none').text(''); 
            deleteButton.prop('disabled', true); 
        });
    });
</script>
@endpush
