{{-- Configurações de Envios --}}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-paper-plane mr-2"></i>Configurações de Envios</h5>
        <small class="text-muted">Selecione pelo menos um tipo de envio</small>
    </div>
    <div class="card-body">
        <div class="form-group mb-4">
            <label class="font-weight-bold">Tipos de Envios Disponíveis</label>
            <div class="row mt-3">
                <div class="col-md-6 mb-3 mb-md-0 mb-lg-0">
                    <div class="card h-100 mb-3 mb-md-0 mb-lg-0 sending-type-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <i class="far fa-envelope fa-2x text-primary mr-2"></i>
                                    <h5 class="d-inline-block mb-0">E-mail</h5>
                                </div>
                                <div class="custom-control custom-switch">
                                    @include('components.inputs.checkbox', [
                                        'name' => 'email_sending',
                                        'label' => '',
                                        'value' => $plan['authorizedFunctions']['email_sending'] ?? 0,
                                        'withErrorText' => false,
                                        'required' => false,
                                        'onchange' => 'emailSendingChange(this)'
                                    ])
                                </div>
                            </div>
                            
                            <p class="text-muted">Habilita o envio de mensagens via e-mail para os clientes.</p>
                            
                            <!-- Configurações de Email -->
                            <div id="email-settings" class="mt-3 pt-3 border-top @if (!$plan || !$plan['authorizedFunctions']['email_sending']) d-none @endif">
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <div class="form-group">
                                            @include('components.inputs.number', [
                                                'name' => 'total_email_sends',
                                                'label' => 'Total de Envios por E-mail',
                                                'placeholder' => 'Ex: 1000',
                                                'value' => $plan['authorizedFunctions']['total_email_sends'] ?? 0,
                                                'withErrorText' => true,
                                                'min' => 0,
                                                'help' => 'O número total de e-mails que podem ser enviados no período.'
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email_sends_type">
                                                <i class="far fa-calendar-alt mr-1"></i> Período de Contagem
                                                <i class="fas fa-question-circle text-muted" data-toggle="tooltip" title="Define como os limites de envio serão contabilizados"></i>
                                            </label>
                                            <select name="email_sends_type" id="email_sends_type" class="form-control" onchange="toggleEmailSendsDays()">
                                                <option value="monthly" {{ $plan && isset($plan['authorizedFunctions']['email_sends_type']) && $plan['authorizedFunctions']['email_sends_type'] == 'monthly' ? 'selected' : '' }}>Mensal</option>
                                                <option value="daily" {{ $plan && isset($plan['authorizedFunctions']['email_sends_type']) && $plan['authorizedFunctions']['email_sends_type'] == 'daily' ? 'selected' : '' }}>Diário</option>
                                            </select>
                                            <span class="invalid-feedback" id="error-email_sends_type"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div id="email_sends_days_container" class="form-group @if (!$plan || !isset($plan['authorizedFunctions']['email_sends_type']) || $plan['authorizedFunctions']['email_sends_type'] != 'daily') d-none @endif">
                                            @include('components.inputs.number', [
                                                'name' => 'email_sends_days',
                                                'label' => 'Quantidade de Dias',
                                                'icon' => 'fas fa-calendar-day',
                                                'value' => $plan['authorizedFunctions']['email_sends_days'] ?? 1,
                                                'withErrorText' => true,
                                                'min' => 1,
                                                'help' => 'Se a contagem for diária, defina por quantos dias o limite se aplica.'
                                            ])
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card h-100 sending-type-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <i class="fab fa-whatsapp fa-2x text-success mr-2"></i>
                                    <h5 class="d-inline-block mb-0">WhatsApp</h5>
                                </div>
                                <div class="custom-control custom-switch">
                                    @include('components.inputs.checkbox', [
                                        'name' => 'whatsapp_sending',
                                        'label' => '',
                                        'value' => $plan['authorizedFunctions']['whatsapp_sending'] ?? 0,
                                        'withErrorText' => false,
                                        'required' => false,
                                        'onchange' => 'whatsappSendingChange(this)'
                                    ])
                                </div>
                            </div>
                            
                            <p class="text-muted">Habilita o envio de mensagens via WhatsApp para os clientes.</p>
                            
                            <!-- Configurações de WhatsApp -->
                            <div id="whatsapp-settings" class="mt-3 pt-3 border-top @if (!$plan || !$plan['authorizedFunctions']['whatsapp_sending']) d-none @endif">
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <div class="form-group">
                                            @include('components.inputs.number', [
                                                'name' => 'total_whatsapp_sends',
                                                'label' => 'Total de Envios por WhatsApp',
                                                'placeholder' => 'Ex: 500',
                                                'value' => $plan['authorizedFunctions']['total_whatsapp_sends'] ?? 0,
                                                'withErrorText' => true,
                                                'min' => 0,
                                                'help' => 'O número total de mensagens de WhatsApp que podem ser enviadas no período.'
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="whatsapp_sends_type">
                                                <i class="far fa-calendar-alt mr-1"></i> Período de Contagem
                                                <i class="fas fa-question-circle text-muted" data-toggle="tooltip" title="Define como os limites de envio serão contabilizados"></i>
                                            </label>
                                            <select name="whatsapp_sends_type" id="whatsapp_sends_type" class="form-control" onchange="toggleWhatsappSendsDays()">
                                                <option value="monthly" {{ $plan && isset($plan['authorizedFunctions']['whatsapp_sends_type']) && $plan['authorizedFunctions']['whatsapp_sends_type'] == 'monthly' ? 'selected' : '' }}>Mensal</option>
                                                <option value="daily" {{ $plan && isset($plan['authorizedFunctions']['whatsapp_sends_type']) && $plan['authorizedFunctions']['whatsapp_sends_type'] == 'daily' ? 'selected' : '' }}>Diário</option>
                                            </select>
                                            <span class="invalid-feedback" id="error-whatsapp_sends_type"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div id="whatsapp_sends_days_container" class="form-group @if (!$plan || !isset($plan['authorizedFunctions']['whatsapp_sends_type']) || $plan['authorizedFunctions']['whatsapp_sends_type'] != 'daily') d-none @endif">
                                            @include('components.inputs.number', [
                                                'name' => 'whatsapp_sends_days',
                                                'label' => 'Quantidade de Dias',
                                                'icon' => 'fas fa-calendar-day',
                                                'value' => $plan['authorizedFunctions']['whatsapp_sends_days'] ?? 1,
                                                'withErrorText' => true,
                                                'min' => 1,
                                                'help' => 'Se a contagem for diária, defina por quantos dias o limite se aplica.'
                                            ])
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sending-type-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.sending-type-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.custom-switch .custom-control-label::before {
    width: 2rem;
}

.custom-switch .custom-control-label::after {
    left: calc(-2.25rem + 2px);
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    transform: translateX(1rem);
}
</style>

<script>
    function emailSendingChange(checkbox) {
        const emailSettings = document.getElementById('email-settings');
        const emailCard = checkbox.closest('.sending-type-card');
        
        if (checkbox.checked) {
            emailSettings.classList.remove('d-none');
            emailCard.classList.add('border-primary');
        } else {
            emailSettings.classList.add('d-none');
            emailCard.classList.remove('border-primary');
        }
    }

    function whatsappSendingChange(checkbox) {
        const whatsappSettings = document.getElementById('whatsapp-settings');
        const whatsappCard = checkbox.closest('.sending-type-card');
        
        if (checkbox.checked) {
            whatsappSettings.classList.remove('d-none');
            whatsappCard.classList.add('border-success');
        } else {
            whatsappSettings.classList.add('d-none');
            whatsappCard.classList.remove('border-success');
        }
    }

    function toggleEmailSendsDays() {
        const type = document.getElementById('email_sends_type').value;
        const container = document.getElementById('email_sends_days_container');
        type === 'daily' ? container.classList.remove('d-none') : container.classList.add('d-none');
    }

    function toggleWhatsappSendsDays() {
        const type = document.getElementById('whatsapp_sends_type').value;
        const container = document.getElementById('whatsapp_sends_days_container');
        type === 'daily' ? container.classList.remove('d-none') : container.classList.add('d-none');
    }
    
    // Função para inicializar o estado dos cartões e tooltips
    function initializeSendingSettings() {
        const emailCheckbox = document.querySelector('input[name="email_sending"]');
        const whatsappCheckbox = document.querySelector('input[name="whatsapp_sending"]');
        
        // Inicializar estado dos cartões
        if (emailCheckbox && emailCheckbox.checked) {
            emailCheckbox.closest('.sending-type-card').classList.add('border-primary');
        }
        
        if (whatsappCheckbox && whatsappCheckbox.checked) {
            whatsappCheckbox.closest('.sending-type-card').classList.add('border-success');
        }
        
        // Inicializar tooltips
        $('[data-toggle="tooltip"]').tooltip();
    }

    // Inicializar quando o DOM estiver pronto (para carregamento inicial)
    document.addEventListener('DOMContentLoaded', initializeSendingSettings);

    // Inicializar quando o conteúdo for carregado via AJAX
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        initializeSendingSettings();
    }
</script>
