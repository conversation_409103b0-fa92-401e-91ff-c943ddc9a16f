{{-- Informações Básicas --}}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Informações Básicas</h5>
    </div>
    <div class="card-body">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'name', 'label' => 'Nome', 'value' => $plan['name'] ?? '', 'withErrorText' => true])
        </div>
        <div class="form-group">
            @include('components.inputs.text_area', ['name' => 'description', 'required' => false, 'label' => 'Descrição', 'value' => $plan['description'] ?? '', 'withErrorText' => true])
        </div>
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    @include('components.inputs.number', ['name' => 'value', 'label' => 'Valor', 'value' => $plan['value'] ?? 0, 'withErrorText' => true, 'step' => '0.01'])
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="active">Ativo</label>
                    <select name="active" id="active" class="form-control">
                        <option value="1" {{ $plan && $plan['active']  ? 'selected' : '' }}>Sim</option>
                        <option value="0" {{ $plan && !$plan['active']  ? 'selected' : '' }}>Não</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="publicly_visible">Visível Publicamente</label>
                    <select name="publicly_visible" id="publicly_visible" class="form-control">
                        <option value="1" {{ $plan && $plan['publicly_visible']  ? 'selected' : '' }}>Sim</option>
                        <option value="0" {{ $plan && !$plan['publicly_visible']  ? 'selected' : '' }}>Não</option>
                    </select>
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    @include('components.inputs.text', [
                        'name' => 'payment_system_tag',
                        'label' => 'Tag do Sistema de Pagamento',
                        'value' => $plan['payment_system_tag'] ?? '',
                        'withErrorText' => true,
                        'placeholder' => 'Ex: PLANO_PREMIUM_1',
                        'required' => false
                    ])
                </div>
            </div>
        </div>
    </div>
</div>