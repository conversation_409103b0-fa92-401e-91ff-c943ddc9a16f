{{-- Permissões do Plano --}}
<div class="card mb-4" id="permissions-card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Permissões do Plano</h5>
        <button type="button" class="btn btn-sm btn-outline-secondary" id="toggle-permissions-btn" data-toggle="tooltip" title="Selecionar ou desmarcar todas as permissões"
                onclick="toggleAllPermissions()">
            <i class="fas fa-check-double"></i> Selecionar Tudo
        </button>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'dashboard',
                        'label' => 'Painel (Visualizar gráficos)',
                        'value' => $plan['authorizedFunctions']['dashboard'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'tooltip' => 'Permite visualizar o dashboard com gráficos e estatísticas'
                    ])
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'evaluations',
                        'label' => 'Avaliações',
                        'value' => $plan['authorizedFunctions']['evaluations'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'tooltip' => 'Permite gerenciar avaliações no sistema'
                    ])
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'parameters',
                        'label' => 'Parâmetros do Negócio',
                        'value' => $plan['authorizedFunctions']['parameters'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'tooltip' => 'Permite configurar os parâmetros gerais do negócio'
                    ])
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'employees',
                        'label' => 'Funcionários',
                        'value' => $plan['authorizedFunctions']['employees'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'tooltip' => 'Permite gerenciar funcionários da empresa'
                    ])
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'manage_business_profile',
                        'label' => 'Gerenciar Perfil do Negócio',
                        'value' => $plan['authorizedFunctions']['manage_business_profile'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'tooltip' => 'Permite editar as informações do perfil do negócio'
                    ])
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'clients',
                        'label' => 'Clientes (Visualização)',
                        'value' => $plan['authorizedFunctions']['clients'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'onchange' => "clientsChange(this)",
                        'tooltip' => 'Permite visualizar clientes no sistema'
                    ])
                    <div class="ml-4 mt-2 @if (!$plan || !$plan['authorizedFunctions']['clients']) d-none @endif"
                        id="clients-options-container">
                        @include('components.inputs.checkbox', [
                            'name' => 'update_clients',
                            'label' => 'Gerenciar Clientes',
                            'value' => $plan['authorizedFunctions']['update_clients'] ?? 0,
                            'withErrorText' => true,
                            'class' => 'permission-checkbox',
                            'required' => false,
                            'tooltip' => 'Permite criar, editar e excluir clientes'
                        ])
                    </div>
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'cashback',
                        'label' => 'Cashbacks (Visualização)',
                        'value' => $plan['authorizedFunctions']['cashback'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'onchange' => "cashbackChange(this)",
                        'tooltip' => 'Permite visualizar cashbacks no sistema'
                    ])
                    <div class="ml-4 mt-2 @if (!$plan || !$plan['authorizedFunctions']['cashback']) d-none @endif"
                        id="cashback-options-container">
                        @include('components.inputs.checkbox', [
                            'name' => 'update_cashback',
                            'label' => 'Gerenciar Cashbacks',
                            'value' => $plan['authorizedFunctions']['update_cashback'] ?? 0,
                            'withErrorText' => true,
                            'class' => 'permission-checkbox',
                            'required' => false,
                            'tooltip' => 'Permite criar, editar e excluir cashbacks'
                        ])
                    </div>
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'notifications',
                        'label' => 'Campanhas (Visualização)',
                        'value' => $plan['authorizedFunctions']['notifications'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'onchange' => "notificationChange(this)",
                        'tooltip' => 'Permite visualizar campanhas no sistema'
                    ])
                    <div class="ml-4 mt-2 @if (!$plan || !$plan['authorizedFunctions']['notifications']) d-none @endif"
                        id="notifications-options-container">
                        @include('components.inputs.checkbox', [
                            'name' => 'update_notifications',
                            'label' => 'Gerenciar Campanhas',
                            'value' => $plan['authorizedFunctions']['update_notifications'] ?? 0,
                            'withErrorText' => true,
                            'class' => 'permission-checkbox',
                            'required' => false,
                            'tooltip' => 'Permite criar, editar e excluir campanhas'
                        ])
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'site_notifications',
                        'label' => 'Notificações (Visualização)',
                        'value' => $plan['authorizedFunctions']['site_notifications'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'onchange' => "siteNotificationChange(this)",
                        'tooltip' => 'Permite visualizar notificações no sistema'
                    ])
                    <div class="ml-4 mt-2 @if (!$plan || !$plan['authorizedFunctions']['site_notifications']) d-none @endif"
                        id="site_notifications-options-container">
                        @include('components.inputs.checkbox', [
                            'name' => 'update_site_notifications',
                            'label' => 'Gerenciar Notificações',
                            'value' => $plan['authorizedFunctions']['update_site_notifications'] ?? 0,
                            'withErrorText' => true,
                            'class' => 'permission-checkbox',
                            'required' => false,
                            'tooltip' => 'Permite criar, editar e excluir notificações'
                        ])
                    </div>
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'forms',
                        'label' => 'Formulários (Visualização)',
                        'value' => $plan['authorizedFunctions']['forms'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'onchange' => "formsChange(this)",
                        'tooltip' => 'Permite visualizar formulários no sistema'
                    ])
                    <div class="ml-4 mt-2 @if (!$plan || !$plan['authorizedFunctions']['forms']) d-none @endif"
                        id="forms-options-container">
                        @include('components.inputs.checkbox', [
                            'name' => 'update_forms',
                            'label' => 'Gerenciar Formulários',
                            'value' => $plan['authorizedFunctions']['update_forms'] ?? 0,
                            'withErrorText' => true,
                            'class' => 'permission-checkbox',
                            'required' => false,
                            'tooltip' => 'Permite criar, editar e excluir formulários'
                        ])
                    </div>
                </div>
                <div class="mb-3">
                    @include('components.inputs.checkbox', [
                        'name' => 'topics',
                        'label' => 'Perguntas (Visualização)',
                        'value' => $plan['authorizedFunctions']['topics'] ?? 0,
                        'withErrorText' => true,
                        'required' => false,
                        'class' => 'permission-checkbox',
                        'onchange' => "topicsChange(this)",
                        'tooltip' => 'Permite visualizar perguntas no sistema'
                    ])
                    <div class="ml-4 mt-2 @if (!$plan || !$plan['authorizedFunctions']['topics']) d-none @endif"
                        id="topics-options-container">
                        @include('components.inputs.checkbox', [
                            'name' => 'update_topics',
                            'label' => 'Gerenciar Perguntas',
                            'value' => $plan['authorizedFunctions']['update_topics'] ?? 0,
                            'withErrorText' => true,
                            'class' => 'permission-checkbox',
                            'required' => false,
                            'tooltip' => 'Permite criar, editar e excluir perguntas'
                        ])
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
  function topicsChange(checkbox) {
    const topicOptionsContainer = document.getElementById('topics-options-container');
    checkbox.checked ? topicOptionsContainer.classList.remove('d-none') : topicOptionsContainer.classList.add('d-none');
    updateButtonText(verifyAllChecked());
  }

  function formsChange(checkbox) {
      const formOptionsContainer = document.getElementById('forms-options-container');
      checkbox.checked ? formOptionsContainer.classList.remove('d-none') : formOptionsContainer.classList.add('d-none');
      updateButtonText(verifyAllChecked());
  }

  function cashbackChange(checkbox) {
      const cashbackOptionsContainer = document.getElementById('cashback-options-container');
      checkbox.checked ? cashbackOptionsContainer.classList.remove('d-none') : cashbackOptionsContainer.classList.add('d-none');
      updateButtonText(verifyAllChecked());
  }

  function clientsChange(checkbox) {
      const clientsOptionsContainer = document.getElementById('clients-options-container');
      checkbox.checked ? clientsOptionsContainer.classList.remove('d-none') : clientsOptionsContainer.classList.add('d-none');
      updateButtonText(verifyAllChecked());
  }

  function notificationChange(checkbox) {
      const notificationOptionsChange = document.getElementById('notifications-options-container');
      checkbox.checked ? notificationOptionsChange.classList.remove('d-none') : notificationOptionsChange.classList.add('d-none');
      updateButtonText(verifyAllChecked());
  }

  function siteNotificationChange(checkbox) {
      const notificationOptionsChange = document.getElementById('site_notifications-options-container');
      checkbox.checked ? notificationOptionsChange.classList.remove('d-none') : notificationOptionsChange.classList.add('d-none');
      updateButtonText(verifyAllChecked());
  }

  function verifyAllChecked() {
      const checkboxes = document.querySelectorAll('.permission-checkbox');
      let allChecked = true;
      checkboxes.forEach(checkbox => {
          if (!checkbox.checked) {
              allChecked = false;
          }
      });
      return allChecked;
  }

  function updateButtonText(allChecked) {
      const toggleButton = document.getElementById('toggle-permissions-btn');
      toggleButton.innerHTML = allChecked ?
          '<i class="fas fa-times-circle"></i> Desmarcar Tudo' :
          '<i class="fas fa-check-double"></i> Selecionar Tudo';
  }

  function toggleAllPermissions() {
      const checkboxes = document.querySelectorAll('.permission-checkbox');
      let allChecked = verifyAllChecked();
      
      checkboxes.forEach(checkbox => {
          checkbox.checked = !allChecked;
          // Dispara o evento de change para mostrar/esconder opções dependentes
          if ('createEvent' in document) {
              const evt = document.createEvent('HTMLEvents');
              evt.initEvent('change', false, true);
              checkbox.dispatchEvent(evt);
          } else {
              checkbox.fireEvent('onchange');
          }
      });

      updateButtonText(!allChecked);
  }

  // Função de inicialização que pode ser chamada múltiplas vezes com segurança
  function initializePermissions() {
      updateButtonText(verifyAllChecked());
  }

  // Remove listeners anteriores e inicializa
  document.removeEventListener('DOMContentLoaded', initializePermissions);
  document.addEventListener('DOMContentLoaded', initializePermissions);
  
  // Garante que o estado inicial está correto mesmo se o DOM já estiver carregado
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
      initializePermissions();
  }
</script>
