@extends('layouts.app', ['activePage' => 'quick_actions'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Disparos
                                </h5>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="container py-4">
                            <div class="row">
                                @if(ApiUser::hasPermission('evaluations'))
                                    @include('evaluations.send_modal', ['forms' => $forms, 'modalId' => 'sendEvaluationModal'])
                                    <div class="col-md-4 mb-4">
                                        <a href="#" class="card h-100 text-decoration-none shadow-sm"
                                           onclick="openEvaluationModal(false)">
                                            <div class="card-body text-center">
                                                <div class="icon-circle">
                                                    <i class="fas fa-star fa-2x icon-main"></i>
                                                </div>
                                                <h5 class="card-title text-dark mt-2">Avaliação sem Cashback</h5>
                                                <p class="card-text text-muted">Envie uma avaliação para seus clientes sem cashback.</p>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-4">
                                        <a href="#" class="card h-100 text-decoration-none shadow-sm"
                                           onclick="openEvaluationModal(true)">
                                            <div class="card-body text-center">
                                                <div class="icon-circle">
                                                    <i class="fas fa-money-bill-wave fa-2x icon-main"></i>
                                                </div>
                                                <h5 class="card-title text-dark mt-2">Avaliação com Cashback</h5>
                                                <p class="card-text text-muted">Envie uma avaliação para seus clientes com cashback.</p>
                                            </div>
                                        </a>
                                    </div>
                                @endif
                                @if(ApiUser::hasPermission('update_cashback'))
                                    @include('components.search-complete-cashback-modal')
                                    <div class="col-md-4 mb-4">
                                        <a href="#" class="card h-100 text-decoration-none shadow-sm"
                                           onclick="openCashbackModal()">
                                            <div class="card-body text-center">
                                                <div class="icon-circle">
                                                    <i class="fas fa-download fa-2x icon-main"></i>
                                                </div>
                                                <h5 class="card-title text-dark mt-2">Baixar Cashback</h5>
                                                <p class="card-text text-muted">Baixe os cashbacks disponíveis.</p>
                                            </div>
                                        </a>
                                    </div>
                                @endif
                                @if(ApiUser::hasPermission('dashboard'))
                                    <div class="col-md-4 mb-4">
                                        <a href="{{route('dashboard.index')}}"
                                           class="card h-100 text-decoration-none shadow-sm">
                                            <div class="card-body text-center">
                                                <div class="icon-circle">
                                                    <i class="fas fa-chart-bar fa-2x icon-main"></i>
                                                </div>
                                                <h5 class="card-title text-dark mt-2">Análise do meu Negócio</h5>
                                                <p class="card-text text-muted">Visualize os dados do seu negócio.</p>
                                            </div>
                                        </a>
                                    </div>
                                @endif
                                @if(ApiUser::hasPermission('cashback'))
                                    @include('components.cashback_direct_send_modal')
                                    <div class="col-md-4 mb-4">
                                        <a href="#" class="card h-100 text-decoration-none shadow-sm"
                                           onclick="openCashbackDirectModal()">
                                            <div class="card-body text-center">
                                                <div class="icon-circle">
                                                    <i class="fas fa-gift fa-2x icon-main"></i>
                                                </div>
                                                <h5 class="card-title text-dark mt-2">Disparo de Cashback</h5>
                                                <p class="card-text text-muted">Envie um cashback direto para o cliente, sem avaliação.</p>
                                            </div>
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        :root {
            --main-color: {{ SystemHelper::get()['MAIN_COLOR'] }};
            --alpha-circle-color: {{ SystemHelper::get()['MAIN_COLOR'] }}33;
        }

        a.card {
            transition: transform 0.2s;
        }

        a.card:hover {
            transform: translateY(-5px);
        }

        .icon-main {
            color: var(--main-color);
        }

        .icon-circle {
            background-color: var(--alpha-circle-color);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            padding: 10px;
        }
    </style>

    <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>

    <script>
        function openCashbackModal() {
            const modal = $('#buscarCashbackModal');
            modal.modal('show');
        }
        @if(session('whatsappNotConnected'))
        Swal.fire({
            title: 'WhatsApp não conectado!',
            icon: 'warning',
            confirmButtonText: 'Fechar',
            @if(ApiUser::hasPermission('parameters'))
            text: 'O WhatsApp do seu negócio não está conectado. Conecte-o nos parâmetros do negócio.',
            cancelButtonText: 'Ir para Parâmetros do Negócio',
            showCancelButton: true,
            cancelButtonColor: '#3085d6',
            reverseButtons: true,
            @else
            text: 'O WhatsApp do seu negócio não está conectado. Por favor, consulte o administrador do seu negócio para ser feito a reconexão.',
            @endif
        }).then((result) => {
            @if(ApiUser::hasPermission('parameters'))
            if (result.dismiss === Swal.DismissReason.cancel) {
                window.location.href = "{{ route('businesses.parameters') }}";
            }
            @endif
        });
        @endif
        //Nenhum whatsapp encontrado para o número informado.
        @if(session('whatsappNotFound'))
        Swal.fire({
            title: 'WhatsApp não encontrado!',
            text: 'Nenhum WhatsApp encontrado para o número informado.',
            icon: 'warning',
            confirmButtonText: 'Fechar'
        });
        @endif
    </script>
@endsection
