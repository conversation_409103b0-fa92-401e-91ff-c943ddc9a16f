@extends('layouts.app')

@section('content')
@include('components.loading', ['message' => 'Aguarde...'])

<div class="container">
    <div class="row justify-content-center">
        <div class="container" style="margin-bottom: 1rem;">

        </div>
        <div class="col-md-12" style="margin-bottom:3rem">
            <div class="card card_conteudo">
                <div class="card-header"
                    style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                    <div class="d-flex justify-content-between align-items-center"
                        style="margin-top: 5px; margin-bottom:4px">
                        <h5 class="mb-0 card-title"
                            style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                            Perfil</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-12 mb-4 card card_conteudo p-2">
                                    <div class="nav nav-pills" id="v-pills-tab" role="tablist">
                                        <a class="nav-link @if(!$errors->any()) active @endif" id="v-pills-home-tab"
                                            data-toggle="pill" href="#v-pills-home" role="tab"
                                            aria-controls="v-pills-home" aria-selected="true">Início</a>
                                        <a class="nav-link @if($errors->any()) active @endif" id="v-pills-profile-tab"
                                            data-toggle="pill" href="#v-pills-profile" role="tab"
                                            aria-controls="v-pills-profile" aria-selected="false">Meus dados</a>
                                        <a class="nav-link" id="v-pills-messages-tab" data-toggle="pill"
                                            href="#v-pills-messages" role="tab" aria-controls="v-pills-messages"
                                            aria-selected="false">Alterar senha</a>
                                        {{--<a class="nav-link" id="v-pills-settings-tab" data-toggle="pill" href="#v-pills-settings" role="tab" aria-controls="v-pills-settings" aria-selected="false">Outras</a>--}}
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="tab-content" id="v-pills-tabContent">
                                        {{-- Inicio --}}
                                        <div class="tab-pane fade show @if(!$errors->any()) active @endif"
                                            id="v-pills-home" role="tabpanel" aria-labelledby="v-pills-home-tab">
                                            <div class="form-row justify-content-center" style="text-align: center">
                                                <div class="col-md-12"><img
                                                        class="card-img-left example-card-img-responsive"
                                                        src="{{ SystemHelper::get()['MAIN_LOGO_THEME']?? '/icon/logo_visao_negocio.svg' }}" width="60%" />
                                                </div>
                                                <div class="col-md-12">
                                                    <h6 style="color: #927373; margin-top:1rem">Bem vindo(a) a área de
                                                        perfil do sistema.</h6>
                                                </div>
                                            </div>
                                        </div>
                                        {{-- Meus dados--}}
                                        <div class="tab-pane fade @if($errors->any()) show active @endif"
                                            id="v-pills-profile" role="tabpanel" aria-labelledby="v-pills-profile-tab">
                                            <div class="form-row">
                                                {{-- Visualizar/editar dados --}}
                                                <div class="col-md-12">
                                                    <form name="form-update-user-infos" id='form-update-user-infos'
                                                        action="{{ route('profile.update') }}" method="post">
                                                        @csrf
                                                        @method('put')
                                                        <div class="alert alert-danger d-none messageBox" role="alert"
                                                            id="errors-ajax"></div>
                                                        <div class="form-row">
                                                            <div class="col-md-12 d-flex justify-content-between"
                                                                style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                                                <div>Dados pessoais</div>
                                                            </div>
                                                            <div class="form-row">
                                                                <div class="col-md-4 d-flex flex-column align-items-center justify-content-center" style="margin-top: 10px; margin-bottom: 10px;">
                                                                    <a onclick="document.getElementById('avatar').click()" id="avatar-wrapper">
                                                                        @php
                                                                            $hasAvatar = (isset($user->avatar) && $user->avatar != null);
                                                                        @endphp
                                                                        <img
                                                                            src="{{ $hasAvatar ? $user->avatar : '' }}"
                                                                            alt="Avatar" id="avatar-preview" class="img-fluid rounded-circle mx-auto"
                                                                            style="width: 250px; height: 250px; object-fit: cover; {{$hasAvatar ? 'display: block' : 'display: none'}}">
                                                                        <i id="avatar_placeholder" class="fas fa-user-circle fa-fw" style="font-size: 15rem; {{!$hasAvatar ? 'display: block;' : 'display: none;'}}"></i>
                                                                    </a>
                                                                    <button 
                                                                        style="position: absolute; right: 15px; top: 10px; display: none;"
                                                                        type="button" 
                                                                        class="btn btn-danger rounded"
                                                                        id="removeAvatar_button"
                                                                        name="removeAvatar_button"
                                                                        onclick="removeImage()"">
                                                                        <i class="fas fa-times fa-fw text-white"></i>
                                                                    </button>
                                                                    <input type="hidden" name="avatar_remove" id="avatar_remove" disabled>
                                
                                                                    <!-- Auxiliary input for file selection -->
                                                                    <input type="file" class="form-control-file d-none" id="avatar"
                                                                           accept="image/png, image/jpeg, image/jpg, image/gif"
                                                                           onchange="validateFileSize(this)">
                                                                </div>
                                                                <div class="col-md-8">
                                                                    <div class="form-group">
                                                                        @include('components.inputs.text', ['name' =>
                                                                        'name', 'label' => 'Nome completo', 'value' =>
                                                                        old('name', $user->name), 'withErrorText' => true,
                                                                        'required' => true])
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <input type="hidden" name="genderTemp" value="{{ $user->gender }}">
                                                                        <label for="gender">Gênero <span
                                                                                style="color: red">*</span></label>
                                                                        <select id="gender" name="gender"
                                                                            class="form-select form-control @error('gender') is-invalid @enderror"
                                                                            aria-label="Default select example" required>
                                                                            <option value="m"
                                                                                {{ old('gender', $user->gender) == 'm' ? 'selected' : '' }}>
                                                                                Masculino</option>
                                                                            <option value="f"
                                                                                {{ old('gender', $user->gender) == 'f' ? 'selected' : '' }}>
                                                                                Feminino</option>
                                                                            <option value=""
                                                                                {{ is_null(old('gender', $user->gender)) ? 'selected' : '' }}>
                                                                                Não informado</option>
                                                                        </select>
                                                                        <small id="error-gender"
                                                                            class="text-danger"></small>
                                                                        @error('gender')
                                                                        <span class="invalid-feedback" role="alert">
                                                                            <strong>{{$message}}</strong>
                                                                        </span>
                                                                        @enderror
                                                                    </div>
                                                                    <div class="form-group">
                                                                        @include('components.inputs.date', ['name' =>
                                                                        'birth_date', 'label' => 'Data de nascimento',
                                                                        'value' => old('birth_date', $user->birth_date),
                                                                        'withErrorText' => true, 'required' => true])
                                                                    </div>
                                                                    <div class="form-group">
                                                                        @include('components.inputs.text', ['name' => 'cpf',
                                                                        'label' => 'CPF', 'placeholder' => 'Digite o CPF',
                                                                        'value' => old('cpf', $user->cpf), 'withErrorText'
                                                                        => true, 'onchange' =>'checkCPF()', 'required' => true])
                                                                    </div>
                                                                    <div class="form-group">
                                                                        @include('components.inputs.text', ['name' =>
                                                                        'email', 'label' => 'E-mail', 'placeholder' =>
                                                                        'Digite o e-mail', 'value' => old('email',
                                                                        $user->email), 'withErrorText' => true,
                                                                        'required' => true])
                                                                        <input type="hidden" name="email_validated" id="email_validated" value="0" disabled/>
                                                                    </div>
                                                            </div>
                                                            <div class="col-md-12"
                                                                style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                                                Endereço</div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    @include('components.inputs.text', ['name' => 'cep',
                                                                    'label' => 'CEP', 'placeholder' => 'Digite o cep',
                                                                    'value' => old('cep', $user->address['cep']),
                                                                    'withErrorText' => true, 'required' => true])
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    @include('components.inputs.text', ['name' =>
                                                                    'state', 'label' => 'Estado', 'placeholder' =>
                                                                    'Digite o estado', 'value' => old('state',
                                                                    $user->address['state']), 'withErrorText' => true,
                                                                    'readonly' => true, 'required' => true])
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    @include('components.inputs.text', ['name' =>
                                                                    'city', 'label' => 'Cidade', 'placeholder' =>
                                                                    'Digite o nome da cidade', 'value' => old('city',
                                                                    $user->address['city']), 'withErrorText' => true,
                                                                    'readonly' => true, 'required' => true])
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    @include('components.inputs.text', ['name' =>
                                                                    'neighborhood', 'label' => 'Bairro', 'placeholder'
                                                                    => 'Digite o nome do bairro', 'value' =>
                                                                    old('neighborhood', $user->address['neighborhood']),
                                                                    'withErrorText' => true, 'required' => true])
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div class="form-group">
                                                                    @include('components.inputs.text', ['name' =>
                                                                    'street', 'label' => 'Rua', 'placeholder' => 'Digite o nome da rua', 'value' => old('street',
                                                                    $user->address['street']), 'withErrorText' => true,
                                                                    'required' => true])
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    @include('components.inputs.text', ['name' =>
                                                                    'number', 'label' => 'Número', 'placeholder' =>
                                                                    'Digite o número da residência (Ex: 01 ou SN)', 'value' => old('number',
                                                                    $user->address['number']), 'withErrorText' => true,
                                                                    'required' => true])
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    @include('components.inputs.text', ['name' =>
                                                                    'complement', 'label' => 'Complemento',
                                                                    'placeholder' => 'Digite o complemento', 'value' =>
                                                                    old('complement', $user->address['complement']),
                                                                    'withErrorText' => true,
                                                                    'required' => false])
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12"
                                                                style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                                                Contato</div>
                                                            @php
                                                                $initialPhoneForValidationLogic = '';
                                                                $initialPhoneValidated = false;
                                                                if (isset($user->phone_number_1)) {
                                                                    $rawPhone = $user->phone_number_1 ?? '';
                                                                    $initialPhoneForValidationLogic = preg_replace('/\D/', '', $rawPhone);
                                                                    $initialPhoneValidated = $user->phone_number_1 != null;
                                                                }
                                                                $isInitialPhoneActuallyValid = (isset($user) && !empty($user->phone_number_1)) && preg_match('/^\d{10,11}$/', $initialPhoneForValidationLogic);
                                                            @endphp
                                                            <input type="hidden" id="initial_phone_number" value="{{ $initialPhoneForValidationLogic }}" name="initial_phone_number">
                                                            <div class="col-md-6">
                                                                @include('components.inputs.phone_number_with_validation', [
                                                                    'name' => 'phone_number_1',
                                                                    'id' => 'phone_number_1',
                                                                    'label' => 'Telefone/Celular 1',
                                                                    'class' => 'phone-input',
                                                                    'value' => old('phone_number_1', $user->phone_number_1),
                                                                    'placeholder' => 'Digite o número do telefone',
                                                                    'required' => true,
                                                                    'currentClientCpf' => $user->cpf ?? '',
                                                                    'validationFieldName' => 'phone_validated',
                                                                    'initialValidated' => ($isInitialPhoneActuallyValid && $initialPhoneValidated) ? '1' : '0'
                                                                ])
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <div class="form-group">
                                                                        @include('components.inputs.text', ['name' =>
                                                                        'phone_number_2', 'label' => 'Telefone/Celular
                                                                        2', 'placeholder' => 'Digite o número do telefone', 'value' => old('phone_number_2',
                                                                        $user->phone_number_2), 'withErrorText' => true, 'class' => 'phone-input',
                                                                        'required' => false, 'required' => false])
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <hr>
                                                        </div>
                                                        <div class="col-md-12" style="text-align: right">
                                                            <div><button type="button" class="btn btn-success"
                                                                    id="botaoAtualizarMeusDados"
                                                                    onclick="validateUserUpdate()">Atualizar</button>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                            </div>
                                        </div>
                                        {{-- alterar senha--}}
                                        <div class="tab-pane fade" id="v-pills-messages" role="tabpanel"
                                            aria-labelledby="v-pills-messages-tab">
                                            <form name="form-update-user-password" id="form-update-user-password"
                                                action="{{ route('profile.update.password') }}" method="post">
                                                @csrf
                                                @method('put')
                                                <div class="alert alert-danger d-none messageBox" role="alert"
                                                    id="errors-ajax-password"></div>

                                                <div class="form-row">
                                                    {{-- Visualizar/editar senha --}}
                                                    <div class="col-md-12">
                                                        <div class="form-row">
                                                            <div class="col-md-12 d-flex justify-content-between"
                                                                style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                                                <div>Alterar senha</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">

                                                        <div class="form-group">
                                                            <label for="email">E-mail/Cpf</label>
                                                            <input type="email" class="form-control" name="email_cpf_alterar" id="email_cpf_alterar"
                                                                value="{{$user->email != null && $user->email != '' ?
                                                                         $user->email : $user->cpf}}" disabled>
                                                            @if($user->email == null || $user->email == '')
                                                                <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>

                                                                <script type="text/javascript">
                                                                    $(document).ready(() => {
                                                                        $("#email_cpf_alterar").mask('000.000.000-00');
                                                                    });
                                                                </script>
                                                            @endif
                                                        </div>

                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="current-password">Senha atual<span
                                                                style="color: red">*</span></label>
                                                        <div class="input-group">
                                                            <input id="current-password" name="password" type="password"
                                                                class="form-control" placeholder="Digite a senha atual"
                                                                required>
                                                            @include('components.buttons.visible_password_button', ['name_input' => 'current-password'])
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="new-password">Nova senha<span
                                                                style="color: red">*</span></label>
                                                        <div class="input-group">
                                                            <input id="new-password" name="new_password" type="password"
                                                                class="form-control" placeholder="Digite a nova senha"
                                                                required>
                                                            @include('components.buttons.visible_password_button', ['name_input' => 'new-password'])
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="confirm-password">Confirmar nova senha<span
                                                                style="color: red">*</span></label>
                                                        <div class="input-group">
                                                            <input id="confirm-password"
                                                                name="new_password_confirmation" type="password"
                                                                class="form-control"
                                                                placeholder="Digite a nova senha novamente" required>
                                                            @include('components.buttons.visible_password_button', ['name_input' => 'confirm-password'])
                                                        </div>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <hr>
                                                    </div>
                                                    <div class="col-md-12" style="text-align: right">
                                                        <div><button type="submit"
                                                                class="btn btn-success">Atualizar</button></div>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('components.modal.modal_confirm_email', ['modalId' => 'modal-confirm-email'])

@push('js')
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
<script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>
<script type="text/javascript">
// =======================================
// VARIÁVEIS GLOBAIS
// =======================================

// CPF do usuário atual - disponível globalmente para o modal WhatsApp
window.currentClientCpf = "{{ $user->cpf }}";
const isHomologation = '{{ config("app.env") }}' === 'homologation';

// Função para logs condicionais
function debugLog(message, data = null) {
    if (isHomologation) {
        if (data) {
            console.log(message, data);
        } else {
            console.log(message);
        }
    }
}

$(document).ready(function() {
    applyMasks();
    setupUnsavedChangesWarning();

    let debounceTimerEmail;
    $('#email').on('input', function() {
        clearTimeout(debounceTimerEmail);
        const emailInput = $(this);
        const errorElement = $('#error-email');
        let email = emailInput.val();

        // Verifica se o email tem um formato básico válido antes de enviar a requisição
        if (email.length > 0 && /\S+@\S+\.\S+/.test(email)) {
            debounceTimerEmail = setTimeout(() => {
                validateEmailAddress(emailInput, errorElement);
            }, 500); // 500ms debounce
        } else {
            // Limpa erros se o email for inválido ou vazio
            emailInput.removeClass('is-invalid');
            errorElement.text('');
        }
    });
    
    // Listener para sucesso da validação do componente de telefone
    $(document).on('phoneValidation:success', function(event, data) {
        // Atualiza também o valor inicial para que a comparação funcione corretamente
        $('#initial_phone_number').val(data.phoneNumber);
    });
});

var counter_phone2 = {
    value: 0
}

function applyMasks() {
    $("#cpf").mask('000.000.000-00');
    $("#cep").mask('00000-000');
    $("#phone_number_2").mask(getMask('phone_number_2')).keyup(function (event) {
        checkPhoneMask(event.target, event.originalEvent.key, counter_phone2);
    });
}

$("#cep").focusout(function() {
    $.ajax({
        url: 'https://viacep.com.br/ws/' + $(this).val() + '/json/',
        dataType: 'json',
        success: function(response) {
            debugLog('CEP response:', response);
            $("#complement").val(response.complemento)
            $("#street").val(response.logradouro)
            $("#neighborhood").val(response.bairro)
            $("#city").val(response.localidade)
            $("#state").val(response.uf)
            $("#number").focus()
        }
    })
})

$(document).ready(function () {
    initialAvatar = $('#avatar-preview').attr('src');
});


function createAvatarInputs() {
    const formulario = document.getElementById('form-update-user-infos');
    const avatarB64 = document.getElementById('avatar_base64');
    if (avatarB64) {
        avatarB64.remove();
    }

    //if has avatar file, create input text with base64 value
    const avatar = document.getElementById('avatar');

    if (avatar.files.length > 0) {
        const reader = new FileReader();
        reader.onload = function (e) {
            const base64 = e.target.result;
            const input = document.createElement('input');
            input.id = 'avatar_base64';
            input.type = 'hidden';
            input.name = 'avatar';
            input.value = base64;
            formulario.appendChild(input);

            //input with name and type file
            const input2 = document.createElement('input');
            input2.id = 'avatar_file';
            input2.type = 'hidden';
            input2.name = 'avatar_file';
            input2.value = avatar.files[0].name;
            formulario.appendChild(input2);

            validateEmail()
        };
        reader.readAsDataURL(avatar.files[0]);
    } else {
        validateEmail();
    }
}


function openConfirmModal() {
    $('#modalBusinessAvatarDelete').modal('show'); 
}

function validateFileSize(inputFile) {
    let files = inputFile.files;
    let maxSizeInBytes = 5 * 1024 * 1024; // 5MB
    let isValid = true;

    for (let i = 0; i < files.length; i++) {
        if (files[i].size > maxSizeInBytes) {
            isValid = false;
            break;
        }
    }

    if (!isValid) {
        inputFile.value = '';
        inputFile.files = null;
        alert('O tamanho máximo permitido para a imagem é de 5MB.');
    } else {
        loadAvatar(inputFile);
    }
}

function loadAvatar(inputFile, isRemoving = false) {
    if (isRemoving) {
        $('#avatar-preview').hide();
        $('#avatar_placeholder').show();
        $('#removeAvatar_button').hide();
        return;
    }
    let reader = new FileReader();
    reader.onload = function (e) {
        $('#avatar-preview').attr('src', e.target.result);
        $('#avatar-preview').show();
        $('#avatar_placeholder').hide();
        $('#removeAvatar_button').show();
    };
    reader.readAsDataURL(inputFile.files[0]);
}

function removeImage() {
    const modal = $('#modalBusinessAvatarDelete');
    const inputFile = document.getElementById('avatar');
    const inputFlagRemoveImg = document.getElementById('avatar_remove');

    inputFile.value = '';
    inputFile.files = null;
    inputFlagRemoveImg.value = true;
    modal.modal('hide');
    loadAvatar(inputFile, true);
}

function checkCPF() {
    var cpfInput = document.getElementById('cpf');
    var cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

    var isValid = cpfIsValid(cpf);

    if (isValid) {
        cpfInput.classList.remove("is-invalid")
        cpfInput.classList.add("is-valid")
    } else {
        cpfInput.classList.add("is-invalid")
    }
}

function validateUserUpdate() {
    // Checa se o telefone está validado usando a API do componente
    const isPhoneValidated = $('#phone_validated').val() === '1';
    
    if (!isPhoneValidated) {
        alert('Por favor valide o telefone antes de enviar.');
        $('#phone_number_1').focus();
        return;
    }
    const form = document.getElementById('form-update-user-infos')
    const formData = new FormData(form)

    
    formData.forEach(function(value, key) {
        if (['cpf', 'cep', 'phone_number_1', 'phone_number_2'].includes(key)) {
            formData.set(key, value.replaceAll('.', '').replaceAll('-', '').replaceAll('(', '').replaceAll(')',
                '').replaceAll(' ', ''))
        }
        if (key == '_method') {
            formData.delete(key)
        }
    })
    var cpfInput = document.getElementById('cpf');
    var cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');
    let cpfValid = cpfIsValid(cpf);
    if (!cpfValid) {
        checkCPF();
        alert('Cpf é inválido!');
        return;
    }
    if (form.reportValidity()) {
        $('#loading').modal('show')
        $.ajax({
            url: "<?= env('API_URL') ?>/api/user/validate/update",
            type: 'post',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>')
            },
            success: function(response) {
                $('#loading').modal('hide')
                removeErrorMessages()
                createAvatarInputs()
            },
            error: function(response, textStatus, msg) {
                $('#loading').modal('hide')
                if (response.status == 422) {
                    const errors = response.responseJSON.errors
                    removeErrorMessages()
                    removeIsInvalidClass()
    
                    Object.entries(errors).forEach(([field, error]) => {
                        const id = 'error-' + field
                        document.getElementById(id).innerHTML = error
                        errorsIdToRemove.push(id)
                    })
    
                    Object.keys(errors).forEach((field) => {
                        document.getElementById(field).classList.add('is-invalid')
                        removeIsInvalidClassId.push(field)
                    })
                }
                alert('Preencha todos os dados corretamente.')
            }
        })
    }
}

function setupUnsavedChangesWarning() {
    let botaoSalvarClicado = false;
    
    const botaoSalvar = $('#botaoAtualizarMeusDados');
    botaoSalvar.click(function() {
        botaoSalvarClicado = true;
    });
    
    const botaoSalvarSenha = $('#form-update-user-password button[type="submit"]');
    botaoSalvarSenha.click(function() {
        botaoSalvarClicado = true;
    });

    const formularioDados = document.getElementById('form-update-user-infos');
  //  const formularioSenha = document.getElementById('form-update-user-password');
    
    // Capturar todos os campos dos dois formulários
    const campos = [...formularioDados.querySelectorAll('input, textarea, select')];
    
    const valoresIniciais = Array.from(campos).reduce((obj, campo) => {
        if (campo.type === 'file') {
            obj[campo.name] = ''; // Arquivos sempre começam vazios
        } else if (campo.type === 'checkbox') {
            obj[campo.name] = campo.checked;
        } else {
            obj[campo.name] = campo.value;
        }
        return obj;
    }, {});

    function mudou() {
        for (let i = 0; i < campos.length; i++) {
            const campo = campos[i];
            
            // Pular campos que são gerados dinamicamente ou não são relevantes
            if (['_token', '_method', 'avatar_base64', 'avatar_file', 'avatar_remove'].includes(campo.name)) {
                continue;
            }
            
            // Tratar campos de CEP (remover formatação)
            if (campo.name === 'cep') {
                let cep = campo.value.replace('-', '');
                let valorInicial = (valoresIniciais[campo.name] || '').replace('-', '');
                if (cep !== valorInicial) {
                    debugLog('Mudou o CEP:', { novo: cep, inicial: valorInicial });
                    return true;  
                }
                continue;
            }
            
            // Tratar campos de telefone (remover formatação)
            if (['phone_number_1', 'phone_number_2'].includes(campo.name)) {
                let phoneValue = campo.value.replace(/\D/g, '');
                let valorInicial = (valoresIniciais[campo.name] || '').replace(/\D/g, '');
                if (phoneValue !== valorInicial) {
                    debugLog('Mudou o telefone:', { novo: phoneValue, inicial: valorInicial });
                    return true;
                }
                continue;
            }
            
            // Tratar campos de CPF (remover formatação)
            if (campo.name === 'cpf') {
                let cpfValue = campo.value.replace(/\D/g, '');
                let valorInicial = (valoresIniciais[campo.name] || '').replace(/\D/g, '');
                if (cpfValue !== valorInicial) {
                    debugLog('Mudou o CPF:', { novo: cpfValue, inicial: valorInicial });
                    return true;
                }
                continue;
            }
            
            // Verificar se o campo de arquivo mudou
            if (campo.type === 'file' && campo.files && campo.files.length > 0) {
                debugLog('Mudou o arquivo:', campo.files[0].name);
                return true;
            }
            
            // Verificar checkboxes
            if (campo.type === 'checkbox') {
                if (campo.checked !== valoresIniciais[campo.name]) {
                    debugLog('Mudou o checkbox:', { nome: campo.name, novo: campo.checked, inicial: valoresIniciais[campo.name] });
                    return true;
                }
                continue;
            }
            
            // Verificar outros campos
            if (campo.value !== (valoresIniciais[campo.name] || '')) {
                debugLog('Mudou o campo:', { nome: campo.name, novo: campo.value, inicial: valoresIniciais[campo.name] });
                return true;
            }
        }
        return false;
    }

    function exibirAlertaSaida(event) {
        if (mudou() && !botaoSalvarClicado) {
            event.preventDefault();
            var mensagem = 'Tem certeza que deseja sair desta página? Alterações não salvas serão perdidas.';
            event.returnValue = mensagem;
            return mensagem;
        } else {
            botaoSalvarClicado = false;
        }
    }

    window.addEventListener('beforeunload', exibirAlertaSaida);
}


// Função para validar email via AJAX
function validateEmailAddress(emailInput, errorElement) {
    let email = emailInput.val();
    const currentUserCpf = "{{ $user->cpf }}";

    emailInput.removeClass('is-invalid');
    errorElement.text('');

    if (email.length === 0) {
        return; // Não valida se o campo estiver vazio
    }

    // Verifica se o email tem um formato básico válido antes de enviar a requisição
    if (!/\S+@\S+\.\S+/.test(email)) {
        emailInput.addClass('is-invalid');
        errorElement.text('Por favor, insira um endereço de e-mail válido.');
        return;
    }

    $.ajax({
        url: `<?= env('API_URL') ?>/api/user/check-email/${email}`,
        type: 'GET',
        beforeSend: function (xhr) {
            xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
        },
        success: function (response) {
            if (response && response.id && response.cpf != currentUserCpf && response.cpf_status === 'completo') {
                emailInput.addClass('is-invalid');
                errorElement.text('Este email já está associado a outro usuário.');
            } else {
                emailInput.removeClass('is-invalid');
                errorElement.text('');
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (jqXHR.status === 404) {
                emailInput.removeClass('is-invalid');
                errorElement.text('');
            } else {
                console.error("Erro ao validar email:", textStatus, errorThrown);
                emailInput.removeClass('is-invalid');
                errorElement.text('');
            }
        }
    });
}

function validateEmail() {
    cleanErrors()
    let email = document.getElementById('email').value;
    let oldEmail = "{{ $user->email }}"
    if (oldEmail !== email && email !== '') {
        validateUpdate('form-update-user-infos', oldEmail)
    } else {
        let form = document.getElementById('form-update-user-infos');
        form.submit();
    }
}
</script>
@endpush
@endsection
