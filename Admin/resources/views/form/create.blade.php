@extends('layouts.app', ['activePage' => 'forms'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-md-8" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex align-items-center" style="margin-top: 9px; margin-bottom:6px">
                                <a href="{{route('forms.index')}}" class="mx-2" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw "></i>
                                 </a>
                                <h5 class="card-title mb-0" style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">Formulários</h5>
                            </div>
                            <div class="col-3 table-legend">
                                <span style="color:red">* Campos obrigatórios</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('forms.store') }}" id="form-form" method="POST">
                            @csrf
                            @include('form.form')
                            <div class="modal-footer d-flex justify-content-end w-100">
                                <button id="submit-button" class="btn btn-success">Adicionar Formulário</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if($message = Session::get('danger'))
            <div class="alert alert-danger alert-block shadow fade show" style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    <script>
        let form = $('#form-form');
        $("#submit-button").click(function () {
            return verifyTopics() && verifyBusinesses();
        });

        $(document).ready(function() {
            const form = $('#form-form');
            let initialFormData = form.serialize();
            let isSubmitting = false;

            function isFormDirty() {
                return form.serialize() !== initialFormData;
            }

            form.on('submit', function() {
                isSubmitting = true;
            });

            $(window).on('beforeunload', function(e) {
                if (!isSubmitting && isFormDirty()) {
                    const confirmationMessage = 'Você tem alterações não salvas. Tem certeza que deseja sair?';
                    e.returnValue = confirmationMessage;
                    return confirmationMessage;
                }
                // Se estiver limpo ou submetendo, permite a navegação
                return undefined;
            });

             $("#submit-button").off('click').on('click', function (event) {
                const topicsValid = typeof verifyTopics === 'function' ? verifyTopics() : true;
                const businessesValid = typeof verifyBusinesses === 'function' ? verifyBusinesses() : true;

                if (topicsValid && businessesValid) {
                    // Validações OK, permite a submissão
                    return true;
                } else {
                    // Validações falharam, previne a submissão
                    event.preventDefault();
                    isSubmitting = false; 
                    return false;
                }
            });
        });
    </script>
@endsection
