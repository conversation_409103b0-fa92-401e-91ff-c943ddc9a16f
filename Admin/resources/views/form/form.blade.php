@php
    use App\Helpers\ApiUser;

    $isOnlyFormsDefault = ApiUser::get()['business_selected'] == null;

@endphp
<div class="form-row">
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'name', 'label' => 'Nome', 'value' => $form['name'] ?? old('name'), 'placeholder' => 'Digite o nome do formulário', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text_area', [
                'name' => 'description',
                'label' => 'Descrição',
                'value' => $form['description'] ?? old('description'),
                'placeholder' => 'Digite a descrição do formulário',
                'withErrorText' => true,
                'required' => false
            ])
        </div>
    </div>
    <div class="@if(!ApiUser::hasUserFunction('default_forms')) d-none @endif">
        <div class="form-check mb-3 ml-1">
            <input class="form-check form-check-input" type="checkbox"
                @if(($form['default'] ?? false) || $isOnlyFormsDefault) checked @endif
                id="default"
                name="default"
                onchange="defaultFormChanged(this)"
                @if($isOnlyFormsDefault) disabled @endif
            >
            <label class="form-check-label" for="default">
                Formulário padrão
            </label>
            @if($isOnlyFormsDefault)
                <input type="hidden" name="default" value="true">
            @endif
        </div>
    </div>
    <div class="@if(!ApiUser::hasUserFunction('default_forms')) d-none @endif">
        <div class="form-check mb-3" style="margin-left: 20px;">
            <input class="form-check form-check-input" type="checkbox"
                   @if($form['active'] ?? false && $form['default'] ?? false) checked @endif
                   id="active" name="active">
            <label class="form-check-label" for="active" id="active_label">
                Ativo
            </label>
        </div>
    </div>

    @include('components.loading', ['message' => 'Aguardando confirmação de email'])

    @include('components.topics-table', ['topics' => $form['topics'] ?? [], 'isEdit' => isset($form)])

    @if(!$isOnlyFormsDefault)
        @include('components.business-table', ['businesses' => $form['businesses'] ?? [], 'isEdit' => isset($form), 'refreshTopics' => true])
    @endif
</div>

<script>
    $(document).ready(function () {
        defaultFormChanged(document.getElementById('default'), true);
    });

    function defaultFormChanged(checkbox, isfirstLoad = false) {
        const isDefaultForm = $(checkbox).is(':checked');
        
        // Elementos de negócios
        const businessElements = [
            '#businessTableFormEmployee',
            '#businessSelectedLabel',
            '#businessesSeparator',
            '#businessPaginationFormEmployee',
            '#businessSearchFormEmployee',
        ];
        
        businessElements.forEach(el => {
            $(el).toggleClass('d-none', isDefaultForm);
        });

        $('#active').toggleClass('d-none', !isDefaultForm);
        $('#active_label').toggleClass('d-none', !isDefaultForm);

        if (isDefaultForm) {
            $('#active').prop('checked', true);
            $('.loading').addClass('d-none');
        } else {
            $('#active').prop('checked', false);
        }

        if (!isfirstLoad) {
            if (!isDefaultForm) {
                loadBusinessesFormEmployee();
                selectActualBusiness();
                if (!$('#default').is(':checked')) {
                    selectTopicsActiveRequired();
                }
            }
            loadTopicsFormEmployee();
            if (typeof showHideNoBusinessSelectedMessage !== "undefined") {
                showHideNoBusinessSelectedMessage();
            }
        }
    }
</script>
