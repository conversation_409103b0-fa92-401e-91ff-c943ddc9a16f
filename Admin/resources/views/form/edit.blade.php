@extends('layouts.app', ['activePage' => 'forms'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-md-8" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex align-items-center"
                                 style="margin-top: 9px; margin-bottom:6px">
                                 <a href="{{route('forms.index')}}" class="mx-2" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw "></i>
                                 </a>
                                <h5 class="card-title mb-0"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Formulários</h5>
                            </div>
                            <div class="col-3">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('forms.update', $form['id']) }}" id="form-form"
                              method="POST">
                            @csrf
                            @method('put')
                            @include('form.form')
                            <div class="modal-footer d-flex justify-content-end w-100">
                                <button id="submit-button" class="btn btn-success">Atualizar Formulário</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if($message = Session::get('danger'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
@endsection

@push('js')
<script>
    $(document).ready(function() {
        let botaoSalvarClicado = false;
        let initialStateCaptured = false;
        let initialSimpleFields = {};
        let initialTopicsJson = '[]'; // Armazenará o JSON inicial com IDs e ordem
        let initialBusinessIds = [];

        const formElement = document.getElementById('form-form');
        
        const simpleFields = formElement.querySelectorAll('input[name="name"], textarea[name="description"], input[name="default"], input[name="active"]');
        const submitButton = $('#submit-button');

        function captureInitialState() {
            if (initialStateCaptured) return;

            simpleFields.forEach(field => {
                if (field.type === 'checkbox') {
                    initialSimpleFields[field.name] = field.checked;
                } else {
                    initialSimpleFields[field.name] = field.value;
                }
            });

            if (typeof businessesSelectedIds !== 'undefined') {
                initialBusinessIds = [...businessesSelectedIds].sort();
            } else {
                console.warn("Global 'businessesSelectedIds' not found during initial state capture.");
                initialBusinessIds = [];
            }

           // Captura o JSON inicial dos tópicos (com ordem) do input hidden
           if ($('#topicsInputJson').length > 0) {
               initialTopicsJson = $('#topicsInputJson').val();
           } else {
               console.warn("Input '#topicsInputJson' not found during initial state capture.");
               initialTopicsJson = '[]';
           }

           initialStateCaptured = true;
        }

        $(document).on('topicsLoaded', function() {
            setTimeout(captureInitialState, 100);
        });

       // Compares two arrays of simple elements for equality
       function areArraysEqual(arr1, arr2) {
            if (arr1.length !== arr2.length) return false;
            for (let i = 0; i < arr1.length; i++) {
               // Compare values directly (assuming simple arrays like IDs)
               if (arr1[i] !== arr2[i]) {
                   return false;
               }
           }
            return true;
        }

        function mudou() {
            if (!initialStateCaptured) {
                return false;
            }

            // 1. Check simple fields
            let simpleChanged = false;
            simpleFields.forEach(field => {
                let currentValue;
                if (field.type === 'checkbox') {
                    currentValue = field.checked;
                } else {
                    currentValue = field.value;
                }
                if (currentValue !== initialSimpleFields[field.name]) {
                    simpleChanged = true;
                }
            });

            if (simpleChanged) return true;

            // 2. Check businesses
             if (typeof businessesSelectedIds !== 'undefined') {
                const currentBusinessIds = [...businessesSelectedIds].sort();
                if (!areArraysEqual(initialBusinessIds, currentBusinessIds)) {
                    return true;
                }
             } else {
                 console.warn("Global 'businessesSelectedIds' not found during change detection.");
             }

            // 3. Check topics by comparing JSON strings (detects ID and order changes)
            let currentTopicsJson = '[]';
            if ($('#topicsInputJson').length > 0) {
                currentTopicsJson = $('#topicsInputJson').val();
            } else {
                console.warn("Input '#topicsInputJson' not found during change detection.");
            }

            if (initialTopicsJson !== currentTopicsJson) {
                return true;
            }
        
            return false;
        }

        function exibirAlertaSaida(event) {
            if (mudou() && !botaoSalvarClicado) {
                const confirmationMessage = 'Você tem alterações não salvas. Tem certeza que deseja sair?';
                event.preventDefault();
                event.returnValue = confirmationMessage; 
                return confirmationMessage; 
            }
        }

        window.addEventListener('beforeunload', exibirAlertaSaida);

        $(document).on('click', '#submit-button', function(e) {
            const topicsValid = typeof verifyTopics === 'function' ? verifyTopics() : true;
            const businessesValid = typeof verifyBusinesses === 'function' ? verifyBusinesses() : true;

            if (topicsValid && businessesValid) {
                botaoSalvarClicado = true;
            } else {
                e.preventDefault(); 
                botaoSalvarClicado = false;
            }
        });
    });
</script>
@endpush