@extends('layouts.app', ['activePage' => 'forms'])

@php
    use App\Helpers\ApiUser;
    $isOnlyTopicsDefault = ApiUser::get()['business_selected'] == null;
@endphp

@section('content')
    @include('topic.modals.show')
    <div class="container">
        <div class="row justify-content-center">
            <div class="container" style="margin-bottom: 1rem;">
            </div>
            <div class="col-md-9" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex align-items-center" style="margin-top: 9px; margin-bottom:6px">
                                <a  href="{{route('forms.index')}}"class="mx-2" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw "></i>
                                 </a>
                                <h5 class="card-title mb-0" style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">Formulários</h5>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="col-md-12">
                                <h6 class="style_campo_titulo">Dados do formulário</h6>
                            </div>
                            <div class="col-md-12">
                                <label for="">Nome</label>
                                <input type="text" class="form-control style_campo_estatico"
                                       value="{{ $form['name'] }}" disabled>
                            </div>
                            <div class="col-md-12 mt-2">
                                <label for="">Descrição</label>
                                <textarea class="form-control style_campo_estatico" disabled>{{ $form['description'] ?? 'Não informado' }}</textarea>
                            </div>

                            <div style="margin-top: 20px; display: flex; flex-direction: row">
                                <div class="@if(!ApiUser::get()['authorizedFunction']['default_forms']) d-none @endif">
                                    <div class="form-check mb-3 ml-1">
                                        <input class="form-check form-check-input" type="checkbox" disabled
                                               @if($form['default'] ?? false) checked @endif
                                               id="default" name="default">
                                        <label class="form-check-label" for="default">
                                            Formulário padrão
                                        </label>
                                    </div>
                                </div>
                                <div class="@if(!ApiUser::get()['authorizedFunction']['default_forms'] || !$form['default']) d-none @endif">
                                    <div class="form-check mb-3" style="margin-left: 20px;">
                                        <input class="form-check form-check-input" type="checkbox" disabled
                                            @if($form['active'] ?? false) checked @endif
                                            id="active" name="active">
                                        <label class="form-check-label" for="active" id="active_label">
                                            Ativo
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12" style="margin-top: 20px">
                                <h6 class="style_campo_titulo">Categorias</h6>
                            </div>
                            <div class="col-md-12">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th scope="col">Nome</th>
                                        <th scope="col">Descrição</th>
                                        @if(ApiUser::hasPermissionOrIsAdmin('topics'))
                                            <th scope="col" style="text-align: center">Ações</th>
                                        @endif
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @php
                                        $form['topics'] = collect($form['topics'])->sortBy('order')->toArray();
                                    @endphp
                                    @foreach($form['topics'] as $topic)
                                        <tr>
                                            <td>{{ $topic['name'] }}</td>
                                            <td>{{ $topic['description'] ?? 'Não informado' }}</td>
                                            @if(ApiUser::hasPermissionOrIsAdmin('topics'))
                                                <td style="text-align: center">
                                                    <div class="btn-group">
                                                        @php
                                                            // Verifica se o usuário tem permissão para editar um tópico padrão
                                                            // e se o tópico é padrão
                                                            $useEditDefaultRoute = !$isOnlyTopicsDefault &&
                                                                                    ApiUser::hasPermissionOrIsAdmin('update_topics') &&
                                                                                    !ApiUser::hasUserFunction('default_topics') &&
                                                                                    $topic['default'];

                                                            $editRoute = $useEditDefaultRoute ? 'topics.edit.default' : 'topics.edit';
                                                        @endphp
                                                        @if(ApiUser::hasPermissionOrIsAdmin('update_topics') || 
                                                            (ApiUser::hasUserFunction('default_topics') && $topic['default']))
                                                            {{-- Usuário pode editar: Mostrar botão Editar --}}
                                                            <button type="button"
                                                                onclick="openTopicModal('{{ $topic['id'] }}', true)"
                                                                class="btn btn-primary"
                                                                style="margin-right: 10px">
                                                                <img class="card-img-left example-card-img-responsive"
                                                                    src="{{url('icon/icon_editar.png')}}" width="20px"/>
                                                            </button>
                                                        @else
                                                            {{-- Usuário não pode editar: Mostrar botão Visualizar --}}
                                                            <button type="button"
                                                                onclick="openTopicModal('{{ $topic['id'] }}')"
                                                                class="btn btn-secondary"
                                                                style="margin-right: 10px">
                                                                <img class="card-img-left example-card-img-responsive"
                                                                    src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                                            </button>
                                                        @endif
                                                    </div>
                                                </td>
                                            @endif
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if(count($form['businesses']) > 0)
                                <div class="col-md-12" style="margin-top: 20px">
                                    <h6 class="style_campo_titulo">Negócios associados</h6>
                                </div>
                                <div class="col-md-12">
                                    <table class="table table-striped">
                                        <tbody>
                                        @foreach($form['businesses'] as $business)
                                            <tr>
                                                <td>{{ $business['name'] }}</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endif

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('js')
    <script>
        function openTopicModal(topicId, isEdit = false) {
            let baseUrl = isEdit ? 
                "{{ route($editRoute, ['topic' => ':id']) }}" :
                "{{ route('topics.show', ['topic' => ':id']) }}";

            let url = baseUrl.replace(':id', topicId);
            $('#showTopicFrame').attr('src', url);
            $('#showTopicModal').modal('show');
        }
    </script>
@endpush
