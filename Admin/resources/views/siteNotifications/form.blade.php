<?php

use App\Helpers\ApiUser;
use App\Helpers\SystemHelper;
use Carbon\Carbon;

?>
<div class="form-row">
    <div class="col-md-12">
        <p class="form-title">Dados Notificação</p>
    </div>
    <div class="col-md-4" style="margin-top: 10px">
        <input type="file" class="form-control-file d-none" id="logo"
            accept="image/png, image/jpeg, image/jpg, image/gif" onchange="validateFileSize(this)">
        <img src="{{ asset('images/no_logo.png') }}" alt="Logo" id="notification-icon-preview"
            class="img-fluid rounded mx-auto d-block" style="max-height: 200px; cursor: pointer"
            onclick="document.getElementById('logo').click()">
        <button style="position: absolute; right: 15px; top: 10px; display: none;" type="button"
            class="btn btn-danger rounded" id="removeLogo_button" onclick="removeImage()">
            <i class="fas fa-times fa-fw text-white"></i>
        </button>
        <div class="col-md-12 d-flex justify-content-center mt-2">
            <p class="text-muted text-center w-75">Caso não selecione um ícone, será exibido esse ícone de sino <i
                    class="fas fa-bell fa-fw"></i></p>
        </div>
    </div>
    <div class="col-md-8">

        <div class="col-md-12">
            <div class="form-group">
                @include('components.inputs.text', [
                    'name' => 'title',
                    'label' => 'Título',
                    'placeholder' => 'Digite o título da Notificação',
                    'withErrorText' => true,
                ])
            </div>
        </div>

        <div class="col-md-12">
            <div class="form-group mb-2">
                @include('components.inputs.text_area', [
                    'name' => 'description',
                    'label' => 'Descrição',
                    'placeholder' => 'Digite a descrição da Notificação',
                    'withErrorText' => true,
                    'minLength' => 6,
                ])
            </div>
            <div class="form-group mb-2" id="linkGroup">
                @php
                    function formatUrl($path = '')
                    {
                        $url = secure_url($path);
                        return $url;
                    }

                    $suggestions = [
                        formatUrl('/'),
                        formatUrl('/notifications/client'),
                        formatUrl('/cashbacks/client'),
                        formatUrl('/contact'),
                    ];
                @endphp
                @include('components.inputs.auto_complete', [
                    'name' => 'url_click',
                    'label' => 'Link',
                    'placeholder' => 'Https://www.',
                    'required' => false,
                    'withErrorText' => true,
                    'suggestions' => $suggestions,
                ])
                <small class="form-text text-muted">Certifique-se de inserir um link válido.</small>
            </div>
        </div>
    </div>
    {{-- <div class="col-md-12 mb-2">
        <label for="name" class="style_campo_titulo d-none" id="usersSelectedLabel">
            Selecionados (<span id="selectedUsersCount">0</span>):
        </label>
        <div class="form-group mb-0 overflow-auto py-3" style="max-height: 15rem">
            <ul class="list-group" id="users_selected">
                <!-- usuários selecionados serão carregados aqui -->
            </ul>
        </div>
    </div> --}}
    <div class="col-md-12 mb-2">
        <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center">
            <div>
                <h6 class="style_campo_titulo mb-0 d-flex align-items-center">
                    Usuários <b class="text-danger ml-1">*</b>
                </h6>
            </div>
        </div>

        <div id="table-container" class="w-100 mt-2">
            @include('siteNotifications.tables.usersNotificationTable')
        </div>
    </div>
</div>

@include('components.loading', ['message' => ''])

<script text="text/javascript">
    let previousSelection = ''

    let errorsList = [];

    function fillErrors(errors) {
        errorsList.forEach((id) => {
            document.getElementById(id).innerHTML = '';
            document.getElementById(id).classList.add('d-none');
        });

        Object.keys(errors).forEach((key) => {
            let error = document.getElementById(`error-${key}`);
            console.log(error);
            if (error) {
                error.innerHTML = errors[key][0];
                error.classList.remove('d-none');
                errorsList.push(error.id);
            }
        });
    }

    function validateStore(formId) {
        const form = document.getElementById(formId);
        const formData = new FormData(form);
        const numberUsers = Number(document.getElementById('numberUsers').value);
        const submitButton = document.getElementById('submit-notification-button');
        const isAllSelected = $('#addAllUsersButton').is(':checked');
        const idsInput = document.querySelector('#userIds');
        let idsStored = idsInput.value ? JSON.parse(idsInput.value) : [];

        if (!form.reportValidity())
            return;

        submitButton.disabled = true;

        formData.delete('is_entrepreneur')

        formData.forEach(function(value, key) {
            if (key == '_method') {
                formData.delete(key);
            }
        });

        if (!isAllSelected && idsStored.length === 0 || numberUsers === 0) {
            submitButton.disabled = false;
            return Swal.fire({
                icon: 'warning',
                title: 'Sem usuários!',
                text: 'É necessário escolher pelo menos um usuário na tabela.'
            });
        }

        const url_click = $('#url_click').val();
        const pattern =
            /(https?:\/\/(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9])(:?\d*)\/?([a-z_\/0-9\-#.]*)\??([a-z_\/0-9\-#=&]*)/g;

        if (url_click !== '' && !url_click.match(pattern)) {
            submitButton.disabled = false;
            return Swal.fire({
                icon: 'warning',
                title: 'Link inválido!',
                text: 'Digite um endereço de link válido para criar a notificação.'
            });
        }

        const logo = document.getElementById('logo');
        $('#loading').modal('show');
        if (logo.files.length > 0) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const base64 = e.target.result;
                const input = document.createElement('input');
                input.id = 'logo_base64';
                input.type = 'hidden';
                input.name = 'logo';
                input.value = base64;
                form.appendChild(input);

                //input with name and type file
                const input2 = document.createElement('input');
                input2.id = 'logo_file';
                input2.type = 'hidden';
                input2.name = 'logo_file';
                input2.value = logo.files[0].name;
                form.appendChild(input2);

                form.submit();
            };
            reader.readAsDataURL(logo.files[0]);
        } else {
            form.submit();
        }
    }

    function openConfirmModal() {
        $('#modalNotificationIconDelete').modal('show');
    }

    function removeImage() {
        const modal = $('#modalNotificationIconDelete');
        const inputFile = document.getElementById('logo');

        inputFile.value = '';
        inputFile.files = null;
        modal.modal('hide');
        loadLogo(inputFile, true);
    }

    function loadLogo(inputFile, isRemoving = false) {
        if (isRemoving) {
            const logoPath = "{{ asset('images/no_logo.png') }}"
            $('#notification-icon-preview').attr('src', logoPath);
            document.getElementById('removeLogo_button').style.display = 'none';
            return;
        }
        let reader = new FileReader();
        reader.onload = function(e) {
            $('#notification-icon-preview').attr('src', e.target.result);
        };
        reader.readAsDataURL(inputFile.files[0]);
        document.getElementById('removeLogo_button').style.display = 'block';
    }

    function validateFileSize(inputFile) {
        let files = inputFile.files;
        let maxSizeInBytes = 5 * 1024 * 1024; // 5MB
        let isValid = true;

        for (let i = 0; i < files.length; i++) {
            if (files[i].size > maxSizeInBytes) {
                isValid = false;
                break;
            }
        }

        if (!isValid) {
            inputFile.value = '';
            inputFile.files = null;
            Swal.fire({
                icon: 'Error',
                title: 'Imagem muito grande!',
                text: 'O tamanho máximo permitido para a imagem é de 5MB.'
            });
        } else {
            loadLogo(inputFile);
        }
    }
</script>
