<?php

use App\Helpers\StringMask;
use Carbon\Carbon;

?>
<div class="modal fade" id="SiteNotificationShowModal-{{ $notification['id'] }}" tabindex="-1" role="dialog"
     aria-labelledby="SiteNotificationShowModalLabel-{{ $notification['id'] }}" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Dados da notificação</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto" style="text-align: left; max-height: calc(100vh - 200px); overflow-y: auto;" id="notification_show_modal_body">
                <div class="form-row">
                    <div class="col-md-12 mb-2">
                        <h6 class="style_campo_titulo">Notificação</h6>
                    </div>
                    <div class="col-md-12 mb-2">
                        <label for="">Título</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ $notification['notification']['title'] }}" disabled>
                    </div>
                    <div class="col-md-12 mb-2">
                        <label for="">Descrição</label>
                        <textarea
                            class="form-control style_campo_estatico"
                            style="width: 100%; min-height: 100px; max-height: 100px"
                            disabled
                        >{{ $notification['notification']['description'] }}</textarea>
                    </div>
                    @if (isset($notification['notification']['url_click']))
                        <div class="col-md-12 mb-2">
                            <label for="">Link</label>
                            <div class="form-control style_campo_estatico" @readonly(true)>
                                <a href="{{ $notification['notification']['url_click'] }}" target="_blank" rel="noopener noreferrer">
                                    {{ $notification['notification']['url_click'] }}
                                </a>
                            </div>
                        </div>
                    @endif
                    <div class="col-md-12 mb-2">
                        <label for="">Negócio</label>
                        <input type="text" class="form-control style_campo_estatico"
                                value="{{ $notification['notification']['business']['name'] ?? config('app.name')}}"
                                    disabled>
                    </div>
                    <div class="form-row col-md-12 mb-2">
                        @if (isset($notification['viewed']))
                            <div class="col-md-6 mb-2">
                                <label for="">Visto em: </label>
                                <input type="text" class="form-control style_campo_estatico"
                                    value="{{ isset($notification['viewed_at']) ? Carbon::parse($notification['viewed_at'])->format('d/m/Y H:i:s') : 'Não informada'}}"
                                        disabled>
                            </div>
                        @endif
                        <div class="col-md-6">
                            <label for="">Enviado em: </label>
                            <input type="text" class="form-control style_campo_estatico"
                                   value="{{ isset($notification['created_at']) ? Carbon::parse($notification['created_at'])->format('d/m/Y H:i:s') : 'Não informada' }}"
                                   - disabled
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" style="width:30%">Fechar</button>
            </div>
        </div>
    </div>
</div>

@push('js')
<script type="text/javascript">
    function closeModal(modalId) {
        $(`#${modalId}`).toggle()
    }
</script>
@endpush