<?php

use App\Helpers\StringMask;
use Carbon\Carbon;

?>
<div class="form-row">
    <div class="col-md-12 mb-2">
        <h6 class="style_campo_titulo">Notificação</h6>
    </div>
    <div class="col-md-12 mb-2">
        <label for="">Título</label>
        <input type="text" class="form-control style_campo_estatico"
               value="{{ $notification['title'] }}" disabled>
    </div>
    <div class="col-md-12 mb-2">
        <label for="">Descrição</label>
        <textarea
            class="form-control style_campo_estatico"
            style="width: 100%; min-height: 100px; max-height: 100px"
            disabled
        >{{ $notification['description'] }}</textarea>
    </div>
    @if (isset($notification['url_click']))
        <div class="col-md-12 mb-2">
            <label for="">Link</label>
            <div class="form-control style_campo_estatico" @readonly(true)>
                <a href="{{ $notification['url_click'] }}" target="_blank" rel="noopener noreferrer">
                    {{ $notification['url_click'] }}
                </a>
            </div>
        </div>
    @endif
    <div class="form-row col-md-12 mb-2">
        <div class="col-md-6">
            <label for="">Data criação</label>
            <input type="text" class="form-control style_campo_estatico"
                   value="{{ isset($notification['created_at']) ? date('d/m/Y H:m:s', strtotime($notification['created_at'])) : 'Não informada' }}"
                   - disabled
            >
        </div>
    </div>
    <div class="col-md-12">
        <h6 class="style_campo_titulo">Usuários</h6>
    </div>
    <div class="form-group my-2 col-md-12">
        <hr id="clientsSeparator"/>
        <form id="search-users-notification-form" method="GET">
            @csrf
            <div class="d-flex flex-column flex-sm-row" style="gap: 4px">
                <input type="text" id="usersNotificationSearch" class="form-control mb-sm-0 w-100"
                       placeholder="Pesquisar usuário por nome/cpf" style="flex: 4;">
                <div class="input-group-append mb-2 mb-sm-0">
                    <button class="btn btn-light w-100" type="submit" id="search-notification-modal">
                        <i class="fas fa-search fa-fw" style="color: #6c757d"></i>
                    </button>
                </div>
        </form>
    </div>
    <div class="table-responsive-sm">
        <table class="table table-striped d-none" style="white-space: nowrap;" id="usersNotificationTable">
            <thead>
            <tr>
                <th scope="col">Nome/CPF</th>
                <th scope="col">Contato</th>
            </tr>
            </thead>
            <tbody id="usersNotificationTableBody">
            <div class="d-flex justify-content-center loading pb-1 d-none">
                <div class="spinner-border loading" role="status">
                </div>
            </div>
            <!-- Usuários serão carregados aqui -->
            </tbody>
        </table>
        <small id="error-clients_id" class="p-1 text-danger"></small>
    </div>
    <nav>
        <ul class="pagination" id="usersNotificationPagination">
            <!-- Paginação será carregada aqui -->
        </ul>
    </nav>
</div>
</div>

<script text="text/javascript">
    document.addEventListener('openShowSiteNotification', function () {
        loadUsers();
    });

    showHideNoClientSelectedMessage()

    function showHideNoClientSelectedMessage() {
        if ($('#clients_selected').children().length === 0) {
            $('#clientsSelectedLabel').addClass('d-none');
            $('#clientsSeparator').addClass('d-none');
        } else {
            $('#clientsSelectedLabel').removeClass('d-none');
            $('#clientsSeparator').removeClass('d-none');
        }
    }

    $('#search-users-notification-form').on('submit', function (event) {
        event.preventDefault();
        loadUsers();
    });

    function formatPhone(value) {
        // Remove todos os caracteres não numéricos
        value = value.replace(/\D/g, '');

        if (value.length <= 10) {
            // Formato para 10 dígitos: (99) 9999-9999
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2') // (99) 9
                .replace(/(\d{4})(\d{1,4})$/, '$1-$2'); // (99) 9999-9999
        } else {
            // Formato para 11 dígitos: (99) 99999-9999
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2') // (99) 9
                .replace(/(\d{5})(\d{1,4})$/, '$1-$2'); // (99) 99999-9999
        }
    }

    function loadUsers(page = 1) {
        event.preventDefault();
        const searchQuery = $('#usersNotificationSearch').val();
        const notificationId = {{ $notification['id'] }};

        function formatCPF(cpf) {
            return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        }

        function formatType(type) {
            const types = {
                'employee': "Funcionário",
                'seller': "Vendedor",
                'client': "Cliente",
            }
            return types[type] ?? 'Não definido'
        }

        $.ajax({
            url: `<?= env('API_URL') ?>/api/siteNotifications/${notificationId}/users?search=${searchQuery}&page=${page}&per_page=5`,
            type: 'get',
            data: {
                search: searchQuery,
                page: page,
                per_page: 5,
            },
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#usersNotificationTableBody').html('<tr><td colspan="3" class="text-center">Carregando...</td></tr>');
                $('#usersNotificationPagination').addClass('d-none');
                $('.loading').removeClass('d-none');
            },
            success: function (response) {
                $('.loading').addClass('d-none');

                // Limpa o corpo da tabela
                $('#usersNotificationTableBody').empty();

                // Adiciona os usuários à tabela
                const notificationClients = response.notifications.data;
                if (notificationClients.length > 0) {
                    $.each(notificationClients, function (index, userNotification) {
                        $('#usersNotificationTableBody').append(`
                            <tr>
                                <td>
                                    ${userNotification.user.name}
                                    <p class="text-muted" style="margin-bottom: 0px">
                                        ${userNotification.user.cpf ? formatCPF(userNotification.user.cpf) : `<span class="info-placeholder"><i class="fas fa-id-card"></i> CPF não informado</span>`}
                                    </p>
                                </td>
                                <td>
                                    <span>
                                        ${userNotification.user.email ? userNotification.user.email : `<span class="info-placeholder"><i class="fas fa-envelope"></i> E-mail não informado</span>`}
                                    </span>                                
                                    <p class="text-muted" style="margin-bottom: 0px">
                                        ${userNotification.user.phone_number_1 ? formatPhone(userNotification.user.phone_number_1) : `<span class="info-placeholder"><i class="fas fa-phone"></i> Telefone não informado</span>`}
                                    </p>
                                </td>
                                <!--<td>${formatType(userNotification.user.type)}</td>-->
                            </tr>
                        `);
                    });
                } else {
                     $('#usersNotificationTableBody').append(`
                        <tr>
                            <td colspan="3" class="text-center" style="padding: 20px; color: #666;">
                                <div style="display: flex; flex-direction: column; align-items: center;">
                                    <i class="fas fa-user-slash" style="font-size: 24px; margin-bottom: 8px;"></i>
                                    <span style="font-style: italic;">Nenhum usuário encontrado</span>
                                </div>
                            </td>
                        </tr>
                    `);
                }

                // Atualiza a paginação
                $('#usersNotificationPagination').empty();
                if (response.notifications.last_page > 1) {
                    for (let i = 1; i <= response.notifications.last_page; i++) {
                        $('#usersNotificationPagination').append(`
                            <li class="page-item ${i === response.notifications.current_page ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i}</a>
                            </li>
                        `);
                    }
                }

                $('#usersNotificationPagination a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadUsers(page);
                });
                
                $('#usersNotificationTable').removeClass('d-none');
                $('#usersNotificationPagination').removeClass('d-none');
            },
            error: function (response, textStatus, msg) {
                $('.loading').addClass('d-none');
                $('#usersNotificationTableBody').html('<tr><td colspan="3" class="text-center">Erro ao carregar usuários</td></tr>');
            }
        });
    }
</script>