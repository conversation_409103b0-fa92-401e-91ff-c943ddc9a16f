<?php

use Carbon\Carbon;

?>
@extends('layouts.app', ['activePage' => 'siteNotifications'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Notificações</h5>
                            </div>

                            <div class="col-lg-9 col-md-6 col-sm-6 col-2 mb-2 d-flex justify-content-end">
                                <div class="d-flex">
                                    @if(ApiUser::hasPermissionOrAdminBusinessSelected('update_site_notifications'))
                                        <a class="btn btn-success mr-1" href="{{ route('siteNotifications.create') }}">
                                            <img class="card-img-left example-card-img-responsive"
                                                 src="{{url('icon/icon_plus.png')}}" width="15px"
                                                 style="margin-top: -2px;"/>
                                            <label class="styleBotaoAdicionar">Cadastrar notificação</label>
                                        </a>
                                    @endif
                                    <button type="button" class="btn btn-secondary" onclick="showNegociosUser(false)">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{url('icon/icon_filtro.png')}}" width="15px"
                                             style="margin-top: -2px;"/>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{route('siteNotifications.index')}}" onsubmit="handleSearch(event)" method="get" id="form_search_notifications">
                                    @csrf
                                    <div class="input-group " id="search_form_desktop">
                                        <input type="text" class="form-control mr-2" name="search"
                                               aria-describedby="search" style="flex: 2;"
                                               placeholder="Pesquisar notificações por título/descrição"
                                               value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        <div class="input-group-append mr-2">
                                            <button 
                                                class="btn btn-light" 
                                                type="submit" 
                                                id="btnSearch" 
                                            >
                                                <img class="card-img-left example-card-img-responsive"
                                                        src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="input-group d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="search"
                                                   aria-describedby="search"
                                                   placeholder="Pesquisar notificações por título/descrição"
                                                   value="{{ isset($search) ? $search : '' }}" id="search_mobile">
                                        </div>
                                        <div class="input-group mb-2">
                                            <button 
                                                class="btn btn-light btn-block" 
                                                type="submit" id="btnSearchMobile" 
                                            >
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="notifications-table">
                                <thead>
                                <tr>
                                    <th scope="col" style="text-align: center;">Ações</th>
                                    <th scope="col" >Título</th>
                                    <th scope="col" >Descrição</th>
                                    <th scope="col" >Negócio</th>
                                    <th scope="col" >Data criação</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($notifications as $notification)
                                    <tr>
                                        <td style="text-align: center">
                                            <div class="btn-group">
                                                <button
                                                    class="btn btn-secondary"
                                                    style="margin-right: 10px"
                                                    onclick="openShowModal({{$notification['id']}})"
                                                >
                                                    <img class="card-img-left example-card-img-responsive"
                                                         src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                                </button>
                                            </div>
                                        </td>
                                        <td title="{{$notification['title']}}">{{ Str::limit($notification['title'], 16, '...') }}</td>
                                        <td title="{{$notification['description']}}">{{ Str::limit($notification['description'], 16, '...') }}</td>
                                        <td title="{{isset($notification['business']) ? $notification['business']['name'] : 'Sem negócio'}}">
                                            {{ isset($notification['business']) ? Str::limit($notification['business']['name'], 16) : 'Sem negócio' }}
                                        </td>
                                        <td>
                                            <div>{{ date('d/m/Y', strtotime($notification['created_at'])) }}</div>
                                            <div>{{ date('H:i:s', strtotime($notification['created_at'])) }}</div>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        {{ $notifications->onEachSide(0)->appends($queryParams)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('siteNotifications.legend')
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('siteNotifications.modal_show')
    @include('components.loading', ['message' => 'Carregando...'])
@endsection

@push('js')
<script type="text/javascript">
    $(document).ready(() => {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });
        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }
    });

    function addLoading(containerId) {
        $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
    }

    function openShowModal(notificationId) {
        addLoading('notification_show_modal_body')
        $(`#SiteNotificationShowModal`).modal('show');
        $.ajax({
            url: `/siteNotifications/${notificationId}/show`,
            type: 'get',
            data: {},
            success: function(response) {
                $('#notification_show_modal_body').html(response);
                const event = new Event('openShowSiteNotification');
                document.dispatchEvent(event);
            },
            error: function(response, textStatus, msg) {
                $('#notification_show_modal_body').html(msg);
            }
        });
    }

    function handleSearch(event) {
        event.preventDefault();
        event.target.submit()
    }

    window.addEventListener('resize', function () {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });
        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }
    });

      document.addEventListener('DOMContentLoaded', () => {
            const element = document.getElementById('next-processing-time');
            if (element) {
                const serverTimestamp = parseInt(element.getAttribute('data-server-time')) * 1000;
                initializeNextProcessingTimeGlobal(element, serverTimestamp);
              }
        });

        function initializeNextProcessingTimeGlobal(element, serverTimestamp) {
            setNextProcessingTime(element, serverTimestamp);
            // Atualiza o horário a cada segundo
            setInterval(() => {
                setNextProcessingTime(element, serverTimestamp);
            }, 1000);
        }

        function setNextProcessingTime(element, serverTimestamp) {
            const clientTimestamp = Date.now();
            const timeDifference = serverTimestamp - clientTimestamp;
            const now = Date.now() + timeDifference;
            const nextExecution = getNextHour(now);
            element.textContent = formatDate(nextExecution);
        }

        function getNextHour(timestamp) {
            const date = new Date(timestamp);
            date.setMinutes(0);
            date.setSeconds(0);
            date.setMilliseconds(0);
            // Avança para a próxima hora cheia
            date.setHours(date.getHours() + 1);
            return date;
        }

        function formatDate(date) {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
        }
    </script>
@endpush
