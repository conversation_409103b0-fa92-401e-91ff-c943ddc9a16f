<?php

use Carbon\Carbon;

?>
@extends('layouts.app', ['activePage' => 'siteNotifications'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Notificações</h5>
                            </div>

                            <div class="col-lg-9 col-md-6 col-sm-6 col-2 mb-2 d-flex justify-content-end">
                                <div class="d-flex">
                                    <button type="button" class="btn btn-secondary" onclick="showNegociosUser(false)">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{url('icon/icon_filtro.png')}}" width="15px"
                                             style="margin-top: -2px;"/>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{route('siteNotifications.indexUser')}}" onsubmit="handleSearch(event)" method="get" id="form_search_notifications">
                                    @csrf
                                    <div class="input-group " id="search_form_desktop">
                                        <input type="text" class="form-control mr-2" name="search"
                                               aria-describedby="search" style="flex: 2;"
                                               placeholder="Pesquisar campanha por título/descrição"
                                               value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        <div class="input-group-append mr-2">
                                            <button 
                                                class="btn btn-light" 
                                                type="submit" 
                                                id="btnSearch" 
                                            >
                                                <img class="card-img-left example-card-img-responsive"
                                                        src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <div class="input-group mb-2" style="flex: 2">
                                            <select class="form-control" name="viewed" aria-describedby="viewed" id="viewed_desktop">
                                                <option value="all" @if(!isset($viewed) || $viewed == 'all') selected @endif>Todas</option>
                                                <option value="1" @if(isset($viewed) && $viewed == '1') selected @endif>Lidas</option>
                                                <option value="0" @if(isset($viewed) && $viewed == '0') selected @endif>Não lidas</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="input-group d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="search"
                                                   aria-describedby="search"
                                                   placeholder="Pesquisar notificações por título/descrição"
                                                   value="{{ isset($search) ? $search : '' }}" id="search_mobile">
                                        </div>
                                        <div class="input-group mb-2" style="flex: 2">
                                            <select class="form-control" name="viewed" aria-describedby="viewed" id="viewed_mobile">
                                                <option value="all" @if(isset($viewed) && $viewed == 'all') selected @endif>Todas</option>
                                                <option value="1" @if(isset($viewed) && $viewed == '1') selected @endif>Lidas</option>
                                                <option value="0" @if(!isset($viewed) || $viewed == '0') selected @endif>Não lidas</option>
                                            </select>
                                        </div>
                                        <div class="input-group mb-2">
                                            <button 
                                                class="btn btn-light btn-block" 
                                                type="submit" id="btnSearchMobile" 
                                            >
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="notifications-table">
                                <thead>
                                <tr>
                                    <th scope="col" style="text-align: center;">Ações</th>
                                    <th scope="col" >Título</th>
                                    <th scope="col" >Descrição</th>
                                    <th scope="col" >Negócio</th>
                                    <th scope="col" >Lida em</th>
                                    <th scope="col" >Data criação</th>
                                </tr>
                                </thead>
                                <tbody>
                                    @foreach ($notifications as $notification)
                                    <tr>
                                        <td style="text-align: center">
                                            <div class="btn-group">
                                                <button
                                                    class="btn btn-secondary"
                                                    style="margin-right: 10px"
                                                    onclick="$('#SiteNotificationShowModal-{{$notification['id']}}').modal('show')"
                                                >
                                                    <img class="card-img-left example-card-img-responsive"
                                                         src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                                </button>
                                                <button
                                                    class="btn btn-danger"
                                                    type="button"
                                                    onclick="$(`#cancelSiteNotificationModal{{$notification['id']}}`).modal('show')"
                                                >
                                                    <i class="fas fa-trash fa-fw"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td title="{{$notification['notification']['title']}}">{{ Str::limit($notification['notification']['title'], 16, '...') }}</td>
                                        <td title="{{$notification['notification']['description']}}">{{ Str::limit($notification['notification']['description'], 16, '...') }}</td>
                                        <td title="{{isset($notification['notification']['business']) ? $notification['notification']['business']['name'] : 'Sem negócio'}}">
                                            {{ isset($notification['notification']['business']) ? Str::limit($notification['notification']['business']['name'], 10) : config('app.name') }}
                                        </td>
                                        <td title="{{isset($notification['viewed_at']) ? Carbon::parse($notification['viewed_at'])->format('d/m/Y H:i:s') : 'Não visualizada'}}">
                                            @if(isset($notification['viewed_at']))
                                                {{ Carbon::parse($notification['viewed_at'])->format('d/m/Y') }}
                                                <p>
                                                    {{ Carbon::parse($notification['viewed_at'])->format('H:i:s') }}
                                                </p>
                                            @else
                                                <p>Não visualizada</p>
                                            @endif
                                        </td>
                                        <td>
                                            {{ Carbon::parse($notification['notification']['created_at'])->format('d/m/Y') }}
                                            <p>
                                                {{ Carbon::parse($notification['notification']['created_at'])->format('H:i:s') }}
                                            </p>
                                        </td>
                                    </tr>
                                    <x-modal.modal_confirm_action 
                                        id="cancelSiteNotificationModal{{$notification['id']}}"
                                        title="Deletar notificação"
                                        onSubmit="deleteUserSiteNotification({{$notification['id']}})"
                                    >
                                        <p>Você realmente deseja deletar essa notificação?</p>
                                    </x-modal.modal_confirm_action>
                                    @include('siteNotifications.modal_show_user_notification', ['notification' => $notification])
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                        {{ $notifications->onEachSide(0)->appends($queryParams)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('siteNotifications.legend_user')
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...'])
@endsection

@push('js')
<script type="text/javascript">
    $(document).ready(() => {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });
        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }

        let viewed_desktop = document.getElementById('viewed_desktop');
        let viewed_mobile = document.getElementById('viewed_mobile');
        viewed_desktop.addEventListener('change', function () {
            viewed_mobile.value = viewed_desktop;
            $('#form_search_notifications').submit();
        });

        viewed_mobile.addEventListener('change', function () {
            viewed_desktop.value = viewed_mobile;
            $('#form_search_notifications').submit();
        });
    });

    function handleSearch(event) {
        event.preventDefault();
        event.target.submit()
    }

    window.addEventListener('resize', function () {
        if (window.innerWidth <= 575) {
            $('#search_form_desktop').addClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });

            $('#search_form_mobile').removeClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });
        } else {
            $('#search_form_desktop').removeClass('d-none');
            $('#search_form_desktop').find('input, select').each(function () {
                $(this).prop('disabled', false);
            });

            $('#search_form_mobile').addClass('d-none');
            $('#search_form_mobile').find('input, select').each(function () {
                $(this).prop('disabled', true);
            });
        }
    });

    function deleteUserSiteNotification(notificationId) {
        const notificationItem = $(`.notification-item[notificationId="${notificationId}"]`);
        $.ajax({
            url: `<?= env('API_URL') ?>/api/siteNotifications/${notificationId}`,
            type: "delete",
            cache: false,
            contentType: false,
            processData: false,
            contentType: 'application/json',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>')
            },
            success: function(response) {
                if (notificationItem) {
                    const notificationList = notificationItem.closest('.notification-list');
                    notificationItem.remove();
                    if (notificationList.children().length === 0) {
                        notificationList.html('<p class="text-center">Nenhuma notificações encontrada</p>');
                    }
                }
                window.location.reload();
            },
            error: function(response) {
                console.error('Erro ao excluir notificação:', response.responseJSON);
                window.location.reload();
            },
        });
    }

</script>
@endpush
