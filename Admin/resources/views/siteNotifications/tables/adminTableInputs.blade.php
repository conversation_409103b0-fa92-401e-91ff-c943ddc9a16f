<div>
    <div class="d-flex flex-column" id="adminTableInputsWrapper">
        <div class="col-md-12 mb-2 d-flex flex-column flex-sm-row" style="gap: 4px">
            @if (ApiUser::hasUserFunction('release_all_business'))
                <select class="form-control mb-sm-0 col-md-4" name="profile" id="siteNotificationUserProfile">
                    <option value="all" selected>Perfil: Todos os perfis</option>
                    <option value="client">Apenas Clientes</option>
                    <option value="employee">Apenas Funcionários</option>
                    @if (ApiUser::hasUserFunction('sellers'))
                        <option value="seller">Apenas Vendedores</option>
                    @endif
                </select>
                <select class="form-control mb-sm-0 col-md-4" name="isAdmin" id="siteNotificationAdminType">
                    <option value="all" selected>Administrador: Todos os Usuários</option>
                    <option value="yes">Administradores</option>
                    <option value="no">Não Administradores</option>
                </select>
                <select class="form-control mb-sm-0 col-md-4" name="is_entrepreneur"
                    id="siteNotificationAdminEntrepreneur">
                    <option value="all" selected>Empreendedor: Todos os Usuários</option>
                    <option value="yes">Apenas Empreendedores</option>
                    <option value="no">Não Empreendedores</option>
                </select>
            @endif
        </div>
        <div class="col-md-12 mb-2 d-flex flex-column flex-sm-row" style="gap: 4px">
            <input type="text" id="usersSearch" class="form-control mb-sm-0 col-md-6"
                placeholder="Pesquisar usuário...">
            <input type="text" id="searchBusiness" class="form-control mb-sm-0 col-md-6"
                placeholder="Pesquisar Negócio/CPNJ...">
        </div>
        <div class="input-group-append mb-2 mb-sm-0">
            <button class="btn btn-light w-100" type="button" id="search-notification-add">
                <i class="fas fa-search fa-fw" style="color: #6c757d"></i>
            </button>
        </div>
    </div>
</div>

