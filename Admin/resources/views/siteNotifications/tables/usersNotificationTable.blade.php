@php
    $isAdminBusinessSelected = ApiUser::isAdminBusinessSelected();
@endphp

<div class="form-group my-2 col-md-12">
    <hr id="clientsSeparator" />
    @if ($isAdminBusinessSelected)
        @include('siteNotifications.tables.adminTableInputs')
    @else
        @include('siteNotifications.tables.employeeTableInputs')
    @endif
</div>
<div class="table" style="overflow: auto;">
    <div class="d-flex align-items-center justify-content-between flex-wrap">
        <p class="text-muted small my-1 mb-sm-0">
            <i class="fas fa-list-ul mr-1"></i> Total de <span class="spanNotificationTableUsersCount">0</span> usuário(s)
            encontrado(s)
        </p>

        <div class="text-muted small d-flex align-items-center justify-content-start justify-content-sm-end mt-2 mt-sm-0"
            id="selectedUsersInfo">
            <i class="fas fa-check-square mr-1"></i>
            <span id="selectedUsersCount" class="mr-1">0</span> usuário(s) selecionado(s)
        </div>
    </div>
    <table class="table table-striped w-100 d-none" style="white-space: nowrap;" id="employeesNotificationTable">
        <thead>
            <tr>
                <th scope="col">
                    <input name="allUsers" type="checkbox" id="addAllUsersButton" />
                </th>
                <th scope="col">Nome/CPF</th>
                <th scope="col">Contato</th>
                @if (ApiUser::isAdminBusinessSelected())
                    <th scope="col">Perfil</th>
                    <th scope="col">Admin</th>
                    <th scope="col">Empreendedor</th>
                    <th scope="col">Negócio</th>
                @endif
            </tr>
        </thead>
        <tbody id="employeesNotificationTableBody">
            <div class="d-flex loading flex-column align-items-center justify-content-center py-2 d-none">
                <div class="spinner-border text-primary loading" role="status" style="width: 3rem; height: 3rem;">
                </div>
            </div>
            <!-- Clientes serão carregados aqui -->
        </tbody>
    </table>
    <p><small id="error-number_clients" class="p-1 text-danger"></small></p>
</div>
<nav class="d-flex justify-content-between align-items-center flex-wrap">
    <div>
        <ul class="pagination mb-0" id="employeesNotificationPagination">
            <!-- Paginação será carregada aqui -->
        </ul>
        <p class="text-muted small mt-2 mb-sm-0" style="margin-bottom: 0px">
            <i class="fas fa-list-ul mr-1"></i> Total de <span class="spanNotificationTableUsersCount">0</span>
            usuário(s) encontrado(s)
        </p>
    </div>

    <div class="d-flex justify-content-end align-items-center gap-2 mt-3">
        <span class="text-muted small mr-1">Usuários por página:</span>
        <select class="form-select form-select-sm shadow-sm border border-secondary-subtle"
            style="width: auto; min-width: 80px;" id="perPageSelect">
            <option value="5">5</option>
            <option value="10" selected>10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
        </select>
    </div>
</nav>
</div>
<input type="hidden" name="number_users" id="numberUsers">
<input type="hidden" name="filtersJSON" id="filtersInput">
<input type="hidden" name="userIdsJSON" id="userIds">

<script>
    // guarda os filtros que foram performados
    let activeFilters = {};

    // guarda os ids de todos os usuários listados
    let activeUsersIds = [];

    $('document').ready(function() {
        loadUsers();
        showHideNoUserSelectedMessage();

        $('#addAllUsersButton').on('click', function() {
            const isChecked = $(this).is(':checked');
            const idsInput = document.querySelector('#userIds');

            if (isChecked) {
                let idsStored = idsInput.value ? JSON.parse(idsInput.value) : [];
                // Usa todos usuários listados (todas páginas)
                let allUserIds = activeUsersIds;

                // remover ids duplicados
                let usersCombinedIds = new Set([...idsStored, ...allUserIds]);

                // volta para array, pois JSON.stringfy() não lida bem com Sets
                let usersCombinedIdsArray = Array.from(usersCombinedIds);
                idsInput.value = JSON.stringify(usersCombinedIdsArray);

                // Marca como selecionado os usuários da página atual (Os outros vão ser modificados pelo loadUsers())
                $('#employeesNotificationTableBody').find('.usersTableCheckbox').each(
                    function() {
                        const userId = Number($(this).val());
                        $(this).prop('checked', usersCombinedIds.has(userId));
                    });

                updateTitleMessage();
                showHideNoUserSelectedMessage();
            } else {
                // Limpa seleção
                idsInput.value = JSON.stringify([]);
                $('#employeesNotificationTableBody').find('.usersTableCheckbox').prop('checked', false);
                updateTitleMessage();
                showHideNoUserSelectedMessage();
            }
        });

        // ao clicar em Enter realiza a busca 
        $('#adminTableInputsWrapper, #employeeInputsWrapper').on('keydown', 'input, select', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                loadUsers();
            }
        });

        // Ao mudar um select nos inputs faz a busca
        $('#adminTableInputsWrapper, #employeeInputsWrapper, #perPageSelect').on('change', function(event) {
            if (event.target.tagName.toLowerCase() === 'select') {
                loadUsers();
            }
        });
    });

    // ao clicar na lupa, executa a pesquisa
    $('#search-notification-add').on('click', function() {
        loadUsers();
    })

    // Função auxiliar pra retornar os tipos em formato de descrição
    function getUserTypeDescription(types = {}) {
        if (Object.keys(types).length === 0)
            return 'Não possui perfil';

        const profiles = Object.values(types)
            .filter(type => type.hasType)
            .map(type => type.name)
            .sort((a, b) => a.localeCompare(b));

        return profiles.length > 0 ? profiles.join(', ') : 'Não possui perfil';
    }

    // Função auxiliar pra retornar os negócios em formato de descrição
    function getUserBusinessesDescription(businesses = [], maxSize = null) {
        if (businesses.length === 0)
            return "Sem negócio";
        const businessesNames = businesses.map((item, index) => item.name);

        let description = businessesNames.join(', ');
        if (maxSize) {
            description = description.slice(0, maxSize);
        }

        return description.length > 0 ? description : 'Sem negócio';
    }

    function formatCPF(cpf) {
        return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    }

    // Retorna o estado atual dos filtros
    function getCurrentFilters() {
        return {
            search: $('#usersSearch').val() || undefined,
            per_page: $('#perPageSelect').val() || 10,
            isAdmin: $('#siteNotificationAdminType').val() !== 'all' ? $('#siteNotificationAdminType').val() :
                undefined,
            isEntrepreneur: $('#siteNotificationAdminEntrepreneur').val() !== 'all' ? $(
                '#siteNotificationAdminEntrepreneur').val() : undefined,
            profile: $('#siteNotificationUserProfile').val() !== 'all' ? $('#siteNotificationUserProfile').val() :
                undefined,
            searchBusiness: $('#searchBusiness').val() || undefined
        };
    }

    function formatPhone(value) {
        // Remove todos os caracteres não numéricos
        value = value.replace(/\D/g, '');

        if (value.length <= 10) {
            // Formato para 10 dígitos: (99) 9999-9999
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2') // (99) 9
                .replace(/(\d{4})(\d{1,4})$/, '$1-$2'); // (99) 9999-9999
        } else {
            // Formato para 11 dígitos: (99) 99999-9999
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2') // (99) 9
                .replace(/(\d{5})(\d{1,4})$/, '$1-$2'); // (99) 99999-9999
        }
    }

    function isAllUsersListedInStoredUsersIds() {
        const idsInput = document.querySelector('#userIds');
        let idsStored = idsInput.value ? JSON.parse(idsInput.value) : [];
        return activeUsersIds.every(id => idsStored.includes(id));
    }

    function loadUsers(page = 1, show_defaut_option = true) {
        const clients_id = [];
        const numberUsersInput = document.querySelector("#numberUsers");
        const addAllUsersButton = $('#addAllUsersButton');
        const idsInput = document.querySelector('#userIds');
        const isAdminBusinessSelected = @json($isAdminBusinessSelected ?? false);
        let idsStored = idsInput.value ? JSON.parse(idsInput.value) : [];

        activeFilters = getCurrentFilters();

        const params = {
            ...activeFilters,
            page: page
        };

        Object.keys(params).forEach(key => params[key] === undefined && delete params[key]);

        $.ajax({
            url: `<?= env('API_URL') ?>/api/users/`,
            type: 'get',
            data: params,
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                // efeito anterior
                // $('#employeesNotificationTable').addClass('d-none'); 

                // Efeito de opacidade 
                $('#employeesNotificationTable').addClass('table-disabled');
                $('.loading').removeClass('d-none');
            },
            success: function(response) {
                const filters = params
                delete filters['page'];
                delete filters['per_page'];
                filtersInput.value = JSON.stringify(filters);
                $('.loading').addClass('d-none');

                $('#employeesNotificationTableBody').empty();
                const users = response.users.data;
                activeUsersIds = response.usersIds;

                numberUsersInput.value = response.users.total;
                updateTitleMessage()

                // reseta o botão de selecionar todos se houver algum usuário que não foi selecionado
                addAllUsersButton.prop('checked', isAllUsersListedInStoredUsersIds());

                if (users.length > 0) {
                    $.each(users, function(index, user) {
                        const isChecked = idsStored.includes(user.id) ? 'checked' : '';
                        $('#employeesNotificationTableBody').append(`
                        <tr>
                            <td style="width: 1%; text-align: center">
                                <input 
                                    type="checkbox" 
                                    id="user_button_${user.id}"
                                    class="usersTableCheckbox"
                                    value="${user.id}"
                                    ${isChecked}
                                    onchange="addUser(this)"
                                />
                            </td>
                            <td>
                                ${user.name}
                                <p class="text-muted" style="margin-bottom: 0px">
                                    ${user.cpf ? formatCPF(user.cpf) : `<span class="info-placeholder"><i class="fas fa-id-card"></i> CPF não informado</span>`}
                                </p>
                            </td>
                            <td>
                                <span>
                                    ${user.email ? user.email : `<span class="info-placeholder"><i class="fas fa-envelope"></i> E-mail não informado</span>`}
                                </span>                                
                                <p class="text-muted" style="margin-bottom: 0px">
                                    ${user.phone_number_1 ? formatPhone(user.phone_number_1) : `<span class="info-placeholder"><i class="fas fa-phone"></i> Telefone não informado</span>`}
                                </p>
                            </td>
                        ${isAdminBusinessSelected ? `<td>
                                ${getUserTypeDescription(user.types)}
                            </td>
                            <td>
                                ${user.is_admin_of_business ? 'Sim' : 'Não'}
                            </td>
                            <td>
                                ${user.is_entrepreneur ? 'Sim' : 'Não'}
                            </td>
                            <td 
                                title="${getUserBusinessesDescription(user.businesses)}"
                                class="ellipsis-td">
                                ${getUserBusinessesDescription(user.businesses)}
                            </td>` : ''}
                        </tr>
                    `);
                    });
                } else {
                    $('#employeesNotificationTableBody').append(
                        `<tr><td colspan="9" style="text-align: center">Nenhum Usuário encontrado</td></tr>`
                    );
                }

                // Paginação
                renderPagination('employeesNotificationPagination', response.users.current_page, response
                    .users.last_page, loadUsers);

                $('#employeesNotificationTable').removeClass('d-none');
                // Efeito de opacidade 
                $('#employeesNotificationTable').removeClass('table-disabled');
            },
            error: function(response, textStatus, msg) {
                $('.loading').addClass('d-none');
            }
        });
    }

    function showHideNoUserSelectedMessage() {
        if ($('#users_selected').children().length === 0) {
            $('#usersSelectedLabel').addClass('d-none');
            $('#usersSeparator').addClass('d-none');
        } else {
            $('#usersSelectedLabel').removeClass('d-none');
            $('#usersSeparator').removeClass('d-none');
        }
    }

    function createSelectedUserElement(userId, name, cpf) {
        let li = document.createElement('li');
        li.className = 'list-group-item d-flex justify-content-between align-items-center flex-column text-start';
        li.id = `user_selected_${userId}`;

        let newElement = document.createElement('div');
        newElement.className = 'w-100 d-flex justify-content-between align-items-center';
        newElement.innerHTML = `
            <span>
                ${name}
                <p class="text-muted mb-0">${formatCPF(cpf)}</p>
            </span>
            <span 
                class="badge badge-danger" 
                style="cursor: pointer; padding: 5px 10px; font-size: 14px;" 
                onclick="removeUser(${userId})"
            >X</span>
        `;

        li.appendChild(newElement);
        $('#users_selected').append(li);
    }

    function updateTitleMessage() {
        const numberUsersInput = document.querySelector("#numberUsers");
        const idsInput = document.querySelector('#userIds');
        let idsStored = idsInput.value ? JSON.parse(idsInput.value) : [];
        const spansTotalUsers = $('.spanNotificationTableUsersCount');
        const countElement = $('#selectedUsersCount');
        const infoElement = $('#selectedUsersInfo');

        countElement.text(idsStored.length);

        spansTotalUsers.each(function() {
            $(this).text(numberUsersInput.value);
        });

        if (idsStored.length > 0) {
            infoElement.removeClass('text-muted').addClass('text-primary font-weight-bold');
        } else {
            infoElement.removeClass('text-primary font-weight-bold').addClass('text-muted');
        }
    }

    function removeUser(userId) {
        const idsInput = document.querySelector('#userIds');
        let idsStored = idsInput.value ? JSON.parse(idsInput.value) : [];
        idsStored = idsStored.filter(id => id !== userId);
        $(`#user_button_${userId}`).prop('checked', false);
        idsInput.value = JSON.stringify(idsStored);
        showHideNoUserSelectedMessage();
        updateTitleMessage();
    }

    function addUser(element) {
        const userId = Number($(element).val());
        const idsInput = document.querySelector('#userIds');
        let idsStored = idsInput.value ? JSON.parse(idsInput.value) : [];
        if ($(element).is(':checked')) {
            idsStored.push(userId);
            idsInput.value = JSON.stringify(idsStored);
            showHideNoUserSelectedMessage();
            updateTitleMessage();
        } else {
            removeUser(userId);
        }
    }
</script>
