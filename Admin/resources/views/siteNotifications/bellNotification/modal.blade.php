<style>
    .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        font-weight: 500;
    }

    .nav-tabs .nav-link.active {
        color: #007bff;
        border-bottom: 2px solid #007bff;
    }

    .notification-list {
        margin-top: 4px;
    }

    .popover-notification {
        position: fixed;
        top: 5%;
        left: 300px;
        z-index: 1000;
        background-color: white;
        border: 1px solid #ddd;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        visibility: hidden;
        opacity: 0;
        width: 50%;
        max-width: 28rem !important;
        transition: opacity 300ms ease, transform 300ms ease, visibility 300ms ease;
        transform: translateX(-20px);
    }

    .popover-notification.collapsed {
        left: 100px;
        margin-left: 5px;
    }

    .popover-notification.show {
        visibility: visible;
        opacity: 1;
        transform: translateX(0);
    }

    .popover-header {
        padding: 10px;
        border-bottom: 1px solid #ddd;
    }

    .popover-body {
        padding: 10px;
        max-height: 20rem;
        overflow-y: auto;
    }

    .popover-body::-webkit-scrollbar {
        width: 8px;
    }

    .popover-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .popover-body::-webkit-scrollbar-thumb {
        background: {{ SystemHelper::get()['MAIN_COLOR'] ?? '#FBBC05' }};
        border-radius: 4px;
    }

    .popover-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    .popover-footer {
        padding: 10px;
        border-top: 1px solid #ddd;
    }

    @media screen and (max-width: 900px) {
        #notification-menu-button {
            display: none;
        }

        .popover-notification {
            left: auto;
            right: 0px;
            width: 90%;
            max-width: 35rem !important;
            transform: translateY(-20px);
        }

        .popover-notification.show {
            transform: translateY(0);
        }
    }
</style>

<div>    
    <input type="hidden" name="unreadNotificationIds" id="unreadNotificationIdsInput" value="">
    <div class="popover-notification" id="notificationPopover">
        <div class="popover-header">
            <ul class="nav nav-tabs" id="notificationTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link" id="all-tab" data-toggle="tab" href="#all" role="tab" aria-controls="all" aria-selected="false">Todas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" id="unread-tab" data-toggle="tab" href="#unread" role="tab" aria-controls="unread" aria-selected="true">Não Lidas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="read-tab" data-toggle="tab" href="#read" role="tab" aria-controls="read" aria-selected="false">Lidas</a>
                </li>
            </ul>
        </div>
        <div class="popover-body">
            <div class="text-center" style="display: none" id="loadingNotifications">
                <div class="spinner-border text-dark" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
            </div>
            <!-- Conteúdo das Abas -->
            <div class="tab-content" id="notificationTabsContent">
                <!-- Todas -->
                <div class="tab-pane fade" id="all" role="tabpanel" aria-labelledby="all-tab">
                    <div class="notification-list">
                        {{-- as notificações vão ser carregads quando selecinada "todas" --}}
                    </div>
                </div>
    
                <!-- Não Lidas -->
                <div class="tab-pane fade show active" id="unread" role="tabpanel" aria-labelledby="unread-tab">
                    <div class="notification-list">
                        {{-- as notificações vão ser carregads quando selecinada "não lidas" --}}
                    </div>
                </div>
    
                <!-- Lidas -->
                <div class="tab-pane fade" id="read" role="tabpanel" aria-labelledby="read-tab">
                    <div class="notification-list">
                        {{-- as notificações vão ser carregads quando selecinada "lidas" --}}
                    </div>
                </div>
            </div>
        </div>
        <div class="popover-footer">
            <a href="{{ route('siteNotifications.indexUser') }}" class="btn btn-outline-dark w-100 rounded-0">
                Ver Todas as Notificações
            </a>
        </div>
    </div>
</div>


<script>
    $(document).ready(function() {        
        const popover = document.getElementById('notificationPopover');
        getUnreadNotificationsCount();

        $('#notification-menu-button, #notification-menu-button-mobile').on('click', function() {
            if ($(this).hasClass('collapsed')) {
                $('#notificationPopover').addClass('collapsed');
            } else $('#notificationPopover').removeClass('collapsed');
            $('#notificationPopover').toggleClass('show');
            if ($('#notificationPopover').hasClass('show')) {
                $('.notification-list').empty();
                selectTabBasedOnNotificationCount();
                decreaseNotificationCount();
                fetchNotifications();
            }
        });

        $('#notificationTabs .nav-link').on('shown.bs.tab', function() {  
            const tabContentId = $(this).attr('href');
            const notificationList = $(tabContentId).find('.notification-list');
            if (notificationList.children().length > 0)
                return; // já foram carregadas as notificações daquela tab
            fetchNotifications();
        });

        function closePopover() {
            $('#notificationPopover').removeClass('show');
            markAsRead();
        }

        $(document).on('click', function(event) {
            if (!$(event.target).closest('#notificationPopover, #notification-menu-button, #notification-menu-button-mobile').length) {
                closePopover();
            }
        });

        window.addEventListener('beforeunload', () => {
            if (popover.classList.contains('show')) {
                // Se o popover estiver aberto quando for desmontado, marcar como lida
                markAsRead()
            }
        })
    });

    function updateNotificationCount(value = 0) {
        $('#unreadNotificationsCount').text(value);
        $('#unreadNotificationsCountMobile').text(value);
        if (value != 0) {
            $('#unreadNotificationBadge').removeClass('d-none');
            $('#unreadNotificationBadgeMobile').removeClass('d-none');
        } else {
            if ($('#unreadNotificationBadge').hasClass('d-none') || $('#unreadNotificationBadgeMobile').hasClass('d-none') )
                        return;
            $('#unreadNotificationBadge').addClass('d-none');
            $('#unreadNotificationBadgeMobile').addClass('d-none'); 
        }
    }

    function decreaseNotificationCount() {
        const unreadCountSpanMobile = document.getElementById('unreadNotificationsCountMobile');
        let storedValue = parseInt(unreadCountSpanMobile.innerText);
        if (storedValue == 0) return;
        if (storedValue > 10) {
            storedValue = Math.max(0, storedValue-10);
        } else storedValue = 0;
        updateNotificationCount(storedValue);
    }

    function selectTabBasedOnNotificationCount() {
        const unreadCountSpanMobile = document.getElementById('unreadNotificationsCountMobile');
        const storedValue = parseInt(unreadCountSpanMobile.innerText);
        if (storedValue == 0){
            $('#read-tab').removeClass('active');
            $('#read').removeClass('show active');
            $('#unread-tab').removeClass('active');
            $('#unread').removeClass('show active');
            $('#all-tab').addClass('active');
            $('#all').addClass('show active'); 
        } else {
            $('#read-tab').removeClass('active');
            $('#read').removeClass('show active');
            $('#all-tab').removeClass('active');
            $('#all').removeClass('show active');
            $('#unread-tab').addClass('active');
            $('#unread').addClass('show active'); 
        }
    }

    function getUnreadNotificationsCount() {
        $('#notification-menu-button').prop('disabled', true);
        $('#notification-menu-button-mobile').prop('disabled', true);
        $.ajax({
            url: "<?= env('API_URL') ?>/api/siteNotifications/countUnread",
            type: "get",
            data: {},
            cache: false,
            contentType: false,
            processData: false,
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>')
            },
            success: function (response) {
                updateNotificationCount(response.count);
                $('#notification-menu-button').prop('disabled', false);
                $('#notification-menu-button-mobile').prop('disabled', false);
            },
            error: function (response) {
                $('#notification-menu-button').prop('disabled', false);
                $('#notification-menu-button-mobile').prop('disabled', false);
            },
        });
    }

    function formatNotificationDate(date) {
        const now = new Date();
        const notificationDate = new Date(date);
        const diff = Math.floor((now - notificationDate) / 1000);

        if (diff < 60) {
            return "agora mesmo";
        } else if (diff < 3600) {
            const minutos = Math.floor(diff / 60);
            return `há ${minutos} minuto${minutos !== 1 ? 's' : ''}`;
        } else if (diff < 86400) {
            const horas = Math.floor(diff / 3600);
            return `há ${horas} hora${horas !== 1 ? 's' : ''} atrás`;
        } else {
            const dias = Math.floor(diff / 86400);
            return `há ${dias} dia${dias !== 1 ? 's' : ''} atrás`;
        }
    }

    function fetchNotifications() {
        const activeTab = $('#notificationTabsContent .tab-pane.active');
        const activeTabNotificationList = activeTab.find('.notification-list');
        activeTabNotificationList.empty();
        const isAllTab = activeTab[0].id === 'all';
        const isReadTab = activeTab[0].id === 'read';
        let viewed = 'all';

        if (!isAllTab) {
            viewed = isReadTab;
        }

        $('#loadingNotifications').show();
        $('#notificationTabs .nav-link').addClass('disabled');

        $.ajax({
            url: `<?= env('API_URL') ?>/api/siteNotifications/list?viewed=${viewed}`,
            type: "get",
            data: {viewed},
            cache: false,
            contentType: false,
            processData: false,
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>')
            },
            success: function (response) {
                $('#loadingNotifications').hide();

                if (response.notifications && response.notifications.data.length > 0) {
                    if (!isAllTab && !isReadTab) {
                        const unreadNotificationIds = response.notifications.data.map(notification => notification.id);
                        document.getElementById('unreadNotificationIdsInput').value = JSON.stringify(unreadNotificationIds);
                    }
                    const notificationsHtml = response.notifications.data.map(userNotification => `
                        <a class="notification-item"
                            ${userNotification.notification.url_click ? `style="cursor: pointer" href="${userNotification.notification.url_click}"` : ''}
                            notificationId=${userNotification.id}
                        >
                            <div class="notification-body">
                                <div class="notification-main">
                                    <div class="notification-icon">
                                        ${userNotification.notification.icon ? 
                                            `<img src="${userNotification.notification.icon}" class="notification-icon-img" alt="Ícone">` 
                                            : `<i class="fas fa-bell fa-fw"></i>`
                                        }
                                    </div>
                                    <div class="notification-content">
                                        <h6>${userNotification.notification.title}</h6>
                                        <p>${userNotification.notification.description}</p>
                                    </div>
                                    <div class="notification-action">
                                        <i class="fas fa-trash" onclick="deleteNotification(${userNotification.id})"></i>
                                    </div>
                                </div>
                                <div class="notification-viewed text-muted">  
                                    ${userNotification.viewed ? `Visto ${formatNotificationDate(userNotification.viewed_at)}` : ''}
                                </div>
                            </div>
                        </a>
                    `).join('');
                    activeTabNotificationList.html(notificationsHtml);
                } else {
                    activeTabNotificationList.html('<p class="text-center">Nenhuma notificação encontrada.</p>');
                }
                $('#notificationTabs .nav-link').removeClass('disabled');
            },
            error: function (response) {
                $('#loadingNotifications').hide();

                activeTabNotificationList.html('<p class="text-center text-danger">Erro ao carregar notificações.</p>');
                console.error(response);
                $('#notificationTabs .nav-link').removeClass('disabled');
            },
        });
    }

    function markAsRead() {
        const ids = document.getElementById('unreadNotificationIdsInput').value ?? null; 
        if (!ids)
            return;
        const unreadNotificationIds = JSON.parse(ids);
        if (unreadNotificationIds.length === 0) {
            return;
        }
        $.ajax({
            url: "<?= env('API_URL') ?>/api/siteNotifications/markAsRead",
            type: "post",
            data: JSON.stringify({ ids: unreadNotificationIds }),
            cache: false,
            contentType: false,
            processData: false,
            contentType: 'application/json',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>')
            },
            success: function(response) {
                getUnreadNotificationsCount();
            },
            error: function(response) {
                console.error('Erro ao marcar notificações como lidas:', response.error);
            },
        });
    }

    function deleteNotification(notificationId) {
        event.stopImmediatePropagation()
        if (!confirm('Tem certeza que deseja excluir esta notificação?')) {
            return;
        }

        const notificationItem = $(`.notification-item[notificationId="${notificationId}"]`);
        if (!notificationItem)
            return;

        notificationItem.addClass('removing')
        $.ajax({
            url: `<?= env('API_URL') ?>/api/siteNotifications/${notificationId}`,
            type: "delete",
            cache: false,
            contentType: false,
            processData: false,
            contentType: 'application/json',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>')
            },
            success: function(response) {
                const notificationList = notificationItem.closest('.notification-list');

                notificationItem.remove();

                if (notificationList.children().length === 0) {
                    notificationList.html('<p class="text-center">Nenhuma notificações encontrada</p>');
                }
            },
            error: function(response) {
                console.error('Erro ao excluir notificação:', response.responseJSON);
            },
        });
    }
</script>