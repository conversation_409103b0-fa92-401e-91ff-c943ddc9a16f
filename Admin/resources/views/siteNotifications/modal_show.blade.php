<?php

use App\Helpers\StringMask;
use Carbon\Carbon;

?>
<div class="modal fade" id="SiteNotificationShowModal" tabindex="-1" role="dialog"
     aria-labelledby="SiteNotificationShowModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Dados da notificação</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto" style="text-align: left; max-height: calc(100vh - 200px); overflow-y: auto;" id="notification_show_modal_body">
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" style="width:30%">Fechar</button>
            </div>
        </div>
    </div>
</div>

@push('js')
<script type="text/javascript">
    function closeModal(modalId) {
        $(`#${modalId}`).toggle()
    }
</script>
@endpush