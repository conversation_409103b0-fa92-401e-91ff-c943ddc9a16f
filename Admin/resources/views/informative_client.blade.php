@php use App\Helpers\ApiUser; @endphp
@extends('layouts.app', ['activePage' => 'informative'])

<style>
    .full-height-container {
        display: flex;
        flex-direction: column;
        height: calc(100dvh - 4.25rem);
        overflow-y: hidden;
    }

    .informative-card {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    #homeIframe {
        flex: 1;
    }

    .informative-card-body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    @media(max-width: 575px) {
        .full-height-container {
            height: calc(100dvh - 8rem);
        }
    }

</style>

@section('content')
<div class="container-fluid full-height-container">
    <div class="row justify-content-center" style="flex: 1;">
        <div class="col-lg-12 col-md-12 col-sm-12 d-flex">
            <div class="card card_conteudo informative-card">
                <div class="card-header p-0"
                        style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <form action="{{ route('informative.markAsRead') }}" id="markAsReadForm" style="display: none; margin: 0;" method="POST">
                            @csrf
                            @method('POST')
                            <div class="form-group col-md-12" style="margin-bottom: 0.5rem; margin-top: 0.5rem">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox"
                                        class="custom-control-input"
                                        name="informative_read"
                                        id="informative_read"
                                        value="1"
                                        @if (ApiUser::get()['is_informative_read']) checked @endif>
                                    <label class="custom-control-label" for="informative_read">
                                        Primeiros passos
                                    </label>
                                </div>
                            </div>
                        </form>
                </div>
                <div class="informative-card-body p-0">
                    <div id="loadingIndicatorHome" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Carregando...</span>
                        </div>
                        <p class="mt-2">Carregando conteúdo...</p>
                    </div>
                    <iframe 
                        frameborder="0"
                        id="homeIframe"
                        style="height: 100%; width:100%; border: none; display: none;">
                    </iframe>
                </div>
                <div class="card-footer table-legend p-0"
                    style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                </div>
            </div>
        </div>
    </div>
    @if ($message = Session::get('success'))
        <div class="alert alert-success alert-block shadow fade show"
                style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
            <strong>{{$message}}</strong>
        </div>
    @elseif($message = Session::get('error'))
        <div class="alert alert-danger alert-block shadow fade show"
                style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
            <strong>{{$message}}</strong>
        </div>
    @endif
</div>

<script>

$(document).ready(() => {
        const iframe = document.getElementById('homeIframe');
        const isInformativeRead = @json(ApiUser::get()['is_informative_read'] ?? false);
        const firstLink = @json(SystemHelper::get()['CLIENT_FIRST_LINK'] ?? null);
        const secondLink = @json(SystemHelper::get()['CLIENT_SECOND_LINK'] ?? null);
        
        iframe.src = !isInformativeRead ? firstLink : secondLink;

        const informativeCheckbox = document.getElementById("informative_read");
        if (informativeCheckbox) {
            informativeCheckbox.addEventListener("change", () => {
                document.getElementById("markAsReadForm").submit();
            });
        }

        $('#homeIframe').on('load', function() {
            $('#loadingIndicatorHome').hide();
            $(this).show();

            if (informativeCheckbox) 
                $("#markAsReadForm").show()
        });

    })
</script>
@endsection
