@extends('layouts.app', ['activePage' => 'employees'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-md-12" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex justify-content-between align-items-center"
                                 style="margin-top: 9px; margin-bottom:6px">
                                <h5 class="card-title mb-0"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Cadastrar funcionário</h5>
                            </div>
                            <div class="col-3">
                                <span style="color:red">* Campos obrigatórios</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('employees.store') }}" id="form-create-employee" method="POST"
                              onsubmit="event.preventDefault();">
                            @csrf
                            @include('employee.form')
                            <div class="modal-footer d-flex flex-column align-items-start w-100">
                                <div style="padding: 0 0 10px 4px;" class="justify-content-start">
                                    <input type="checkbox" class="form-check-input" id="term-checkbox" name="term"
                                           required>
                                    <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os
                                        dados passados de acordo com o termo em anexo <a
                                            href="{{route('data-processing-agreement', ['user_type' => 'employee'])}}"
                                            target="_blank">(clique aqui para ler o termo).</a></label>
                                </div>
                                <div class="d-flex justify-content-end w-100">
                                    <button id="submit-button" disabled type="button" class="btn btn-success"
                                            onclick="validateStoreUpdate('form-create-employee');">Adicionar funcionário
                                    </button>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if($message = Session::get('danger'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>

    <script>
        $("#term-checkbox").click(showHideFormButton);

        function showHideFormButton() {
            if ($("#term-checkbox").is(":checked")) {
                $("#submit-button").prop("disabled", false);
            } else {
                $("#submit-button").prop("disabled", true);
            }
        }

        @if(isset($user))
        $(document).ready(function () {
            alert("Encontramos esse usuário já cadastrado no nosso sistema para um outro perfil de login, segue as informações dele abaixo.");
        });
        @endif

    </script>

@endsection
