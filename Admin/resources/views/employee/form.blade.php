<div class="form-row">
    <div class="col-md-12"><p class="form-title"><PERSON><PERSON> pessoais</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'name',
                'label' => 'Nome completo',
                'value' => old('name') ?? ($employee['name'] ?? $user['name']?? ''),
                'placeholder' => 'Digite o nome completo do funcionário',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="gender">Gênero</label>
            <select id="gender" name="gender" class="form-select form-control @error('gender') is-invalid @enderror"
                    aria-label="Default select example" required>
                <option value="other">-- Não Informar --</option>
                <option value="m"
                        @if(old('gender') == 'm' || isset($employee) && $employee['gender'] == 'm' || isset($user) && $user['gender'] == 'm') selected @endif >
                    Masculino
                </option>
                <option value="f"
                        @if(old('gender') == 'f' || isset($employee) && $employee['gender'] == 'f' || isset($user) && $user['gender'] == 'f') selected @endif >
                    Feminino
                </option>
            </select>
            @error('gender')
            <span class="invalid-feedback" role="alert">
                <strong>{{$message}}</strong>
            </span>
            @enderror
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.date', [
                'name' => 'birth_date',
                'label' => 'Data de nascimento',
                'value' => (old('birth_date') ?? ($employee['birth_date'] ?? $user['birth_date'] ?? '')),
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'cpf',
                'label' => 'CPF',
                'value' => old('cpf') ?? ($employee['cpf'] ?? $user['cpf'] ?? $cpf ?? ''),
                'placeholder' => 'Digite o CPF',
                'onchange' =>'checkCPF()',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Endereço</p></div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'cep',
                'label' => 'CEP',
                'value' => old('cep') ?? ($employee['address']['cep'] ?? $user['address']['cep'] ?? ''),
                'placeholder' => 'Digite o cep do funcionário',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'neighborhood',
                'label' => 'Bairro',
                'value' => old('neighborhood') ?? ($employee['address']['neighborhood'] ?? $user['address']['neighborhood'] ?? ''),
                'placeholder' => 'Digite o nome do bairro',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'street',
                'label' => 'Rua',
                'value' => old('street') ?? ($employee['address']['street'] ?? $user['address']['street'] ?? ''),
                'placeholder' => 'Digite o nome da rua',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'number',
                'label' => 'Número',
                'value' => old('number') ?? ($employee['address']['number'] ?? $user['address']['number'] ?? ''),
                'placeholder' => 'Digite o número da residência (Ex: 01 ou SN)',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'complement',
                'label' => 'Complemento',
                'value' => old('complement') ?? ($employee['address']['complement'] ?? $user['address']['complement'] ?? ''),
                'placeholder' => 'Digite o complemento',
                'required' => false,
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="row flex-fill mb-4">
        <div class="col">
            @include('components.inputs.text', [
                'name' => 'city',
                'label' => 'Cidade',
                'value' => old('city') ?? ($employee['address']['city'] ?? $user['address']['city'] ?? ''),
                'readonly' => true,
                'withErrorText' => true
            ])
        </div>
        <div class="col">
            @include('components.inputs.text', [
                'name' => 'state',
                'label' => 'Estado',
                'value' => old('state') ?? ($employee['address']['state'] ?? $user['address']['state'] ?? ''),
                'readonly' => true,
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Contato</p></div>
    @php
        $initialPhoneForValidationLogic = '';
        $initialPhoneValidated = false;
        if (isset($employee['phone_number_1']) || isset($user['phone_number_1'])) {
            $rawPhone = $employee['phone_number_1'] ?? ($user['phone_number_1'] ?? '');
            $initialPhoneForValidationLogic = preg_replace('/\D/', '', $rawPhone);
            $initialPhoneValidated = !empty($rawPhone);
        }
        $isInitialPhoneActuallyValid = (isset($employee) || isset($user)) && preg_match('/^\d{10,11}$/', $initialPhoneForValidationLogic);
        $currentEmployeeCpf = $employee['cpf'] ?? $user['cpf'] ?? $cpf ?? '';
    @endphp
    <div class="col-md-6">
        @include('components.inputs.phone_number_with_validation', [
            'name' => 'phone_number_1',
            'id' => 'phone_number_1',
            'label' => 'Telefone/Celular 1',
            'class' => 'phone-input',
            'value' => old('phone_number_1') ?? ($employee['phone_number_1'] ?? $user['phone_number_1'] ?? ''),
            'placeholder' => 'Digite o número do telefone do funcionário',
            'required' => true,
            'currentClientCpf' => $currentEmployeeCpf,
            'validationFieldName' => 'phone_validated',
            'initialValidated' => ($isInitialPhoneActuallyValid && $initialPhoneValidated) ? '1' : '0',
            'includeModal' => true
        ])
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'phone_number_2',
                'label' => 'Telefone/Celular 2',
                'value' => old('phone_number_2') ?? ($employee['phone_number_2'] ?? $user['phone_number_2'] ?? ''),
                'placeholder' => 'Digite o número do telefone do funcionário',
                'required' => false,
                'withErrorText' => true,
                'class' => 'phone-input'
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Login</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'email',
                'type' => 'email',
                'label' => 'E-mail',
                'value' => old('email') ?? ($employee['email'] ?? $user['email'] ?? ''),
                'placeholder' => 'Digite o e-mail do funcionário',
                'withErrorText' => true,
                'required' => true
            ])
        </div>
    </div>

    @if(isset($employee))
        <div class="col-md-12">
            <div class="form-group">
                <input type="checkbox" id="reset_password" name="reset_password">
                <label for="reset_password">Resetar a senha do funcionário</label>
            </div>
        </div>
    @endif

    <div class="col-md-12"><p class="form-title">Permissão</p></div>
    <div class="col-md-12">
        <label for="name">Permissões de acesso ao sistema<span style="color: red">*</span></label>
        <div class="form-group" id="permissions_list">
            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['evaluations']) d-none @endif"
                    type="checkbox" value="1"
                    name="evaluations" id="evaluations"
                    @if((isset($employee) && $employee['authorizedFunction']['evaluations'] && ApiUser::get()['authorizedFunction']['evaluations']) || old('evaluations')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['evaluations']) d-none @endif"
                       for="evaluations">
                    Avaliações
                </label>
            </div>

            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['dashboard']) d-none @endif"
                    type="checkbox" value="1"
                    name="dashboard" id="dashboard"
                    @if((isset($employee) && $employee['authorizedFunction']['dashboard'] && ApiUser::get()['authorizedFunction']['dashboard']) || old('dashboard')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['dashboard']) d-none @endif"
                       for="dashboard">
                    Painel (Visualizar gráficos)
                </label>
            </div>

            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['employees']) d-none @endif"
                    type="checkbox" value="1"
                    name="employees" id="employees"
                    @if((isset($employee) && $employee['authorizedFunction']['employees'] && ApiUser::get()['authorizedFunction']['employees']) || old('employees')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['employees']) d-none @endif"
                       for="employees">
                    Funcionários
                </label>
            </div>

            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['manage_business_profile']) d-none @endif"
                    type="checkbox" value="1"
                    name="manage_business_profile" id="manage_business_profile"
                    @if((isset($employee) && $employee['authorizedFunction']['manage_business_profile'] && ApiUser::get()['authorizedFunction']['manage_business_profile']) || old('manage_business_profile')) checked
                    @endif>
                <label
                    class="form-check-label @if(!ApiUser::get()['authorizedFunction']['manage_business_profile']) d-none @endif"
                    for="manage_business_profile">
                    Gerenciar Perfil do Negócio
                </label>
            </div>

            <div class="form-check">
                <input onclick="checkTopicsMarked()"
                       class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['topics']) d-none @endif"
                       type="checkbox" value="1"
                       name="topics" id="topics"
                       @if((isset($employee) && $employee['authorizedFunction']['topics'] && ApiUser::get()['authorizedFunction']['topics']) || old('topics')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['topics']) d-none @endif"
                       for="topics">
                    Perguntas (Apenas Visualização)
                </label>
            </div>
            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['topics'] || !ApiUser::get()['authorizedFunction']['update_topics']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['topics'] && $employee['authorizedFunction']['update_topics']) || old('topics')) checked
                       @endif name="update_topics" id="update_topics_permission">
                <label style="display: none;" class="form-check-label" for="update_topics"
                       id="update_topics_permission_label">
                    Gerenciar Perguntas
                </label>
            </div>

            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['topics'] || !ApiUser::get()['authorizedFunction']['default_topics']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['topics'] && $employee['authorizedFunction']['default_topics']) || old('topics')) checked
                       @endif name="default_topics" id="default_topics_permission">
                <label style="display: none;" class="form-check-label" for="default_topics"
                       id="default_topics_permission_label">
                    Gerenciar Perguntas Padrão
                </label>
            </div>

            <div class="form-check">
                <input onclick="checkFormsMarked()"
                       class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['forms']) d-none @endif"
                       type="checkbox" value="1"
                       name="forms" id="forms"
                       @if((isset($employee) && $employee['authorizedFunction']['forms'] && ApiUser::get()['authorizedFunction']['forms']) || old('forms')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['forms']) d-none @endif"
                       for="forms">
                    Formulários (Apenas Visualização)
                </label>
            </div>

            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['forms'] || !ApiUser::get()['authorizedFunction']['update_forms']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['forms'] && $employee['authorizedFunction']['update_forms']) || old('forms')) checked
                       @endif name="update_forms" id="update_forms_permission">
                <label style="display: none;" class="form-check-label" for="update_forms"
                       id="update_forms_permission_label">
                    Gerenciar Formulários
                </label>
            </div>

            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['forms'] || !ApiUser::get()['authorizedFunction']['default_forms']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['forms'] && $employee['authorizedFunction']['default_forms']) || old('forms')) checked
                       @endif name="default_forms" id="default_forms_permission">
                <label style="display: none;" class="form-check-label" for="default_forms"
                       id="default_forms_permission_label">
                    Gerenciar Formulários Padrão
                </label>
            </div>

            <div class="form-check">
                <input onclick="checkClientsMarked()"
                       class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['clients']) d-none @endif"
                       type="checkbox" value="1"
                       name="clients" id="clients"
                       @if((isset($employee) && $employee['authorizedFunction']['clients'] && ApiUser::get()['authorizedFunction']['clients']) || old('clients')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['clients']) d-none @endif"
                       for="clients">
                    Clientes (Apenas Visualização)
                </label>
            </div>

            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['clients'] || !ApiUser::get()['authorizedFunction']['update_clients']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['clients'] && $employee['authorizedFunction']['update_clients']) || old('clients')) checked
                       @endif name="update_clients" id="update_clients_permission">
                <label style="display: none;" class="form-check-label" for="update_clients"
                       id="update_clients_permission_label">
                    Gerenciar Clientes
                </label>
            </div>

            <div class="form-check">
                <input onclick="checkCashbackMarked()"
                       class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['cashback']) d-none @endif"
                       type="checkbox" value="1"
                       name="cashback" id="cashback"
                       @if((isset($employee) && $employee['authorizedFunction']['cashback'] && ApiUser::get()['authorizedFunction']['cashback']) || old('cashback')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['cashback']) d-none @endif"
                       for="checkbox">
                    Cashbacks (Apenas Visualização)
                </label>
            </div>

            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['cashback'] || !ApiUser::get()['authorizedFunction']['update_cashback']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['cashback'] && $employee['authorizedFunction']['update_cashback']) || old('cashback')) checked
                       @endif name="update_cashback" id="update_cashback_permission">
                <label style="display: none;" class="form-check-label" for="update_cashback"
                       id="update_cashback_permission_label">
                    Gerenciar CashBacks
                </label>
            </div>

            <div class="form-check">
                <input onclick="checkNotificationMarked()"
                       class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['notifications']) d-none @endif"
                       type="checkbox" value="1"
                       name="notifications" id="notifications"
                       @if((isset($employee) && $employee['authorizedFunction']['notifications'] && ApiUser::get()['authorizedFunction']['notifications']) || old('notifications')) checked
                    @endif>
                <label
                    class="form-check-label @if(!ApiUser::get()['authorizedFunction']['notifications']) d-none @endif"
                    for="notifications">
                    Campanhas (Apenas Visualização)
                </label>
            </div>

            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['notifications'] || !ApiUser::get()['authorizedFunction']['update_notifications']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['notifications'] && $employee['authorizedFunction']['update_notifications']) || old('notifications')) checked
                       @endif name="update_notifications" id="update_notifications_permission">
                <label style="display: none;" class="form-check-label" for="update_notifications"
                       id="update_notifications_permission_label">
                    Gerenciar Campanhas
                </label>
            </div>

            <div class="form-check">
                <input onclick="checkSiteNotificationMarked()"
                       class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['site_notifications']) d-none @endif"
                       type="checkbox" value="1"
                       name="site_notifications" id="site_notifications"
                       @if((isset($employee) && $employee['authorizedFunction']['site_notifications'] && ApiUser::get()['authorizedFunction']['site_notifications']) || old('site_notifications')) checked
                    @endif>
                <label
                    class="form-check-label @if(!ApiUser::get()['authorizedFunction']['site_notifications']) d-none @endif"
                    for="site_notifications">
                    Notificações (Apenas Visualização)
                </label>
            </div>

            <div class="form-check" style="margin-left: 20px;">
                <input style="display: none;"
                       class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['site_notifications'] || !ApiUser::get()['authorizedFunction']['update_site_notifications']) d-none @endif"
                       type="checkbox" value="1"
                       @if((isset($employee) && $employee['authorizedFunction']['site_notifications'] && $employee['authorizedFunction']['update_site_notifications']) || old('site_notifications')) checked
                       @endif name="update_site_notifications" id="update_site_notifications_permission">
                <label style="display: none;" class="form-check-label" for="update_site_notifications"
                       id="update_site_notifications_permission_label">
                    Gerenciar Notificações
                </label>
            </div>

            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['parameters']) d-none @endif"
                    type="checkbox" value="1"
                    name="parameters" id="parameters"
                    @if((isset($employee) && $employee['authorizedFunction']['parameters'] && ApiUser::get()['authorizedFunction']['parameters']) || old('parameters')) checked @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['parameters']) d-none @endif"
                       for="parameters">
                    Parâmetros do Negócio
                </label>
            </div>

            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['plans']) d-none @endif"
                    type="checkbox" value="1"
                    name="plans" id="plans"
                    @if((isset($employee) && $employee['authorizedFunction']['plans'] && ApiUser::get()['authorizedFunction']['plans']) || old('plans')) checked @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['plans']) d-none @endif"
                       for="plans_permission">
                    Planos
                </label>
            </div>

            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['update_business']) d-none @endif"
                    type="checkbox" value="1"
                    name="update_business" id="update_business"
                    @if((isset($employee) && $employee['authorizedFunction']['update_business'] && ApiUser::get()['authorizedFunction']['update_business']) || old('update_business')) checked
                    @endif>
                <label
                    class="form-check-label @if(!ApiUser::get()['authorizedFunction']['update_business']) d-none @endif"
                    for="update_business_permission">
                    Gerenciar Negócios
                </label>
            </div>

            <div class="form-check">
                <input
                    class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['sellers']) d-none @endif"
                    type="checkbox" value="1"
                    name="sellers" id="sellers"
                    @if((isset($employee) && $employee['authorizedFunction']['sellers'] && ApiUser::get()['authorizedFunction']['sellers']) || old('sellers')) checked @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['sellers']) d-none @endif"
                       for="sellers_permission">
                    Vendedores
                </label>
            </div>

            <div class="form-check">
                <input onclick="checkSettingsMarked()"
                       class="form-check-input top-permission @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['settings']) d-none @endif"
                       type="checkbox" value="1"
                       name="settings" id="settings"
                       @if((isset($employee) && $employee['authorizedFunction']['settings'] && ApiUser::get()['authorizedFunction']['settings']) || old('settings')) checked
                    @endif>
                <label class="form-check-label @if(!ApiUser::get()['authorizedFunction']['settings']) d-none @endif"
                       for="settings" style="margin-right: 25px;"
                       id="settings_permission_label">
                    Configurações do Sistema
                </label>
                <div class="form-check">
                    <input style="display: none;"
                           class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['settings'] || !ApiUser::get()['authorizedSettingsFunction']['contact']) d-none @endif"
                           type="checkbox" value="1"
                           @if((isset($employee) && $employee['authorizedFunction']['settings'] && $employee['authorizedSettingsFunction']['contact']) || old('settings')) checked
                           @endif name="contact" id="contact_permission">
                    <label style="display: none;" class="form-check-label" for="contact" id="contact_permission_label">
                        Contato
                    </label>
                </div>
                <div class="form-check">
                    <input style="display: none;"
                           class="form-check-input @error('permission') is-invalid @enderror @if(!ApiUser::get()['authorizedFunction']['settings'] || !ApiUser::get()['authorizedSettingsFunction']['system_parameters']) d-none @endif"
                           type="checkbox" value="1"
                           @if((isset($employee) && $employee['authorizedFunction']['settings'] && $employee['authorizedSettingsFunction']['system_parameters']) || old('settings')) checked
                           @endif name="system_parameters" id="system_parameters_permission">
                    <label style="display: none;" class="form-check-label" for="flexCheckChecked"
                           id="system_parameters_permission_label">
                        Parâmetros do Sistema
                    </label>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12">
        <input type="hidden" id="initial_screen_value" name="initial_screen_value"
               value="{{ old('initial_screen') ?? ($employee['initial_screen'] ?? '') }}">
        <div class="form-group">
            <label for="initial_screen">Tela Inicial<span style="color: red">*</span></label>
            <select id="initial_screen" name="initial_screen"
                    class="form-control" required>
                <!-- Options will be added dynamically -->
            </select>
        </div>
    </div>
    <div class="col-md-12">
        <label for="name"><p class="form-title">Negócios autorizados<span style="color: red">*</span></p></label>
        <div class="form-group mb-0 @if(!ApiUser::get()['authorizedFunction']['release_all_business']) d-none @endif">
            <div class="form-check mb-0">
                <input class="form-check form-check-input" type="checkbox"
                       @if(ApiUser::get()['authorizedFunction']['release_all_business'] && ((isset($employee) && $employee['authorizedFunction']['release_all_business']) || old('release_all_business'))) checked
                       @endif
                       id="release_all_business" name="release_all_business" onchange="setAllBusinesses()"
                       value="@if(isset($employee)){{ $employee['authorizedFunction']['release_all_business'] === true ? 1 : 0}}@else{{0}}@endif">
                <label class="form-check form-check-label" for="checkAllBusinesses">Selecionar todos os negócios</label>
            </div>
        </div>
        <div class="form-group mt-0">
            <hr id="businessesSeparator"/>
            <label for="name" id="businessSelectedLabel">Selecionados:</label>
            <div class="form-group mb-0">
                <ul class="list-group" id="business_selected">
                    <!-- Negócios selecionados serão carregados aqui -->
                </ul>
            </div>
        </div>
        <div class="form-group mb-0 mt-0">
            <input type="text" id="businessSearchFormEmployee" class="form-control" placeholder="Pesquisar negócio...">
        </div>
        <div class="d-flex justify-content-center loading pb-1 d-none">
            <div class="spinner-border loading" role="status">
                <span class="sr-only loading">Loading...</span>
            </div>
        </div>
        <table class="table table-bordered d-none" id="businessTableFormEmployee">
            <thead>
            <tr>
                <th style="width: 1%">Selecionar</th>
                <th>Nome</th>
            </tr>
            </thead>
            <tbody id="businessTableFormEmployeeBody">
            <!-- Negócios serão carregados aqui -->
            </tbody>
        </table>
        <nav>
            <ul class="pagination" id="businessPaginationFormEmployee">
                <!-- Paginação será carregada aqui -->
            </ul>
        </nav>
        <div class="d-none" id="businessesInputs">
            @if(isset($employee['businesses']))
                @foreach($employee['businesses'] as $business)
                    <input type="hidden" name="businessesIds[]" value="{{$business['id']}}"
                           id="businesses_input_{{$business['id']}}">
                @endforeach
            @endif
        </div>
    </div>
    <input type="hidden" name="email_validated" id="email_validated" value="0">

    @include('components.loading', ['message' => 'Aguardando confirmação de email'])
    @include('components.modal.modal_confirm_email', ['modalId' => 'modal-confirm-email'])
    @push('js')
        <script>
            // Variável global necessária para o componente de validação do telefone
            window.currentClientCpf = "{{ $employee['cpf'] ?? $user['cpf'] ?? $cpf ?? '' }}";

            $(document).ready(function () {

                let debounceTimerEmail;
                $('#email').on('input', function() {
                    clearTimeout(debounceTimerEmail);
                    const emailInput = $(this);
                    const errorElement = $('#error-email');
                    let email = emailInput.val();

                    // Verifica se o email tem um formato básico válido antes de enviar a requisição
                    if (email.length > 0 && /\S+@\S+\.\S+/.test(email)) {
                         debounceTimerEmail = setTimeout(() => {
                            validateEmailAddress(emailInput, errorElement);
                         }, 500); // 500ms debounce
                    } else {
                        // Limpa erros se o email for inválido ou vazio
                        emailInput.removeClass('is-invalid');
                        errorElement.text('');
                    }
                });

                loadBusinessesFormEmployee();
                $('#businessSearchFormEmployee').on('keyup', debounce(function () {
                    loadBusinessesFormEmployee();
                }, 500));

                // Initialize the select element based on the initial checked permissions
                updateInitialScreenOptions(true);

                // Attach change event listeners to top-level permission checkboxes
                let topPermissionCheckboxes = document.querySelectorAll('.top-permission');
                topPermissionCheckboxes.forEach(function (checkbox) {
                    checkbox.addEventListener('change', function () {
                        updateInitialScreenOptions(false);
                    });
                });

                // Listener para sucesso da validação do componente de telefone
                $(document).on('phoneValidation:success', function(event, data) {});
            });

            //criar variavel para armazenar as permissões marcadas
            let permissionsMarked = [];

            function updateInitialScreenOptions(firstLoad = false) {
                let select = document.getElementById('initial_screen');
                let initialScreenValue = document.getElementById('initial_screen_value').value;
                // Limpa as opções atuais
                select.innerHTML = '';

                let selectedPermissions = [];
                // Coleta os checkboxes de permissões de nível superior
                let topPermissionCheckboxes = document.querySelectorAll('.top-permission');
                topPermissionCheckboxes.forEach(function (checkbox) {
                    if (checkbox.checked) {
                        selectedPermissions.push({
                            name: checkbox.name,
                            label: checkbox.nextElementSibling.textContent.trim()
                        });
                    }
                });

                // Verifica se alguma das permissões (['evaluations', 'dashboard', 'cashback']) necessárias está selecionada e foi marcada comparando com as permissões marcadas
                // Uma permissão que foi desabilitada é aquela que estava marcada(permissionsMarked) e foi desmarcada(selectedPermissions)
                // criando um array com as permissões marcadas que estavam desmarcadas
                let permissionsEnabledNow = selectedPermissions.filter(permission => !permissionsMarked.includes(permission.name));
              
                // Uma permissão que foi habilitada é aquela que estava desmarcada e foi marcada
                // criando um array com as permissões marcadas que foram marcadas
                let permissionsDisabledNow = permissionsMarked.filter(permission => !selectedPermissions.map(p => p.name).includes(permission));
               
                //verificar se uma das 3 permissões foi marcada e estava desmarcada
                let temAcoesRapidasNow = permissionsEnabledNow.some(permission => ['evaluations', 'dashboard', 'cashback'].includes(permission.name));
                
                let temAcoesRapidas = selectedPermissions.some(permission => ['evaluations', 'dashboard', 'cashback'].includes(permission.name));

                // Escolhe por padrão a tela de informativos como inicial
                let opt = document.createElement('option');
                opt.value = "informative";
                opt.text = "Tela Inicial - Informativo";
                opt.selected = initialScreenValue === 'informative' ?? !initialScreenValue;
                select.appendChild(opt);

                // Adiciona as demais opções das permissões selecionadas
                if (selectedPermissions.length > 0) {
                    selectedPermissions.forEach(function (permission) {
                        let option = document.createElement('option');
                        option.value = permission.name;
                        // Remove textos adicionais como "(Apenas Visualização)" e "(Visualizar gráficos)"
                        option.text = permission.label.replace(/(\(Apenas Visualização\)|\(Visualizar gráficos\)|\( Selecione pelo menos uma opção \))/g, '');
                        if (permission.name === initialScreenValue) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });

                    // Se tiver, adiciona a opção "Ações Rápidas"
                    if (temAcoesRapidasNow && !firstLoad) {
                        //tira o selected dos outros
                        let options = select.options;
                        for (let i = 0; i < options.length; i++) {
                            options[i].selected = false;
                        }

                        let opt = document.createElement('option');
                        opt.value = "quick_actions";
                        opt.text = "Disparos - Ações Rápidas";
                        // opt.selected = true;
                        select.appendChild(opt);
                    }else if(temAcoesRapidas){
                        //se tiver uma das 3 selecionada mostra a opção "Ações Rápidas" sem selecionar por padrao
                        let opt = document.createElement('option');
                        opt.value = "quick_actions";
                        opt.text = "Disparos - Ações Rápidas";
                        if (initialScreenValue === "quick_actions" ) {
                            opt.selected = true;
                        }
                        select.appendChild(opt);
                    }
                } else {
                    // Se nenhuma permissão estiver selecionada, adiciona uma opção padrão
                    let option = document.createElement('option');
                    option.value = '';
                    option.text = 'Nenhuma permissão selecionada';
                    select.appendChild(option);
                }

                // Atualiza as permissões marcadas
                permissionsMarked = selectedPermissions.map(p => p.name);
            }

            let businessesSelectedIds = [];
            let savedBusinesses = @json($employee['businesses'] ?? []);
            let oldBusinessesIds = [];
            if (savedBusinesses.length > 0) {
                savedBusinesses.forEach(business => {
                    businessesSelectedIds.push(business.id.toString());
                    oldBusinessesIds.push(business.id.toString());
                });
            }
            let bussinesses_autorizados = @json(ApiUser::get()['businesses'] ?? []);
            let bussinesses_autorizados_ids = bussinesses_autorizados.map(business => business.id.toString());
            let isAdmin = {{ ApiUser::getLoginType() == 'admin' ? 'true' : 'false' }};
            let isAuthorizedAllBusiness = {{ ApiUser::get()['authorizedFunction']['release_all_business'] ? 'true' : 'false' }};

            @if(isset($employee))
            @foreach($employee['businesses'] as $business)
            addBusinessesSelectedItem(@json($business));
            @endforeach
            @endif

            function addBusinessesSelectedItem(business) {
                let p = document.createElement('p');
                p.className = 'row justify-content-between m-0';

                if (bussinesses_autorizados_ids.includes(business.id.toString()) || isAdmin || isAuthorizedAllBusiness) {
                    p.innerHTML = `- ${business.name} <span class="badge badge-danger" style="cursor: pointer; padding: 5px 10px; font-size: 14px;" onclick="removeBusiness(${business.id})">X</span>`;
                } else {
                    p.innerHTML = `- ${business.name}`;
                }

                let li = document.createElement('li');
                li.className = 'list-group list-group-item';
                li.id = `business_selected_${business.id}`;
                li.appendChild(p);
                $('#business_selected').append(li);
            }

            showHideNoBusinessSelectedMessage();
            setAllBusinesses();

            function setAllBusinesses() {
                if ($('#release_all_business').is(':checked')) {
                    $('#business_selected').empty();
                    $('#businessesInputs').empty();
                    $('#businessTableFormEmployee').addClass('d-none');
                    $('#businessSelectedLabel').addClass('d-none');
                    $('#businessesSeparator').addClass('d-none');
                    $('#businessPaginationFormEmployee').addClass('d-none');
                    $('#businessSearchFormEmployee').addClass('d-none');
                    $('.loading').addClass('d-none');
                    businessesSelectedIds = [];
                    $('#release_all_business').val('1');
                } else {
                    $('#businessTableFormEmployee').removeClass('d-none');
                    $('#businessSelectedLabel').removeClass('d-none');
                    $('#businessPaginationFormEmployee').removeClass('d-none');
                    $('#businessSearchFormEmployee').removeClass('d-none');
                    loadBusinessesFormEmployee();
                    showHideNoBusinessSelectedMessage();
                    $('#release_all_business').val('0');
                }
            }

            function businessSelected(checkbox) {
                const businessId = $(checkbox).val();
                if ($(checkbox).is(':checked')) {
                    businessesSelectedIds.push(businessId);
                    if ($('businesses_input_' + businessId).length === 0) {
                        $('#businessesInputs').append(`<input type="hidden" name="businessesIds[]" value="${businessId}" id="businesses_input_${businessId}">`);
                    }

                    if ($(`#business_selected_${businessId}`).length === 0) {

                        let p = document.createElement('p');
                        p.className = 'row justify-content-between m-0';
                        if (bussinesses_autorizados_ids.includes(businessId.toString()) || isAdmin || isAuthorizedAllBusiness) {
                            p.innerHTML = `- ${$(checkbox).parent().next().text()} <span class="badge badge-danger" style="cursor: pointer; padding: 5px 10px; font-size: 14px;" onclick="removeBusiness(${businessId})">X</span>`;
                        } else {
                            p.innerHTML = `- ${$(checkbox).parent().next().text()}`;
                        }

                        let li = document.createElement('li');
                        li.className = 'list-group list-group-item';
                        li.id = `business_selected_${businessId}`;
                        li.appendChild(p);
                        $('#business_selected').append(li);
                    }
                    showHideNoBusinessSelectedMessage();
                } else {
                    businessesSelectedIds = businessesSelectedIds.filter(id => id !== businessId);
                    $('#businesses_input_' + businessId).remove();
                    $(`#business_selected_${businessId}`).remove();
                    showHideNoBusinessSelectedMessage();
                }
            }

            function showHideNoBusinessSelectedMessage() {
                if ($('#business_selected').children().length === 0) {
                    $('#businessSelectedLabel').addClass('d-none');
                    $('#businessesSeparator').addClass('d-none');
                } else {
                    $('#businessSelectedLabel').removeClass('d-none');
                    $('#businessesSeparator').removeClass('d-none');
                }
            }

            function removeBusiness(businessId) {
                $(`#business_selected_${businessId}`).remove();
                $(`#business_${businessId}`).prop('checked', false);
                businessesSelectedIds = businessesSelectedIds.filter(id => id !== businessId.toString());
                $('#businesses_input_' + businessId).remove();
                showHideNoBusinessSelectedMessage();
            }

            function loadBusinessesFormEmployee(page = 1) {
                if ($('#businessTableFormEmployee').hasClass('d-none')) {
                    return;
                }
                const searchQuery = $('#businessSearchFormEmployee').val();
                $.ajax({
                    url: "<?= env('API_URL') ?>/api/business/getUserBusiness?search=" + searchQuery + "&page=" + page + "&per_page=5",
                    type: 'get',
                    data: {
                        search: searchQuery,
                        page: page,
                        per_page: 5,
                        is_crud_employee: true
                    },
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                        $('#businessTableFormEmployee').addClass('d-none');
                        $('#businessPaginationFormEmployee').addClass('d-none');
                        $('.loading').removeClass('d-none');
                    },
                    success: function (response) {
                        $('.loading').addClass('d-none');

                        $('#businessTableFormEmployeeBody').empty();
                        if (response.businesses.data.length > 0) {
                            $.each(response.businesses.data, function (index, business) {
                                let isSelected = businessesSelectedIds.includes(business.id.toString());
                                $('#businessTableFormEmployeeBody').append(`
                            <tr>
                                <td style="width: 1%" class="text-center">
                                    <input type="checkbox" id="business_${business.id}" name="businesses[]" value="${business.id}" onchange="businessSelected(this);">
                                </td>
                                <td>
                                    ${business.name}
                                </td>

                            </tr>
                        `);
                                $(`#business_${business.id}`).prop('checked', isSelected);
                            });
                        } else {
                            $('#businessTableFormEmployeeBody').append(`
                                <tr>
                                    <td colspan="2" style="text-align: center; padding: 20px; color: #666;">
                                        <div style="display: flex; flex-direction: column; align-items: center;">
                                            <i class="fas fa-store-slash" style="font-size: 24px; margin-bottom: 8px;"></i>
                                            <span style="font-style: italic;">Nenhum negócio encontrado</span>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        }

                        // Paginação
                        $('#businessPaginationFormEmployee').empty();
                        if (response.businesses.last_page > 1) {
                            for (let i = 1; i <= response.businesses.last_page; i++) {
                                $('#businessPaginationFormEmployee').append(`
                            <li class="page-item ${i === response.businesses.current_page ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i}</a>
                            </li>
                        `);
                            }
                        }

                        $('#businessPaginationFormEmployee a').on('click', function (e) {
                            e.preventDefault();
                            const page = $(this).data('page');
                            loadBusinessesFormEmployee(page);
                        });

                        $('#businessTableFormEmployee').removeClass('d-none');
                        $('#businessPaginationFormEmployee').removeClass('d-none');
                    },
                    error: function (response, textStatus, msg) {
                        $('.loading').addClass('d-none');
                    }
                });
            }
        </script>

        <script type="text/javascript"
                src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
        <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>

        <script type="text/javascript">
            $(document).ready(function () {
                checkSettingsMarked();
                checkTopicsMarked();
                checkFormsMarked();
                checkClientsMarked();
                checkNotificationMarked();
                checkSiteNotificationMarked();
                checkCashbackMarked();
            });
            $("#cep").focusout(function () {
                $.ajax({
                    url: 'https://viacep.com.br/ws/' + $(this).val().replaceAll('-', '') + '/json/',
                    dataType: 'json',
                    success: function (response) {
                        $("#complement").val(response.complemento);
                        $("#street").val(response.logradouro);
                        $("#neighborhood").val(response.bairro);
                        $("#city").val(response.localidade);
                        $("#state").val(response.uf);
                        $("#number").focus();
                    }
                });
            });

            function checkTopicsMarked() {
                if ($('#topics').is(':checked')) {
                    @if(ApiUser::hasUserFunction('default_topics'))
                    $("#default_topics_permission").show();
                    $("#default_topics_permission_label").show();
                    @endif
                    @if(ApiUser::hasUserFunction('update_topics'))
                    $("#update_topics_permission").show();
                    $("#update_topics_permission_label").show();
                    @endif
                } else {
                    @if(ApiUser::hasUserFunction('default_topics') || ApiUser::hasUserFunction('update_topics'))
                    $("#default_topics_permission").hide();
                    $("#default_topics_permission_label").hide();
                    $("#update_topics_permission").hide();
                    $("#update_topics_permission_label").hide();
                    @endif
                }
            }

            function checkFormsMarked() {
                if ($('#forms').is(':checked')) {
                    @if(ApiUser::hasUserFunction('default_forms'))
                    $("#default_forms_permission").show();
                    $("#default_forms_permission_label").show();
                    @endif
                    @if(ApiUser::hasUserFunction('update_forms'))
                    $("#update_forms_permission").show();
                    $("#update_forms_permission_label").show();
                    @endif
                } else {
                    @if(ApiUser::hasUserFunction('default_forms') || ApiUser::hasUserFunction('update_forms'))
                    $("#default_forms_permission").hide();
                    $("#default_forms_permission_label").hide();
                    $("#update_forms_permission").hide();
                    $("#update_forms_permission_label").hide();
                    @endif
                }
            }

            function checkClientsMarked() {
                if ($('#clients').is(':checked')) {
                    @if(ApiUser::hasUserFunction('update_clients'))
                    $("#update_clients_permission").show();
                    $("#update_clients_permission_label").show();
                    @endif
                } else {
                    @if(ApiUser::hasUserFunction('update_clients'))
                    $("#update_clients_permission").hide();
                    $("#update_clients_permission_label").hide();
                    @endif
                }
            }

            function checkNotificationMarked() {
                if ($('#notifications').is(':checked')) {
                    @if(ApiUser::hasUserFunction('update_notifications'))
                    $("#update_notifications_permission").show();
                    $("#update_notifications_permission_label").show();
                    @endif
                } else {
                    @if(ApiUser::hasUserFunction('update_notifications'))
                    $("#update_notifications_permission").hide();
                    $("#update_notifications_permission_label").hide();
                    @endif
                }
            }

            function checkSiteNotificationMarked() {
                if ($('#site_notifications').is(':checked')) {
                    @if(ApiUser::hasUserFunction('update_site_notifications'))
                    $("#update_site_notifications_permission").show();
                    $("#update_site_notifications_permission_label").show();
                    @endif
                } else {
                    @if(ApiUser::hasUserFunction('update_site_notifications'))
                    $("#update_site_notifications_permission").hide();
                    $("#update_site_notifications_permission_label").hide();
                    @endif
                }
            }

            function checkCashbackMarked() {
                if ($('#cashback').is(':checked')) {
                    @if(ApiUser::hasUserFunction('update_cashback'))
                    $("#update_cashback_permission").show();
                    $("#update_cashback_permission_label").show();
                    @endif
                } else {
                    @if(ApiUser::hasUserFunction('update_cashback'))
                    $("#update_cashback_permission").hide();
                    $("#update_cashback_permission_label").hide();
                    @endif
                }
            }

            function checkSettingsMarked() {
                if ($('#settings').is(':checked')) {
                    @if(ApiUser::get()['authorizedSettingsFunction']['contact'])
                    $("#contact_permission").show();
                    $("#contact_permission_label").show();
                    @endif

                    @if(ApiUser::get()['authorizedSettingsFunction']['system_parameters'])
                    $("#system_parameters_permission").show();
                    $("#system_parameters_permission_label").show();
                    @endif

                    $("#settings_permission_label").text("Configurações do Sistema ( Selecione pelo menos uma opção )");
                } else {
                    @if(ApiUser::get()['authorizedSettingsFunction']['contact'])
                    $("#contact_permission").hide();
                    $("#contact_permission_label").hide();
                    @endif

                    @if(ApiUser::get()['authorizedSettingsFunction']['system_parameters'])
                    $("#system_parameters_permission").hide();
                    $("#system_parameters_permission_label").hide();
                    @endif

                    $("#settings_permission_label").text("Configurações do Sistema");
                }
            }

            $(document).ready(applyMasks());
            var counter_phone1 = {value: 0};
            var counter_phone2 = {value: 0};

            function applyMasks() {
                $("#cpf").mask('000.000.000-00');
                $("#cpf_search").mask('000.000.000-00');
                $("#cep").mask('00000-000');
                $("#phone_number_2").mask(getMask('phone_number_2')).keyup(function (event) {
                    checkPhoneMask(event.target, event.originalEvent.key, counter_phone2);
                });
            }

            function checkPermissions() {
                let evaluations = document.getElementById('evaluations');
                let employees = document.getElementById('employees');
                let manage_business_profile = document.getElementById('manage_business_profile');
                let topics = document.getElementById('topics');
                let forms = document.getElementById('forms');
                let settings = document.getElementById('settings');
                let plans = document.getElementById('plans');
                let sellers = document.getElementById('sellers');
                let cashback = document.getElementById('cashback');
                let clients = document.getElementById('clients');
                let notifications = document.getElementById('notifications');
                let dashboard = document.getElementById('dashboard');
                let parameters = document.getElementById('parameters');

                let contact_permission = document.getElementById('contact_permission');
                let system_parameters_permission = document.getElementById('system_parameters_permission');
                let cpfInput = document.getElementById('cpf');
                let cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

                let cpfValid = cpfIsValid(cpf);
                let status = false;

                if (!employees.checked && !settings.checked && !topics.checked &&
                    !forms.checked && !manage_business_profile.checked && !plans.checked &&
                    !sellers.checked && !cashback.checked && !clients.checked && !evaluations.checked && !dashboard.checked
                    && !notifications.checked && !parameters.checked) {
                    alert('Selecione pelo menos uma permissão.');
                } else if (settings.checked && !contact_permission.checked && !system_parameters_permission.checked) {
                    alert('Selecione pelo menos uma permissão de configuração.');
                } else if (!cpfValid) {
                    alert('Informe um CPF Válido.');
                } else if ($('#business_selected').children().length === 0 && !$('#release_all_business').is(':checked')) {
                    alert('Selecione pelo menos um negócio.');
                } else {
                    status = true;
                }

                return status;
            }

            function checkCPF() {
                var cpfInput = document.getElementById('cpf');
                var cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

                var isValid = cpfIsValid(cpf);

                if (isValid) {
                    cpfInput.classList.remove("is-invalid")
                    cpfInput.classList.add("is-valid")
                } else {
                    cpfInput.classList.add("is-invalid")
                }
            }

            function getAjaxParams(employeeId = null) {
                if (employeeId != null) {
                    return {
                        url: "<?= env('API_URL') ?>/api/employees/validate/update/" + employeeId,
                        method: 'post',
                    };
                }

                return {
                    url: "<?= env('API_URL') ?>/api/employees/validate/store",
                    method: 'post',
                };
            }

            function validateStoreUpdate(formId, employeeId = null, emailChange = false) {
                const form = document.getElementById(formId);
                const formData = new FormData(form);

                // Verificar se o telefone foi validado usando o componente
                if ($('#phone_validated').val() !== '1') {
                    alert('É necessário validar o telefone pelo WhatsApp antes de continuar.');
                    $('#phone_number_1').focus();
                    return false;
                }

                formData.forEach(function (value, key) {
                    if (['cpf', 'cep', 'phone_number_1', 'phone_number_2'].includes(key)) {
                        formData.set(key, value.replaceAll('.', '').replaceAll('-', '').replaceAll('(', '').replaceAll(')', '').replaceAll(' ', ''));
                    }
                    if (key == '_method') {
                        formData.delete(key);
                    }
                });

                const ajaxData = getAjaxParams(employeeId);
                let email = $('#email').val();

                let isSetUser = {{isset($user) ? 'true' : 'false'}};
                let oldEmailUser = "{{isset($user) ?  $user['email'] : ''}}";

                if (checkPermissions()) {
                    $('#loading').modal('show');
                    $.ajax({
                        url: ajaxData.url,
                        type: ajaxData.method,
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                        },
                        success: function (response) {
                            $('#loading').modal('hide');
                            removeErrorMessages();
                            if ((formId === 'form-create-employee' && !(isSetUser && oldEmailUser === email)) || ((emailChange && email !== "") && !(isSetUser && oldEmailUser === email)) || (isSetUser && oldEmailUser !== email)) {
                                confirmEmployeeEmail(formId);
                            } else {
                                form.submit();
                            }
                        },
                        error: function (response, textStatus, msg) {
                            $('#loading').modal('hide');
                            if (response.status == 422) {
                                const errors = response.responseJSON.errors;
                                removeErrorMessages();
                                removeIsInvalidClass();

                                Object.entries(errors).forEach(([field, error]) => {
                                    const id = 'error-' + field;
                                    document.getElementById(id).innerHTML = error;
                                    errorsIdToRemove.push(id);
                                })

                                Object.keys(errors).forEach((field) => {
                                    document.getElementById(field).classList.add('is-invalid')
                                    removeIsInvalidClassId.push(field)
                                })
                            }
                            alert('Preencha todos os dados corretamente.');
                        }
                    });
                }
            }

            function confirmEmployeeEmail(formId) {
                const email = document.getElementById('email').value;
                const name = document.getElementById('name').value;
                const form = document.getElementById(formId);

                if (form.reportValidity() && checkPermissions() && email) {
                    const formData = new FormData();
                    formData.append('email', email);
                    formData.append('name', name);

                    $('#loading').modal('show');

                    $.ajax({
                        url: "{{route('send.email.employee.confirm')}}",
                        type: 'post',
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        beforeSend: function (xhr) {},
                        success: function (response) {
                            const code = response.code;
                            const modal = document.getElementById('modal-confirm-email');
                            modal.dataset.code = code;
                            modal.dataset.formId = formId;
                            modal.dataset.email = email;
                            modal.dataset.name = name;

                            $('#loading').modal('hide');
                            $("#modal-confirm-email").modal('toggle');
                        },
                        error: function (response, textStatus, msg) {
                            $('#loading').modal('hide');
                            alert('Falha ao enviar email de confirmação!');
                        }
                    });
                }
            }

            setInterval(checkCPF, 1000);


            // Função para validar o email via AJAX
            function validateEmailAddress(emailInput, errorElement) {
                let email = emailInput.val();
                const currentEmployeeCpf = "{{$employee['cpf'] ?? $user['cpf'] ?? $cpf}}";

                emailInput.removeClass('is-invalid');
                errorElement.text('');

                if (email.length === 0) {
                    return; // Não valida se o campo estiver vazio
                }

                // Verifica se o email tem um formato básico válido antes de enviar a requisição
                if (!/\S+@\S+\.\S+/.test(email)) {
                    emailInput.addClass('is-invalid');
                    errorElement.text('Por favor, insira um endereço de e-mail válido.');
                    return;
                }


                $.ajax({
                    url: `<?= env('API_URL') ?>/api/user/check-email/${email}`,
                    type: 'GET',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                    },
                    success: function (response) {
                        // A API retorna 404 se o email não for encontrado, então success significa que foi encontrado
                        // Verifica se o email encontrado pertence a outro usuário com CPF completo
                        if (response && response.id && response.cpf != currentEmployeeCpf && response.cpf_status === 'completo') {
                            emailInput.addClass('is-invalid');
                            errorElement.text('Este email já está associado a outro usuário.');
                        } else {
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Se a resposta for 404, significa que o email não foi encontrado, o que é OK
                        if (jqXHR.status === 404) {
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        } else {
                            console.error("Erro ao validar email:", textStatus, errorThrown);
                            // Para outros erros, remove a validação para não bloquear o usuário indevidamente
                            emailInput.removeClass('is-invalid');
                            errorElement.text('');
                        }
                    },
                });
            }
        </script>
@endpush
