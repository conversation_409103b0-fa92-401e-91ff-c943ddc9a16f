@php use App\Helpers\StringMask; @endphp
<div class="modal fade" id="{{ $modalId }}" tabindex="-1" role="dialog" aria-labelledby="modalVisualizarMedico"
     aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Dados do funcionário</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="text-align: left">
                <div class="form-row">
                    <div class="col-md-12">
                        <h6 class="style_campo_titulo"><PERSON><PERSON> pessoais</h6>
                    </div>
                    <div class="col-md-12">
                        <label for="">Nome completo</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ $employee['name'] }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">Gênero</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::gender($employee['gender']) }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">Data de nascimento</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::brDate($employee['birth_date']) }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">CPF</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::cpf($employee['cpf']) }}" disabled>
                    </div>

                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Contato</h6>
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Email</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ $employee['email']?? 'Não informado' }}" disabled>
                    </div>
                    <div class="col-md-{{ isset($employee['phone_number_2']) ? 3 : 6 }}" style="margin-top: 10px">
                        <label for="">Telefone 1</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::phone($employee['phone_number_1']) }}" disabled>
                    </div>
                    @if(isset($employee['phone_number_2']))
                        <div class="col-md-3" style="margin-top: 10px">
                            <label for="">Telefone 2</label>
                            <input type="text" class="form-control style_campo_estatico"
                                   value="{{ StringMask::phone($employee['phone_number_2']) }}" disabled>
                        </div>
                    @endif
                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Endereço</h6>
                    </div>
                    <div class="col-md-12">
                        <label for="">CEP</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ StringMask::cep($employee['address']['cep']) }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Estado</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $employee['address']['state'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Cidade</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $employee['address']['city'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Bairro</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $employee['address']['neighborhood'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Rua</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $employee['address']['street'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Número</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $employee['address']['number'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Complemento</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $employee['address']['complement'] }}">
                    </div>

                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Módulos autorizados</h6>
                    </div>
                    <div class="col-md-12">
                        <ul class="list-group">
                            @if($employee['authorizedFunction']['evaluations'])
                                <li class="list-group-item">Avaliações</li>
                            @endif
                            @if($employee['authorizedFunction']['employees'])
                                <li class="list-group-item">Funcionários</li>
                            @endif
                            @if($employee['authorizedFunction']['manage_business_profile'])
                                <li class="list-group-item">Gerenciar Perfil do Negócio</li>
                            @endif
                            @if($employee['authorizedFunction']['dashboard'])
                                <li class="list-group-item">Painel (Visualizar gráficos)</li>
                            @endif
                            @if($employee['authorizedFunction']['topics'])
                                <ul class="list-group-item">Perguntas (Apenas Visualização)
                                    @if($employee['authorizedFunction']['update_topics'])
                                        <li class="list-group-item">Gerenciar Perguntas</li>
                                    @endif
                                    @if($employee['authorizedFunction']['default_topics'])
                                        <li class="list-group-item">Gerenciar Perguntas Padrão</li>
                                    @endif
                                </ul>
                            @endif

                            @if($employee['authorizedFunction']['forms'])
                                <ul class="list-group-item">Formulários (Apenas Visualização)
                                    @if($employee['authorizedFunction']['update_forms'])
                                        <li class="list-group-item">Gerenciar Formulários</li>
                                    @endif
                                    @if($employee['authorizedFunction']['default_forms'])
                                        <li class="list-group-item">Gerenciar Formulários Padrão</li>
                                    @endif
                                </ul>
                            @endif

                            @if($employee['authorizedFunction']['clients'])
                                <ul class="list-group-item">Clientes (Apenas Visualização)
                                    @if($employee['authorizedFunction']['update_clients'])
                                        <li class="list-group-item">Gerenciar Clientes</li>
                                    @endif
                                </ul>
                            @endif

                            @if($employee['authorizedFunction']['cashback'])
                                <ul class="list-group-item">Cashbacks (Apenas Visualização)
                                    @if($employee['authorizedFunction']['update_cashback'])
                                        <li class="list-group-item">Gerenciar Cashbacks</li>
                                    @endif
                                </ul>
                            @endif

                            @if($employee['authorizedFunction']['notifications'])
                                <ul class="list-group-item">Campanhas (Apenas Visualização)
                                    @if($employee['authorizedFunction']['update_notifications'])
                                        <li class="list-group-item">Gerenciar Campanhas</li>
                                    @endif
                                </ul>
                            @endif

                            @if($employee['authorizedFunction']['site_notifications'])
                                <ul class="list-group-item">Notificações (Apenas Visualização)
                                    @if($employee['authorizedFunction']['update_site_notifications'])
                                        <li class="list-group-item">Gerenciar Notificações</li>
                                    @endif
                                </ul>
                            @endif

                            @if($employee['authorizedFunction']['parameters'])
                                <li class="list-group-item">Parâmetros do Negócio</li>
                            @endif 

                            @if($employee['authorizedFunction']['plans'])
                                <li class="list-group-item">Planos</li>
                            @endif

                            @if($employee['authorizedFunction']['settings'])
                                <ul class="list-group-item">Configurações
                                    @if($employee['authorizedSettingsFunction']['contact'])
                                        <li class="list-group-item">Contatos</li>
                                    @endif
                                    @if($employee['authorizedSettingsFunction']['system_parameters'])
                                        <li class="list-group-item">Parâmetros do Sistema</li>
                                    @endif
                                </ul>
                            @endif
                        </ul>
                    </div>
                    @if(count($employee['businesses']) > 0 || $employee['authorizedFunction']['release_all_business'])
                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Negócios autorizados</h6>
                        <ul class="list-group">
                            @if($employee['businesses'])
                                @foreach($employee['businesses'] as $business)
                                    <li class="list-group-item">{{ $business['name'] }}</li>
                                @endforeach
                            @elseif($employee['authorizedFunction']['release_all_business'])
                                <li class="list-group-item">Todos os negócios</li>
                            @else
                                <li class="list-group-item">Nenhum negócio autorizado</li>
                            @endif
                        </ul>
                    </div>
                    @endif
                    @if(!empty($employee['businesses_canceled']) && empty($employee['businesses']) && !$employee['authorizedFunction']['release_all_business'])
                        <div class="col-md-12" style="margin-top: 20px">
                            <h6 class="style_campo_titulo">Negócios cancelados</h6>
                            <ul class="list-group">
                                @foreach($employee['businesses_canceled'] as $business)
                                    <li class="list-group-item">{{ $business['name'] }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" style="width:30%">Fechar</button>
            </div>
        </div>
    </div>
</div>
