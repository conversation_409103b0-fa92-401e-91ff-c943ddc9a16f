@extends('layouts.app', ['activePage' => 'employees'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-md-8" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex justify-content-between align-items-center"
                                 style="margin-top: 9px; margin-bottom:6px">
                                <div class="d-flex align-items-center ">
                                    @if (isset($redirectTo))
                                        <a href="{{route($redirectTo)}}" class="mx-2" style="cursor: pointer">
                                            <i class="fas fa-chevron-left fa-fw"></i>
                                        </a>
                                    @else
                                        <a href="{{route('employees.index')}}" class="mx-2" id="back-button" style="cursor: pointer">
                                            <i class="fas fa-chevron-left fa-fw"></i>
                                        </a>
                                    @endif
                                    <h5 class="card-title mb-0" style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                        Atualizar Funcionário
                                    </h5>
                                </div>
                            </div>
                            <div class="col-3">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('employees.update', $employee['id']) }}" id="form-update-employee"
                              method="POST" onsubmit="event.preventDefault();">
                            @csrf
                            @method('put')
                            @include('employee.form')
                            <div class="modal-footer">
                                <div style="padding: 0 0 10px 4px;">
                                    <input type="checkbox" class="form-check-input" id="term-checkbox" name="term"
                                           required>
                                    <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os
                                        dados passados de acordo com o termo em anexo <a
                                            href="{{route('data-processing-agreement', ['user_type' => 'employee'])}}"
                                            target="_blank">(clique aqui para ler o termo).</a></label>
                                </div>
                                <button id="submit-button" disabled type="button" onclick="submitUpdate();"
                                        class="btn btn-success">Atualizar funcionário
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if($message = Session::get('danger'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
@endsection
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.5/jquery.min.js"></script>
<script type="text/javascript">

    function submitUpdate() {
        const email = document.getElementById('email').value;
        const oldEmail = "<?= $employee['email'] ?>";
        const formId = 'form-update-employee';
        const employeeId = "<?= $employee['id'] ?>";
        const emailChange = (email != oldEmail);

        validateStoreUpdate(formId, employeeId, emailChange);
    }
</script>

<script>

    $(document).ready(function () {
        let botaoSalvarClicado = false;
        let permissions_alterados = false;
        let business_alterados = false;

        const botaoSalvar = $('#submit-button');
        botaoSalvar.click(function (e) {
            e.preventDefault();
            botaoSalvarClicado = true;
            $('#form-update-employee').submit();
        });

        const formulario = document.getElementById('form-update-employee');

        const campos = formulario.querySelectorAll('input, textarea, select');

        const valoresIniciais = Array.from(campos).reduce((obj, campo) => {
            obj[campo.name] = campo.value;
            if (campo.name === 'highlight') {
                obj[campo.name] = document.getElementById('highlight-yes').checked ? '1' : '0';
            }
            if (campo.name === 'release_all_business') {
                obj[campo.name] = document.getElementById('release_all_business').checked ? '1' : '0';
            }
            return obj;
        }, {});

        const permissions_iniciais = $('#permissions_list').children().map(function () {
            return {
                checked: $(this).children()[0].checked,
                name: $(this).children()[1].innerText,
                children: $(this).children().map(function () {
                    if ($(this).children().length > 0) {
                        return {
                            checked: $(this).children()[0].checked,
                            name: $(this).children()[1].innerText,
                        }
                    }
                    return null;
                }).get(),
            }
        }).get();

        function validatePermissions() {
            let permissions = $('#permissions_list').children().map(function () {
                return {
                    checked: $(this).children()[0].checked,
                    name: $(this).children()[1].innerText,
                    children: $(this).children().map(function () {
                        if ($(this).children().length > 0) {
                            return {
                                checked: $(this).children()[0].checked,
                                name: $(this).children()[1].innerText,
                            }
                        }
                        return null;
                    }).get(),
                }
            }).get();
            for (let i = 0; i < permissions.length; i++) {
                const permission = permissions[i];
                const permission_inicial = permissions_iniciais[i];
                if (permission.checked !== permission_inicial.checked) {
                    permissions_alterados = true;
                    return;
                }
                for (let j = 0; j < permission.children.length; j++) {
                    const child = permission.children[j];
                    const child_inicial = permission_inicial.children[j];
                    if (child.checked !== child_inicial.checked) {
                        permissions_alterados = true;
                        return;
                    }
                }
            }
            permissions_alterados = false;
        }

        function validateBusinesses() {
            let old = oldBusinessesIds;
            let selected = businessesSelectedIds;
            //verifica se os arrays são diferentes
            if (old.length !== selected.length) {
                business_alterados = true;
                return;
            }
            for (let i = 0; i < old.length; i++) {
                if (!selected.includes(old[i])) {
                    business_alterados = true;
                    return;
                }
            }
            business_alterados = false;
        }

        function mudou() {
            validateBusinesses();
            validatePermissions();
            if (permissions_alterados || business_alterados) {
                return true;
            }
            for (let i = 0; i < campos.length; i++) {
                const campo = campos[i];
                if (campo.name === 'cep') {
                    let cep = campo.value.replace('-', '');
                    let valorInicial = valoresIniciais[campo.name].replace('-', '');
                    if (cep !== valorInicial) {
                        return true;
                    } else {
                        continue;
                    }
                }
                if (campo.name === 'release_all_business') {
                    if (campo.checked !== (valoresIniciais[campo.name] === '1')) {
                        return true;
                    } else {
                        continue;
                    }
                }

                if (campo.name === 'businessesIds[]' || campo.name === 'businesses[]') {
                    continue;
                }

                if(campo.name === 'initial_screen'){
                    if(campo.value !== '{{ $employee['initial_screen'] }}' && "{{ $employee['initial_screen'] }}" !== ''){
                        return true;
                    }
                    continue;
                }

                if (campo.value !== valoresIniciais[campo.name]) {
                    return true;
                }
            }
            return false;
        }

        function exibirAlertaSaida(event) {
            if (mudou() && botaoSalvarClicado === false) {
                event.preventDefault();
                var mensagem = 'Tem certeza que deseja sair desta página?';
                event.returnValue = mensagem;
                return mensagem;
            } else {
                botaoSalvarClicado = false;
            }
        }

        window.addEventListener('beforeunload', exibirAlertaSaida);

        $("#term-checkbox").click(showHideFormButton);

        function showHideFormButton() {
            if ($("#term-checkbox").is(":checked")) {
                $("#submit-button").prop("disabled", false);
            } else {
                $("#submit-button").prop("disabled", true);
            }
        }
    });
</script>
