@php use App\Helpers\ApiUser;use App\Helpers\StringMask; @endphp
@extends('layouts.app', ['activePage' => 'employees'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-4 col-md-12 col-sm-12 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Funcionários</h5>
                            </div>
                            <div class="col-lg-8 col-md-12 col-sm-12 mb-2">
                                <div class="d-flex justify-content-between">
                                    @include('components.forms.search', [
                                        'route' => route('employees.search'),
                                        'placeholder' => 'Nome, E-mail, CPF ou Telefone',
                                        'value' => $search ?? ''])
                                    <div class="d-flex">
                                        <button onclick="$('#loadingEmployee').modal('show');checkEmployees();" type="button"
                                                class="btn btn-success mr-1">
                                            <img class="card-img-left example-card-img-responsive"
                                                 src="{{url('icon/icon_plus.png')}}" width="15px"
                                                 style="margin-top: -2px;"/>
                                            <label class="styleBotaoAdicionar">Adicionar/Editar</label>
                                        </button>
                                        <div class="modal fade" id="cpfModal" tabindex="-1"
                                             aria-labelledby="cpfModalLabel" aria-hidden="true">
                                            <div class="modal-dialog modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="cpfModalLabel">Digite o CPF do
                                                            Funcionário</h5>
                                                        <button type="button" class="close" data-dismiss="modal"
                                                                aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <form id="cpfForm">
                                                            <input type="text" class="form-control mb-3" id="cpf_search"
                                                                   name="cpf" maxlength="14" required>
                                                            <button type="button" class="btn btn-primary"
                                                                    onclick="search()">Continuar
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-secondary"
                                                onclick="showNegociosUser(false)">
                                            <img class="card-img-left example-card-img-responsive"
                                                 src="{{url('icon/icon_filtro.png')}}" width="15px"
                                                 style="margin-top: -2px;"/>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="employees-table">
                                <thead>
                                <tr>
                                    <th scope="col" style="text-align: center">Ações</th>
                                    <th scope="col">Nome/CPF</th>
                                    <th scope="col">Email/Telefone</th>
                                    <th scope="col">Negócios</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($employees as $employee)
                                    <tr>
                                        <td style="text-align: center">
                                            <div class="btn-group">
                                                <button class="btn btn-secondary" data-toggle="modal"
                                                        data-target="#modal-view-employee-{{ $employee['id'] }}"
                                                        style="margin-right: 10px">
                                                    <img class="card-img-left example-card-img-responsive"
                                                         src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                                </button>
                                                @include('employee.modal_show', ['modalId' => "modal-view-employee-".($employee['id'])])
                                                <div>
                                                    <a class="btn btn-primary" style="margin-right: 10px"
                                                       href="{{ route('employees.edit', $employee['id']) }}">
                                                        <img class="card-img-left example-card-img-responsive"
                                                             src="{{url('icon/icon_editar.png')}}" width="20px"/>
                                                    </a>
                                                </div>
                                                <div>
                                                    <button class="btn btn-danger" data-toggle="modal"
                                                            data-target="#delete-collaborator-{{ $employee['id'] }}">
                                                        <img class="card-img-left example-card-img-responsive"
                                                             src="{{url('icon/icon_lixeira.png')}}" width="18px"/>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-form">
                                                <div>{{ $employee['name'] }}</div>
                                                <div
                                                    style="color: #909090">{{ StringMask::cpf($employee['cpf']) }}</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-form">
                                                <div>{{ $employee['email'] }}</div>
                                                <div>{{ StringMask::phone($employee['phone_number_1']) }}</div>
                                            </div>
                                        </td>
                                        @php
                                            if($employee['authorizedFunction']['release_all_business']){
                                                $businesses = 'Todos os negócios';
                                                $allBusinesses = 'Todos os negócios';
                                            }else{
                                                $businesses = $employee['businesses'];
                                                usort($businesses, function ($a, $b) {
                                                    return $a['name'] <=> $b['name'];
                                                });
                                                $allBusinesses = array_map(function ($business) {
                                                    return $business['name'];
                                                }, $businesses);
                                                $allBusinesses = implode(', ', $allBusinesses);
                                                $businessesCount = count($businesses);
                                                $businessesLimit = 3;

                                                if ($businessesCount > $businessesLimit) {
                                                    $businesses = array_slice($businesses, 0, $businessesLimit);
                                                }

                                                $businesses = array_map(function ($business) {
                                                    return $business['name'];
                                                }, $businesses);

                                                $businesses = implode(', ', $businesses);

                                                if ($businessesCount > $businessesLimit) {
                                                    $businesses .= '...';
                                                }
                                            }
                                        @endphp
                                        <td style="max-width: 12rem; text-overflow: ellipsis; overflow: hidden;"
                                            title="{{ $allBusinesses }}">
                                            {{ $businesses }}
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                            @foreach ($employees as $employee)
                                @include('employee.modal_delete',['modalId' => 'delete-collaborator-'.($employee['id'])])
                            @endforeach
                        </div>

                        {{ $employees->onEachSide(0)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('employee.subtitle')
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...', 'id' => 'loadingEmployee'])
    <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>
    <script>
        function formatCPF(cpf) {
            cpf = cpf.replace(/\D/g, ""); // Remove all non-digit characters
            cpf = cpf.replace(/(\d{3})(\d)/, "$1.$2"); // Add a dot after the first 3 digits
            cpf = cpf.replace(/(\d{3})(\d)/, "$1.$2"); // Add a dot after the next 3 digits
            cpf = cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2"); // Add a dash before the last 2 digits
            return cpf;
        }

        document.addEventListener('DOMContentLoaded', function () {
            const cpfInput = document.getElementById('cpf_search');
            cpfInput.addEventListener('input', function () {
                cpfInput.value = formatCPF(cpfInput.value);
            });
        });
    </script>
    <script>
        function validateCpf() {
            let cpfInput = document.getElementById('cpf_search');
            let cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

            let isValid = cpfIsValid(cpf);

            if (isValid) {
                cpfInput.classList.remove("is-invalid")
                cpfInput.classList.add("is-valid")
                return true;
            } else {
                cpfInput.classList.add("is-invalid")
                return false;
            }
        }

        function search() {
            if (validateCpf()) {
                $('#cpfModal').modal('hide');
                $('#loadingEmployee').modal('show');
                searchEmployee();
            } else {
                alert('CPF inválido');
            }
        }

        async function checkEmployees() {
            @if(ApiUser::hasUserFunction('release_all_business') && ApiUser::get()['business_selected'] === null)
                $('#loadingEmployee').modal('hide');
                $('#cpfModal').modal('show');
                return;
            @endif

            try {
                const response = await fetch("{{ env('API_URL') }}/api/user/validate-employees", {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('Erro na requisição');
                }

                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.msg || 'Erro ao verificar funcionários');
                }

                const totalEmployees = data.data.total_employees;
                const totalEmployeesPlan = data.data.total_employees_plan;
                const remainingEmployees = data.data.remaining_employees;

                if (remainingEmployees <= 0) {
                  Swal.fire({
                      icon: 'error',
                      title: 'Limite de Funcionários Excedido',
                      text: 'Você atingiu o limite de funcionários permitidos no plano.',
                      confirmButtonText: 'Ok',
                      @if(ApiUser::hasPermission('dashboard'))
                      showCancelButton: true,
                      cancelButtonText: 'Ver Limites',
                      cancelButtonColor: '#3085d6'
                      @endif
                  }).then((result) => {
                      @if(ApiUser::hasPermission('dashboard'))
                      if (result.dismiss === Swal.DismissReason.cancel) {
                          window.location.href = "{{ route('dashboard.index') }}";
                      }
                      @endif
                  });
                  $('#loadingEmployee').modal('hide');
                  return;
                }
                $('#loadingEmployee').modal('hide');
                $('#cpfModal').modal('show');
            } catch (error) {
                console.error('Erro:', error);
                $('#loadingEmployee').modal('hide');
                alert('Erro ao verificar funcionários');
            }
        }

        function searchEmployee() {
            let cpf = $('#cpf_search').val().replace(/\D/g, '');
            $.ajax({
                url: "<?= env('API_URL') ?>/api/user/show/by-cpf",
                type: 'get',
                data: {
                    cpf: cpf,
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {
                    if (response.employee != null) {
                        if (response.employee.id === {{ApiUser::get()['id']}}) {
                            alert("Não é possível alterar seus dados por essa tela. Para alterar seus dados, entre na tela de perfil.");
                            return;
                        }
                        let myReleaseAllBusiness = {{ApiUser::get()['authorizedFunction']['release_all_business'] ? 'true' : 'false'}};
                        if (myReleaseAllBusiness) {
                            let employeeId = response.employee.id;
                            let urlEdit = "{{ route('employees.edit', 'id') }}";
                            urlEdit = urlEdit.replace('id', employeeId);
                            window.location.href = urlEdit;
                        }

                        let releaseAllBusinessEmployee = response.employee.authorizedFunction.release_all_business;
                        if (!myReleaseAllBusiness && releaseAllBusinessEmployee) {
                            alert('Esse cpf já está sendo utilizado por outro funcionário e você não tem permissão para alterar seus dados.');
                            return;
                        }
                        @php
                            $businesses = ApiUser::get()['businesses'];
                            $businessesIds = array_map(function ($business) {
                                return $business['id'];
                            }, $businesses);
                        @endphp

                        let myBusinessesIds = @json($businessesIds);

                        let businessesEmployee = response.employee.businesses;
                        let businessesEmployeeIds = businessesEmployee.map(business => business.id);

                        let hasBusinessInCommon = businessesEmployeeIds.some(id => myBusinessesIds.includes(id));

                        if (!myReleaseAllBusiness && !hasBusinessInCommon) {
                            alert('Esse cpf já está sendo utilizado por outro funcionário e você não tem permissão para alterar seus dados.');
                            return;
                        }
                        if (response.employee.admin) {
                            alert('Esse cpf já está sendo utilizado por outro funcionário e você não tem permissão para alterar seus dados.');
                            return;
                        }

                        let employeeId = response.employee.id;
                        let urlEdit = "{{ route('employees.edit', 'id') }}";
                        urlEdit = urlEdit.replace('id', employeeId);
                        window.location.href = urlEdit;
                    } else {
                        window.location.href = "{{ route('employees.create') }}?cpf=" + cpf;
                    }
                },
                error: function (response, textStatus, msg) {
                  if (response.status === 404) {
                    $('#loadingEmployee').modal('hide');
                    window.location.href = "{{ route('employees.create') }}?cpf=" + cpf;
                  } else {
                    $('#loadingEmployee').modal('hide');
                    alert('Erro ao buscar funcionário');
                  }
                },
                complete: function () {
                    $('#loadingEmployee').modal('hide');
                }
            });
        }
    </script>
@endsection
