<div class="modal" tabindex="-1" role="dialog" id="{{ $modalId }}">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Deletar Funcionário</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            @php
                // Filtra os negócios onde o funcionário é administrador
                $adminBusinesses = collect($employee['businesses'])->filter(function($business) use ($employee) {
                    return isset($business['admin']) && $business['admin']['id'] === $employee['id'];
                });
            @endphp

            @if($adminBusinesses->isNotEmpty())
                <div class="modal-body">
                    <div style="border: 1px solid red; padding: 15px; background-color: #f8d7da; color: #721c24; border-radius: 5px;">
                        <h6 style="font-weight: bold;">Atenção!</h6>
                        <p>Não é possível deletar este funcionário pois ele é administrador das seguintes empresas:</p>
                        <ul>
                            @foreach($adminBusinesses as $business)
                                <li><strong>{{ $business['name'] }}</strong></li>
                            @endforeach
                        </ul>
                        <p>Por favor, transfira a administração para outro funcionário antes de proceder com a deleção.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
                </div>
            @else
                <form action="{{ route('employees.destroy', $employee['id']) }}" method="POST">
                    @csrf
                    @method('delete')
                    <input type="hidden" id="employee_id-{{ $employee['id'] }}" name="employee_id" value="{{ $employee['id'] }}">
                    <div class="modal-body overflow-auto">
                        <div class="row">
                            <div class="col">
                                <p>Você deseja mesmo deletar o funcionário <strong>{{ $employee['name'] }}</strong>?</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">Deletar</button>
                    </div>
                </form>
            @endif
        </div>
    </div>
</div>