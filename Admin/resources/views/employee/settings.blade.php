@extends('layouts.app', ['activePage' => 'settings'])

@section('content')
    @include('components.loading', ['message' => 'Aguarde...'])

    <x-modal.modal_confirm_action
        id="modalSettingsDelete"
        title="Tem certeza que deseja continuar?"
        onSubmit="removeImage()"
    >
        <p class="text-muted">A imagem será removida definitivamente.</p>
    </x-modal.modal_confirm_action>

    <div class="container">
        <div class="row justify-content-center">
            <div class="container" style="margin-bottom: 1rem;">
            </div>
            <div class="col-md-12" style="margin-bottom:3rem">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="d-flex justify-content-between align-items-center"
                             style="margin-top: 5px; margin-bottom:4px">
                            <h5 class="mb-0 card-title"
                                style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                Configurações</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="col-md-12 ">
                                <div class="row">
                                    <div class="col-12 mb-4 card card_conteudo p-2">
                                        <div class="nav nav-pills" id="v-pills-tab" role="tablist">
                                            <a class="nav-link @if(!$errors->any()) active @endif" id="v-pills-home-tab"
                                               data-toggle="pill" href="#v-pills-home" role="tab"
                                               aria-controls="v-pills-home" aria-selected="true">Início</a>
                                            @if((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin') && ApiUser::get()['authorizedFunction']['settings'] && ApiUser::get()['authorizedSettingsFunction']['contact'])
                                                <a class="nav-link @if($errors->any() && old('tab') == 'contact') active @endif"
                                                   id="v-pills-contact-tab"
                                                   data-toggle="pill" href="#v-pills-contact" role="tab"
                                                   aria-controls="v-pills-contact" aria-selected="false">Contato</a>
                                            @endif
                                            @if((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin') && ApiUser::get()['authorizedFunction']['settings'] && ApiUser::get()['authorizedSettingsFunction']['system_parameters'])
                                                <a class="nav-link @if($errors->any() && old('tab') == 'system_parameters') active @endif"
                                                   id="v-pills-system_parameters-tab"
                                                   data-toggle="pill" href="#v-pills-system_parameters" role="tab"
                                                   aria-controls="v-pills-system_parameters" aria-selected="false">Sistema</a>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="tab-content" id="v-pills-tabContent">
                                            {{-- Inicio --}}
                                            <div class="tab-pane fade show @if(!$errors->any()) active @endif"
                                                 id="v-pills-home" role="tabpanel" aria-labelledby="v-pills-home-tab">
                                                <div class="form-row justify-content-center" style="text-align: center">
                                                    <div class="col-md-12"><img
                                                            class="card-img-left example-card-img-responsive"
                                                            src="{{ SystemHelper::get()['MAIN_LOGO_THEME']?? '/icon/logo_visao_negocio.svg' }}"
                                                            width="60%"/>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <h6 style="color: #927373; margin-top:1rem">Bem vindo(a) a área
                                                            de
                                                            configurações do sistema.</h6>
                                                    </div>
                                                </div>
                                            </div>
                                            @if((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin') && ApiUser::get()['authorizedFunction']['settings'] && ApiUser::get()['authorizedSettingsFunction']['contact'])
                                                {{-- Contato--}}
                                                <div
                                                    class="tab-pane fade @if($errors->any() && old('tab') == 'contact') show active @endif"
                                                    id="v-pills-contact" role="tabpanel"
                                                    aria-labelledby="v-pills-contact-tab">
                                                    <div class="form-row">
                                                        <div class="col-md-12">
                                                            <div class="card-header"
                                                                 style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                                                                <div
                                                                    class="row d-flex justify-content-between align-items-center mx-0"
                                                                    style="margin-top: 5px; margin-bottom:4px">
                                                                    <h5 class="mb-0 w-50 card-title"
                                                                        style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                                                        Contatos</h5>
                                                                </div>
                                                            </div>
                                                            <div class="card-body">
                                                                <form name="form-contact" method="POST"
                                                                      action="{{route('settings.contact.update')}}">
                                                                    @csrf
                                                                    <input type="hidden" name="tab" value="contact">
                                                                    <div class="form-group">
                                                                        <label for="email">E-mail</label>
                                                                        <input id="email" name="email" type="text"
                                                                               class="form-control  @error('email') is-invalid @enderror"
                                                                               value="{{old('email') ?? $email ?? ''}}">
                                                                    </div>

                                                                    <div class="form-group">
                                                                        <label
                                                                            for="phone_number">Telefone/Celular</label>
                                                                        <input id="phone_number" name="phone_number"
                                                                               type="text"
                                                                               class="form-control mask-phone @error('phone_number') is-invalid @enderror"
                                                                               value="{{old('phone_number') ?? $phone_number ?? ''}}">
                                                                    </div>

                                                                    <div
                                                                        class="row d-flex justify-content-end align-items-center mx-0"
                                                                        style="margin-top: 5px; margin-bottom:4px">
                                                                        <button type="submit" class="btn btn-success">
                                                                            Salvar
                                                                        </button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                            @if((ApiUser::getLoginType() == 'employee' || ApiUser::getLoginType() == 'admin') && ApiUser::get()['authorizedFunction']['settings'] && ApiUser::get()['authorizedSettingsFunction']['system_parameters'])
                                                {{-- Global Parameters --}}
                                                <div
                                                    class="tab-pane fade @if($errors->any() && old('tab') == 'system_parameters') show active @endif"
                                                    id="v-pills-system_parameters" role="tabpanel"
                                                    aria-labelledby="v-pills-system_parameters-tab">
                                                    <div class="form-row">
                                                        <div class="col-md-12">
                                                            <div class="card-header"
                                                                 style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                                                                <div
                                                                    class="row d-flex justify-content-between align-items-center mx-0"
                                                                    style="margin-top: 5px; margin-bottom:4px">
                                                                    <h5 class="mb-0 w-50 card-title"
                                                                        style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                                                        Sistema</h5>
                                                                    <span
                                                                        class='text-danger'>* Campos obrigatórios</span>
                                                                </div>
                                                            </div>
                                                            <div class="card-body">
                                                                <form id="form-system_parameters"
                                                                      name="form-system_parameters" method="POST"
                                                                      action="{{route('settings.system_parameters.update')}}">
                                                                    @csrf
                                                                    <input type="hidden" name="tab"
                                                                           value="system_parameters">
                                                                    <table class="table table-responsive"
                                                                           id="health-clinic-table"
                                                                           style="display: block; overflow-x: auto; white-space: nowrap;">
                                                                        <thead>
                                                                        <tr>
                                                                            <th scope="col">Nome</th>
                                                                            <th scope="col">Descrição</th>
                                                                            <th scope="col">Valor</th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        @foreach ($system_parameters as $system_parameter)
                                                                            <tr>
                                                                                <td class="text-truncate"
                                                                                    style="max-width: 25rem">{{ $system_parameter['name'] }}@if($system_parameter['required'])
                                                                                        <b class='text-danger'>*</b>
                                                                                    @endif</td>
                                                                                <td class="text-truncate"
                                                                                    style="max-width: 28rem">{{ $system_parameter['description'] }}</td>
                                                                                <td style="min-width: 23rem">
                                                                                    @switch($system_parameter['type'])
                                                                                        @case('link')
                                                                                            <input type="text"
                                                                                                   name="{{ $system_parameter['code_name'] }}"
                                                                                                   value="{{ $system_parameter['value'] }}"
                                                                                                   id="{{ $system_parameter['name'] }}"
                                                                                                   class="form-control"
                                                                                                   @if($system_parameter['required']) required @endif>
                                                                                            @break
                                                                                        @case('string')
                                                                                            <input type="text"
                                                                                                   name="{{ $system_parameter['code_name'] }}"
                                                                                                   value="{{ $system_parameter['value'] }}"
                                                                                                   id="{{ $system_parameter['name'] }}"
                                                                                                   class="form-control"
                                                                                                   @if($system_parameter['required']) required @endif>
                                                                                            @break
                                                                                        @case('number')
                                                                                            <input type="text"
                                                                                                   name="{{ $system_parameter['code_name'] }}"
                                                                                                   value="{{ $system_parameter['value'] }}"
                                                                                                   id="{{ $system_parameter['name'] }}"
                                                                                                   class="form-control"
                                                                                                   oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                                                                                                   @if($system_parameter['required']) required @endif >
                                                                                            @break
                                                                                        @case('float')
                                                                                            <input type="number"
                                                                                                   step="0.01"
                                                                                                   name="{{ $system_parameter['code_name'] }}"
                                                                                                   value="{{ $system_parameter['value'] }}"
                                                                                                   id="{{ $system_parameter['name'] }}"
                                                                                                   class="form-control"
                                                                                                   @if($system_parameter['required']) required @endif>
                                                                                            @break
                                                                                        @case('boolean')
                                                                                            <div class="form-check">
                                                                                                <input type="checkbox"
                                                                                                       name="{{ $system_parameter['code_name'] }}"
                                                                                                       value="1"
                                                                                                       @if($system_parameter['value'] == '1') checked
                                                                                                       @endif id="{{ $system_parameter['name'] }}"
                                                                                                       class="form-check-input">
                                                                                            </div>
                                                                                            @break
                                                                                        @case('yes_not')
                                                                                            <select
                                                                                                name="{{ $system_parameter['code_name'] }}"
                                                                                                id="{{ $system_parameter['name'] }}"
                                                                                                class="form-control">
                                                                                                <option value="S"
                                                                                                        @if($system_parameter['value'] == 'S') selected @endif>
                                                                                                    Sim
                                                                                                </option>
                                                                                                <option value="N"
                                                                                                        @if($system_parameter['value'] == 'N') selected @endif>
                                                                                                    Não
                                                                                                </option>
                                                                                            </select>
                                                                                            @break
                                                                                        @case('color')
                                                                                            <input type="color"
                                                                                                   name="{{ $system_parameter['code_name'] }}"
                                                                                                   value="{{ $system_parameter['value'] }}"
                                                                                                   id="{{ $system_parameter['name'] }}"
                                                                                                   class="form-control"
                                                                                                   @if($system_parameter['required']) required @endif
                                                                                            >
                                                                                            @break
                                                                                        @case('image')
                                                                                            <button type="button"
                                                                                                    class="btn @if($system_parameter['value']) btn-info @else btn-secondary @endif "
                                                                                                    id="{{ $system_parameter['name'] }}_button"
                                                                                                    onclick="document.getElementById('{{ $system_parameter['name'] }}').click()">@if($system_parameter['value'])
                                                                                                    Alterar
                                                                                                @else
                                                                                                    Adicionar
                                                                                                @endif</button>
                                                                                            <input type="file"
                                                                                                   id="{{ $system_parameter['name']}}"
                                                                                                   class="form-control d-none"
                                                                                                   @if($system_parameter['required']) required
                                                                                                   @endif accept="image/png, image/jpeg, image/jpg, image/gif"
                                                                                                   onchange="validateFileSize(this)">
                                                                                            <button
                                                                                                class="btn btn-secondary @if(!$system_parameter['value']) d-none @endif "
                                                                                                id="{{ $system_parameter['name'] }}_view"
                                                                                                @if($system_parameter['value']) onclick="event.preventDefault();window.open('{{ $system_parameter['value'] }}', '_blank')" @endif>
                                                                                                <img
                                                                                                    class="card-img-left example-card-img-responsive"
                                                                                                    src="{{url('icon/icon_visualizar.svg')}}"
                                                                                                    width="24px"/>
                                                                                            </button>
                                                                                            @if ($system_parameter['value'])
                                                                                                <button
                                                                                                    type="button"
                                                                                                    class="btn btn-danger rounded"
                                                                                                    id="{{ $system_parameter['name'] }}_button"
                                                                                                    onclick="openConfirmModal('{{ $system_parameter['name'] }}')"
                                                                                                ">
                                                                                                <i class="fas fa-times fa-fw text-white"></i>
                                                                                                </button>
                                                                                                <input type="hidden"
                                                                                                       name="{{ $system_parameter['code_name']."_remove" }}"
                                                                                                       id="{{ $system_parameter['name'] }}_remove">
                                                                                            @endif
                                                                                            <input type="hidden"
                                                                                                   name="{{ $system_parameter['code_name']."_type" }}"
                                                                                                   id="{{ $system_parameter['name'] }}_type">
                                                                                            <input type="hidden"
                                                                                                   name="{{ $system_parameter['code_name'] }}"
                                                                                                   id="{{ $system_parameter['name']."_base64" }}">
                                                                                            @break
                                                                                    @endswitch
                                                                                </td>
                                                                            </tr>
                                                                        @endforeach
                                                                        </tbody>
                                                                    </table>
                                                                </form>
                                                                <div
                                                                    class="row d-flex justify-content-end align-items-center mx-0"
                                                                    style="margin-top: 5px; margin-bottom:4px">
                                                                    <button class="btn btn-success" onclick="salvar()"
                                                                            @if(count($system_parameters) === 0) disabled @endif>
                                                                        Salvar
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <script>
                                                    function validateFileSize(inputFile) {
                                                        let files = inputFile.files;
                                                        let maxSizeInBytes = 5 * 1024 * 1024; // 5MB
                                                        let isValid = true;

                                                        for (let i = 0; i < files.length; i++) {
                                                            if (files[i].size > maxSizeInBytes) {
                                                                isValid = false;
                                                                break;
                                                            }
                                                        }

                                                        if (!isValid) {
                                                            inputFile.value = '';
                                                            inputFile.files = null;
                                                            alert('O tamanho máximo permitido para a imagem é de 5MB.');
                                                        } else {
                                                            console.log(inputFile);
                                                            loadImage(inputFile);
                                                        }
                                                    }

                                                    function loadImage(inputFile) {
                                                        console.log('loadImage');
                                                        const reader = new FileReader();
                                                        reader.onload = function (e) {
                                                            const base64 = e.target.result;
                                                            const input = document.getElementById(inputFile.id + '_base64');
                                                            input.value = base64;

                                                            const type = document.getElementById(inputFile.id + '_type');
                                                            type.value = inputFile.files[0].name.split('.').pop();

                                                            //set name in the button
                                                            inputFile.previousElementSibling.innerHTML = inputFile.files[0].name;
                                                            inputFile.previousElementSibling.classList.remove('btn-secondary');
                                                            inputFile.previousElementSibling.classList.remove('btn-info');
                                                            inputFile.previousElementSibling.classList.add('btn-success');
                                                            inputFile.nextElementSibling.classList.remove('d-none');

                                                            let url = window.URL.createObjectURL(inputFile.files[0]);
                                                            inputFile.nextElementSibling.setAttribute('onclick', 'event.preventDefault();window.open("' + url + '", "_blank")');
                                                        };
                                                        reader.readAsDataURL(inputFile.files[0]);

                                                    }

                                                    function toggleFields(checkbox) {
                                                        const fieldName = checkbox.name;
                                                        const relatedField = document.getElementById(fieldName + '-hidden'); // Adicione um sufixo para identificar o campo oculto

                                                        if (!checkbox.checked) {
                                                            if (!relatedField) {
                                                                const field = document.createElement("input");
                                                                field.type = "hidden";
                                                                field.value = "0";
                                                                field.name = fieldName;
                                                                field.id = fieldName + '-hidden';
                                                                field.className = "form-control";
                                                                field.required = true;

                                                                checkbox.parentElement.appendChild(field);
                                                            }
                                                        } else {
                                                            if (relatedField) {
                                                                relatedField.remove();
                                                            }
                                                        }
                                                    }
                                                    @foreach ($system_parameters as $system_parameter)
                                                    @if ($system_parameter['type'] === 'boolean')
                                                    const checkbox = document.querySelector('input[name="{{ $system_parameter['code_name'] }}"]');
                                                    checkbox.addEventListener('change', () => {
                                                        toggleFields(checkbox);
                                                    });

                                                    toggleFields(checkbox);
                                                    @endif
                                                    @endforeach


                                                    function salvar() {
                                                        let form = $('#form-system_parameters');
                                                        if (!form[0].checkValidity()) {
                                                            form[0].reportValidity();
                                                        } else {
                                                            form.submit();
                                                        }
                                                    }

                                                    function openConfirmModal(parameterName) {
                                                        $('#modalSettingsDelete').modal('show');
                                                        $('#modalSettingsDelete').data('parameterName', parameterName)
                                                    }

                                                    function removeImage() {
                                                        const form = $('#form-system_parameters');
                                                        const modal = $('#modalSettingsDelete');
                                                        const parameterName = modal.data('parameterName');
                                                        const inputFile = $('#' + parameterName);
                                                        const inputFlagRemoveImg = document.getElementById(parameterName + '_remove');

                                                        inputFile.val('');
                                                        inputFlagRemoveImg.value = true;
                                                        modal.modal('hide');
                                                        form.submit();
                                                    }
                                                </script>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('js')
        <script type="text/javascript">

            $('.mask-phone').on('input', function (e) {
                let number = e.target.value.replace(/[^a-zA-Z0-9]/g, '')

                if (number && number !== '') {
                    e.target.value = number.length <= 10
                        ? number
                            .replace(/\D/g, '')
                            .replace(/(\d{2})(\d)/, '($1) $2')
                            .replace(/(\d{4})(\d)/, '$1-$2')
                            .replace(/(-\d{4})\d+?$/, '$1')
                        : number
                            .replace(/\D/g, '')
                            .replace(/(\d{2})(\d)/, '($1) $2')
                            .replace(/(\d{5})(\d)/, '$1-$2')
                            .replace(/(-\d{4})\d+?$/, '$1');
                } else {
                    e.target.value = '';
                }

            });


        </script>

    @endpush

@endsection
