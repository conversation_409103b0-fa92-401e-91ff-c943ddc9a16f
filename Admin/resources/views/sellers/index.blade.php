@extends('layouts.app', ['activePage' => 'sellers'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="col-12 d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0"
                                style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                Vendedores</h5>
                            <button onclick="$('#cpfModal').modal('show')" type="button" class="btn btn-success">
                                <img class="card-img-left example-card-img-responsive"
                                      src="{{url('icon/icon_plus.png')}}" width="15px"
                                      style="margin-top: -2px;"/>
                                <label class="styleBotaoAdicionar">Adicionar/Editar</label>
                            </button>
                        </div>
                        <div class="col-12">
                            <div class="d-flex justify-content-between flex-wrap">
                                <div class="d-flex flex-grow-1 mb-2">
                                    <form class='w-100' action="" method="get" id="searchForm">
                                        @csrf
                                        <div class="d-flex flex-column flex-sm-row">
                                            <div class="d-flex flex-grow-1 mb-2 mb-sm-0">
                                                <input type="text" class="form-control mr-2" name="search" aria-describedby="search" placeholder="Pesquisar vendedor" value="{{ $search ?? old('search') }}">
                                                
                                                <button class="btn btn-light" type="submit">
                                                    <img class="card-img-left example-card-img-responsive" src="{{url('icon/icon_search.png')}}" width="20px" />
                                                </button>
                                            </div>

                                            <select name="seller_type" id="seller_type" class="form-control" style="width: auto;" onchange="document.getElementById('searchForm').submit()">
                                                <option value="all" {{ request("seller_type", "all") == "all" ? "selected" : "" }}>Todos os Vendedores</option>
                                                <option value="main" {{ request("seller_type") == "main" ? "selected" : "" }}>Vendedores Principais</option>
                                                <option value="child" {{ request("seller_type") == "child" ? "selected" : "" }}>Vendedores Filhos</option>
                                            </select>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal fade" id="cpfModal" tabindex="-1" aria-labelledby="cpfModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="cpfModalLabel">Digite o CPF do Vendedor</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="cpfForm">
                                                    <input type="text" class="form-control mb-3" id="cpf_search" name="cpf" maxlength="14" required>
                                                    <button type="button" class="btn btn-primary" onclick="search()">Continuar</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="sellers-table">
                            <thead>
                                <tr>
                                    <th scope="col" style="text-align: center">Ações</th>
                                    <th scope="col">Nome/CPF</th>
                                    <th scope="col">Email</th>
                                    <th scope="col">Telefone</th>
                                    @if(!isset($fromSeller))
                                        <th scope="col">Vendedor Principal</th>
                                    @endif
                                </tr>
                            </thead>
                            <tbody>
                            @foreach ($sellers as $seller)
                            <tr>
                                <td style="text-align: center">
                                    <button class="btn btn-secondary" data-toggle="modal"
                                            data-target="#modal-view-seller-{{ $seller['id'] }}"
                                            style="margin-right: 10px">
                                        <img class="card-img-left example-card-img-responsive"
                                                src="{{url('icon/icon_visualizar.svg')}}" width="24px"/>
                                    </button>
                                    @include('sellers.modal_show', ['modalId' => "modal-view-seller-".($seller['id'])])

                                    <a class="btn btn-primary" style="margin-right: 10px"
                                        href="{{ isset($fromSeller) && $fromSeller ? route('sellers.seller.edit', $seller['id']) :
                                         route('sellers.edit', $seller['id']) }}">
                                        <img class="card-img-left example-card-img-responsive"
                                                src="{{url('icon/icon_editar.png')}}" width="20px"/>
                                    </a>

                                    <button class="btn btn-danger" data-toggle="modal"
                                            data-target="#delete-seller-{{ $seller['id'] }}">
                                        <img class="card-img-left example-card-img-responsive"
                                             src="{{url('icon/icon_lixeira.png')}}" width="18px"/>
                                    </button>

                                    @if(!isset($fromSeller) && ApiUser::hasUserFunction('update_business')) 
                                        <a href="{{ route('businesses.index', ['seller_id' => $seller['id']]) }}" class="btn btn-info" style="margin-left: 10px" title="Negócios do Vendedor">
                                            <i class="fas fa-store"></i>
                                        </a>
                                    @endif

                                    @if(isset($fromSeller) && $fromSeller)
                                        <a class="btn btn-info" style="margin-left: 10px"
                                          href="{{ route('businesses.seller.index', ['seller_id' => $seller['id']]) }}">
                                            <i class="fas fa-store"></i>
                                        </a>
                                    @endif
                                </td>
                                <td>
                                    <div>{{ $seller['name'] }}</div>
                                    <div style="color: #909090">{{ StringMask::cpf($seller['cpf']) }}</div>
                                </td>
                                <td>
                                    {{ $seller['email'] }}
                                </td>
                                <td>
                                    {{ StringMask::phone($seller['phone_number_1']) }}
                                </td>
                                @if(!isset($fromSeller))
                                    <td>
                                        {{ $seller['registered_by'] != null ? $seller['registered_by']['name'] : '-' }}
                                    </td>
                                @endif
                            </tr>
                            @include('sellers.modal_delete',['modalId' => 'delete-seller-'.($seller['id'])])
                            @endforeach
                            </tbody>
                        </table>
                    </div>

                        {{ $sellers->onEachSide(0)->links() }}
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('sellers.legend')
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>
    @include('components.loading', ['message' => 'Carregando...', 'id' => 'loadingEmployee'])
    <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>
    <script>
        function formatCPF(cpf) {
            cpf = cpf.replace(/\D/g, ""); // Remove all non-digit characters
            cpf = cpf.replace(/(\d{3})(\d)/, "$1.$2"); // Add a dot after the first 3 digits
            cpf = cpf.replace(/(\d{3})(\d)/, "$1.$2"); // Add a dot after the next 3 digits
            cpf = cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2"); // Add a dash before the last 2 digits
            return cpf;
        }

        document.addEventListener('DOMContentLoaded', function() {
            const cpfInput = document.getElementById('cpf_search');
            cpfInput.addEventListener('input', function() {
                cpfInput.value = formatCPF(cpfInput.value);
            });
        });
    </script>
    <script>
        function validateCpf() {
            let cpfInput = document.getElementById('cpf_search');
            let cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

            let isValid = cpfIsValid(cpf);

            if (isValid) {
                cpfInput.classList.remove("is-invalid")
                cpfInput.classList.add("is-valid")
                return true;
            } else {
                cpfInput.classList.add("is-invalid")
                return false;
            }
        }

        function search() {
            if(validateCpf()){
                $('#cpfModal').modal('hide');
                $('#loadingEmployee').modal('show');
                searchSeller();
            }
        }

        function searchSeller() {
            let cpf = $('#cpf_search').val().replace(/\D/g, '');
            $.ajax({
                url: "<?= env('API_URL') ?>/api/user/show/by-cpf",
                type: 'get',
                data: {cpf: cpf},
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {''
                    $('#loadingEmployee').modal('hide');
                    if(response.seller != null){
                        let sellerId = response.seller.id;
                        @if(isset($fromSeller) && $fromSeller)
                            let urlEdit = "{{ route('sellers.seller.edit', 'id') }}";
                        @else
                            let urlEdit = "{{ route('sellers.edit', 'id') }}";
                        @endif
                        urlEdit = urlEdit.replace('id', sellerId);
                        window.location.href = urlEdit;
                    }else{
                        @if(isset($fromSeller) && $fromSeller)
                            window.location.href = "{{ route('sellers.seller.create') }}?cpf=" + cpf;
                        @else
                            window.location.href = "{{ route('sellers.create') }}?cpf=" + cpf;
                        @endif
                    }
                },
                error: function (response, textStatus, msg) {
                    $('#loadingEmployee').modal('hide');
                    @if(isset($fromSeller) && $fromSeller)
                        window.location.href = "{{ route('sellers.seller.create') }}?cpf=" + cpf;
                    @else
                        window.location.href = "{{ route('sellers.create') }}?cpf=" + cpf;
                    @endif
                },
            });
        }
    </script>
@endsection
