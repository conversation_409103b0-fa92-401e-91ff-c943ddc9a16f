@php use App\Helpers\ApiUser; @endphp
<div class="form-row">
    <div class="col-md-12"><p class="form-title"><PERSON><PERSON> pessoais</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'name',
                'label' => 'Nome completo',
                'value' => old('name') ?? ($seller['name'] ?? $user['name'] ?? ''),
                'placeholder' => 'Digite o nome completo do vendedor',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="gender">Gênero</label>
            <select id="gender" name="gender" class="form-select form-control @error('gender') is-invalid @enderror"
                    aria-label="Default select example" required>
                <option value="other">-- Não Informar --</option>
                <option value="m"
                        @if(old('gender') == 'm' || isset($seller) && $seller['gender'] == 'm'  || isset($user) && $user['gender'] == 'm') selected @endif >
                    Masculino
                </option>
                <option value="f"
                        @if(old('gender') == 'f' || isset($seller) && $seller['gender'] == 'f' || isset($user) && $user['gender'] == 'f') selected @endif >
                    Feminino
                </option>
            </select>
            @error('gender')
            <span class="invalid-feedback" role="alert">
                <strong>{{$message}}</strong>
            </span>
            @enderror
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.date', [
                'name' => 'birth_date',
                'label' => 'Data de nascimento',
                'value' => old('birth_date') ?? ($seller['birth_date'] ?? $user['birth_date'] ?? ''),
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'cpf',
                'label' => 'CPF',
                'value' => old('cpf') ?? ($seller['cpf'] ?? $user['cpf'] ?? $cpf ?? ''),
                'placeholder' => 'Digite o CPF',
                'onchange' =>'checkCPF()',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Endereço</p></div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'cep',
                'label' => 'CEP',
                'value' => old('cep') ?? ($seller['address']['cep'] ?? $user['address']['cep'] ?? ''),
                'placeholder' => 'Digite o cep do vendedor',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'neighborhood',
                'label' => 'Bairro',
                'value' => old('neighborhood') ?? ($seller['address']['neighborhood'] ?? $user['address']['neighborhood'] ?? ''),
                'placeholder' => 'Digite o nome do bairro',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'street',
                'label' => 'Rua',
                'value' => old('street') ?? ($seller['address']['street'] ?? $user['address']['street'] ?? ''),
                'placeholder' => 'Digite o nome da rua',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'number',
                'label' => 'Número',
                'value' => old('number') ?? ($seller['address']['number'] ?? $user['address']['number'] ?? ''),
                'placeholder' => 'Digite o número da residência (Ex: 01 ou SN)',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'complement',
                'label' => 'Complemento',
                'value' => old('complement') ?? ($seller['address']['complement'] ?? $user['address']['complement'] ?? ''),
                'placeholder' => 'Digite o complemento',
                'required' => false,
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="row flex-fill mb-4">
        <div class="col">
            @include('components.inputs.text', [
                'name' => 'city',
                'label' => 'Cidade',
                'value' => old('city') ?? ($seller['address']['city'] ?? $user['address']['city'] ?? ''),
                'readonly' => true,
                'withErrorText' => true
            ])
        </div>
        <div class="col">
            @include('components.inputs.text', [
                'name' => 'state',
                'label' => 'Estado',
                'value' => old('state') ?? ($seller['address']['state'] ?? $user['address']['state'] ?? ''),
                'readonly' => true,
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Contato</p></div>
    @php
        $initialPhoneForValidationLogic = '';
        $initialPhoneValidated = false;
        if (isset($seller['phone_number_1']) || isset($user['phone_number_1'])) {
            $rawPhone = $seller['phone_number_1'] ?? $user['phone_number_1'] ?? '';
            $initialPhoneForValidationLogic = preg_replace('/\D/', '', $rawPhone);
            $initialPhoneValidated = !empty($rawPhone);
        }
        $isInitialPhoneActuallyValid = (!empty($initialPhoneForValidationLogic)) && preg_match('/^\d{10,11}$/', $initialPhoneForValidationLogic);
        $currentSellerCpf = $seller['cpf'] ?? $user['cpf'] ?? $cpf ?? '';
    @endphp
    <input type="hidden" id="initial_phone_number" value="{{ $initialPhoneForValidationLogic }}" name="initial_phone_number">
    <div class="col-md-6">
        @include('components.inputs.phone_number_with_validation', [
            'name' => 'phone_number_1',
            'id' => 'phone_number_1',
            'label' => 'Telefone/Celular 1',
            'class' => 'phone-input',
            'value' => old('phone_number_1') ?? ($seller['phone_number_1'] ?? $user['phone_number_1'] ?? ''),
            'placeholder' => 'Digite o número do telefone do vendedor',
            'required' => true,
            'currentClientCpf' => $currentSellerCpf,
            'validationFieldName' => 'phone_validated',
            'initialValidated' => ($isInitialPhoneActuallyValid && $initialPhoneValidated) ? '1' : '0'
        ])
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'phone_number_2',
                'label' => 'Telefone/Celular 2',
                'value' => old('phone_number_2') ?? ($seller['phone_number_2'] ?? $user['phone_number_2'] ?? ''),
                'placeholder' => 'Digite o número do telefone do vendedor',
                'required' => false,
                'class' => 'phone-input',
                'withErrorText' => true
            ])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Login</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', [
                'name' => 'email',
                'type' => 'email',
                'label' => 'E-mail',
                'value' => old('email') ?? ($seller['email'] ?? $user['email'] ?? ''),
                'placeholder' => 'Digite o e-mail do vendedor',
                'withErrorText' => true,
                'required' => true
            ])
        </div>
    </div>
    @if($seller['id'] ?? false)
        <div class="col-md-12">
            <div class="form-group">
                <input type="checkbox" id="reset_password" name="reset_password">
                <label for="reset_password">Resetar a senha do vendedor</label>
            </div>
        </div>
    @endif

    <div class="col-md-12">
        <p class="form-title">
            Dados de Comissão <span class="text-danger">*</span>
        </p>
        <div class="form-group">
           @php
                $isSeller = ApiUser::isSeller();
                $max = 100;
                if($isSeller) {
                  $registeredBy = ApiUser::get()['registered_by'];

                  $max = 100;
                  if($isSeller && !is_null($registeredBy)) {
                      $max = $registeredBy['percentage_per_sale'];
                  } else if($isSeller && is_null($registeredBy)) {
                      $max = ApiUser::get()['percentage_per_sale'];
                  } else {
                      $max = 100;
                  }
                }
           @endphp
           @include('components.inputs.number', [
               'name' => 'percentage_per_sale',
               'label' => 'Porcentagem Por Venda',
               'value' => ($seller['percentage_per_sale'] ?? old('percentage_per_sale', 0)),
               'withErrorText' => true,
               'step' => '0.1',
               'max' => $max,
               'oninput' => "if(parseFloat(this.value) > parseFloat(this.max)){ this.value = this.max; }",
               'showMaxLegend' => true
           ])
            @if(!ApiUser::isSeller() || is_null(ApiUser::get()['registered_by']))
                <div class="mt-4">
                    @include('components.inputs.checkbox', ['name' => 'register_sellers', 'label' => 'Permissão Para Cadastrar Vendedores', 'value' => $seller['register_sellers'] ?? 0, 'withErrorText' => true, 'required' => false])
                </div>
            @endif
        </div>
    </div>
</div>

@include('components.loading', ['message' => 'Aguardando confirmação de email'])
<input type="hidden" name="email_validated" id="email_validated" value="0">
@include('components.modal.modal_confirm_email', ['modalId' => 'modal-confirm-seller-email'])

@push('js')
    <script type="text/javascript"
            src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
    <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>

    <script type="text/javascript">
        // =======================================
        // VARIÁVEIS GLOBAIS
        // =======================================
        
        // CPF do vendedor atual - disponível globalmente para o modal WhatsApp
        window.currentClientCpf = "{{ ($seller['cpf'] ?? $user['cpf'] ?? $cpf ?? '') }}".replace(/\D/g, '');

        $("#cep").focusout(function () {
            $.ajax({
                url: 'https://viacep.com.br/ws/' + $(this).val().replaceAll('-', '') + '/json/',
                dataType: 'json',
                success: function (response) {
                    $("#complement").val(response.complemento);
                    $("#street").val(response.logradouro);
                    $("#neighborhood").val(response.bairro);
                    $("#city").val(response.localidade);
                    $("#state").val(response.uf);
                    $("#number").focus();
                }
            });
        });

        $(document).ready(function() {
            applyMasks();
            
            // Listener para sucesso da validação do componente de telefone
            $(document).on('phoneValidation:success', function(event, data) { 
                $('#initial_phone_number').val(data.phoneNumber);
            });
        });

        var counter_phone2 = {value: 0};

        function applyMasks() {
            $("#cpf").mask('000.000.000-00');
            $("#cep").mask('00000-000');
            // phone_number_1 agora é gerenciado pelo componente de validação
            $("#phone_number_2").mask(getMask('phone_number_2')).keyup(function (event) {
                checkPhoneMask(event.target, event.originalEvent.key, counter_phone2);
            });
        }

        function checkCPF() {
            var cpfInput = document.getElementById('cpf');
            var cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');

            var isValid = cpfIsValid(cpf);

            if (isValid) {
                cpfInput.classList.remove("is-invalid")
                cpfInput.classList.add("is-valid")
            } else {
                cpfInput.classList.add("is-invalid")
            }
        }

        function getAjaxParams(sellerId = null) {
            if (sellerId != null) {
                return {
                    url: "<?= env('API_URL') ?>/api/sellers/validate/update/" + sellerId,
                    method: 'post',
                };
            }

            return {
                url: "<?= env('API_URL') ?>/api/sellers/validate/store",
                method: 'post',
            };
        }

        function validateStoreUpdate(formId, sellerId = null, emailChange = false, onSuccessCallback = null) {
            // Verifica se o telefone está validado antes de prosseguir
            const isPhoneValidated = $('#phone_validated').val() === '1';
            
            if (!isPhoneValidated) {
                alert('Por favor valide o telefone antes de enviar.');
                $('#phone_number_1').focus();
                return;
            }

            const form = document.getElementById(formId);
            const formData = new FormData(form);

            formData.forEach(function (value, key) {
                if (['cpf', 'cep', 'phone_number_1', 'phone_number_2'].includes(key)) {
                    formData.set(key, value.replaceAll('.', '').replaceAll('-', '').replaceAll('(', '').replaceAll(')', '').replaceAll(' ', ''));
                }
                if (key == '_method') {
                    formData.delete(key);
                }
            });

            $('#loading').modal('show');
            const ajaxData = getAjaxParams(sellerId);

            let email = $('#email').val();
            $.ajax({
                url: ajaxData.url,
                type: ajaxData.method,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {
                    $('#loading').modal('hide');
                    removeErrorMessages();
                    if(onSuccessCallback) {
                        onSuccessCallback();
                    }
                },
                error: function (response, textStatus, msg) {
                    $('#loading').modal('hide');
                    if (response.status == 422) {
                        const errors = response.responseJSON.errors;
                        removeErrorMessages();
                        removeIsInvalidClass();

                        Object.entries(errors).forEach(([field, error]) => {
                            const id = 'error-' + field;
                            document.getElementById(id).innerHTML = error;
                            errorsIdToRemove.push(id);
                        })

                        Object.keys(errors).forEach((field) => {
                            document.getElementById(field).classList.add('is-invalid')
                            removeIsInvalidClassId.push(field)
                        })
                    }
                    alert('Preencha todos os dados corretamente.');
                }
            });
        }

        function confirmSellerEmail(formId) {
            // Verifica se o telefone está validado antes de prosseguir
            const isPhoneValidated = $('#phone_validated').val() === '1';
            
            if (!isPhoneValidated) {
                alert('Por favor valide o telefone antes de enviar.');
                $('#phone_number_1').focus();
                return;
            }

            const email = document.getElementById('email').value;
            const name = document.getElementById('name').value;
            const form = document.getElementById(formId);

            if (form.reportValidity() && email) {
                const formData = new FormData();
                formData.append('email', email);
                formData.append('name', name);

                $('#loading').modal('show');

                $.ajax({
                    url: "<?= route('send.email.seller.confirm') ?>",
                    type: 'post',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (response) {
                        const code = response.code;
                        const modal = document.getElementById('modal-confirm-seller-email');
                        modal.dataset.code = code;
                        modal.dataset.formId = formId;
                        modal.dataset.email = email;
                        modal.dataset.name = name;

                        $('#loading').modal('hide');
                        $("#modal-confirm-seller-email").modal('toggle');
                    },
                    error: function (response, textStatus, msg) {
                        $('#loading').modal('hide');
                        alert('Falha ao enviar email de confirmação!');
                    }
                });
            }
        }

        setInterval(checkCPF, 1000);
    </script>
@endpush
