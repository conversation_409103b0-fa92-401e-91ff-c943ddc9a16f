@php use App\Helpers\StringMask; @endphp
<div class="modal fade" id="{{ $modalId }}" tabindex="-1" role="dialog" aria-labelledby="modalVisualizarVendedor"
     aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Dados do Vendedor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="text-align: left">
                <div class="form-row">
                    <div class="col-md-12">
                        <h6 class="style_campo_titulo">Dados pessoais</h6>
                    </div>
                    <div class="col-md-12">
                        <label for="">Nome completo</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ $seller['name'] }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">Gênero</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::gender($seller['gender']) }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">Data de nascimento</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::brDate($seller['birth_date']) }}" disabled>
                    </div>
                    <div class="col-md-4" style="margin-top: 10px">
                        <label for="">CPF</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::cpf($seller['cpf']) }}" disabled>
                    </div>

                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Contato</h6>
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Email</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ $seller['email']?? 'Não informado' }}" disabled>
                    </div>
                    <div class="col-md-{{ isset($seller['phone_number_2']) ? 3 : 6 }}" style="margin-top: 10px">
                        <label for="">Telefone 1</label>
                        <input type="text" class="form-control style_campo_estatico"
                               value="{{ StringMask::phone($seller['phone_number_1']) }}" disabled>
                    </div>
                    @if(isset($seller['phone_number_2']))
                        <div class="col-md-3" style="margin-top: 10px">
                            <label for="">Telefone 2</label>
                            <input type="text" class="form-control style_campo_estatico"
                                   value="{{ StringMask::phone($seller['phone_number_2']) }}" disabled>
                        </div>
                    @endif
                    <div class="col-md-12" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Endereço</h6>
                    </div>
                    <div class="col-md-12">
                        <label for="">CEP</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ StringMask::cep($seller['address']['cep']) }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Estado</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['address']['state'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Cidade</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['address']['city'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Bairro</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['address']['neighborhood'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Rua</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['address']['street'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Número</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['address']['number'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Complemento</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['address']['complement'] }}">
                    </div>
                    <div class="col-md-6" style="margin-top: 10px">
                        <label for="">Complemento</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['address']['complement'] }}">
                    </div>
                    <span class="col-md-12"></span>
                    <div class="@if(!isset($fromSeller)) col-md-6 @else col-md-12 @endif" style="margin-top: 20px">
                        <h6 class="style_campo_titulo">Dados de Comissão</h6>
                    </div>
                    @if(!isset($fromSeller))
                        <div class="col-md-6" style="margin-top: 20px">
                            <h6 class="style_campo_titulo">Dados de Cadastro</h6>
                        </div>
                    @endif
                    <div class="col-md-6">
                        <label for="">Porcentagem Por Venda</label>
                        <input type="text" class="form-control style_campo_estatico" disabled
                               value="{{ $seller['percentage_per_sale'] }}%">
                    </div>
                    @if(!isset($fromSeller))
                        <div class="col-md-6">
                            <label for="">Vendedor Principal</label>
                            <input type="text" class="form-control style_campo_estatico" disabled
                                   value="{{ $seller['registered_by'] != null ? $seller['registered_by']['name'] : '-' }}">
                        </div>
                    @endif
                    @if(!ApiUser::isSeller())
                      <div class="col-md-6" style="margin-top: 10px">
                          <input type="checkbox" class="style_campo_estatico" disabled
                                  @if($seller['register_sellers']) checked @endif>
                          <label for="">Permissão Para Cadastrar Vendedores</label>
                      </div>
                    @endif
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" style="width:30%">Fechar</button>
            </div>
        </div>
    </div>
</div>
