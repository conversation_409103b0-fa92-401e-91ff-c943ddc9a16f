@extends('layouts.app', ['activePage' => 'sellers'])

@section('content')
<div class="container">
    <div class="row justify-content-center">

        <div class="col-md-8" style="margin-bottom: 2rem;">
            <div class="card card_conteudo">
                <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                    <div class="row">
                        <div class="col-9 d-flex justify-content-start align-items-center" style="margin-top: 9px; margin-bottom:6px">
                            @if(isset($fromSeller) && $fromSeller)
                                <a href="{{route('sellers.seller.index')}}" class="mx-2" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw "></i>
                                </a>
                            @elseif(isset($redirectTo))
                                <a href="{{route($redirectTo)}}" class="mx-2" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw"></i>
                                </a>
                            @else
                                <a href="{{route('sellers.index')}}" class="mx-2" id="back-button" style="cursor: pointer">
                                    <i class="fas fa-chevron-left fa-fw"></i>
                                </a>
                            @endif
                            <h5 class="card-title mb-0" style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">Atualizar vendedor</h5>
                        </div>
                        <div class="col-3">
                            <span style="color:red">* Campos obrigatórios</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{isset($fromSeller) && $fromSeller? route('sellers.seller.update',$seller['id'] ) : route('sellers.update', $seller['id']) }}" id="form-update-seller" method="POST" onsubmit="event.preventDefault();">
                        @csrf
                        @method('put')
                        @include('sellers.form', ['seller' => $seller])
                        <div class="modal-footer">
                            <div style="padding: 0 0 10px 4px;">
                                <input type="checkbox" class="form-check-input" id="term-checkbox" name="term" required>
                                <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os dados passados de acordo com o termo em anexo <a href="{{route('data-processing-agreement', ['user_type' => 'seller'])}}" target="_blank">(clique aqui para ler o termo).</a></label>
                            </div>
                            <button id="submit-button" disabled type="button" class="btn btn-success" onclick="validateStoreUpdate('form-update-seller', {{ $seller['id'] }}, false, onValidadeStoreUpdate);">Atualizar vendedor</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @if($message = Session::get('danger'))
        <div class="alert alert-danger alert-block shadow fade show" style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
            <strong>{{$message}}</strong>
        </div>
    @endif
</div>

<script>
    function onValidadeStoreUpdate() {
        const oldEmail = "{{ $seller['email'] }}"
        const newEmail = document.getElementById('email').value;

        if (newEmail != oldEmail) {
            confirmSellerEmail('form-update-seller');
        }else{
            const form = document.getElementById('form-update-seller');
            form.submit();
        }
    }

    $("#term-checkbox").click(showHideFormButton);
    function showHideFormButton() {
        if ($("#term-checkbox").is(":checked")) {
            $("#submit-button").prop("disabled", false);
        } else {
            $("#submit-button").prop("disabled", true);
        }
    }
</script>

@endsection
