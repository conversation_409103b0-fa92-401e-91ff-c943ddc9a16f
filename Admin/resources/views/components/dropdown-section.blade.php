@props([
    'title' => '',
    'id' => 'dropdown-' . uniqid(),
    'defaultOpen' => false
])

@php
    $showClass = $defaultOpen ? 'defaultOpened' : '';
    $ariaExpanded = $defaultOpen ? 'true' : 'false';
@endphp

<style>
    .dropdown-toggle-header {
        cursor: pointer;
        user-select: none;
        padding: 10px 0;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .icon-rotate {
        transition: transform 0.3s ease;
    }

    .icon-rotate.rotate {
        transform: rotate(90deg);
    }

    .dropdown-content-body {
        overflow: hidden;
        max-height: 0;
        transition: max-height 0.4s ease;
    }

    .dropdown-content-body.defaultOpened {
        max-height: fit-content;
    }

</style>

<div class="mb-2">
    <div class="dropdown-toggle-header"
        role="button"
        aria-expanded="{{ $ariaExpanded }}"
        aria-controls="{{ $id }}"
        onclick="toggleDropdown('{{ $id }}')"
    >
        <h6 class="mb-0 style_campo_titulo">{{ $title }}</h6>
        <i id="icon-{{ $id }}" class="fas fa-chevron-right icon-rotate {{ $showClass ? 'rotate' : '' }}"></i>
    </div>

    <div id="{{ $id }}" class="dropdown-content-body {{ $showClass }}">
        <div class="pt-2 pb-3 px-1">
            {{ $slot }}
        </div>
    </div>
</div>

<script>
    function toggleDropdown(id) {
        const content = document.getElementById(id);
        const icon = document.getElementById('icon-' + id);

        if (content.classList.contains('show') || content.classList.contains('defaultOpened')) {
            content.style.maxHeight = content.scrollHeight + 'px';
            setTimeout(() => {
                content.style.maxHeight = '0px';
            }, 10);
            content.classList.remove('defaultOpened');
            content.classList.remove('show');
            icon.classList.remove('rotate');
        } else {
            content.classList.add('show');
            content.style.maxHeight = content.scrollHeight + 'px';
            icon.classList.add('rotate');
        }
    }
</script>
