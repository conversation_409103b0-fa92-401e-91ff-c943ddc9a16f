<?php 
$pages_count = intval(ceil($pagination_data['total']/$pagination_data['per_page']));
$current_page = $pagination_data['current_page'];
$last_page = $pagination_data['last_page'];
?>
<nav aria-label="Page navigation">
  <ul class="pagination">
    <li class="page-item @if($current_page == 1) disabled @endif">
        <a class="page-link" href="{{ $base_url }}?page={{ $current_page-1 > 1 ? $current_page-1 : 1 }}@if(isset($append)){{ '&'.$append }}@endif">
            <span aria-hidden="true">&laquo;</span>
        </a>
    </li>
    @for ($count = 1; $count <= $pages_count; $count++)
        <li class="page-item @if($current_page == $count) active @endif"><a class="page-link" href="{{ $base_url }}?page={{ $count }}@if(isset($append)){{ '&'.$append }}@endif">{{ $count }}</a></li>    
    @endfor
    <li class="page-item @if($current_page == $pages_count) disabled @endif">
        <a class="page-link" href="{{ $base_url }}?page={{ $current_page+1 <= $last_page ? $current_page+1 : $last_page }}@if(isset($append)){{ '&'.$append }}@endif">
            <span aria-hidden="true">&raquo;</span>
        </a>
    </li>
  </ul>
</nav>