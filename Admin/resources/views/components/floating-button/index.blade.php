@php
    use App\Helpers\SystemHelper; 
    use App\Helpers\ApiUser; 

    $shouldOpenNewTab = isset(SystemHelper::get()['FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB']) && SystemHelper::get()['FLOATING_BUTTON_SHOULD_OPEN_NEW_TAB'];
    $hasLink = isset(SystemHelper::get()['FLOATING_BUTTON_LINK']);
@endphp

<style>
    .floating-button-container {
        position: fixed;
        bottom: 24px;
        right: 10px;
    }

    .floating-button {
        background-color: {{SystemHelper::get()['MAIN_COLOR']}};
        width: 3.5rem;
        height: 3.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.8rem;
        border-top-right-radius: 0.2rem;
        transition: all 0.2s;
        color: #fff;
        font-size: 1.2em;
        font-weight: bold;
        border: 1px solid #f0f3f0;
    }

    .floating-button:hover {
        filter: brightness(0.8);
        cursor: pointer;
    }

    .floating-button-icon {
        width: 1.2em;
        height: 1.2em;
    }
</style>

<div class="floating-button-container">
    <a class="floating-button" id="floating-button" 
        @if ($shouldOpenNewTab && $hasLink)
            href="{{SystemHelper::get()['FLOATING_BUTTON_LINK']}}"
            target="_blank" 
            rel="noopener noreferrer"
        @endif
    >
        @if (isset(SystemHelper::get()['FLOATING_BUTTON_ICON']) && SystemHelper::get()['FLOATING_BUTTON_ICON'] !== "")
            <img src="{{SystemHelper::get()['FLOATING_BUTTON_ICON']}}" class="floating-button-icon" alt="logo">
        @else
            <i class="fas fa-headset" ></i>
        @endif
    </a>
</div>

@if (!$shouldOpenNewTab)
    @include('layouts.modals.floating-button-modal')
@endif

<script>
    function openModal() {
        const modal = $('#floating-button-modal');
        modal.modal('show');

        modal.on('shown.bs.modal', function () {
            modal.trigger('focus');
        });
    }

    @if (!$shouldOpenNewTab)
        $('#floating-button').on('click', () => {
            openModal()
        })
    @endif
</script>