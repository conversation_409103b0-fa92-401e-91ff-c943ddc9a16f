@php use App\Helpers\ApiUser;use App\Helpers\EmployeePermissions; @endphp
@include('topic.modals.show')
<div class="col-md-12" id="topicsTopic">
    <label class="form-title mb-0" for="name">Categorias Vinculadas<span style="color: red">*</span></label>
    <small class="form-text text-muted" style="font-size: 0.9rem; margin-top: 0px; margin-bottom: 0.8rem">As categorias vinculadas definem a ordem em que as perguntas aparecerão no formulário</small>
    <div class="form-group mt-0">
        <hr class="my-1" style="padding-top: 1rem; padding-bottom: 0rem" id="topicsSeparator"/>
        <div class="form-group mb-0">
            <ul class="list-group d-none" id="topics_selected">
                <!-- Tópicos selecionados serão carregados aqui -->
            </ul>
        </div>
    </div>
    <label class="form-title mb-0" for="name" style="padding-bottom: 0.5rem;">Todas as Categorias</label>
    <div class="form-group mb-0 mt-0 d-flex align-items-center">
        <input type="text" id="topicSearchFormEmployee"
               class="form-control @if(ApiUser::hasPermission(EmployeePermissions::TOPICS) && ApiUser::hasPermission(EmployeePermissions::UPDATE_TOPICS)) mr-2 @endif flex-grow-1"
               placeholder="Pesquisar categoria...">
        @if(ApiUser::hasPermission(EmployeePermissions::TOPICS) && ApiUser::hasPermission(EmployeePermissions::UPDATE_TOPICS))
            <button type="button" class="btn btn-primary d-flex align-items-center"
                    onclick="openCreateTopicModal($('#default').is(':checked'))" title="Adicionar Categoria">
                <i class="fas fa-plus mr-1"></i> Adicionar
            </button>
        @endif
    </div>
    <div class="d-flex justify-content-center loadingTopics pb-1 d-none">
        <div class="spinner-border loadingTopics" role="status">
            <span class="sr-only loadingTopics">Loading...</span>
        </div>
    </div>
    <table class="table table-bordered d-none" id="topicTableFormEmployee">
        <thead>
        <tr>
            <th style="width: 1%">Selecionar</th>
            <th>Nome</th>
            <th style="width: 10%; text-align: center;">Ações</th>
        </tr>
        </thead>
        <tbody id="topicTableFormEmployeeBody">
        <!-- Tópicos serão carregados aqui -->
        </tbody>
    </table>
    <nav>
        <ul class="pagination" id="topicPaginationFormEmployee">
            <!-- Paginação será carregada aqui -->
        </ul>
    </nav>
    <div class="d-none" id="topicsInputs">
        <!-- Inputs hidden para enviar os tópicos selecionados -->
    </div>
</div>

<!-- Modal de Confirmação de Exclusão -->
<div class="modal fade" id="confirmDeleteTopicModal" tabindex="-1" role="dialog"
     aria-labelledby="confirmDeleteTopicModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteTopicModalLabel">Confirmar Exclusão</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="deleteVerificationLoading" class="text-center mb-2 d-none">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="sr-only">Verificando...</span>
                    </div>
                    <span class="ml-2">Verificando se a categoria pode ser excluída...</span>
                </div>
                <div id="deleteConfirmMessage">
                    Tem certeza que deseja excluir a categoria "<strong id="topicNameToDelete"></strong>"? Esta ação não
                    pode ser desfeita.
                </div>
                <div id="deleteErrorMessage" class="d-none"
                     style="color: #721c24; padding: 12px; margin-bottom: 10px; border: 1px solid #f5c6cb; border-radius: 4px; background-color: #f8d7da;"
                     role="alert">
                    <!-- Mensagem de erro será inserida aqui -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteTopicButton" disabled>Excluir</button>
                <!-- Botão desabilitado por padrão -->
            </div>
        </div>
    </div>
</div>

<!-- Loading para Exclusão -->
@include('components.loading', ['message' => 'Deletando...', 'id' => 'deleteTopicLoading'])

<script>
    let canUpdateTopics = {{ ApiUser::hasPermissionOrIsAdmin('topics') && ApiUser::hasPermissionOrIsAdmin('update_topics') ? 'true' : 'false' }};
    let canUpdateTopicsDefault = {{ ApiUser::hasPermissionOrIsAdmin('topics') && ApiUser::hasUserFunction('default_topics') ? 'true' : 'false' }};
    let topicShowUrlTemplate = "{{ route('topics.show', ['topic' => ':id']) }}";
    let topicEditUrlTemplate = "{{ route('topics.edit', ['topic' => ':id']) }}";
    let topicEditUrlTemplateForBusiness = "{{ route('topics.edit.default', ['topic' => ':id']) }}";
    let topicsCreateUrl = "{{ route('topics.create') }}";
    let topicDeleteUrlTemplate = "{{ route('topics.destroy', ['topic' => ':id']) }}"; // Assumindo rota resource
    let topicCheckDeletionUrlTemplate = "{{ env('API_URL') }}/api/topics/:id/check-deletion"; // Nova URL para verificação

    function verifyTopics() {
        let topics = $('#topicsInputJson').val();
        if (topics === '[]' || topicsSelectedIds.length === 0) {
            alert('Selecione ao menos uma categoria');
            return false;
        }
        return true;
    }

    function openTopicModal(topicId, isDefault = false) {
        let isEdit = canUpdateTopics || canUpdateTopicsDefault || isDefault;
        let url;
        let modalTitle;
        if (canUpdateTopics && !isDefault) {
            url = topicEditUrlTemplate.replace(':id', topicId);
            modalTitle = 'Editar Categoria';
        } else if (canUpdateTopics && isDefault && !canUpdateTopicsDefault) {
            url = topicEditUrlTemplateForBusiness.replace(':id', topicId);
            modalTitle = 'Editar Categoria Padrão para o Negócio';
        } else if (canUpdateTopics && isDefault && canUpdateTopicsDefault) {
            url = topicEditUrlTemplate.replace(':id', topicId);
            modalTitle = 'Editar Categoria Padrão';
        } else {
            url = topicShowUrlTemplate.replace(':id', topicId);
            modalTitle = 'Visualizar Categoria';
        }

        $('#showTopicModalLabel').text(modalTitle);

        $('#showTopicFrame').attr('src', url);
        $('#showTopicModal').modal('show');
    }

    function openCreateTopicModal(isDefaultForm = false) {
        $('#showTopicModalLabel').text('Cadastrar Categoria');
        let url = topicsCreateUrl;
        if (isDefaultForm) {
            url += (url.includes('?') ? '&' : '?') + 'default=true';
        }
        $('#showTopicFrame').attr('src', url);
        $('#showTopicModal').modal('show');
    }

    $(document).ready(function () {
        loadTopicsFormEmployee();
        $('#topicSearchFormEmployee').on('keyup', debounce(function () {
            loadTopicsFormEmployee();
        }, 500));

        // Carregar tópicos salvos
        loadSavedTopics();

        // Atualizar a ordem dos tópicos
        updateTopicOrder();
        if (typeof loadBusinessesFormEmployee !== "undefined") {
            loadBusinessesFormEmployee();
        }

        $('#confirmDeleteTopicButton').on('click', function () {
            const topicId = $(this).data('topic-id');
            if (topicId) {
                deleteTopic(topicId);
            }
        });

        if (!$('#default').is(':checked')) {
            selectTopicsActiveRequired();
        }
        showHideNoTopicSelectedMessage();

        // Resetar modal ao fechar
        $('#confirmDeleteTopicModal').on('hidden.bs.modal', function () {
            $('#deleteVerificationLoading').addClass('d-none');
            $('#deleteConfirmMessage').removeClass('d-none alert-danger');
            $('#deleteConfirmMessage').html(`Tem certeza que deseja excluir a categoria "<strong id="topicNameToDelete"></strong>"? Esta ação não pode ser desfeita.`);
            $('#deleteErrorMessage').addClass('d-none').text('');
            $('#confirmDeleteTopicButton').prop('disabled', true);
            $('#confirmDeleteTopicButton').removeData('topic-id');
            $('#topicNameToDelete').text('');
        });
    });


    // Tornar global para acesso em edit.blade.php
    window.topicsSelectedIds = [];
    let savedTopics = @json($topics ?? []);
    let oldTopicsIds = [];

    function loadSavedTopics() {
        savedTopics.forEach(topic => {
            window.topicsSelectedIds.push(topic.id.toString()); // Usar window.topicsSelectedIds
            addTopicsSelectedItem(topic, window.topicsSelectedIds.length); // Usar window.topicsSelectedIds
            $(`#topic_${topic.id}`).prop('checked', true);
        });
    }

    let topics_autorizados = @json(ApiUser::get()['topics'] ?? []);
    let topics_autorizados_ids = topics_autorizados.map(topic => topic.id.toString());
    let isAdminTopic = {{ ApiUser::getLoginType() == 'admin' ? 'true' : 'false' }};

    function addTopicsSelectedItem(topic, order) {
        let li = document.createElement('li');
        li.className = 'list-group list-group-item d-flex justify-content-end align-items-center';
        li.style = "background: rgba(0, 0, 0, 0.05)";
        li.id = `topic_selected_${topic.id}`;
        li.dataset.order = order;

        let description = topic.name;
        // Escapar o nome do tópico para uso seguro em atributos HTML e JavaScript
        let escapedDescription = $('<div>').text(description).html();


        let div = document.createElement('div');
        div.className = 'd-flex flex-column flex-sm-row w-100 justify-content-between align-items-start align-items-sm-center';

        let isDefaultTopic = topic.default || false;
        let canEditThisTopic = (canUpdateTopics && !isDefaultTopic) || (canUpdateTopics && isDefaultTopic && canUpdateTopicsDefault) || (canUpdateTopics && isDefaultTopic && !canUpdateTopicsDefault);
        let canViewThisTopic = !canEditThisTopic; // Se não pode editar, pode visualizar

        let actionButtonsHtml = '';
        if (canEditThisTopic) {
            // Lógica específica para determinar se é edição normal ou edição padrão para negócio
            let editTitle = 'Editar Categoria';
            if (isDefaultTopic && !canUpdateTopicsDefault) {
                editTitle = 'Editar Categoria Padrão para o Negócio';
            } else if (isDefaultTopic && canUpdateTopicsDefault) {
                editTitle = 'Editar Categoria Padrão';
            }

            // Não precisa mostrar mais o botão editar nas categorias vinculadas
            /*actionButtonsHtml = `
                <button type="button" onclick="openTopicModal(${topic.id}, ${isDefaultTopic})" class="btn btn-primary btn-sm mr-1" title="${editTitle}">
                    <img src="{{url('icon/icon_editar.png')}}" width="18px"/>
                </button>
            `;*/
        } else { // Se não pode editar, mostra botão de visualizar
            actionButtonsHtml = `
                <button type="button" onclick="openTopicModal(${topic.id}, ${isDefaultTopic})" class="btn btn-secondary btn-sm mr-1" title="Visualizar Categoria">
                    <img src="{{url('icon/icon_visualizar.svg')}}" width="20px"/>
                </button>
            `;
        }


        div.innerHTML = `
          <div class="d-flex align-items-center w-100 w-sm-auto">
            <input type="checkbox" class="mr-2" id="topic_selected_checkbox_${topic.id}" value="${topic.id}"
                 onchange="if (!this.checked) { removeTopic(${topic.id}); }"
                 ${topic.required && !$('#default').is(':checked') ? 'disabled' : ''} checked>
            <p class="m-0 text-truncate" title="${escapedDescription}">${escapedDescription}</p>
          </div>
          <div class="d-flex align-items-center mt-2 mt-sm-0 ml-sm-auto">
            <button type="button" class="btn btn-light btn-sm btn-up mr-1" onclick="moveUp(${topic.id})" title="Mover para cima">
              <i class="fas fa-arrow-up"></i>
            </button>
            <button type="button" class="btn btn-light btn-sm btn-down mr-1" onclick="moveDown(${topic.id})" title="Mover para baixo">
              <i class="fas fa-arrow-down"></i>
            </button>
            ${actionButtonsHtml}
          </div>
        `;

        li.appendChild(div);

        $('#topics_selected').append(li);

        showHideNoTopicSelectedMessage();
        updateTopicOrder();
    }

    function topicSelected(checkbox) {
        const topicId = $(checkbox).val();
        if ($(checkbox).is(':checked')) {
            let order = window.topicsSelectedIds.length + 1;
            window.topicsSelectedIds.push(topicId);

            let topicName = $(`#topic_name_${topicId}`).text();
            if (!topicName) {
                topicName = `Categoria ${topicId}`;
                console.warn("Nome da categoria não encontrada localmente ao selecionar. Busque via API se necessário.");
            }

            addTopicsSelectedItem({
                id: topicId,
                name: topicName,
                default: false,
                required: $(checkbox).prop('disabled')
            }, order);
            if (typeof loadBusinessesFormEmployee !== "undefined") {
                loadBusinessesFormEmployee();
            }
        } else {
            removeTopic(topicId);
        }
        showHideNoTopicSelectedMessage();
    }

    function moveUp(topicId) {
        let li = $(`#topic_selected_${topicId}`);
        let prev = li.prev();

        if (prev.length > 0) {
            li.insertBefore(prev);
            updateTopicOrder();
        }
    }

    function moveDown(topicId) {
        let li = $(`#topic_selected_${topicId}`);
        let next = li.next();

        if (next.length > 0) {
            li.insertAfter(next);
            updateTopicOrder();
        }
    }

    function removeTopic(topicId) {
        $(`#topic_selected_${topicId}`).remove();
        $(`#topic_${topicId}`).prop('checked', false);
        topicsSelectedIds = topicsSelectedIds.filter(id => id !== topicId.toString());
        updateTopicOrder();
        showHideNoTopicSelectedMessage();
        if (typeof loadBusinessesFormEmployee !== "undefined") {
            loadBusinessesFormEmployee();
        }
    }

    function updateTopicOrder() {
        let topicsArray = [];
        $('#topics_selected li').each(function (index) {
            let topicId = $(this).attr('id').split('_')[2];
            let order = index + 1;
            $(this).attr('data-order', order);

            topicsArray.push({id: topicId, order: order});

            $(this).find('.btn-up').prop('disabled', false);
            $(this).find('.btn-down').prop('disabled', false);
        });

        $('#topics_selected li:first-child').find('.btn-up').prop('disabled', true);

        $('#topics_selected li:last-child').find('.btn-down').prop('disabled', true);

        let jsonString = JSON.stringify(topicsArray);
        if ($('#topicsInputJson').length === 0) {
            $('#topicsInputs').append(`<input type="hidden" name="topicsJson" id="topicsInputJson" value='${jsonString}'>`);
        } else {
            $('#topicsInputJson').val(jsonString);
        }
    }

    function showHideNoTopicSelectedMessage() {
        if ($('#topics_selected').children().length === 0) {
            $('#topicsSelectedLabel').addClass('d-none');
            $('#topicsSeparator').addClass('d-none');
            $('#topics_selected').addClass('d-none');
        } else {
            $('#topicsSelectedLabel').removeClass('d-none');
            $('#topicsSeparator').removeClass('d-none');
            $('#topics_selected').removeClass('d-none');
        }
    }


    function selectTopicsActiveRequired() {
        let url = "<?= env('API_URL') ?>/api/topics/getActiveRequiredTopics";

        $.ajax({
            url: url,
            type: 'get',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
            },
            success: function (response) {
                if (response.topics.length > 0) {
                    response.topics.forEach(topic => {
                        if (!window.topicsSelectedIds.includes(topic.id.toString())) { // Usar window.topicsSelectedIds
                            window.topicsSelectedIds.push(topic.id.toString()); // Usar window.topicsSelectedIds
                            addTopicsSelectedItem(topic, window.topicsSelectedIds.length); // Usar window.topicsSelectedIds
                            $(`#topic_${topic.id}`).prop('checked', true);
                        }
                    });
                    loadBusinessesFormEmployee();
                }
                $('.loadingTopics').addClass('d-none');
                showHideNoTopicSelectedMessage();
            },
            error: function (response, textStatus, msg) {
                $('.loadingTopics').addClass('d-none');
                showHideNoTopicSelectedMessage();
            }
        });
    }


    function loadTopicsFormEmployee(page = 1) {
        const searchQuery = $('#topicSearchFormEmployee').val();
        let isDefaultForm = false;
        if ($('#default') !== undefined && $('#default').is(':checked')) {
            isDefaultForm = true;
        }

        let url;
        let release_all_bussiness = {{ ApiUser::get()['authorizedFunction']['release_all_business'] ?? false ? 'true' : 'false' }};
        if(release_all_bussiness){
          if (searchQuery.length === 0) {
              url = "<?= env('API_URL') ?>/api/topics/getAllTopics?page=" + page + "&per_page=5";
          } else {
              url = "<?= env('API_URL') ?>/api/topics/getAllTopics?search=" + searchQuery + "&page=" + page + "&per_page=5";
          }
        }else{
          if (searchQuery.length === 0) {
              url = "<?= env('API_URL') ?>/api/topics/getActiveTopics?page=" + page + "&per_page=5";
          } else {
              url = "<?= env('API_URL') ?>/api/topics/getActiveTopics?search=" + searchQuery + "&page=" + page + "&per_page=5";
          }
        }

        let idsBusiness = [];
        if (typeof businessesSelectedIds !== "undefined") {
            idsBusiness = businessesSelectedIds;
        }

        $.ajax({
            url: url,
            type: 'get',
            data: {
                search: searchQuery,
                page: page,
                per_page: 5,
                is_default_form: isDefaultForm,
                businesses_selected_ids: idsBusiness,
                verify_topics: true,
                topics_selected_ids: window.topicsSelectedIds // Usar window.topicsSelectedIds
            },
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#topicTableFormEmployee').addClass('d-none');
                $('#topicPaginationFormEmployee').addClass('d-none');
                $('.loadingTopics').removeClass('d-none');
            },
            success: function (response) {
                $('#topicTableFormEmployeeBody').empty();
                if (response.topics.data.length > 0) {
                    $.each(response.topics.data, function (index, topic) {
                        let isSelected = window.topicsSelectedIds.includes(topic.id.toString()); // Usar window.topicsSelectedIds
                        let escapedTopicName = $('<div>').text(topic.name).html();

                        let actionsHtml = '<td class="text-center"><div class="btn-group">';
                        let canEditThisTopic = (canUpdateTopics && !topic.default) || (canUpdateTopics && topic.default && canUpdateTopicsDefault) || (canUpdateTopics && topic.default && !canUpdateTopicsDefault);
                        if (canEditThisTopic) {
                            let editTitle = topic.default ? (canUpdateTopicsDefault ? 'Editar Categoria Padrão' : 'Editar Categoria Padrão para o Negócio') : 'Editar Categoria';
                            actionsHtml += `
                                <button type="button" onclick="openTopicModal('${topic.id}', ${topic.default})" class="btn btn-primary btn-sm" style="margin-right: 5px" title="${editTitle}">
                                    <img src="{{url('icon/icon_editar.png')}}" width="18px"/>
                                </button>
                                ${(canUpdateTopicsDefault && topic.default) || (canUpdateTopics && !topic.default) ? `
                                    <button type="button" onclick="confirmDeleteTopic('${topic.id}', '${escapedTopicName}')" class="btn btn-danger btn-sm" style="margin-right: 5px" title="Excluir Categoria Permanentemente">
                                        <img src="{{url('icon/icon_lixeira.png')}}" width="18px"/>
                                    </button>
                                ` : ''}
                            `;
                        } else {
                            actionsHtml += `
                                <button type="button" onclick="openTopicModal('${topic.id}', ${topic.default})" class="btn btn-secondary btn-sm" style="margin-right: 5px" title="Visualizar">
                                    <img src="{{url('icon/icon_visualizar.svg')}}" width="20px"/>
                                </button>
                            `;
                        }
                        actionsHtml += '</div></td>';
                        if(topic.active == 0 && topic.default == 1 && release_all_bussiness){
                          $('#topicTableFormEmployeeBody').append(`
                        <tr id="topic_row_${topic.id}">
                            <td style="width: 1%" class="text-center">
                                <input type="checkbox" id="topic_${topic.id}" name="topics[]" value="${topic.id}" onchange="topicSelected(this);" ${isSelected ? 'checked' : ''} disabled>
                            </td>
                            <td><p id="topic_name_${topic.id}" class="m-0">${escapedTopicName}</p> ${topic.required && !$('#default').is(':checked') ? '<span class="badge badge-danger">Obrigatório</span>' : ''} ${topic.default ? '<span class="badge badge-secondary">Padrão</span>' : ''} ${topic.active == 0 ? '<span class="badge badge-warning">Inativo</span>' : ''}
                            </td>
                            ${actionsHtml}
                        </tr>
                    `);
                        }else{
                          $('#topicTableFormEmployeeBody').append(`
                        <tr id="topic_row_${topic.id}">
                            <td style="width: 1%" class="text-center">
                                <input type="checkbox" id="topic_${topic.id}" name="topics[]" value="${topic.id}" onchange="topicSelected(this);" ${topic.required && !$('#default').is(':checked') ? 'disabled' : ''} ${isSelected ? 'checked' : ''}>
                            </td>
                            <td><p id="topic_name_${topic.id}" class="m-0">${escapedTopicName}</p> ${topic.required && !$('#default').is(':checked') ? '<span class="badge badge-danger">Obrigatório</span>' : ''} ${topic.default ? '<span class="badge badge-secondary">Padrão</span>' : ''}
                            </td>
                            ${actionsHtml}
                        </tr>
                    `);
                        }

                        
                        $(`#topic_${topic.id}`).prop('checked', isSelected);
                    });
                } else {
                    $('#topicTableFormEmployeeBody').append('<tr><td colspan="3">Nenhuma categoria encontrada</td></tr>'); {{-- Colspan ajustado para 3 --}}
                }

                // Paginação
                $('#topicPaginationFormEmployee').empty();
                if (response.topics.last_page > 1) {
                    for (let i = 1; i <= response.topics.last_page; i++) {
                        $('#topicPaginationFormEmployee').append(`
                        <li class="page-item ${i === response.topics.current_page ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `);
                    }
                }

                $('#topicPaginationFormEmployee a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadTopicsFormEmployee(page);
                });

                $('#topicTableFormEmployee').removeClass('d-none');
                $('#topicPaginationFormEmployee').removeClass('d-none');
                $('.loadingTopics').addClass('d-none');
                $(document).trigger('topicsLoaded');
            },
            error: function (response, textStatus, msg) {
                $('.loadingTopics').addClass('d-none');
                $(document).trigger('topicsLoaded');
            }
        });
    }

    function confirmDeleteTopic(topicId, topicName) {
        // Decodificar o nome caso tenha sido escapado
        let decodedTopicName = $('<div>').html(topicName).text();
        let url = topicCheckDeletionUrlTemplate.replace(':id', topicId);

        // Mostrar loading da verificação e esconder mensagem de confirmação
        $('#deleteVerificationLoading').removeClass('d-none');
        $('#deleteConfirmMessage').addClass('d-none'); // Esconder msg padrão inicialmente
        $('#deleteErrorMessage').addClass('d-none'); // Esconder msg erro inicialmente
        $('#confirmDeleteTopicButton').prop('disabled', true); // Desabilitar botão enquanto verifica
        $('#confirmDeleteTopicModal').modal('show'); // Mostra o modal com o loading

        $.ajax({
            url: url,
            type: 'GET',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
            },
            success: function (response) {
                $('#deleteVerificationLoading').addClass('d-none'); // Esconder loading

                if (response.can_delete) {
                    // Se pode deletar, mostrar mensagem de confirmação e habilitar botão
                    $('#topicNameToDelete').text(decodedTopicName);
                    $('#deleteConfirmMessage').removeClass('d-none');
                    $('#deleteErrorMessage').addClass('d-none');
                    $('#confirmDeleteTopicButton').data('topic-id', topicId);
                    $('#confirmDeleteTopicButton').prop('disabled', false);
                } else {
                    // Se não pode deletar, mostrar mensagem de erro no modal e manter botão desabilitado
                    $('#deleteConfirmMessage').addClass('d-none');
                    $('#deleteErrorMessage').text(response.message || 'Está categoria não pode ser excluída pois está vinculada a formulários ativos.');
                    $('#deleteErrorMessage').removeClass('d-none');
                    $('#confirmDeleteTopicButton').prop('disabled', true);
                }
            },
            error: function (xhr, textStatus, errorThrown) {
                $('#deleteVerificationLoading').addClass('d-none');
                $('#deleteConfirmMessage').addClass('d-none');

                let errorMessage = 'Erro ao verificar a possibilidade de exclusão.';
                if (xhr.responseJSON && xhr.responseJSON.msg) {
                    errorMessage += ' Detalhes: ' + xhr.responseJSON.msg;
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage += ' Detalhes: ' + xhr.responseJSON.message;
                }

                $('#deleteErrorMessage').text(errorMessage);
                $('#deleteErrorMessage').removeClass('d-none');
                $('#confirmDeleteTopicButton').prop('disabled', true);
            }
        });
    }

    // Função para executar a exclusão via AJAX
    function deleteTopic(topicId) {
        $('#confirmDeleteTopicModal').modal('hide');
        $('#deleteTopicLoading').modal('show');

        let url = topicDeleteUrlTemplate.replace(':id', topicId);

        $.ajax({
            url: url,
            type: 'DELETE',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                xhr.setRequestHeader('X-CSRF-TOKEN', $('meta[name="csrf-token"]').attr('content'));
            },
            success: function (response) {
                $('#deleteTopicLoading').modal('hide');
                Swal.fire({
                    toast: true,
                    position: 'top-end',
                    icon: 'success',
                    title: 'Categoria excluída com sucesso!',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true
                });

                removeTopic(topicId);
                $(`#topic_row_${topicId}`).remove();

                loadTopicsFormEmployee($('#topicPaginationFormEmployee .active a').data('page') || 1);

            },
            error: function (xhr, textStatus, errorThrown) {
                $('#deleteTopicLoading').modal('hide');
                let errorMessage = 'Erro ao excluir a categoria.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage += ' Detalhes: ' + xhr.responseJSON.message;
                }
                Swal.fire({
                    toast: true,
                    position: 'top-end',
                    icon: 'error',
                    title: errorMessage,
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true
                });
            }
        });
    }


    $(document).ready(function () {
        if (!$('#default').is(':checked')) {
            selectTopicsActiveRequired();
        }
        showHideNoTopicSelectedMessage(); // Chama no início para garantir estado correto
    });

    function handleTopicUpdate(topicData) {
        if (!topicData || !topicData.id || !topicData.name) {
            if (typeof loadTopicsFormEmployee === 'function') {
                loadTopicsFormEmployee();
            }
            return;
        }

        const topicIdStr = topicData.id.toString();

        if (!topicsSelectedIds.includes(topicIdStr)) {
            let topicObjectForList = {
                id: topicData.id,
                name: topicData.name,
                default: false,
                required: false
            };
            // Adiciona visualmente e ao array de controle
            addTopicsSelectedItem(topicObjectForList, topicsSelectedIds.length + 1);
            topicsSelectedIds.push(topicIdStr);
            updateTopicOrder(); // Atualiza o input hidden
        }

        // 2. Marca o checkbox na tabela (se existir)
        const checkbox = $(`#topic_${topicData.id}`);
        if (checkbox.length) {
            checkbox.prop('checked', true);
            // Garante que a função `topicSelected` não seja chamada duas vezes
            // A adição manual acima já cuida da lógica de seleção.
        } else {
            // 3. Se o checkbox não existe na tabela atual, recarrega a tabela
            // para potencialmente mostrar o novo item e garantir que esteja selecionado.
            // Isso é importante caso o item tenha sido criado e não estivesse na página atual.
            if (typeof loadTopicsFormEmployee === 'function') {
                // Recarrega a página atual da tabela.
                // A lógica de `loadTopicsFormEmployee` já deve marcar os checkboxes
                // com base no array `topicsSelectedIds` atualizado.
                let currentPage = $('#topicPaginationFormEmployee .active a').data('page') || 1;
                loadTopicsFormEmployee(currentPage);
            }
        }

        // 4. Garante que a mensagem "nenhum selecionado" esteja oculta
        showHideNoTopicSelectedMessage();
    }

    // Adicionar chamada a markTopicAsUpdated para compatibilidade,
    // caso outra parte do sistema use isso. Ela pode apenas chamar handleTopicUpdate
    // mas sem dados, forçando um reload.
    function markTopicAsUpdated() {
        if (typeof loadTopicsFormEmployee === 'function') {
            loadTopicsFormEmployee();
        }
    }

</script>
