<?php
    $input_type = $type ?? 'text';
    $is_required = isset($required) ? $required : 'required';
    $is_readonly = isset($readonly) ? 'readonly' : '';
    $pholder = $placeholder ?? '';
    $maxLength = $maxLength ?? null;
    $minLength = $minLength ?? null;
?>

<label for="{{ $name }}">
    {{ $label }} 
    <span id="{{ $id ?? $name }}_maxLength" class="text-muted">
        @if($maxLength) 
            ({{$maxLength}} caracteres) 
        @endif
    </span>
    @if($is_required)
        <b class='text-danger'>*</b>
    @endif
</label>
<textarea class="form-control @error($name) is-invalid @enderror" name="{{ $name }}" id="{{ $id ?? $name }}" cols="30" rows="{{ $rows ?? 2 }}"
          {{ $is_required }} {{ $is_readonly }} placeholder="{{ $pholder }}" @if(isset($onchange)) onchange="{{ $onchange }}" @endif maxlength="{{$maxLength}}" minlength="{{$minLength}}">{{ $value ?? old($name) }}</textarea>
@error($name)
<small class="text-danger">{{ $message }}</small>
@enderror
@if(isset($withErrorText) && $withErrorText)
<small id="error-{{ $id ?? $name }}" class="text-danger"></small>
@endif