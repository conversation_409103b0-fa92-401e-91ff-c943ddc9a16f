<?php
    $input_type = $type ?? 'date';
    $is_required = isset($required) && $required ? 'required' : '';
    $is_readonly = isset($readonly) ? 'readonly' : '';
    $pholder = $placeholder ?? '';
?>

<label for="{{ $name }}">{{ $label }} @if($is_required)<b class='text-danger'>*</b>@endif</label>
<input type="{{ $input_type }}" class="form-control @error($name) is-invalid @enderror" name="{{ $name }}" id="{{ $id ?? $name }}" value="{{ $value ?? old($name) }}"
       {{ $is_required }} {{ $is_readonly }} placeholder="{{ $pholder }}"
       @if(isset($disabled) && $disabled) disabled @endif >
@error($name)
<small class="text-danger">{{ $message }}</small>
@enderror
@if(isset($withErrorText) && $withErrorText)
<small id="error-{{ $id ?? $name }}" class="text-danger"></small>
@endif
