<?php
    $is_required = isset($required) ? $required : 'required';
    $is_readonly = isset($readonly) ? 'readonly' : '';
    $pholder = $placeholder ?? '';
    $class = $class ?? '';
?>
<div class="row">
    <div class="col">
        <div class="form-check">
            <input type="checkbox" class="form-check-input @error($name) is-invalid @enderror {{ $class }}" name="{{ $name }}" id="{{ $id ?? $name }}" value="1"
            {{ $is_required }} {{ $is_readonly }} @if($value ?? old($name)) checked @endif onchange="{{ $onchange ?? '' }}">
            <label class="form-check-label" for="{{ $name }}">{{ $label }} @if($is_required)<b class='text-danger'>*</b>@endif</label>
        </div>
        @error($name)
        <small class="text-danger">{{ $message }}</small>
        @enderror
        @if(isset($withErrorText) && $withErrorText)
        <small id="error-{{ $id ?? $name }}" class="text-danger"></small>
        @endif
    </div>
</div>
