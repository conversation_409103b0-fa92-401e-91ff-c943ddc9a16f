<?php
    $input_name = $name ?? 'phone_number_1';
    $input_id = $id ?? $input_name;
    $input_label = $label ?? 'Telefone/Celular';
    $input_value = $value ?? '';
    $input_placeholder = $placeholder ?? 'Digite o número do telefone';
    $is_required = (!isset($required) || isset($required) && $required);
    $is_disabled = isset($disabled) && $disabled;
    $additional_class = $class ?? '';
    $current_client_cpf = $currentClientCpf ?? '';
    $with_hidden_field = $withHiddenField ?? false;
    $validation_field_name = $validationFieldName ?? 'phone_validated';
    $include_modal = $includeModal ?? true;
?>

<div class="form-group phone-validation-component" data-phone-input-id="{{ $input_id }}">
    <label for="{{ $input_id }}">
        {{ $input_label }}
        @if($is_required)
            <span class="text-danger">*</span>
        @endif
    </label>
    
    <input type="text"
           class="form-control phone-input {{ $additional_class }} @error($input_name) is-invalid @enderror"
           name="{{ $input_name }}"
           id="{{ $input_id }}"
           value="{{ old($input_name, $input_value) }}"
           placeholder="{{ $input_placeholder }}"
           @if($is_required) required @endif
           @if($is_disabled) disabled @endif>
    
    @if($with_hidden_field && $is_disabled)
        <input type="hidden" name="{{ $input_name }}" id="{{ $input_id }}_hidden" value="{{ old($input_name, $input_value) }}">
    @endif
    
    @error($input_name)
        <small class="text-danger">{{ $message }}</small>
    @enderror
    
    <small id="error-{{ $input_id }}" class="text-danger"></small>
    
    <!-- Campo hidden para controlar status de validação -->
    @php
        $initialValidationValue = $initialValidated ?? '0';
    @endphp
    <input type="hidden" id="{{ $validation_field_name }}" name="{{ $validation_field_name }}" value="{{ $initialValidationValue }}">
</div>

{{-- Modal de validação WhatsApp incluído automaticamente --}}
@if($include_modal)
    @include('components.modal.modal_validate_whatsapp')
@endif

@push('js')
<script type="text/javascript">
$(document).ready(function() {
    // =======================================
    // VARIÁVEIS ESPECÍFICAS DO COMPONENTE
    // =======================================
    const componentId = '{{ $input_id }}';
    const validationFieldName = '{{ $validation_field_name }}';
    const currentClientCpf = '{{ $current_client_cpf }}';
    window.currentClientCpf = currentClientCpf.replace(/\D/g, '');
    const isHomologation = '{{ config("app.env") }}' === 'homologation';
    
    // Função para logs condicionais
    function debugLog(message, data = null) {
        if (isHomologation) {
            if (data) {
                console.log(message, data);
            } else {
                console.log(message);
            }
        }
    }
    
    // Variáveis de controle específicas para este componente
    let validatedPhoneNumber_{{ $input_id }};
    let counter_{{ $input_id }} = {value: 0};
    
    // =======================================
    // FUNÇÕES DE VALIDAÇÃO DE TELEFONE
    // =======================================
    function isPhoneNumberValid_{{ $input_id }}(phoneNumber) {
        const regex = /^\d{10,11}$/;
        return regex.test(phoneNumber);
    }

    function arePhoneNumbersEquivalent_{{ $input_id }}(num1, num2) {
        const rawNum1 = String(num1).replace(/\D/g, '');
        const rawNum2 = String(num2).replace(/\D/g, '');

        debugLog('[WhatsApp Validation {{ $input_id }}] arePhoneNumbersEquivalent chamada:', {
            num1: num1,
            num2: num2,
            rawNum1: rawNum1,
            rawNum2: rawNum2
        });

        if (rawNum1 === rawNum2) {
            debugLog('[WhatsApp Validation {{ $input_id }}] Números são idênticos');
            return true;
        }

        // Verifica se um tem 11 dígitos e o outro 10, e se são equivalentes
        // Para números brasileiros, o 9º dígito (3º após o DDD) é o adicional
        if (rawNum1.length === 11 && rawNum2.length === 10) {
            const num1WithoutNinth = rawNum1.substring(0, 2) + rawNum1.substring(3);
            debugLog('[WhatsApp Validation {{ $input_id }}] Comparando 11->10:', {
                rawNum1: rawNum1,
                rawNum2: rawNum2,
                num1WithoutNinth: num1WithoutNinth,
                isEqual: num1WithoutNinth === rawNum2
            });
            
            return num1WithoutNinth === rawNum2;
        }
        if (rawNum1.length === 10 && rawNum2.length === 11) {
            const num2WithoutNinth = rawNum2.substring(0, 2) + rawNum2.substring(3);
            debugLog('[WhatsApp Validation {{ $input_id }}] Comparando 10->11:', {
                rawNum1: rawNum1,
                rawNum2: rawNum2,
                num2WithoutNinth: num2WithoutNinth,
                isEqual: rawNum1 === num2WithoutNinth
            });
            
            return rawNum1 === num2WithoutNinth;
        }

        debugLog('[WhatsApp Validation {{ $input_id }}] Números não são equivalentes - tamanhos diferentes');
        return false;
    }

    function updateValidateButtonState_{{ $input_id }}(phoneInput) {
        const phoneInputId = phoneInput.attr('id');
        const rawValue = phoneInput.val();
        const phoneNumber = rawValue.replace(/\D/g, '');
        const isFormatValid = isPhoneNumberValid_{{ $input_id }}(phoneNumber);
        let validateButton = $(`#btn-validate-whatsapp-${phoneInputId}`);
        
        debugLog('[WhatsApp Validation {{ $input_id }}] Atualizando estado do botão de validação:', {
            phoneInputId: phoneInputId,
            phoneNumber: phoneNumber,
            isFormatValid: isFormatValid,
            isPhoneValidated: $(`#${validationFieldName}`).val() === '1',
            hasValidateButton: validateButton.length > 0,
            validatedPhoneNumber: validatedPhoneNumber_{{ $input_id }},
            isEquivalentToValidated: validatedPhoneNumber_{{ $input_id }} && arePhoneNumbersEquivalent_{{ $input_id }}(phoneNumber, validatedPhoneNumber_{{ $input_id }})
        });
        
        // Remove qualquer mensagem de validação existente
        phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').remove();

        if (isFormatValid) {
            // Se o número é equivalente ao já validado, marca como validado
            if (validatedPhoneNumber_{{ $input_id }} && arePhoneNumbersEquivalent_{{ $input_id }}(phoneNumber, validatedPhoneNumber_{{ $input_id }})) {
                debugLog('[WhatsApp Validation {{ $input_id }}] Número equivalente ao já validado, marcando como válido');
                phoneInput.removeClass('is-invalid').addClass('is-valid');
                const successMsg = $('<small class="text-success ml-2">Telefone validado!</small>');
                phoneInput.after(successMsg);
                $(`#${validationFieldName}`).val("1");
                if (validateButton.length > 0) {
                    validateButton.remove();
                }
            }
            // Se o formato é válido e o telefone não está validado, mostra o botão de validação
            else if ($(`#${validationFieldName}`).val() !== '1') {
                if (validateButton.length === 0) {
                    debugLog('[WhatsApp Validation {{ $input_id }}] Criando botão de validação');
                    validateButton = $(`<button type="button" id="btn-validate-whatsapp-${phoneInputId}" class="btn btn-sm btn-warning mt-2">Validar por WhatsApp</button>`);
                    const errorElement = $(`#error-${phoneInputId}`);
                    if (errorElement.length > 0) {
                        errorElement.before(validateButton);
                    } else {
                        phoneInput.closest('.form-group').append(validateButton);
                    }
                }
                validateButton.prop('disabled', false);
            } else {
                // Formato válido, mas telefone já validado: remover botão de validação
                debugLog('[WhatsApp Validation {{ $input_id }}] Telefone já validado, removendo botão');
                if (validateButton.length > 0) {
                    validateButton.remove();
                }
            }
        } else {
            // Formato inválido: remover botão de validação se existir
            debugLog('[WhatsApp Validation {{ $input_id }}] Formato inválido, removendo botão se existir');
            if (validateButton.length > 0) {
                validateButton.remove();
            }
        }
    }

    // =======================================
    // APLICAÇÃO DE MÁSCARA
    // =======================================
    function applyPhoneMask_{{ $input_id }}() {
        if (typeof getMask === 'function') {
            $(`#${componentId}`).mask(getMask(componentId)).keyup(function (event) {
                if (typeof checkPhoneMask === 'function') {
                    checkPhoneMask(event.target, event.originalEvent.key, counter_{{ $input_id }});
                }
            });
        } else {
            // Máscara básica caso getMask não esteja disponível
            $(`#${componentId}`).mask('(00) 00000-0000');
        }
    }

    // =======================================
    // INICIALIZAÇÃO DO COMPONENTE
    // =======================================
    
    // Inicializa variável global de validação
    const initialPhoneNumber = $(`#${componentId}`).val().replace(/\D/g, '');
    const isInitiallyValidated = $(`#${validationFieldName}`).val() === '1';
    validatedPhoneNumber_{{ $input_id }} = isInitiallyValidated ? initialPhoneNumber : '';
    
    debugLog('[WhatsApp Validation {{ $input_id }}] Inicialização do componente:', {
        componentId: componentId,
        initialPhoneNumber: initialPhoneNumber,
        isValidated: isInitiallyValidated,
        validatedPhoneNumber: validatedPhoneNumber_{{ $input_id }}
    });

    // Se já estiver validado na inicialização, mostra como validado
    if (isInitiallyValidated && initialPhoneNumber.length >= 10) {
        const phoneInput = $(`#${componentId}`);
        phoneInput.addClass('is-valid');
        const successMsg = $('<small class="text-success ml-2">Telefone validado!</small>');
        phoneInput.after(successMsg);
        debugLog('[WhatsApp Validation {{ $input_id }}] Telefone já validado na inicialização');
    }

    // Event listener para input de telefone
    $(`#${componentId}`).on('input', function() {
        
        const phoneInput = $(this);
        const newPhoneNumber = phoneInput.val().replace(/\D/g, '');
        
        // Limita o número de dígitos a 11 (máximo para telefone brasileiro)
        if (newPhoneNumber.length > 11) {
            const truncatedNumber = newPhoneNumber.substring(0, 11);
            
            // Reaplica a máscara com o número truncado
            if (typeof getMask === 'function') {
                const mask = getMask(componentId);
                phoneInput.val(truncatedNumber).mask(mask);
            } else {
                // Aplica máscara básica
                const formatted = truncatedNumber.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
                phoneInput.val(formatted);
            }
            return;
        }
        
        debugLog('[WhatsApp Validation {{ $input_id }}] Input de telefone alterado:', {
            newValue: phoneInput.val(),
            cleanNumber: newPhoneNumber,
            wasValidated: $(`#${validationFieldName}`).val() === '1',
            validatedNumber: validatedPhoneNumber_{{ $input_id }}
        });
        
        // Verifica se o número digitado é equivalente ao já validado
        const isEquivalent = validatedPhoneNumber_{{ $input_id }} && arePhoneNumbersEquivalent_{{ $input_id }}(newPhoneNumber, validatedPhoneNumber_{{ $input_id }});
        
        if (isEquivalent) {
            phoneInput.removeClass('is-invalid').addClass('is-valid');
            phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').remove();
            const successMsg = $('<small class="text-success ml-2">Telefone validado!</small>');
            phoneInput.after(successMsg);
            $(`#${validationFieldName}`).val("1");
            updateValidateButtonState_{{ $input_id }}(phoneInput);
            return;
        }
        
        // Reset da validação quando o número é alterado para um diferente
        $(`#${validationFieldName}`).val('0');
        phoneInput.removeClass('is-valid');
        phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').remove();

        // Executa validação diretamente sem debounce
        updateValidateButtonState_{{ $input_id }}(phoneInput);

        let phoneNumberRaw = phoneInput.val().replace(/\D/g, '');
        const errorElement = $(`#error-${componentId}`);

        if (isPhoneNumberValid_{{ $input_id }}(phoneNumberRaw)) {
            phoneInput.removeClass('is-invalid');
            errorElement.text('');
        } else {
            if (phoneNumberRaw.length === 0) {
                phoneInput.removeClass('is-invalid is-valid');
                errorElement.text('');
            }
        }
    });

    // Aplicar máscara
    applyPhoneMask_{{ $input_id }}();
    
    // Adiciona listener adicional para keyup como backup
    $(`#${componentId}`).on('keyup', function() {
        const phoneInput = $(this);
        const newPhoneNumber = phoneInput.val().replace(/\D/g, '');

        const isEquivalent = arePhoneNumbersEquivalent_{{ $input_id }}(newPhoneNumber, validatedPhoneNumber_{{ $input_id }});
        
        if (isEquivalent) {
            phoneInput.removeClass('is-invalid').addClass('is-valid');
            phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').remove();
            const successMsg = $('<small class="text-success ml-2">Telefone validado!</small>');
            phoneInput.after(successMsg);
            $(`#${validationFieldName}`).val("1");
            $(`#btn-validate-whatsapp-${componentId}`).remove();
            
            // Dispara evento de sucesso
            $(document).trigger('phoneValidation:success', {
                componentId: componentId,
                phoneNumber: newPhoneNumber,
                validationFieldName: validationFieldName
            });
        }
        
        debugLog('[DEBUG {{ $input_id }}] 🎯 EVENT KEYUP DISPARADO:', {
            valor: phoneInput.val(),
            numeroLimpo: newPhoneNumber,
            timestamp: new Date().toISOString()
        });
    });

    // =======================================
    // INTEGRAÇÃO COM MODAL DE VALIDAÇÃO WHATSAPP
    // =======================================

    // Manipulador de eventos específico para este componente
    $(document).on('click', `#btn-validate-whatsapp-${componentId}`, function() {
        debugLog('[WhatsApp Validation {{ $input_id }}] Botão "Validar por WhatsApp" clicado');
        const phoneInput = $(`#${componentId}`);
        const phoneNumber = phoneInput.val().replace(/\D/g, '');

        debugLog('[WhatsApp Validation {{ $input_id }}] Dados coletados:', {
            phoneInputId: componentId,
            phoneNumber: phoneNumber,
            hasModal: !!window.openWhatsappValidationModal
        });

        if (phoneNumber && window.openWhatsappValidationModal) {
            debugLog('[WhatsApp Validation {{ $input_id }}] Abrindo modal de validação');
            window.openWhatsappValidationModal(phoneNumber, componentId);
        } else {
            if (isHomologation) {
                console.error("[WhatsApp Validation {{ $input_id }}] Erro: Número de telefone não encontrado ou modal não disponível para validação.");
            }
        }
    });

    // Listener para evento de sucesso na validação
    $(document).on('whatsappModal:validationSuccess', function(event, data) {
        if (data.phoneInputId === componentId) {
            debugLog('[WhatsApp Validation {{ $input_id }}] Evento de sucesso recebido:', data);
            const { phoneNumber } = data;
            
            debugLog('[WhatsApp Validation {{ $input_id }}] Atualizando interface do componente');
            const phoneInput = $(`#${componentId}`);
            const errorElement = $(`#error-${componentId}`);
            
            phoneInput.removeClass('is-invalid').addClass('is-valid');
            errorElement.text('');

            // Adiciona mensagem de sucesso
            phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').remove();
            const successMsg = $('<small class="text-success ml-2">Telefone validado!</small>');
            phoneInput.after(successMsg);

            // Remove o botão "Validar por WhatsApp"
            $(`#btn-validate-whatsapp-${componentId}`).remove();

            // Atualiza variáveis do componente
            debugLog('[WhatsApp Validation {{ $input_id }}] Marcando telefone como validado');
            $(`#${validationFieldName}`).val("1");
            validatedPhoneNumber_{{ $input_id }} = phoneNumber;
            
            // Dispara evento customizado para comunicar a validação bem-sucedida
            $(document).trigger('phoneValidation:success', {
                componentId: componentId,
                phoneNumber: phoneNumber,
                validationFieldName: validationFieldName
            });
        }
    });

    // Listener para erros na validação
    $(document).on('whatsappModal:validationError', function(event, data) {
        if (data.phoneInputId === componentId) {
            if (isHomologation) {
                console.error('[WhatsApp Validation {{ $input_id }}] Erro na validação do WhatsApp:', data);
            }
        }
    });

    // =======================================
    // FUNÇÕES PÚBLICAS DO COMPONENTE
    // =======================================
    
    // Função para verificar se o telefone está validado
    window[`isPhoneValidated_${componentId}`] = function() {
        return $(`#${validationFieldName}`).val() === '1';
    };
    
    // Função para obter o número de telefone validado
    window[`getValidatedPhoneNumber_${componentId}`] = function() {
        return validatedPhoneNumber_{{ $input_id }};
    };
    
    // Função para resetar a validação
    window[`resetPhoneValidation_${componentId}`] = function() {
        debugLog('[WhatsApp Validation {{ $input_id }}] Resetando validação manualmente');
        $(`#${validationFieldName}`).val('0');
        validatedPhoneNumber_{{ $input_id }} = '';
        const phoneInput = $(`#${componentId}`);
        phoneInput.removeClass('is-valid');
        phoneInput.siblings('small.text-success.ml-2:contains("Telefone validado!")').remove();
        $(`#btn-validate-whatsapp-${componentId}`).remove();
        updateValidateButtonState_{{ $input_id }}(phoneInput);
    };
});
</script>
@endpush
