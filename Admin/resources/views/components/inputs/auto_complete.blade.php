<?php
    $input_type = $input_type ?? $type ?? 'text';
    $is_required = (!isset($required) || isset($required) && $required);
    $is_readonly = isset($readonly) && $readonly;
    $pholder = $placeholder ?? '';
    $maxLength = $maxLength ?? null;
    $minLength = $minLength ?? null;
    $suggestionsArray = $suggestions ?? [];
?>

<style>
    .suggestions-box {
        border: 1px solid #ccc;
        background: white;
        position: absolute;
        width: 100%;
        max-height: 150px;
        overflow-y: auto;
        visibility: hidden;
        z-index: 1000;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        opacity: 0;
    }

    .suggestions-box.show {
        transform: translateY(0);
        visibility: visible;
        display: block;
        opacity: 1;
    }
    
    .suggestion-item {
        padding: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .suggestion-item:hover {
        background: #f0f0f0;
        font-weight: 600;
        padding-left: 14px;
    }
    .suggestion-item.active {
        background: #e9ecef;
        font-weight: bold;
    }
</style>

<label for="{{ $name }}">
    {{ $label }} 
    <span id="{{ $id ?? $name }}_maxLength" class="text-muted">
        @if($maxLength) 
            ({{$maxLength}} caracteres) 
        @endif
    </span>
    @if($is_required)
        <b class='text-danger'>*</b>
    @endif
</label>

<div style="position: relative;">
    <input type="text" autocomplete="off" class="form-control @error($name) is-invalid @enderror" name="{{ $name }}"
        id="{{ $id ?? $name }}" value="{{ old($name, $value ?? '') }}" @if($is_required) required @endif @if($is_readonly)
        readonly @endif placeholder="{{ $pholder }}" onfocus="showSuggestions()" oninput="filterSuggestions()">

    <div id="suggestions-box-{{ $id ?? $name }}" class="suggestions-box">
        @foreach ($suggestionsArray as $suggestion)
            <div class="suggestion-item">{{ $suggestion }}</div>
        @endforeach
    </div>
</div>

@error($name)
<small class="text-danger">{{ $message }}</small>
@enderror
@if(isset($withErrorText) && $withErrorText)
<small id="error-{{ $id ?? $name }}" class="text-danger"></small>
@endif

<script>
    let suggestionBoxId = 'suggestions-box-{{ $id ?? $name }}';
    
    $(document).ready(() => {
        $(`#${suggestionBoxId}`).on('click', '.suggestion-item', function() {
            selectSuggestion($(this));
        })
    })
    
    function showSuggestions() {
        $(`#${suggestionBoxId}`).addClass('show');
    }

    function hideSuggestions() {
        $(`#${suggestionBoxId}`).removeClass('show');
    }

    function selectSuggestion(element) {
        let $items = $('.suggestion-item');
        $items.removeClass('active');

        let $newActiveItem = $(element);
        $newActiveItem.addClass('active');

        $('#{{ $id ?? $name }}').val($newActiveItem.text()).trigger('change');
        hideSuggestions();
    }

    function filterSuggestions() {
        let input = document.getElementById('{{ $id ?? $name }}').value.toLowerCase();
        let items = document.querySelectorAll(`#${suggestionBoxId} .suggestion-item`);
        items.forEach(item => {
            if (item.innerText.toLowerCase().includes(input)) {
                item.style.display = "block";
            } else {
                item.style.display = "none";
            }
        });
    }

    function navigateSuggestions(event) {
        let items = $(`#${suggestionBoxId} .suggestion-item`);
        if (items.length === 0) return;

        let activeItem = items.filter('.active');
        let activeItemIndex = items.index(activeItem);

        if (event.key === "ArrowDown") {
            activeItemIndex = (activeItemIndex + 1) % items.length;
        } else if (event.key === "ArrowUp") {
            activeItemIndex = (activeItemIndex - 1 + items.length) % items.length;
        } else if (event.key === "Enter") {
            event.preventDefault();
            if (activeItemIndex >= 0) {
                selectSuggestion(items.eq(activeItemIndex));
            }
        }

        let newActiveItem = items.eq(activeItemIndex);
        items.removeClass('active');
        newActiveItem.addClass('active');

        let container = $('#' + suggestionBoxId);
        let itemOffset = newActiveItem.position().top;
        let itemHeight = newActiveItem.outerHeight();
        let containerHeight = container.innerHeight();
        let scrollTop = container.scrollTop();

        if (itemOffset < 0) {
            // Se o item ativo está acima da visualização, rola para cima
            container.scrollTop(scrollTop + itemOffset);
        } else if (itemOffset + itemHeight > containerHeight) {
            // Se o item ativo está abaixo da visualização, rola para baixo
            container.scrollTop(scrollTop + (itemOffset + itemHeight - containerHeight));
        }
    }

    document.getElementById('{{ $id ?? $name }}').addEventListener('blur', hideSuggestions);
    document.getElementById('{{ $id ?? $name }}').addEventListener('keydown', navigateSuggestions);
</script>