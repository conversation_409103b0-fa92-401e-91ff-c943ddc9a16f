<?php
    $input_type = 'number';
    $is_required = isset($required) ? $required : 'required';
    $is_readonly = isset($readonly) ? 'readonly' : '';
    $pholder = $placeholder ?? '';
    $oninput = $oninput ?? '';
    $showMaxLegend = isset($showMaxLegend) ? $showMaxLegend : false;
?>

<label for="{{ $name }}">{{ $label }} @if($is_required)<b class='text-danger'>*</b>@endif</label>
<input type="{{ $input_type }}" class="form-control @error($name) is-invalid @enderror" name="{{ $name }}" id="{{ $id ?? $name }}" value="{{ $value ?? old($name) }}"
       {{ $is_required }} {{ $is_readonly }} placeholder="{{ $pholder }}" step="{{ $step ?? 1 }}" min="{{ $min ?? 0 }}" @if(isset($max)) max="{{ $max }}" @endif
       oninput="{{ $oninput }}">
@error($name)
<small class="text-danger">{{ $message }}</small>
@enderror
@if(isset($withErrorText) && $withErrorText)
<small id="error-{{ $id ?? $name }}" class="text-danger"></small>
@endif
@if($showMaxLegend && isset($max))
<small class="text-muted">A porcentagem máxima permitida é de {{ $max }}%</small>
@endif