<?php
    $input_type = $type ?? 'file';
    $is_required = isset($required) ? $required : 'required';
    $is_multiple = isset($multiple);
    $img_container = isset($images_container) ? $images_container : 'images_container';
?>
<div id="multiple_upload">
    <input type="hidden" id="files" name="files" onChange="alert(this.value)" />
    <input type="file" class="form-control @error($name) is-invalid @enderror" name="{{ $name }}" id="{{ $id ?? 'images' }}" multiple="multiple" accept="image/*" onchange="loadFiles(this)">
    <div id="message">Selecione as imagens</div>
    <input type="file" id="temp_images" style="display:none">
    <button type="button" id="botao" onclick="document.getElementById('<?= $id ?? 'images' ?>').click()">Upload</button>
</div>
@error($name)
<small class="text-danger">{{ $message }}</small>
@enderror

@push('js')
<script type="text/javascript">
    function copyFiles(from, to) {
        const dt = new DataTransfer()
        
        for(var i = 0; i < from.files.length; i++) { dt.items.add(from.files[i]); }
        for(var i = 0; i < to.files.length; i++) { dt.items.add(to.files[i]); }
        
        to.files = dt.files;
    }

    function updateMessage(images) {
        var msg = document.getElementById('message');
        
        if(images.files.length == 0) { msg.innerHTML = 'Selecione as imagens'; } 
        if(images.files.length > 1) { msg.innerHTML = images.files.length+' imagens selecionadas'; }
        else { msg.innerHTML = images.files.length+' imagem selecionada'; }
    }

    function createImageContainer(index, file) {
        var url = URL.createObjectURL(file);
        var img_container_name = 'img_container_'+ index;
        var img_link = "<a href='"+url+"' target='_blank' rel='noopener noreferrer'>";
        var image = "<img src='"+URL.createObjectURL(file)+"' class='card-img-top img-cover sized-preview-img' alt='image'>";
        var btn_remove = '<button type="button" class="btn btn-block btn-light text-danger" onclick="removeImage('+index+');">Remover</button>'
        var img_container = '<div class="card sized-img-card mb-2 mr-2" id="'+img_container_name+'">'+img_link+image+'</a>'+btn_remove+'</div>';
        return img_container;
    }

    function loadFiles(inputFile) {
        var temp = document.getElementById('temp_images');
        
        copyFiles(inputFile, temp);
        inputFile.files = temp.files;

        var images_container = document.getElementById('<?= $img_container ?>');
        images_container.innerHTML = ''

        for(var i = 0; i < inputFile.files.length; i++) {
            images_container.innerHTML = images_container.innerHTML + createImageContainer(i, inputFile.files[i]);
        }
        if(inputFile.files.length > 0) { $('#collapse-image-preview').collapse('show'); }
        else { $('#collapse-image-preview').collapse('hide'); }
        
        updateMessage(inputFile);
    };

    function reloadFiles(inputFile) {
        
        var images_container = document.getElementById('<?= $img_container ?>');
        images_container.innerHTML = ''

        for(var i = 0; i < inputFile.files.length; i++) {
            images_container.innerHTML = images_container.innerHTML + createImageContainer(i, inputFile.files[i]);
        }

        if(inputFile.files.length > 0) { $('#collapse-image-preview').collapse('show'); }
        else { $('#collapse-image-preview').collapse('hide'); }

        updateMessage(inputFile);
    };

    function removeImage(index) {
        document.getElementById('img_container_'+index).remove();
        removeFile(index);
    }

    function removeFile(index) {
        images = document.getElementById('<?= $id ?? 'images' ?>');
        const dt = new DataTransfer()
        
        for(var i = 0; i < images.files.length; i++) {
            if(i != index) { dt.items.add(images.files[i]); }
        }
        images.files = dt.files;
        var temp = document.getElementById('temp_images');
        temp.files = dt.files;

        reloadFiles(images);
    }
</script>
@endpush