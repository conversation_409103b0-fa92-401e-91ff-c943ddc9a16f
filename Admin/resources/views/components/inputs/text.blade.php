<?php
    $input_type = $input_type ?? $type ?? 'text';
    $is_required = (!isset($required) || isset($required) && $required);
    $is_readonly = isset($readonly) && $readonly;
    $pholder = $placeholder ?? '';
    $autocompleteText = '';
    $maxLength = $maxLength ?? null;
    $minLength = $minLength ?? null;

    if (isset($autocomplete)) {
        $autocompleteText = match ($autocomplete) {
            'on', true => 'autocomplete="on"',
            'off', false => 'autocomplete="do-not-autofill"',
            'new-password' => 'autocomplete="new-password"',
            default => '',
        };
    }
?>

<label for="{{ $name }}">
    {{ $label }} 
    <span id="{{ $id ?? $name }}_maxLength" class="text-muted">
        @if($maxLength) 
            ({{$maxLength}} caracteres) 
        @endif
    </span>
    @if($is_required)
        <b id="required-{{ $id ?? $name }}" class='text-danger'>*</b>
    @endif
</label>

@if ($input_type == 'password') <div class="input-group">@endif

<input type="{{ $input_type }}" class="form-control @if(isset($class)) {{ $class }} @endif @error($name) is-invalid @enderror" name="{{ $name }}"
    id="{{ $id ?? $name }}" value="{{ old($name, $value ?? '') }}" @if($is_required) required @endif @if($is_readonly)
    readonly @endif placeholder="{{ $pholder }}" @if(isset($onchange)) onchange="{{ $onchange }}" @endif {!! $autocompleteText !!}
    @if(isset($disabled) && $disabled) disabled @endif  @if($maxLength) maxlength="{{ $maxLength }}" @endif @if($minLength) minLength="{{ $minLength }}" @endif)>

@if ($input_type == 'password')
    @include('components.buttons.visible_password_button', [ 'name_input' => $name ])
    </div>
@endif

@error($name)
<small id="error-text-{{ $id ?? $name }}" class="text-danger">{{ $message }}</small>
@enderror
@if(isset($withErrorText) && $withErrorText)
<small id="error-{{ $id ?? $name }}" class="text-danger"></small>
@endif
