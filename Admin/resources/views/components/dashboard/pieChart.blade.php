<div class="mt-4" style="display: block; width: 100%;">
    <canvas id="{{ $chartId }}" class="pie-chart" width="400" height="400"></canvas>
</div>

@push('js')
<script>
    
    document.addEventListener('DOMContentLoaded', function () {
        let legendOrientation = 'right';
        const ctx = document.getElementById('{{ $chartId }}');
        const baseDetailUrl = '{{ $baseDetailUrl }}';

        const ids = @json($ids);
        const labels = @json($labels);
        const dataValues = @json($data);

        const redColors = [
            "#D32F2F", "#E53935", "#F44336", "#FF3B30", "#FF4C40", "#FF5C50", "#FF6A60",
            "#FF796F", "#FF8A7F", "#FF9A8F", "#FFAB9F", "#FFBBAD", "#FFCCBD", "#D75F17"
        ]; // quanto mais à direita mais próximo do #D84B16

        const yellowColors = [
            "#D84B16", "#E15B00", "#F07B00", "#F69100", "#F7A200", "#F9B200", "#F9C200",
            "#F9D200", "#F9E200", "#F9E700", "#F9EB00", "#F9F000", "#F9F500", "#F9F900"
        ];
        
        const greenColors = [
            "#00FF7F", "#00F570", "#00E861", "#00DB52", "#00CF43", "#00C235", "#00B626",
            "#00A91A", "#009D0E", "#009200", "#008600", "#007A00", "#006E00", "#006200"
        ];

        const peekColor = (colorsArray, index) => {
            console.log(redColors, yellowColors, greenColors)
            if (colorsArray.length > 1) {
                const safeIndex = Math.min(index, colorsArray.length - 1);
                const color = colorsArray.splice(safeIndex, 1)[0];
                return color;
            } else return colorsArray[0];
        }

        function getColorBasedOnAvarage(avarage) {
            if (avarage <= 2) {
                const index = Math.floor((avarage / 2) * (redColors.length - 1));
                return peekColor(redColors, index);
            } else if (avarage <= 3.5) {
                const index = Math.floor((avarage / 3.5) * (redColors.length - 1));
                return peekColor(yellowColors, index);
            } else {
                const index = Math.floor((avarage / 5) * (redColors.length - 1));
                return peekColor(greenColors, index);
            }
        }
        
        const allLabels = labels.map((label, index) => ({
            id: ids[index],
            label: label,
            value: dataValues[index],
            color: getColorBasedOnAvarage(dataValues[index])
        }));

        const filteredData = dataValues.map((value) => (value === null || value === 0) ? null : value);
        const chartData = filteredData.filter(value => value !== null);
        const chartLabels = allLabels
            .filter(item => item.value !== null && item.value !== 0)
            .map(item => ({
                    id: item.id, 
                    label: item.label, 
                    color: item.color
            }));

        const newLegendClickHandler = function (e, legendItem, legend) {
            const index = legendItem.datasetIndex;
            const itemId = allLabels[index].id;

            openTopicDetails('modal-details-topic', itemId, allLabels[index].label);
        };

        if (window.innerWidth <= 1100) {
            legendOrientation = 'bottom';
        }

        const chart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: chartLabels.map((item) => item.label),
                datasets: [{
                    label: ' {{ $datasetLabel }}',
                    data: chartData,
                    backgroundColor: chartLabels.map((item) => item.color)
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const clickedElementIndex = elements[0].index;
                        const itemId = chartLabels[clickedElementIndex].id;
                        

                        openTopicDetails('modal-details-topic', itemId, chartLabels[clickedElementIndex].label);
                    }
                },
                onHover: (event, chartElement) => {
                    event.native.target.style.cursor = chartElement[0] ? 'pointer' : 'default';
                },
                plugins: {
                    datalabels: {
                        formatter: (value, context) => {
                            let newValue = Number(value).toFixed(2);
                            newValue = newValue.endsWith('.00') ? newValue.slice(0, -3) : newValue;
                            return `${newValue}★`;
                        },
                        color: '#fff',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        onHover: function(event) {
                            event.native.target.style.cursor = 'pointer';
                        },
                        onLeave: function(event) {
                            chart.update();
                            event.native.target.style.cursor = 'default';
                        },
                        onClick: newLegendClickHandler,
                        labels: {
                            generateLabels: function (chart) {
                                return allLabels.map((item, index) => ({
                                    text: `${item.label.length > 15 ? item.label.slice(0,15).concat('...') : item.label}`,
                                    fillStyle: (item.value === null || item.value === 0) ? 'transparent' : allLabels[index].color,
                                    datasetIndex: index,
                                }));
                            },
                            font: {
                                size: 16,
                                family: 'Arial, sans-serif',
                                weight: 'bold'
                            },
                            color: '#0842A0',
                            usePointStyle: false,
                            boxWidth: 20,
                            boxHeight: 15,
                        },
                        position: legendOrientation,
                        align: 'center',
                        fullSize: false
                    }
                }
            },
            plugins: [ChartDataLabels]
        });

        window.addEventListener('resize', function() {
            const newOrientation = window.innerWidth <= 1100 ? 'bottom' : 'right';
            if (newOrientation !== legendOrientation) {
                legendOrientation = newOrientation;
                chart.options.plugins.legend.position = legendOrientation;
                chart.update();
            }
        })

    });
</script>

@endpush
