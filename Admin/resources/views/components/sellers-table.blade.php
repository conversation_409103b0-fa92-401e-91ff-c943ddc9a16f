<div class="col-md-12" id="sellers">
    <label class="form-title mb-0" for="seller">Vendedor</label>
    <div class="form-group mt-0">
        <hr class="my-1 d-none" id="sellersSeparator"/>
        <label class="m-0 d-none" for="seller" id="sellerSelectedLabel">Selecionado:</label>
        <div class="form-group mb-0">
            <ul class="list-group" id="seller_selected">
                <!-- Vendedor selecionado será carregado aqui -->
            </ul>
        </div>
    </div>
    <div class="form-group mb-0 mt-0">
        <input type="text" id="sellerSearchFormEmployee" class="form-control" placeholder="Pesquisar vendedor...">
    </div>
    <div class="d-flex justify-content-center loadingSellers pb-1 d-none">
        <div class="spinner-border loadingSellers" role="status">
            <span class="sr-only loadingSellers">Loading...</span>
        </div>
    </div>
    <table class="table table-bordered d-none" id="sellerTableFormEmployee">
        <thead>
        <tr>
            <th>Nome</th>
            <th>Ações</th>
        </tr>
        </thead>
        <tbody id="sellerTableFormEmployeeBody">
        <!-- Vendedores serão carregados aqui -->
        </tbody>
    </table>
    <nav>
        <ul class="pagination" id="sellerPaginationFormEmployee">
            <!-- Paginação será carregada aqui -->
        </ul>
    </nav>
    <div class="d-none" id="sellerInputs">
        <!-- Inputs hidden para enviar o vendedor selecionado -->
    </div>
</div>

<script>
    let sellerSelectedId = null;
    @if(isset($business['seller_id']) && isset($business['seller_name']))
        sellerSelectedId = {{ $business['seller_id'] }};
    $('#seller_selected').append(`<li class="list-group-item">
        <div class="d-flex justify-content-between">
            <span>{{ $business['seller_name'] }}</span>
            <a href="#" button type="button" class="btn btn-danger fa fa-trash"  onclick="clearSellerSelection()"></a>
        </div>
    </li>`);
    $('#sellerInputs').append(`<input type="hidden" name="seller_id" value="{{ $business['seller_id'] }}">`);
    $('#sellerInputs').append(`<input type="hidden" name="seller_name" value="{{ $business['seller_name'] }}">`);
    $('#sellersSeparator').removeClass('d-none');
    $('#sellerSelectedLabel').removeClass('d-none');
    @endif

    function loadSellersFormEmployee(page = 1) {
        const searchQuery = $('#sellerSearchFormEmployee').val();
        const url = `<?= env('API_URL') ?>/api/sellers?search=${searchQuery}&page=${page}&per_page=5`;

        $.ajax({
            url: url,
            type: 'get',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#sellerTableFormEmployee').addClass('d-none');
                $('#sellerPaginationFormEmployee').addClass('d-none');
                $('.loadingSellers').removeClass('d-none');
            },
            success: function (response) {
                $('.loadingSellers').addClass('d-none');
                $('#sellerTableFormEmployeeBody').empty();
                if (response.sellers.data.length > 0) {
                    $.each(response.sellers.data, function (index, seller) {
                        let isSelected = seller.id === sellerSelectedId;
                        $('#sellerTableFormEmployeeBody').append(`
                        <tr>
                            <td class="align-content-center m-0 pr-1" style="overflow: hidden; text-overflow: ellipsis;"
                                    title="${seller.name}">${seller.name}</td>
                            <td style="width: 70px">
                                <input 
                                    type="radio" 
                                    name="seller" 
                                    id="seller-${seller.id}" 
                                    value="${seller.id}" 
                                    ${isSelected ? 'checked' : ''} 
                                    onclick="selectSeller(${seller.id}, '${seller.name}', this);" 
                                >
                            </td>
                        </tr>
                    `);
                    });
                } else {
                    $('#sellerTableFormEmployeeBody').append('<tr><td colspan="2">Nenhum vendedor encontrado</td></tr>');
                }

                $('#sellerPaginationFormEmployee').empty();
                if (response.sellers.last_page > 1) {
                    for (let i = 1; i <= response.sellers.last_page; i++) {
                        $('#sellerPaginationFormEmployee').append(`
                        <li class="page-item ${i === response.sellers.current_page ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `);
                    }
                }

                $('#sellerPaginationFormEmployee a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadSellersFormEmployee(page);
                });

                $('#sellerTableFormEmployee').removeClass('d-none');
                $('#sellerPaginationFormEmployee').removeClass('d-none');
            },
            error: function () {
                $('.loadingSellers').addClass('d-none');
            }
        });
    }

    function selectSeller(sellerId, sellerName, button) {
        if (sellerSelectedId === sellerId) {
            clearSellerSelection();
            return;
        }
        sellerSelectedId = sellerId;
        $('#seller_selected').empty();
        $('#seller_selected').append(`
        <li class="list-group-item">
            <div class="d-flex justify-content-between">
                <span>${sellerName}</span>
                <a href="#" button type="button" class="btn btn-danger fa fa-trash"  onclick="clearSellerSelection()"></a>
            </div>
        </li>
    `);

        $('#sellerInputs').empty();
        $('#sellerInputs').append(`<input type="hidden" name="seller_id" value="${sellerId}">`);
        $('#sellerInputs').append(`<input type="hidden" name="seller_name" value="${sellerName}">`);

        $('#sellerTableFormEmployee a').each(function () {
            $(this).removeClass('btn-success').addClass('btn-primary');
        });

        $(button).removeClass('btn-primary').addClass('btn-success');

        $('#sellersSeparator').removeClass('d-none');
        $('#sellerSelectedLabel').removeClass('d-none');
    }

    function clearSellerSelection() {
        sellerSelectedId = null;
        $('#seller_selected').empty();
        $('#sellerInputs').empty();
        $('#sellerTableFormEmployee input[type="radio"]').each(function () {
            $(this).prop('checked', false);
        });

        $('#sellersSeparator').addClass('d-none');
        $('#sellerSelectedLabel').addClass('d-none');
    }

    $(document).ready(function () {
        loadSellersFormEmployee();
        $('#sellerSearchFormEmployee').on('keyup', debounce(function () {
            loadSellersFormEmployee();
        }, 500));
    });

</script>
