<style>
    .menu-item-active {
        color: #fff;
        font-weight: bold;
        background-color: {{ SystemHelper::get()['MAIN_COLOR'] ?? '#FBBC05' }};
    }
    .menu-item-active:hover {
        color: inherit;
    }
</style>

@props(['route', 'page', 'submenu' => false, 'textClass' => '', 'activePage' => '', 'withDivider' => false, 'isExternal' => false])

@if(isset($withDivider) && $withDivider)
    <div class="dropdown-divider"></div>
@endif
<a href="{{ $isExternal ? $route : route($route) }}" 
   id="{{ $page }}_menu"
   class="menu-item @if($submenu) dropdown-item @else my-1 @endif @if($activePage == $page) menu-item-active  @else {{ $textClass }} text-dark @endif"
   @if($isExternal) target="_blank" rel="noopener noreferrer" @endif>
    {{ $slot }}
</a>
