<div class="col-md-12 d-none" id="businessesTopic">
    <label class="form-title mb-0" for="name">Negócios vinculados
        @if (!((ApiUser::get()['authorizedFunction']['release_all_business'] || ApiUser::get()['login_type'] == 'admin')
                && ApiUser::get()['business_selected'] == null && isset($isClient) && $isClient))
            <span style="color: red">*</span>
        @endif
    </label>
    <div class="form-group mt-0">
        <hr class="my-1" id="businessesSeparator"/>
        <label class="m-0" for="name" id="businessSelectedLabel">Selecionados:</label>
        <div class="form-group mb-0">
            <ul class="list-group" id="business_selected">
                <!-- Negócios selecionados serão carregados aqui -->
            </ul>
        </div>
    </div>
    <div class="form-group mb-0 mt-0">
        <input type="text" id="businessSearchFormEmployee" class="form-control" placeholder="Pesquisar negócio...">
    </div>
    <div class="d-flex justify-content-center loading pb-1 d-none">
        <div class="spinner-border loading" role="status">
            <span class="sr-only loading">Loading...</span>
        </div>
    </div>
    <table class="table table-bordered d-none" id="businessTableFormEmployee">
        <thead>
        <tr>
            <th style="width: 1%">Selecionar</th>
            <th>Nome</th>
        </tr>
        </thead>
        <tbody id="businessTableFormEmployeeBody">
        <!-- Negócios serão carregados aqui -->
        </tbody>
    </table>
    <nav>
        <ul class="pagination" id="businessPaginationFormEmployee">
            <!-- Paginação será carregada aqui -->
        </ul>
    </nav>
    <div class="d-none" id="businessesInputs">
        @if(isset($businesses))
            @foreach($businesses as $business)
                <input type="hidden" name="businessesIds[]" value="{{$business['id']}}"
                       id="businesses_input_{{$business['id']}}">
            @endforeach
        @endif
    </div>
</div>

<script>
    function verifyBusinesses() {
        if (businessesSelectedIds.length === 0 && !$('#default').is(':checked')) {
            alert('Selecione ao menos um negócio');
            return false;
        }
        return true;
    }

    $(document).ready(function () {
        $('#businessSearchFormEmployee').on('keyup', debounce(function () {
            loadBusinessesFormEmployee();
        }, 500));
        selectActualBusiness();
    });

    savedBusinesses = @json($businesses ?? []);
    oldBusinessesIds = [];
    businessesSelectedIds = [];
    if (savedBusinesses.length > 0) {
        savedBusinesses.forEach(business => {
            businessesSelectedIds.push(business.id.toString());
            oldBusinessesIds.push(business.id.toString());
        });
    }
    window.businesses_autorizados = @json(ApiUser::get()['businesses'] ?? []);
    window.businesses_autorizados_ids = window.businesses_autorizados.map(business => business.id.toString());
    window.isAdmin = {{ ApiUser::getLoginType() == 'admin' ? 'true' : 'false' }};
    window.isAuthorizedAllBusiness = {{ ApiUser::get()['authorizedFunction']['release_all_business'] ? 'true' : 'false' }};
    window.business_selected_id = {{ApiUser::get()['business_selected']['id'] ?? -1}};

    @if($isEdit)
    @foreach($businesses as $business)
    addBusinessesSelectedItem(@json($business));
    @endforeach
    @endif

    function addBusinessesSelectedItem(business) {
        let p = document.createElement('p');
        p.className = 'row justify-content-between m-0';

        if (businesses_autorizados_ids.includes(business.id.toString()) || isAdmin || isAuthorizedAllBusiness) {
            p.innerHTML = `- ${business.name} 
                            <button 
                                type="button" 
                                class="btn ${business_selected_id === business.id ? 'btn-secondary' : 'btn-danger'} fa fa-trash" 
                                onclick="removeBusiness(${business.id})" 
                                ${business_selected_id === business.id ? 'disabled' : ''}></button>`;
        } else {
            p.innerHTML = `- ${business.name}`;
        }

        let li = document.createElement('li');
        li.className = 'list-group list-group-item';
        li.id = `business_selected_${business.id}`;
        li.appendChild(p);
        $('#business_selected').append(li);
    }

    showHideNoBusinessSelectedMessage();

    function businessSelected(checkbox) {
        const businessId = $(checkbox).val();
        if ($(checkbox).is(':checked')) {
            businessesSelectedIds.push(businessId);
            if ($('businesses_input_' + businessId).length === 0) {
                $('#businessesInputs').append(`<input type="hidden" name="businessesIds[]" value="${businessId}" id="businesses_input_${businessId}">`);
            }

            if ($(`#business_selected_${businessId}`).length === 0) {

                let p = document.createElement('p');
                p.className = 'row justify-content-between m-0';
                if (businesses_autorizados_ids.includes(businessId.toString()) || isAdmin || isAuthorizedAllBusiness) {
                    p.innerHTML = `- ${$(checkbox).parent().next().text()} <button class="btn ${business_selected_id === businessId ? 'btn-light' : 'btn-danger'} fa fa-trash" onclick="removeBusiness(${businessId})" ${business_selected_id === businessId ? 'disabled' : ''}></button>`;
                } else {
                    p.innerHTML = `- ${$(checkbox).parent().next().text()}`;
                }

                let li = document.createElement('li');
                li.className = 'list-group list-group-item';
                li.id = `business_selected_${businessId}`;
                li.appendChild(p);
                $('#business_selected').append(li);
            }
            showHideNoBusinessSelectedMessage();
        } else {
            businessesSelectedIds = businessesSelectedIds.filter(id => id !== businessId);
            $('#businesses_input_' + businessId).remove();
            $(`#business_selected_${businessId}`).remove();
            showHideNoBusinessSelectedMessage();
        }
        @if(isset($refreshTopics) && $refreshTopics)
        loadTopicsFormEmployee();
        @endif
    }

    function selectActualBusiness() {
        if (!businessesSelectedIds.includes(business_selected_id.toString())) {
            if (business_selected_id !== -1) {
                businessesSelectedIds.push(business_selected_id.toString());
                $('#businessesInputs').append(`<input type="hidden" name="businessesIds[]" value="${business_selected_id}" id="businesses_input_${business_selected_id}">`);
                addBusinessesSelectedItem({id: business_selected_id, name: '{{ApiUser::get()['business_selected']['name'] ?? null}}'});
            }
            @if(isset($refreshTopics) && $refreshTopics)
            loadTopicsFormEmployee();
            @endif
        }
    }

    function showHideNoBusinessSelectedMessage() {
        if ($('#business_selected').children().length === 0) {
            $('#businessSelectedLabel').addClass('d-none');
            $('#businessesSeparator').addClass('d-none');
        } else {
            $('#businessSelectedLabel').removeClass('d-none');
            $('#businessesSeparator').removeClass('d-none');
        }
    }

    function removeBusiness(businessId) {
        $(`#business_selected_${businessId}`).remove();
        $(`#business_${businessId}`).prop('checked', false);
        businessesSelectedIds = businessesSelectedIds.filter(id => id !== businessId.toString());
        $('#businesses_input_' + businessId).remove();
        showHideNoBusinessSelectedMessage();
        @if(isset($refreshTopics) && $refreshTopics)
        loadTopicsFormEmployee();
        @endif
    }

    function loadBusinessesFormEmployee(page = 1) {
        let isCrudEmployee = window.location.href.includes('/employees') && (window.location.href.includes('/create') || window.location.href.includes('/edit'));
        let isCrudTopic = window.location.href.includes('/topics') && (window.location.href.includes('/create') || window.location.href.includes('/edit'));
        let isCrudForm = window.location.href.includes('/forms') && (window.location.href.includes('/create') || window.location.href.includes('/edit'));
        let isCrudClients = window.location.href.includes('/clients') && (window.location.href.includes('/create') || window.location.href.includes('/edit'));

        const searchQuery = $('#businessSearchFormEmployee').val();
        $.ajax({
            url: "<?= env('API_URL') ?>/api/business/getUserBusiness?search=" + searchQuery + "&page=" + page + "&per_page=5",
            type: 'get',
            data: {
                search: searchQuery,
                page: page,
                per_page: 5,
                is_crud_employee: isCrudEmployee,
                is_crud_topic: isCrudTopic,
                is_crud_form: isCrudForm,
                is_crud_clients: isCrudClients,
                @if(isset($refreshTopics) && $refreshTopics)
                topics_selected_ids: topicsSelectedIds,
                @endif
            },
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#businessTableFormEmployee').addClass('d-none');
                $('#businessPaginationFormEmployee').addClass('d-none');
                $('.loading').removeClass('d-none');
            },
            success: function (response) {
                $('.loading').addClass('d-none');

                $('#businessTableFormEmployeeBody').empty();
                if (response.businesses.data.length > 0) {
                    $.each(response.businesses.data, function (index, business) {
                        let isSelected = businessesSelectedIds.includes(business.id.toString());
                        $('#businessTableFormEmployeeBody').append(`
                            <tr>
                                <td style="width: 1%" class="text-center">
                                    <input type="checkbox" id="business_${business.id}" value="${business.id}" onchange="businessSelected(this);" ${business_selected_id === business.id ? 'disabled' : ''}>
                                </td>
                                <td>
                                    ${business.name}
                                </td>

                            </tr>
                        `);
                        $(`#business_${business.id}`).prop('checked', isSelected);
                    });
                } else {
                    $('#businessTableFormEmployeeBody').append(`
                        <tr>
                            <td colspan="2" style="text-align: center; padding: 20px; color: #666;">
                                <div style="display: flex; flex-direction: column; align-items: center;">
                                    <i class="fas fa-store-slash" style="font-size: 24px; margin-bottom: 8px;"></i>
                                    <span style="font-style: italic;">Nenhum negócio encontrado</span>
                                </div>
                            </td>
                        </tr>
                    `);
                }

                // Paginação
                $('#businessPaginationFormEmployee').empty();
                $('#businessPaginationFormEmployee').empty();
                if (response.businesses.last_page > 1) {
                    for (let i = 1; i <= response.businesses.last_page; i++) {
                        $('#businessPaginationFormEmployee').append(`
                            <li class="page-item ${i === response.businesses.current_page ? 'active' : ''}">
                                <a class="page-link" href="#" data-page="${i}">${i}</a>
                            </li>
                        `);
                    }
                }

                $('#businessPaginationFormEmployee a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadBusinessesFormEmployee(page);
                });

                $('#businessTableFormEmployee').removeClass('d-none');
                $('#businessPaginationFormEmployee').removeClass('d-none');
            },
            error: function (response, textStatus, msg) {
                $('.loading').addClass('d-none');
            }
        });
    }
</script>
