<div class="modal" tabindex="-1" role="dialog" id="{{ $modalId }}" data-phone_number="" data-current_cpf="" data-formId="">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Telefone</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto">
                <div class="row">
                    <div class="col">
                        <input class="form-control" name="phone_validation_code" id="phone_validation_code_input_{{ $modalId }}" type="text"
                               placeholder="Insira o código enviado por WhatsApp">
                        <div id="alert-code-confirm-phone_{{ $modalId }}"></div>
                    </div>
                </div>
                <div>
                    <div>
                        <b style="font-size: 15px">Foi enviado um código de confirmação para seu WhatsApp. Caso não tenha recebido, verifique suas mensagens.</b>
                    </div>

                    <div class="w-100 d-flex align-items-center mt-3 mb-3" style="gap: 8px">
                        <i class="fas fa-clock fa-fw"></i>
                        <span id="resendPhoneTimer_{{ $modalId }}"></span>
                        <a class="nav-link disabled" style="width: 10rem; height: 2.5rem" id="resendPhoneButton_{{ $modalId }}" onclick="resendPhoneValidationCode('{{ $modalId }}')">
                            <span id="resendPhoneButtonText_{{ $modalId }}">Reenviar código</span>
                            <div class="spinner-border text-secondary spinner-border-sm" style="display: none;" role="status" id="resendPhoneSpinner_{{ $modalId }}">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" onclick="checkPhoneCode('{{ $modalId }}');">Confirmar</button>
                </div>
            </div>
        </div>
    </div>
</div>


@php
    $resendPhoneTimeSeconds = App\Helpers\SystemHelper::getAuthCodeResendTimeSeconds();
@endphp

@push('js')
    <script type="text/javascript">
        const RESEND_PHONE_TIME_SECONDS = {{ $resendPhoneTimeSeconds }};
        $(document).ready(function () {
            const modalId = '{{ $modalId }}';
            $(`#${modalId}`).on('shown.bs.modal', function () {
                resetPhoneTimer(modalId);
                // Limpar campo de código e alertas ao abrir
                $(`#phone_validation_code_input_${modalId}`).val('');
                $(`#alert-code-confirm-phone_${modalId}`).html('');
            });
        });

        let phoneIntervals = {}; // Para armazenar intervalos de timers por modalId

        function resetPhoneTimer(modalId) {
            let timeLeft = RESEND_PHONE_TIME_SECONDS - 1;
            const timer = document.getElementById(`resendPhoneTimer_${modalId}`);
            const resendButton = document.getElementById(`resendPhoneButton_${modalId}`);

            if (!resendButton) return; // Sai se o botão não existir

            if (!resendButton.classList.contains('disabled')) {
                resendButton.classList.add('disabled');
            }

            if (phoneIntervals[modalId]){
                clearInterval(phoneIntervals[modalId]);
            }

            // Definir exibição inicial do timer
            const initialMinutes = String(Math.floor(RESEND_PHONE_TIME_SECONDS / 60)).padStart(2, '0');
            const initialSeconds = String(RESEND_PHONE_TIME_SECONDS % 60).padStart(2, '0');
            timer.textContent = `${initialMinutes}:${initialSeconds}`;

            phoneIntervals[modalId] = setInterval(() => {
                if (timeLeft <= 0) {
                    clearInterval(phoneIntervals[modalId]);
                    resendButton.classList.remove('disabled');
                    timer.textContent = '00:00';
                } else {
                    const minutes = String(Math.floor(timeLeft / 60)).padStart(2, '0');
                    const seconds = String(timeLeft % 60).padStart(2, '0');
                    timer.textContent = `${minutes}:${seconds}`;
                }
                timeLeft -= 1;
            }, 1000);
        }

        function checkPhoneCode(modalId) {
            const modal = document.getElementById(modalId);
            const codeInput = document.getElementById(`phone_validation_code_input_${modalId}`);
            const code = codeInput.value;
            const phoneNumber = modal.dataset.phoneNumber;
            const currentCpf = modal.dataset.currentCpf; // CPF do usuário que está validando
            const formToSubmitId = modal.dataset.formId; // ID do formulário para submeter após sucesso

            if (!code) {
                showAlertPhone(modalId, 'Por favor, insira o código de validação.', 'danger');
                return;
            }

            const formData = new FormData();
            formData.append('phone_number', phoneNumber);
            formData.append('validation_code', code);
            formData.append('current_cpf', currentCpf);
            formData.append('_token', '{{ csrf_token() }}'); // Adicionar CSRF token

            // Adicionar um spinner ao botão de confirmar
            const confirmButton = $(modal).find('.btn-success');
            const originalButtonText = confirmButton.html();
            confirmButton.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verificando...').prop('disabled', true);


            $.ajax({
                url: "{{ route('api.user.verify_phone_validation_code') }}", // Rota para verificar o código do telefone
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (response) {
                    showAlertPhone(modalId, response.message || 'Telefone validado com sucesso!', 'success');
                    $(`#${modalId}`).modal('hide');

                    // Se um formId foi especificado, submete o formulário
                    if (formToSubmitId) {
                        const form = document.getElementById(formToSubmitId);
                        if (form) {
                            // Adicionar campo hidden para indicar que o telefone foi validado, se necessário
                            let validatedInput = $(form).find('input[name="phone_validated"]');
                            if(validatedInput.length === 0) {
                                $(form).append('<input type="hidden" name="phone_validated" value="1">');
                            } else {
                                validatedInput.val('1');
                            }
                            // Atualizar o campo do telefone no formulário com o número validado (se necessário)
                            // Ex: $(form).find('input[name="phone_number_field_name"]').val(phoneNumber);

                            // Opcional: Mostrar um loading global antes de submeter
                            // const loadingModal = document.getElementById('loading'); // Supondo que exista um modal de loading global
                            // if(loadingModal) {
                            //     $(loadingModal).modal('show');
                            // }
                            form.submit();
                        }
                    }
                    // Ou, você pode emitir um evento para que a página que chamou o modal saiba do sucesso
                    $(document).trigger('phoneValidatedSuccess', { modalId: modalId, phoneNumber: phoneNumber });
                },
                error: function (xhr, textStatus, errorThrown) {
                    let errorMessage = 'Falha ao verificar o código.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        errorMessage = Object.values(xhr.responseJSON.errors).join(' ');
                    }
                    showAlertPhone(modalId, errorMessage, 'danger');
                },
                complete: function() {
                    // Restaurar botão de confirmar
                    confirmButton.html(originalButtonText).prop('disabled', false);
                }
            });
        }

        function resendPhoneValidationCode(modalId) {
            const modal = document.getElementById(modalId);
            const phoneNumber = modal.dataset.phoneNumber;

            if (!phoneNumber) {
                showAlertPhone(modalId, 'Número de telefone não encontrado para reenviar o código.', 'danger');
                return;
            }

            const formData = new FormData();
            formData.append('phone_number', phoneNumber);
            formData.append('_token', '{{ csrf_token() }}'); // Adicionar CSRF token


            const resendButton = $(`#resendPhoneButton_${modalId}`);
            const resendSpinner = $(`#resendPhoneSpinner_${modalId}`);
            const resendButtonText = $(`#resendPhoneButtonText_${modalId}`);

            resendSpinner.show();
            resendButtonText.hide();
            resendButton.prop('disabled', true).addClass('disabled');

            $.ajax({
                url: "{{ route('api.user.start_phone_validation') }}", // Rota para iniciar a validação (reenviar código)
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (response) {
                    showAlertPhone(modalId, response.message || 'Código de validação reenviado com sucesso.', 'success');
                    resetPhoneTimer(modalId); // Reinicia o timer
                },
                error: function (xhr, textStatus, errorThrown) {
                    let errorMessage = 'Falha ao reenviar o código de validação.';
                     if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showAlertPhone(modalId, errorMessage, 'danger');
                },
                complete: function () {
                    resendButton.prop('disabled', false); // Habilita o botão para o timer controlar
                    resendButtonText.show();
                    resendSpinner.hide();
                    // O timer irá remover a classe 'disabled' quando o tempo acabar
                }
            });
        }

        function showAlertPhone(modalId, message, type) {
            const alertDiv = document.getElementById(`alert-code-confirm-phone_${modalId}`);
            alertDiv.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show mt-2">
                    <button type="button" aria-hidden="true" class="close" data-dismiss="alert" aria-label="Close">
                        <i class="nc-icon nc-simple-remove"></i>
                    </button>
                    <span>${message}</span>
                </div>
            `;
            // Auto-dismiss alert after 5 seconds
            setTimeout(() => {
                $(alertDiv).find('.alert').alert('close');
            }, 5000);
        }

        // Para limpar os alertas automaticamente (opcional, já que o showAlertPhone tem seu próprio timeout)
        // setInterval(function() {
        //     $('.modal').each(function(){
        //         const modalId = $(this).attr('id');
        //         if (modalId && $(`#alert-code-confirm-phone_${modalId}`).length) {
        //             // $(`#alert-code-confirm-phone_${modalId}`).html(''); // Isso pode ser muito agressivo
        //         }
        //     });
        // }, 7000);

    </script>
@endpush