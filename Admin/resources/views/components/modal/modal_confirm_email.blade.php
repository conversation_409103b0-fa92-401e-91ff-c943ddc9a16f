<div class="modal" tabindex="-1" role="dialog" id="{{ $modalId }}" data-code="" data-formId="form-create-employee">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar email</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto">
                <div class="row">
                    <div class="col">
                        <input class="form-control" name="code" id="code" type="text"
                               placeholder="Insira o codigo enviado">
                        <div id="alert-code-confirm-email"></div>
                    </div>
                </div>
                <div>
                    <div>
                        <b style="font-size: 15px">Foi enviado um código de confirmação para seu e-mail, caso não esteja na sua caixa de entrada, verifique sua caixa de span.</b>
                    </div>

                    <div class="w-100 d-flex align-items-center mt-3 mb-3" style="gap: 8px">
                        <i class="fas fa-clock fa-fw"></i>
                        <span id="resendTimer"></span>
                        <a class="nav-link disabled" style="width: 10rem; height: 2.5rem" id="resendButton" onclick="resendConfirmEmail()">
                            <span id="resendButtonText">Reenviar código</span>
                            <div class="spinner-border text-secondary spinner-border-sm" style="display: none;" role="status" id="resendSpinner">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" onclick="checkCode();">Confirmar</button>
                </div>
            </div>
        </div>
    </div>
</div>


@php
    $resendEmailTimeSeconds = App\Helpers\SystemHelper::getAuthCodeResendTimeSeconds();
@endphp

@push('js')
    <script type="text/javascript">
        const RESEND_EMAIL_TIME_SECONDS = {{ $resendEmailTimeSeconds }};
        $(document).ready(function () {
            const modalId = '{{ $modalId }}'
            $(`#${modalId}`).on('shown.bs.modal', function () {
                resetTimer();
            });
        });

        let interval;
        function resetTimer() {
            let timeLeft = RESEND_EMAIL_TIME_SECONDS - 1;
            const timer = document.getElementById('resendTimer');
            const resendButton = document.getElementById('resendButton');

            if (!resendButton.classList.contains('disabled'))
                resendButton.classList.add('disabled');

            if (interval){
                clearInterval(interval);
            }

            // Definir exibição inicial do timer
            const initialMinutes = String(Math.floor(RESEND_EMAIL_TIME_SECONDS / 60)).padStart(2, '0');
            const initialSeconds = String(RESEND_EMAIL_TIME_SECONDS % 60).padStart(2, '0');
            timer.textContent = `${initialMinutes}:${initialSeconds}`;

            interval = setInterval(() => {
                if (timeLeft <= 0) {
                    clearInterval(interval);
                    resendButton.classList.remove('disabled');
                    timer.textContent = '00:00';
                } else {
                    const minutes = String(Math.floor(timeLeft / 60)).padStart(2, '0');
                    const seconds = String(timeLeft % 60).padStart(2, '0');
                    timer.textContent = `${minutes}:${seconds}`;
                }
                timeLeft -= 1;
            }, 1000);
        }

        function checkCode() {
            const modal = document.getElementById('{{ $modalId }}');
            const code = document.getElementById('code').value;

            if (code == modal.dataset.code) {
                const form = document.getElementById(modal.dataset.formId);
                $('#email_validated').val('1');
                $('#{{ $modalId }}').modal('hide');
                const loading = document.getElementById('loading');
                if(loading != null) {
                    $('#loading').modal('show');
                }
                form.submit();
            } else {
                const alert = document.getElementById('alert-code-confirm-email');
                alert.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show">
                    <button type="button" aria-hidden="true" class="close" data-dismiss="alert" aria-label="Close">
                        <i class="nc-icon nc-simple-remove"></i>
                    </button>
                    <span>Código inválido</span>
                </div>
            `;
            }
        }

        function removeAlert() {
            const alert = document.getElementById('alert-code-confirm-email');
            alert.innerHTML = '';
        }

        function resendConfirmEmail() {
            const modal = document.getElementById('{{ $modalId }}');
            const formId = modal.dataset.formId;
            const email = modal.dataset.email;
            const name = modal.dataset.name;
            const form = document.getElementById(formId);

            if (email) {
                const formData = new FormData();
                formData.append('email', email);
                formData.append('name', name);

                $('#resendSpinner').show();
                $('#resendButtonText').hide();
                $('#resendButton').prop('disabled', true)

                $.ajax({
                    url: "<?= route('send.email.confirm') ?>",
                    type: 'post',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    beforeSend: function (xhr) {},
                    success: function (response) {
                        const code = response.code;
                        modal.dataset.code = code;
                        modal.dataset.formId = formId;

                        $('#resendButton').prop('disabled', false)
                        $('#resendButtonText').show();
                        $('#resendSpinner').hide();
                        resetTimer();
                    },
                    error: function (response, textStatus, msg) {
                        $('#resendButton').prop('disabled', false)
                        $('#resendButtonText').show();
                        $('#resendSpinner').hide();
                        resetTimer();
                        alert('Falha ao enviar email de confirmação!');
                    }
                });
            }
        }

        setInterval(removeAlert, 5000);
    </script>
@endpush
