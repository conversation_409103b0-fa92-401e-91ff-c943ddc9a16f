@props([
    'title' => 'Compartilhar',
    'modalTitle' => 'Compartilhar notícia',
    'messageText',
    'url'
  ])
<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#shareModal">
    {{$title}}
</button>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>


<div class="modal modal-share fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">{{$modalTitle}}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Compartilhe esse link via:</p>
                <div class="d-flex align-items-center icons">
                    <a href="https://api.whatsapp.com/send?text={{$messageText.' '.$url}}" target="_blank" class="fs-5 d-flex align-items-center justify-content-center">
                        <span class="fab fa-whatsapp"></span>
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{$url}}" target="_blank" class="fs-5 d-flex align-items-center justify-content-center">
                        <span class="fab fa-facebook-f"></span>
                    </a>

                    <a href="https://twitter.com/intent/tweet?text={{$messageText.' '.$url}}" target="_blank" class="fs-5 d-flex align-items-center justify-content-center">
                        <span class="fab fa-twitter"></span>
                    </a>

                    <a href="https://t.me/share/url?url={{$url}}&text={{$messageText}}" target="_blank" class="fs-5 d-flex align-items-center justify-content-center">
                        <span class="fab fa-telegram-plane"></span>
                    </a>
{{--                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{$url}}" target="_blank" class="fs-5 d-flex align-items-center justify-content-center">--}}
{{--                        <span class="fab fa-linkedin-in"></span>--}}
{{--                    </a>--}}
                </div>
                <p>Or copy link</p>
                <div class="field d-flex align-items-center justify-content-between">
                    <span class="fas fa-link text-center"></span>
                    <input id="copy_input" type="text" value="{{$url}}">
                    <button id="copy_btn" onclick="myFunction()" class="fas fa-copy"></button>
                    <div id="copied">Copiado</div>
                    <div id="arrow"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

    #copied{
        display: none;
        position: absolute;
        bottom: 55px;
        right: 10px;
        background-color: #7d2ae8;
        color: white;
        padding: 10px;
        border-radius: 10px;
        box-shadow: 0px 0px 5px 0px #00000040;
    }
    #arrow{
        display: none;
        position: absolute;
        bottom: 45px;
        right: 35px;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 10px solid #7d2ae8;
    }

    .modal-share{
        top: 20%;
    }

    .modal-body .field{
        margin: 15px 0px -5px 0px;
        height: 45px;
        border: 1px solid #dfdfdf;
        border-radius: 5px;
        padding: 0 5px;
    }

    .modal-body .field.active{
        border-color: #7d2ae8;
    }

    .field span{
        width: 50px;
        font-size: 1.1rem;
    }

    .field.active span{
        color: #7d2ae8;
    }

    .field input{
        border: none;
        outline: none;
        font-size: 0.89rem;
        width: 100%;
        height: 100%;
    }

    .field button{
        padding: 5px 16px;
        color: #fff;
        background: #7d2ae8;
        border: 2px solid transparent;
        border-radius: 5px;
        font-weight: 500;
    }

    .field button:active{
        background: #7114de;
    }

    @media (max-width: 330px) {
        .modal-body .icons a{
            margin-right: 15px;
            width: 35px;
            height: 35px;
        }
    }

    .modal-body .icons{
        margin: 15px 0px 20px 0px;
    }

    .modal-body .icons a{
        text-decoration: none;
        border: 1px solid transparent;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 20px;
        transition: all 0.3s ease-in-out;
    }

    .modal-body .icons a:nth-child(1){
        color: #25d366;
        border-color: #bef4d2;
    }

    .modal-body .icons a:nth-child(1):hover{
        background-color: #25d366;
        color: #fff;
    }



    .modal-body .icons a:nth-child(2){
        color: #46C1F6;
        border-color: #b6e7fc;
    }

    .modal-body .icons a:nth-child(2):hover{
        background-color: #46C1F6;
        color: #fff;
    }


    .modal-body .icons a:nth-child(3){
        color: #1877F2;
        border-color: #B7D4FB;
    }

    .modal-body .icons a:nth-child(3):hover{
        background-color: #1877F2;
        color: #fff;
    }

    .modal-body .icons a:nth-child(4){
        color: #0088cc;
        border-color: #b3e6ff;
    }

    .modal-body .icons a:nth-child(4):hover{
        background-color: #0088cc;
        color: #fff;
    }
</style>

<script>
    function myFunction() {
        $('#copy_input').select();
        document.execCommand("copy");
        window.getSelection().removeAllRanges();
        $('#copied').show();
        $('#arrow').show();
        setTimeout(function(){
            $('#copied').hide();
            $('#arrow').hide();
        }, 2000);
    }
</script>


