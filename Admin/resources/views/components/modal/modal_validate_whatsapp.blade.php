@php
    $resendWhatsappTimeSeconds = App\Helpers\SystemHelper::getAuthCodeResendTimeSeconds();
@endphp

<div class="modal fade" id="modalValidateWhatsapp" tabindex="-1" aria-labelledby="modalValidateWhatsappLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false" data-resend-time="{{ $resendWhatsappTimeSeconds }}">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalValidateWhatsappLabel">Validar Número de Telefone</h5>
            </div>
            <div class="modal-body">
                <p id="whatsappValidationIntro">Para validar o seu número de telefone, enviaremos um código de verificação via WhatsApp.</p>

                <div id="whatsappInitialActions">
                    <button type="button" class="btn btn-primary w-100" id="btnSendWhatsappCode">Enviar Código de Verificação</button>
                </div>

                <div id="whatsappCodeEntrySection" style="display: none;">
                    <p id="whatsappCodeSentMessage">Um código de 6 dígitos foi enviado para o seu WhatsApp. Por favor, insira-o abaixo:</p>
                    <div class="form-group">
                        <label for="whatsappVerificationCode">Código de Verificação</label>
                        <input type="text" class="form-control" id="whatsappVerificationCode" placeholder="_ _ _ _ _ _" maxlength="6">
                        <small id="whatsappVerificationCodeError" class="text-danger"></small>
                    </div>
                    <button type="button" class="btn btn-success w-100 mt-2" id="btnConfirmWhatsappCode">Confirmar Código</button>

                    <div class="mt-3 text-center">
                        <button type="button" class="btn btn-link" id="btnResendWhatsappCode" disabled>Reenviar código</button>
                        <span id="whatsappResendTimer">(aguarde <span id="whatsappResendCountdown"></span>s)</span>
                    </div>
                </div>

                <div id="whatsappValidationFeedback" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>

@push('js')
<script>
$(document).ready(function() {
    // ========================================
    // VARIÁVEIS GLOBAIS DO MODAL WHATSAPP
    // ========================================
    const whatsappModal = $('#modalValidateWhatsapp');
    const btnSendCode = $('#btnSendWhatsappCode');
    const codeEntrySection = $('#whatsappCodeEntrySection');
    const whatsappVerificationCodeInput = $('#whatsappVerificationCode');
    const btnConfirmCode = $('#btnConfirmWhatsappCode');
    const btnResendCode = $('#btnResendWhatsappCode');
    const resendCountdownSpan = $('#whatsappResendCountdown');
    const whatsappValidationFeedback = $('#whatsappValidationFeedback');
    const whatsappInitialActions = $('#whatsappInitialActions');
    const whatsappCodeSentMessage = $('#whatsappCodeSentMessage');
    const whatsappVerificationCodeError = $('#whatsappVerificationCodeError');
    const isHomologation = '{{ config("app.env") }}' === 'homologation';

    // Função para logs condicionais
    function debugLog(message, data = null) {
        if (isHomologation) {
            if (data) {
                console.log(message, data);
            } else {
                console.log(message);
            }
        }
    }

    let resendTimerInterval;
    let countdown = parseInt(whatsappModal.data('resend-time')) || 180;

    // ========================================
    // FUNÇÃO PARA GERENCIAR TIMER DE REENVIO
    // ========================================
    function startResendTimer() {
        countdown = parseInt(whatsappModal.data('resend-time')) || 180;
        btnResendCode.prop('disabled', true).text('Reenviar código');
        resendCountdownSpan.text(countdown);
        $('#whatsappResendTimer').show();

        resendTimerInterval = setInterval(() => {
            countdown--;
            resendCountdownSpan.text(countdown);
            if (countdown <= 0) {
                clearInterval(resendTimerInterval);
                btnResendCode.prop('disabled', false).text('Reenviar código');
                $('#whatsappResendTimer').hide();
            }
        }, 1000);
    }

    // ========================================
    // EVENTOS DO MODAL
    // ========================================
    
    /**
     * Evento para abrir o modal - reseta o estado
     */
    whatsappModal.on('show.bs.modal', function (event) {
        debugLog('[WhatsApp Modal] Modal de validação sendo aberto');
        debugLog('[WhatsApp Modal] Dados do modal:', {
            phoneNumber: whatsappModal.data('phoneNumber'),
            phoneInputId: whatsappModal.data('phoneInputId')
        });
        
        // Resetar o estado do modal ao abrir
        whatsappInitialActions.show();
        codeEntrySection.hide();
        whatsappValidationFeedback.empty();
        whatsappVerificationCodeInput.val('');
        whatsappVerificationCodeError.empty();
        btnSendCode.prop('disabled', false).text('Enviar Código de Verificação');
        btnConfirmCode.prop('disabled', false).text('Confirmar Código');
        btnResendCode.prop('disabled', true).text('Reenviar código');
        $('#whatsappResendTimer').hide();
        clearInterval(resendTimerInterval); // Limpa qualquer timer anterior
        
        debugLog('[WhatsApp Modal] Estado do modal resetado');
    });

    /**
     * Evento para fechar o modal - limpa dados e timers
     */
    whatsappModal.on('hidden.bs.modal', function (event) {
        debugLog('[WhatsApp Modal] Modal de validação foi fechado');
        whatsappVerificationCodeInput.val('');
        whatsappVerificationCodeError.empty();
        whatsappValidationFeedback.empty();
        clearInterval(resendTimerInterval); // Garante que o timer pare ao fechar
        
        // Dispara evento customizado para comunicar o fechamento
        debugLog('[WhatsApp Modal] Disparando evento whatsappModal:closed');
        $(document).trigger('whatsappModal:closed');
    });

    // ========================================
    // EVENTOS DOS BOTÕES
    // ========================================

    /**
     * Botão "Enviar Código de Verificação"
     */
    btnSendCode.on('click', function() {
        debugLog('[WhatsApp Modal] Botão "Enviar Código" clicado');
        const phoneNumber = whatsappModal.data('phoneNumber');
        debugLog('[WhatsApp Modal] Número de telefone para validação:', phoneNumber);
        
        if (!phoneNumber) {
            console.error('[WhatsApp Modal] Erro: Número de telefone não encontrado');
            whatsappValidationFeedback.text('Erro: Número de telefone não encontrado.').removeClass('text-success').addClass('text-danger');
            return;
        }

        debugLog('[WhatsApp Modal] Enviando requisição para iniciar validação');
        $(this).prop('disabled', true).text('Enviando...');
        whatsappValidationFeedback.empty(); // Limpa mensagens anteriores

        $.ajax({
            url: `<?= env('API_URL') ?>/api/user/start-phone-validation`,
            type: 'POST',
            data: JSON.stringify({ phone_number: phoneNumber }),
            contentType: 'application/json',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                debugLog('[WhatsApp Modal] Enviando requisição AJAX para start-phone-validation');
            },
            success: function (response) {
                debugLog('[WhatsApp Modal] Código enviado com sucesso:', response);
                whatsappInitialActions.hide();
                codeEntrySection.show();
                whatsappCodeSentMessage.text('Código de 6 dígitos enviado para o seu WhatsApp. Por favor, insira-o abaixo:').removeClass('text-danger').addClass('text-success');
                whatsappVerificationCodeInput.focus();
                startResendTimer(); // Inicia o timer após o envio bem-sucedido
                
                // Dispara evento customizado
                debugLog('[WhatsApp Modal] Disparando evento whatsappModal:codeSent');
                $(document).trigger('whatsappModal:codeSent', { phoneNumber: phoneNumber });
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error('[WhatsApp Modal] Erro ao enviar código:', {
                    status: jqXHR.status,
                    responseText: jqXHR.responseText,
                    textStatus: textStatus,
                    errorThrown: errorThrown
                });
                btnSendCode.prop('disabled', false).text('Enviar Código de Verificação'); // Reabilita o botão
                let errorMsg = "Falha ao solicitar código de validação.";
                if(jqXHR.responseJSON && jqXHR.responseJSON.message){
                    errorMsg = jqXHR.responseJSON.message;
                }
                whatsappValidationFeedback.text(errorMsg).removeClass('text-success').addClass('text-danger');
                
                // Dispara evento customizado de erro
                debugLog('[WhatsApp Modal] Disparando evento whatsappModal:error (sendCode)');
                $(document).trigger('whatsappModal:error', {
                    type: 'sendCode',
                    message: errorMsg,
                    phoneNumber: phoneNumber
                });
            }
        });
    });

    /**
     * Botão "Confirmar Código"
     */
    btnConfirmCode.on('click', function() {
        debugLog('[WhatsApp Modal] Botão "Confirmar Código" clicado');
        const validationCode = whatsappVerificationCodeInput.val();
        const phoneNumber = whatsappModal.data('phoneNumber');
        const phoneInputId = whatsappModal.data('phoneInputId');
        const currentClientCpf = window.currentClientCpf || '';

        debugLog('[WhatsApp Modal] Dados para confirmação:', {
            validationCode: validationCode,
            phoneNumber: phoneNumber,
            phoneInputId: phoneInputId,
            currentClientCpf: currentClientCpf
        });

        if (!validationCode || validationCode.length !== 6) {
            console.error('[WhatsApp Modal] Código inválido: deve ter 6 dígitos');
            whatsappVerificationCodeError.text('Por favor, insira um código de 6 dígitos.');
            return;
        }

        debugLog('[WhatsApp Modal] Enviando requisição para verificar código');
        whatsappVerificationCodeError.empty(); // Limpa erros anteriores do campo
        whatsappValidationFeedback.empty(); // Limpa mensagens gerais anteriores
        $(this).prop('disabled', true).text('Confirmando...');

        $.ajax({
            url: `<?= env('API_URL') ?>/api/user/verify-phone-validation-code`,
            type: 'POST',
            data: JSON.stringify({
                phone_number: phoneNumber,
                validation_code: validationCode,
                current_cpf: currentClientCpf
            }),
            contentType: 'application/json',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                debugLog('[WhatsApp Modal] Enviando requisição AJAX para verify-phone-validation-code');
            },
            success: function (response) {
                debugLog('[WhatsApp Modal] Código confirmado com sucesso:', response);
                clearInterval(resendTimerInterval); // Para o timer em caso de sucesso
                whatsappModal.modal('hide'); // Fecha o modal

                // Dispara evento customizado de sucesso com os dados necessários
                debugLog('[WhatsApp Modal] Disparando evento whatsappModal:validationSuccess');
                $(document).trigger('whatsappModal:validationSuccess', {
                    phoneNumber: phoneNumber,
                    phoneInputId: phoneInputId,
                    validationCode: validationCode,
                    response: response
                });
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error('[WhatsApp Modal] Erro ao confirmar código:', {
                    status: jqXHR.status,
                    responseText: jqXHR.responseText,
                    textStatus: textStatus,
                    errorThrown: errorThrown
                });
                btnConfirmCode.prop('disabled', false).text('Confirmar Código');
                let errorMsg = 'Falha ao verificar código.';
                if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                    errorMsg = jqXHR.responseJSON.message;
                }
                whatsappValidationFeedback.text(errorMsg).removeClass('text-success').addClass('text-danger');
                
                // Dispara evento customizado de erro
                debugLog('[WhatsApp Modal] Disparando evento whatsappModal:error (confirmCode)');
                $(document).trigger('whatsappModal:error', {
                    type: 'confirmCode',
                    message: errorMsg,
                    phoneNumber: phoneNumber,
                    phoneInputId: phoneInputId
                });
            }
        });
    });

    /**
     * Botão "Reenviar código"
     */
    btnResendCode.on('click', function() {
        debugLog('[WhatsApp Modal] Botão "Reenviar código" clicado');
        const phoneNumber = whatsappModal.data('phoneNumber');
        debugLog('[WhatsApp Modal] Número para reenvio:', phoneNumber);
        
        if (!phoneNumber) {
            console.error('[WhatsApp Modal] Erro: Número de telefone não encontrado para reenvio');
            whatsappValidationFeedback.text('Erro: Número de telefone não encontrado para reenvio.').removeClass('text-success').addClass('text-danger');
            return;
        }

        debugLog('[WhatsApp Modal] Enviando requisição para reenviar código');
        $(this).prop('disabled', true).text('Enviando...');
        whatsappValidationFeedback.empty(); // Limpa mensagens anteriores
        whatsappCodeSentMessage.text('Enviando novo código...').removeClass('text-success text-danger'); // Mensagem de status

        $.ajax({
            url: `<?= env('API_URL') ?>/api/user/start-phone-validation`,
            type: 'POST',
            data: JSON.stringify({ phone_number: phoneNumber }),
            contentType: 'application/json',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                debugLog('[WhatsApp Modal] Enviando requisição AJAX para reenviar código');
            },
            success: function (response) {
                debugLog('[WhatsApp Modal] Código reenviado com sucesso:', response);
                whatsappCodeSentMessage.text('Novo código de 6 dígitos enviado para o seu WhatsApp.').removeClass('text-danger').addClass('text-success');
                whatsappVerificationCodeInput.val(''); // Limpa o campo de código
                whatsappVerificationCodeError.empty(); // Limpa erro do campo
                startResendTimer(); // Reinicia o timer
                
                // Dispara evento customizado
                debugLog('[WhatsApp Modal] Disparando evento whatsappModal:codeResent');
                $(document).trigger('whatsappModal:codeResent', { phoneNumber: phoneNumber });
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error('[WhatsApp Modal] Erro ao reenviar código:', {
                    status: jqXHR.status,
                    responseText: jqXHR.responseText,
                    textStatus: textStatus,
                    errorThrown: errorThrown
                });
                btnResendCode.prop('disabled', false).text('Reenviar código'); // Reabilita o botão
                let errorMsg = "Falha ao solicitar novo código de validação.";
                if(jqXHR.responseJSON && jqXHR.responseJSON.message){
                    errorMsg = jqXHR.responseJSON.message;
                }
                whatsappValidationFeedback.text(errorMsg).removeClass('text-success').addClass('text-danger');
                whatsappCodeSentMessage.text('Falha no reenvio.').removeClass('text-success').addClass('text-danger'); // Atualiza mensagem de status
                
                // Dispara evento customizado de erro
                debugLog('[WhatsApp Modal] Disparando evento whatsappModal:error (resendCode)');
                $(document).trigger('whatsappModal:error', {
                    type: 'resendCode',
                    message: errorMsg,
                    phoneNumber: phoneNumber
                });
            }
        });
    });

    // ========================================
    // EVENTO PARA PRESSIONAR ENTER NO CAMPO DE CÓDIGO
    // ========================================
    
    /**
     * Evento para pressionar Enter no campo de código de verificação
     */
    whatsappVerificationCodeInput.on('keydown', function(e) {
        if (e.key === 'Enter' || e.keyCode === 13) {
            debugLog('[WhatsApp Modal] Enter pressionado no campo de código - clicando no botão confirmar');
            e.preventDefault(); // Previne o comportamento padrão do Enter
            btnConfirmCode.click(); // Simula o clique no botão de confirmar
        }
    });

    // ========================================
    // INTERFACE DE COMUNICAÇÃO COM FORMULÁRIO
    // ========================================

    /**
     * Função pública para abrir o modal com dados do formulário
     * @param {string} phoneNumber - Número de telefone a ser validado
     * @param {string} phoneInputId - ID do input de telefone no formulário
     */
    window.openWhatsappValidationModal = function(phoneNumber, phoneInputId) {
        if (!phoneNumber || !phoneInputId) {
            console.error('openWhatsappValidationModal: phoneNumber e phoneInputId são obrigatórios');
            return;
        }

        whatsappModal.data('phoneNumber', phoneNumber);
        whatsappModal.data('phoneInputId', phoneInputId);
        whatsappModal.modal('show');
    };

    /**
     * Função pública para verificar se um número está validado
     * @param {string} phoneNumber - Número a verificar
     * @returns {boolean} - True se o número está validado
     */
    window.isPhoneNumberValidated = function(phoneNumber) {
        // Esta função pode ser implementada pelo formulário pai
        // Por padrão, verifica o campo phone_validated
        return $('#phone_validated').val() === '1';
    };

    // ========================================
    // EVENTOS CUSTOMIZADOS DISPONÍVEIS
    // ========================================
    /*
     * whatsappModal:codeSent - Disparado quando código é enviado com sucesso
     * whatsappModal:codeResent - Disparado quando código é reenviado com sucesso
     * whatsappModal:validationSuccess - Disparado quando validação é bem-sucedida
     * whatsappModal:error - Disparado em caso de erro
     * whatsappModal:closed - Disparado quando modal é fechado
     */
});
</script>
@endpush
