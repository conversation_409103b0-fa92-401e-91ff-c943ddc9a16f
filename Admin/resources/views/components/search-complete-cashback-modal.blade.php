@php
    use App\Helpers\ApiUser;
    use App\Helpers\SystemHelper;
    
    $user = ApiUser::get();
    $parameters = $user['business_selected']['parameters'];
    $plan = $user['business_selected']['plan'];
    
    $canSendEmail = $plan['authorizedFunctions']['email_sending'] ?? false;
    $canSendWhatsapp = $plan['authorizedFunctions']['whatsapp_sending'] ?? false;
    $whatsappConfigured = !empty($parameters['whatsapp_phone_number']) && !empty($parameters['whatsapp_instance_key']);
    
    $resendCashbackTimeSeconds = SystemHelper::getAuthCodeResendTimeSeconds();
@endphp

{{-- Modal para Buscar e Completar Cashback do Cliente --}}
<style>
/* Animações personalizadas */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes zoomIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.shake {
    animation: shake 0.6s ease-in-out;
}

.pulse {
    animation: pulse 0.6s ease-in-out;
}

.zoom-in {
    animation: zoomIn 0.5s ease-out;
}

.fade-in-up {
    animation: fadeInUp 0.4s ease-out;
}

/* Spinner personalizado */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    border-radius: 0.25rem;
}

/* Success screen */
.success-screen {
    text-align: center;
    padding: 2rem;
}

.success-icon {
    font-size: 4rem;
    color: #28a745;
    margin-bottom: 1rem;
}

/* Button loading state */
.btn-loading {
    pointer-events: none;
}

.btn-loading .btn-text {
    display: none !important;
}

.btn-loading .btn-loading-content {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
}

/* Focus styles */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Transition smooth */
.form-control {
    transition: all 0.3s ease;
}

.alert {
    margin-bottom: 0;
}

/* Estilo para botão desabilitado */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-secondary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Estilos simplificados para o card de cashback */
.cashback-card {
    border: 1px solid #28a745 !important;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
}

.cashback-card .card-body {
    padding: 1.5rem;
}

.cashback-amount {
    background: #28a745;
    color: white;
    font-size: 1.4rem;
    font-weight: 700;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    display: inline-block;
    margin: 0.5rem 0 1rem;
}

.method-selection {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.5rem;
    margin: 0.5rem 0;
    border: 1px solid #e9ecef;
}

.method-selection label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.method-selection select {
    border-radius: 6px;
    padding: 0.5rem;
}

/* Navegação */
.step-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.btn-step-back {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-step-back:hover {
    background-color: #5a6268;
    border-color: #545b62;
    color: white;
}
</style>

<div class="modal fade" id="buscarCashbackModal" tabindex="-1" aria-labelledby="buscarCashbackModalLabel">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="buscarCashbackModalLabel">
                    <i class="fas fa-search mr-2"></i>Buscar Cashback do Cliente
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body position-relative">
                <!-- Loading Overlay -->
                <div id="loadingOverlay" class="loading-overlay d-none">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Carregando...</span>
                        </div>
                        <div class="mt-2 text-muted">Processando...</div>
                    </div>
                </div>


                <!-- Etapa 1: Formulário de Busca -->
                <div id="etapa1" class="step-content">
                    <form id="formBuscar" action="{{ route('cashbacks.index') }}" method="GET">
                    <div class="mb-3">
                        <label for="buscarPor" class="form-label">
                            Buscar por:
                        </label>
                        <select id="buscarPor" name="search_type" class="form-control">
                            <option value="phone">Telefone</option>
                            <option value="cpf">CPF</option>
                            <option value="email">E-mail</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="valorBusca" class="form-label" id="labelValorBusca">
                            Digite o Telefone
                        </label>
                        <input type="text" id="valorBusca" name="search" class="form-control" placeholder="Digite o Telefone" />
                        <div class="invalid-feedback" id="feedbackValorBusca">
                            Por favor, informe um valor válido.
                        </div>
                    </div>
                    <button type="button" id="btnBuscar" class="btn btn-primary btn-block">
                        <span class="btn-text">
                            <i class="fas fa-search mr-2"></i>Buscar Cashback
                        </span>
                        <span class="btn-loading-content d-none">
                            <div class="spinner-border spinner-border-sm mr-2" role="status">
                                <span class="sr-only">Carregando...</span>
                            </div>
                            Buscando...
                        </span>
                    </button>
                </form>
            </div>

            <!-- Etapa 2: Resultado e Confirmação -->
            <div id="etapa2" class="step-content d-none">
                <div class="card cashback-card">
                    <div class="card-body text-center">
                        <i class="fas fa-coins text-success mb-2" style="font-size: 2rem;"></i>
                        <h5 class="card-title text-success mb-2">Cashback Encontrado!</h5>
                        <div class="mb-3">
                            <p class="mb-1">
                                <strong>Cliente:</strong> <span class="text-primary font-weight-bold" id="nomeCliente">-</span>
                            </p>
                        </div>
                        <p class="mb-2">
                            <strong>Total disponível:</strong>
                        </p>
                        <div class="cashback-amount" id="totalCashback">
                            R$ 0,00
                        </div>

                        <!-- Seleção do método de envio -->
                        <div class="method-selection">
                            <label for="metodoEnvio" class="form-label d-block">
                                <i class="fas fa-paper-plane mr-1"></i>
                                Método de envio do código:
                            </label>
                            <select id="metodoEnvio" class="form-control" style="max-width: 250px; margin: 0 auto;">
                                <option value="">Selecione o método</option>
                                <option value="whatsapp">WhatsApp</option>
                                <option value="email">E-mail</option>
                                <option value="both">WhatsApp e E-mail</option>
                            </select>
                            <div id="metodoEnvioWarning" class="mt-2 text-warning text-center d-none">
                                <small><i class="fas fa-exclamation-triangle mr-1"></i>Nenhum método de envio disponível para este cliente</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navegação da Etapa 2 -->
                <div class="step-navigation">
                    <button type="button" id="btnVoltarEtapa1" class="btn btn-step-back">
                        <i class="fas fa-arrow-left mr-2"></i>Voltar
                    </button>
                    <button type="button" id="btnEnviarCodigo" class="btn btn-success" disabled>
                        <span class="btn-text">
                            <i class="fas fa-paper-plane mr-2"></i>Enviar Código
                        </span>
                        <span class="btn-loading-content d-none">
                            <div class="spinner-border spinner-border-sm mr-2" role="status">
                                <span class="sr-only">Enviando...</span>
                            </div>
                            Enviando...
                        </span>
                    </button>
                </div>
            </div>

            <!-- Etapa 3: Código de Segurança -->
            <div id="etapa3" class="step-content d-none">
                <div class="card border-warning">
                    <div class="card-body">
                        <h6 class="card-title text-center">
                            <i class="fas fa-shield-alt mr-2 text-warning"></i>Confirmação de Segurança
                        </h6>
                        <p class="text-center text-muted mb-3">
                            Digite o código de 6 dígitos enviado para o cliente:
                        </p>
                        <div class="mb-3">
                            <label for="codigoConfirmacao" class="form-label">
                                Código de Confirmação
                            </label>
                            <div class="input-group">
                                <input type="text" id="codigoConfirmacao" class="form-control text-center"
                                       placeholder="000000" maxlength="6" style="letter-spacing: 0.5rem; font-weight: bold;" />
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="invalid-feedback d-block" id="feedbackCodigoConfirmacao"></div>
                        </div>

                        <!-- Botão de reenvio de código -->
                        <div class="text-center mt-3">
                            <div class="w-100 d-flex align-items-center justify-content-center" style="gap: 8px">
                                <i class="fas fa-clock fa-fw"></i>
                                <span id="resendCashbackTimer"></span>
                                <button type="button" class="btn btn-link disabled" id="resendCashbackButton" onclick="resendCashbackValidationCode()">
                                    <span id="resendCashbackButtonText">Reenviar código</span>
                                    <div class="spinner-border text-secondary spinner-border-sm" style="display: none;" role="status" id="resendCashbackSpinner">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navegação da Etapa 3 -->
                <div class="step-navigation">
                    <button type="button" id="btnVoltarEtapa2" class="btn btn-step-back">
                        <i class="fas fa-arrow-left mr-2"></i>Voltar
                    </button>
                    <button type="button" id="btnConfirmarCodigo" class="btn btn-success">
                        <span class="btn-text">
                            <i class="fas fa-check-circle mr-2"></i>Confirmar
                        </span>
                        <span class="btn-loading-content d-none">
                            <div class="spinner-border spinner-border-sm mr-2" role="status">
                                <span class="sr-only">Processando...</span>
                            </div>
                            Processando...
                        </span>
                    </button>
                </div>
            </div>

                <!-- Tela de Sucesso -->
                <div id="successScreen" class="d-none">
                    <div class="success-screen zoom-in">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4 class="text-success mb-3">Sucesso!</h4>
                        <p class="text-muted mb-4">Cashback processado com sucesso!</p>
                        <button type="button" class="btn btn-outline-success" onclick="closeCashbackModalAndReload()">
                            <i class="fas fa-times mr-2"></i>Fechar
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Configurações de permissões e WhatsApp
    const canSendEmail = {{ $canSendEmail ? 'true' : 'false' }};
    const canSendWhatsapp = {{ $canSendWhatsapp ? 'true' : 'false' }};
    const whatsappConfigured = {{ $whatsappConfigured ? 'true' : 'false' }};
    const RESEND_CASHBACK_TIME_SECONDS = {{ $resendCashbackTimeSeconds }};

    // Referencias
    const modal = $('#buscarCashbackModal');
    const buscarPor = $('#buscarPor');
    const labelValorBusca = $('#labelValorBusca');
    const valorBusca = $('#valorBusca');
    const btnBuscar = $('#btnBuscar');
    const resultadoBusca = $('#resultadoBusca');
    const totalCashback = $('#totalCashback');
    const nomeCliente = $('#nomeCliente');
    const metodoEnvio = $('#metodoEnvio');
    const metodoEnvioWarning = $('#metodoEnvioWarning');
    const btnEnviarCodigo = $('#btnEnviarCodigo');
    const formConfirmarCodigo = $('#formConfirmarCodigo');
    const btnConfirmarCodigo = $('#btnConfirmarCodigo');
    const feedbackValorBusca = $('#feedbackValorBusca');
    const formBuscar = $('#formBuscar');
    const loadingOverlay = $('#loadingOverlay');
    const successScreen = $('#successScreen');
    const codigoConfirmacao = $('#codigoConfirmacao');
    const feedbackCodigoConfirmacao = $('#feedbackCodigoConfirmacao');

    // Stepper elements
    const etapa1 = $('#etapa1');
    const etapa2 = $('#etapa2');
    const etapa3 = $('#etapa3');
    const btnVoltarEtapa1 = $('#btnVoltarEtapa1');
    const btnVoltarEtapa2 = $('#btnVoltarEtapa2');

    // Elementos de reenvio de código
    const resendCashbackTimer = $('#resendCashbackTimer');
    const resendCashbackButton = $('#resendCashbackButton');
    const resendCashbackButtonText = $('#resendCashbackButtonText');
    const resendCashbackSpinner = $('#resendCashbackSpinner');

    // Controle de etapas
    let etapaAtual = 1;

    // Variáveis para controle do timer de reenvio
    let resendCashbackInterval;

    // Estados de validação em tempo real
    let realTimeValidation = true;
    let validationTimeout;

    function irParaEtapa(novaEtapa) {

        // Verificar se a etapa atual é a mesma da nova etapa 
        if (etapaAtual === novaEtapa) {
            return; // Não fazer nada se já estiver na etapa atual
        }

        // Ocultar todas as etapas
        etapa1.addClass('d-none');
        etapa2.addClass('d-none');
        etapa3.addClass('d-none');

        // Mostrar etapa atual
        const etapaElement = $(`#etapa${novaEtapa}`);
        etapaElement.removeClass('d-none');

        // Adicionar animação apenas se não for a etapa 1 (inicial)
        if (novaEtapa !== 1) {
            etapaElement.addClass('fade-in-up');
        }

        // Atualizar variável de controle
        etapaAtual = novaEtapa;
    }

    // Função para resetar modal
    function resetModal() {
        // Ir para etapa 1
        irParaEtapa(1);

        // Limpar formulários
        valorBusca.val('').removeClass('is-invalid is-valid');
        codigoConfirmacao.val('').removeClass('is-invalid is-valid');
        feedbackCodigoConfirmacao.text('');
        buscarPor.val('phone');
        metodoEnvio.val('');
        
        // Limpar dados exibidos
        nomeCliente.text('-');
        totalCashback.text('R$ 0,00');

        // Resetar select de método de envio para estado padrão
        metodoEnvio.empty();
        metodoEnvio.append('<option value="">Selecione o método</option>');
        metodoEnvio.append('<option value="whatsapp">WhatsApp</option>');
        metodoEnvio.append('<option value="email">E-mail</option>');
        metodoEnvio.append('<option value="both">WhatsApp e E-mail</option>');
        metodoEnvio.prop('disabled', false);
        metodoEnvioWarning.addClass('d-none');

        // Ocultar seções
        successScreen.addClass('d-none');
        loadingOverlay.addClass('d-none');

        // Resetar botões
        resetButton(btnBuscar);
        resetButton(btnEnviarCodigo);
        resetButton(btnConfirmarCodigo);

        // Desabilitar botão de enviar código inicialmente
        btnEnviarCodigo.prop('disabled', true);

        // Aplicar máscara inicial
        aplicarMascara();

        // Atualizar labels
        updateLabels();

        // Limpar dados globais
        window.dadosClienteCashback = null;
        window.lastSendMethod = null;
        
        // Resetar flag de sucesso da validação
        validationSuccess = false;

        // Limpar timer de reenvio
        if (resendCashbackInterval) {
            clearInterval(resendCashbackInterval);
        }
        resendCashbackTimer.text('');
        resendCashbackButton.addClass('disabled');
    }

    // Função para resetar estado do botão
    function resetButton(btn) {
        btn.removeClass('btn-loading');
        btn.find('.btn-text').removeClass('d-none');
        btn.find('.btn-loading-content').addClass('d-none');
        btn.prop('disabled', false);
    }

    // Função para mostrar loading no botão
    function showButtonLoading(btn) {
        btn.addClass('btn-loading');
        btn.find('.btn-text').addClass('d-none');
        btn.find('.btn-loading-content').removeClass('d-none');
        btn.prop('disabled', true);
    }

    // Função para mostrar shake animation
    function shakeElement(element) {
        element.addClass('shake');
        setTimeout(() => {
            element.removeClass('shake');
        }, 600);
    }

    // Função para mostrar pulse animation
    function pulseElement(element) {
        element.addClass('pulse');
        setTimeout(() => {
            element.removeClass('pulse');
        }, 600);
    }

    // Função para mostrar loading overlay
    function showLoadingOverlay() {
        loadingOverlay.removeClass('d-none');
    }

    // Função para ocultar loading overlay
    function hideLoadingOverlay() {
        loadingOverlay.addClass('d-none');
    }

    // Função para iniciar timer de reenvio de código
    window.startResendCashbackTimer = function() {
        let timeLeft = RESEND_CASHBACK_TIME_SECONDS - 1;
        const timer = resendCashbackTimer;
        const button = resendCashbackButton;

        if (!button.hasClass('disabled')) {
            button.addClass('disabled');
        }

        if (resendCashbackInterval) {
            clearInterval(resendCashbackInterval);
        }

        // Definir exibição inicial do timer
        const initialMinutes = String(Math.floor(RESEND_CASHBACK_TIME_SECONDS / 60)).padStart(2, '0');
        const initialSeconds = String(RESEND_CASHBACK_TIME_SECONDS % 60).padStart(2, '0');
        timer.text(`${initialMinutes}:${initialSeconds}`);

        resendCashbackInterval = setInterval(() => {
            if (timeLeft <= 0) {
                clearInterval(resendCashbackInterval);
                button.removeClass('disabled');
                timer.text('00:00');
            } else {
                const minutes = String(Math.floor(timeLeft / 60)).padStart(2, '0');
                const seconds = String(timeLeft % 60).padStart(2, '0');
                timer.text(`${minutes}:${seconds}`);
            }
            timeLeft -= 1;
        }, 1000);
    }

    // Função para atualizar opções de método de envio baseado nas permissões e dados do cliente
    function updateMetodoEnvioOptions() {
        metodoEnvio.empty();
        metodoEnvio.append('<option value="">Selecione o método</option>');
        metodoEnvioWarning.addClass('d-none');

        if (!window.dadosClienteCashback) {
            metodoEnvio.prop('disabled', true);
            return;
        }

        const hasClientEmail = window.dadosClienteCashback.client_email;
        const hasClientPhone = window.dadosClienteCashback.client_phone;
        
        let whatsappOptionEnabled = canSendWhatsapp && whatsappConfigured && hasClientPhone;
        let emailOptionEnabled = canSendEmail && hasClientEmail;
        
        let availableOptions = [];
        
        if (whatsappOptionEnabled) {
            metodoEnvio.append('<option value="whatsapp">WhatsApp</option>');
            availableOptions.push('whatsapp');
        }
        
        if (emailOptionEnabled) {
            metodoEnvio.append('<option value="email">E-mail</option>');
            availableOptions.push('email');
        }
        
        if (whatsappOptionEnabled && emailOptionEnabled) {
            metodoEnvio.append('<option value="both">WhatsApp e E-mail</option>');
            availableOptions.push('both');
        }
        
        // Se nenhuma opção estiver disponível
        if (!whatsappOptionEnabled && !emailOptionEnabled) {
            metodoEnvio.prop('disabled', true);
            metodoEnvioWarning.removeClass('d-none');
            btnEnviarCodigo.prop('disabled', true);
        } else {
            metodoEnvio.prop('disabled', false);
            
            // Seleção automática baseada no número de opções disponíveis
            if (availableOptions.length === 1) {
                // Se houver apenas uma opção, selecioná-la automaticamente
                metodoEnvio.val(availableOptions[0]);
                metodoEnvio.trigger('change'); // Disparar evento change para habilitar o botão
            } else if (availableOptions.length > 1) {
                // Se houver várias opções, selecionar a primeira
                metodoEnvio.val(availableOptions[0]);
                metodoEnvio.trigger('change'); // Disparar evento change para habilitar o botão
            }
        }
    }

    // Função para mostrar alerta de configuração do WhatsApp
    function showWhatsAppConfigAlert() {
        Swal.fire({
            icon: 'warning',
            title: 'WhatsApp Não Configurado',
            html: 'O envio por WhatsApp não está disponível pois o WhatsApp não foi configurado.<br>Por favor, configure-o nos parâmetros da empresa.',
            confirmButtonText: 'Entendi',
            @if(ApiUser::hasPermission('parameters'))
            showCancelButton: true,
            cancelButtonText: 'Configurar WhatsApp',
            cancelButtonColor: '#3085d6'
            @endif
        }).then((result) => {
            @if(ApiUser::hasPermission('parameters'))
            if (result.dismiss === Swal.DismissReason.cancel) {
                window.location.href = "{{ route('businesses.parameters') }}";
            }
            @endif
        });
    }

    // Atualizar labels e placeholders
    function updateLabels() {
        const tipo = buscarPor.val();
        const textos = {
            'phone': 'Digite o Telefone',
            'cpf': 'Digite o CPF',
            'email': 'Digite o E-mail'
        };

        const texto = textos[tipo];

        labelValorBusca.html(`${texto}`);
        valorBusca.attr('placeholder', texto);
    }

    // Aplica as máscaras conforme o tipo de busca
    function aplicarMascara() {
        const tipo = buscarPor.val();
        if (typeof valorBusca.unmask === 'function') {
            valorBusca.unmask();
        }
        valorBusca.removeClass('is-invalid is-valid real-time-valid real-time-invalid');

        if (tipo === 'cpf') {
            if (typeof valorBusca.mask === 'function') {
                valorBusca.mask('000.000.000-00');
            }
        } else if (tipo === 'phone') {
            if (typeof valorBusca.mask === 'function') {
                var SPMaskBehavior = function (val) {
                    return val.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
                },
                spOptions = {
                    onKeyPress: function(val, e, field, options) {
                        field.mask(SPMaskBehavior.apply({}, arguments), options);
                    }
                };
                valorBusca.mask(SPMaskBehavior, spOptions);
            }
        }
    }

    // Validação em tempo real
    function validarTempoReal() {
        if (!realTimeValidation) return;

        clearTimeout(validationTimeout);
        validationTimeout = setTimeout(() => {
            const tipo = buscarPor.val();
            const valor = valorBusca.val().trim();

            valorBusca.removeClass('is-valid is-invalid');

            if (!valor) return; // Não validar se estiver vazio

            // Só validar quando tiver todos os dígitos necessários
            let shouldValidate = false;

            if (tipo === 'cpf') {
                const cpfSemMascara = valor.replace(/\D/g, '');
                shouldValidate = cpfSemMascara.length === 11; // CPF completo
            } else if (tipo === 'phone') {
                const telefoneSemMascara = valor.replace(/\D/g, '');
                shouldValidate = telefoneSemMascara.length >= 10; // Telefone completo (10 ou 11 dígitos)
            } else if (tipo === 'email') {
                shouldValidate = true; // Email pode ser validado a qualquer momento
            }

            if (shouldValidate) {
                const isValid = validarValorBusca(false);
                if (isValid) {
                    valorBusca.addClass('is-valid');
                } else {
                    valorBusca.addClass('is-invalid');
                }
            }
        }, 300);
    }

    // Atualiza label conforme seleção
    buscarPor.on('change', function() {
        valorBusca.val('');
        resultadoBusca.addClass('d-none');
        formConfirmarCodigo.addClass('d-none');
        successScreen.addClass('d-none');

        valorBusca.removeClass('is-invalid is-valid');
        feedbackValorBusca.text('');

        updateLabels();
        aplicarMascara();

        // Auto-focus no input após mudança
        setTimeout(() => {
            valorBusca.focus();
        }, 100);
    });

    // Validação de CPF
    function cpfIsValid(cpf) {
        if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;

        let soma = 0;
        for (let i = 0; i < 9; i++) {
            soma += parseInt(cpf.charAt(i)) * (10 - i);
        }

        let resto = 11 - (soma % 11);
        if (resto === 10 || resto === 11) resto = 0;
        if (resto !== parseInt(cpf.charAt(9))) return false;

        soma = 0;
        for (let i = 0; i < 10; i++) {
            soma += parseInt(cpf.charAt(i)) * (11 - i);
        }

        resto = 11 - (soma % 11);
        if (resto === 10 || resto === 11) resto = 0;
        return resto === parseInt(cpf.charAt(10));
    }

    // Validação do valor de busca
    function validarValorBusca(showFeedback = true) {
        const tipo = buscarPor.val();
        const valor = valorBusca.val().trim();

        if (showFeedback) {
            valorBusca.removeClass('is-invalid is-valid');
            feedbackValorBusca.text('');
        }

        if (!valor) {
            if (showFeedback) {
                feedbackValorBusca.text('Por favor, preencha este campo.');
                valorBusca.addClass('is-invalid');
                shakeElement(valorBusca);
            }
            return false;
        }

        if (tipo === 'cpf') {
            const cpfSemMascara = valor.replace(/\D/g, '');
            if (cpfSemMascara.length !== 11 || !cpfIsValid(cpfSemMascara)) {
                if (showFeedback) {
                    feedbackValorBusca.text('Por favor, insira um CPF válido.');
                    valorBusca.addClass('is-invalid');
                    shakeElement(valorBusca);
                }
                return false;
            }
        } else if (tipo === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(valor)) {
                if (showFeedback) {
                    feedbackValorBusca.text('Por favor, insira um E-mail válido.');
                    valorBusca.addClass('is-invalid');
                    shakeElement(valorBusca);
                }
                return false;
            }
        } else if (tipo === 'phone') {
            const telefoneSemMascara = valor.replace(/\D/g, '');
            if (telefoneSemMascara.length < 10 || telefoneSemMascara.length > 11) {
                if (showFeedback) {
                    feedbackValorBusca.text('Por favor, insira um Telefone válido.');
                    valorBusca.addClass('is-invalid');
                    shakeElement(valorBusca);
                }
                return false;
            }
        }

        if (showFeedback) {
            valorBusca.addClass('is-valid');
            pulseElement(valorBusca);
        }
        return true;
    }

    // Validação do código
    function validarCodigo() {
        const codigo = codigoConfirmacao.val().trim();
        codigoConfirmacao.removeClass('is-invalid is-valid');

        if (!codigo) {
            codigoConfirmacao.addClass('is-invalid');
            shakeElement(codigoConfirmacao);
            return false;
        }

        if (codigo.length !== 6 || !/^\d{6}$/.test(codigo)) {
            codigoConfirmacao.addClass('is-invalid');
            shakeElement(codigoConfirmacao);
            return false;
        }

        return true;
    }

    // Event listeners para navegação
    btnVoltarEtapa1.on('click', function() {
        irParaEtapa(1);
    });

    btnVoltarEtapa2.on('click', function() {
        irParaEtapa(2);
    });

    // Variáveis globais para armazenar dados do cliente e cashbacks
    window.dadosClienteCashback = null;
    window.lastSendMethod = null;

    // Buscar cashback
    btnBuscar.on('click', function() {
        if (!validarValorBusca()) {
            return;
        }

        showButtonLoading(btnBuscar);

        // Fazer requisição real para a API
        $.ajax({
            url: '{{ route("cashbacks.search-pending-client") }}',
            method: 'POST',
            data: {
                search_type: buscarPor.val(),
                search: valorBusca.val(),
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                resetButton(btnBuscar);

                if (response.success && response.data) {
                    // Verificar se o total é maior que 0
                    if (response.data.totalAmount <= 0) {
                        // Mostrar mensagem de erro quando total for 0
                        shakeElement(valorBusca);
                        valorBusca.addClass('is-invalid');
                        feedbackValorBusca.text('Nenhum cashback disponível encontrado para este cliente.');
                        return;
                    }

                    // Armazenar dados para uso posterior - ajustar para nova estrutura
                    window.dadosClienteCashback = {
                        client_id: response.data.client_id,
                        client_name: response.data.client_name,
                        client_phone: response.data.client_phone,
                        client_email: response.data.client_email,
                        encrypted_cashback_ids: response.data.cashbacks.map(cb => cb.encrypted_id),
                        total_amount: response.data.totalAmount
                    };

                    // Atualizar interface com dados reais
                    totalCashback.text(`R$ ${response.data.totalAmount.toFixed(2).replace('.', ',')}`);
                    nomeCliente.text(response.data.client_name || 'Nome não informado');

                    // Atualizar opções de método de envio baseado nas permissões e dados do cliente
                    updateMetodoEnvioOptions();

                    // Ir para etapa 2
                    irParaEtapa(2);
                    pulseElement(etapa2);
                } else {
                    // Mostrar mensagem de erro ou nenhum cashback encontrado
                    shakeElement(valorBusca);
                    valorBusca.addClass('is-invalid');
                    feedbackValorBusca.text(response.message || 'Nenhum cashback pendente encontrado para este cliente.');
                }
            },
            error: function(xhr) {
                resetButton(btnBuscar);
                shakeElement(valorBusca);
                valorBusca.addClass('is-invalid');

                let errorMessage = 'Erro ao buscar cashbacks pendentes.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                feedbackValorBusca.text(errorMessage);
            }
        });
    });

    // Controlar habilitação do botão de enviar código baseado na seleção do método
    metodoEnvio.on('change', function() {
        const metodoSelecionado = $(this).val();
        let enableButton = false;

        if (metodoSelecionado && window.dadosClienteCashback) {
            const hasClientEmail = window.dadosClienteCashback.client_email;
            const hasClientPhone = window.dadosClienteCashback.client_phone;

            if (metodoSelecionado === 'whatsapp') {
                if (canSendWhatsapp && hasClientPhone) {
                    if (whatsappConfigured) {
                        enableButton = true;
                    } else {
                        showWhatsAppConfigAlert();
                        $(this).val(''); // Resetar seleção
                    }
                }
            } else if (metodoSelecionado === 'email') {
                if (canSendEmail && hasClientEmail) {
                    enableButton = true;
                }
            } else if (metodoSelecionado === 'both') {
                const whatsappPossible = canSendWhatsapp && whatsappConfigured && hasClientPhone;
                const emailPossible = canSendEmail && hasClientEmail;
                
                if (whatsappPossible && emailPossible) {
                    enableButton = true;
                } else if (!whatsappConfigured && canSendWhatsapp) {
                    showWhatsAppConfigAlert();
                    $(this).val(''); // Resetar seleção
                }
            }
        }

        if (enableButton) {
            btnEnviarCodigo.prop('disabled', false);
            pulseElement(btnEnviarCodigo);
        } else {
            btnEnviarCodigo.prop('disabled', true);
        }
    });

    // Enviar código
    btnEnviarCodigo.on('click', function() {
        const metodoSelecionado = metodoEnvio.val();

        if (!metodoSelecionado) {
            shakeElement(metodoEnvio);
            return;
        }

        if (!window.dadosClienteCashback || !window.dadosClienteCashback.client_id) {
            alert('Erro: Dados do cliente não encontrados. Tente buscar novamente.');
            irParaEtapa(1);
            return;
        }

        showButtonLoading(btnEnviarCodigo);

        // Fazer requisição real para enviar código
        $.ajax({
            url: '{{ route("cashbacks.send-validation-code") }}',
            method: 'POST',
            data: {
                client_id: window.dadosClienteCashback.client_id,
                send_method: metodoSelecionado,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                resetButton(btnEnviarCodigo);

                if (response.success) {
                    // Armazenar método de envio para reenvios
                    window.lastSendMethod = metodoSelecionado;
                    
                    // Ir para etapa 3
                    irParaEtapa(3);

                    // Iniciar timer de reenvio
                    startResendCashbackTimer();

                    // Auto-focus no campo do código
                    setTimeout(() => {
                        codigoConfirmacao.focus();
                    }, 300);
                } else {
                    // Verificar se é erro de conexão do WhatsApp
                    const errorMessage = response.message || 'Erro desconhecido';
                    const isWhatsAppConnectionError = response.whatsapp_connection_error ||
                        errorMessage.includes('Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado') ||
                        errorMessage.includes('Chave de instância do WhatsApp não configurada para o negócio');
                    
                    if (isWhatsAppConnectionError) {
                        Swal.fire({
                            title: 'WhatsApp não conectado!',
                            icon: 'warning',
                            confirmButtonText: 'Fechar',
                            @if(ApiUser::hasPermission('parameters'))
                            text: 'O WhatsApp do seu negócio não está conectado. Conecte-o nos parâmetros do negócio.',
                            cancelButtonText: 'Ir para Parâmetros do Negócio',
                            showCancelButton: true,
                            cancelButtonColor: '#3085d6',
                            reverseButtons: true,
                            @else
                            text: 'O WhatsApp do seu negócio não está conectado. Por favor, consulte o administrador do seu negócio para ser feito a reconexão.',
                            @endif
                        }).then((result) => {
                            @if(ApiUser::hasPermission('parameters'))
                            if (result.dismiss === Swal.DismissReason.cancel) {
                                window.location.href = "{{ route('businesses.parameters') }}";
                            }
                            @endif
                        });
                    } else {
                        alert('Erro ao enviar código: ' + errorMessage);
                    }
                }
            },
            error: function(xhr) {
                resetButton(btnEnviarCodigo);
                let errorMessage = 'Erro ao enviar código de validação.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                // Verificar se é erro de configuração do WhatsApp também no error handler
                const isWhatsAppConnectionError = errorMessage.includes('Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado') ||
                    errorMessage.includes('Chave de instância do WhatsApp não configurada para o negócio');
                
                if (isWhatsAppConnectionError) {
                    Swal.fire({
                        title: 'WhatsApp não conectado!',
                        icon: 'warning',
                        confirmButtonText: 'Fechar',
                        @if(ApiUser::hasPermission('parameters'))
                        text: 'O WhatsApp do seu negócio não está conectado. Conecte-o nos parâmetros do negócio.',
                        cancelButtonText: 'Ir para Parâmetros do Negócio',
                        showCancelButton: true,
                        cancelButtonColor: '#3085d6',
                        reverseButtons: true,
                        @else
                        text: 'O WhatsApp do seu negócio não está conectado. Por favor, consulte o administrador do seu negócio para ser feito a reconexão.',
                        @endif
                    }).then((result) => {
                        @if(ApiUser::hasPermission('parameters'))
                        if (result.dismiss === Swal.DismissReason.cancel) {
                            window.location.href = "{{ route('businesses.parameters') }}";
                        }
                        @endif
                    });
                } else {
                    alert(errorMessage);
                }
            }
        });
    });

    // Confirmar código
    btnConfirmarCodigo.on('click', function() {
        if (!validarCodigo()) {
            return;
        }

        if (!window.dadosClienteCashback || !window.dadosClienteCashback.client_id || !window.dadosClienteCashback.encrypted_cashback_ids) {
            alert('Erro: Dados do cliente não encontrados. Tente buscar novamente.');
            irParaEtapa(1);
            return;
        }

        const codigo = codigoConfirmacao.val().trim();
        showButtonLoading(btnConfirmarCodigo);
        showLoadingOverlay();

        // Fazer requisição real para validar código e concluir cashbacks
        $.ajax({
            url: '{{ route("cashbacks.validate-and-complete") }}',
            method: 'POST',
            data: {
                client_id: window.dadosClienteCashback.client_id,
                validation_code: codigo,
                encrypted_cashback_ids: window.dadosClienteCashback.encrypted_cashback_ids,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                hideLoadingOverlay();
                resetButton(btnConfirmarCodigo);

                if (response.success) {
                    // Marcar que houve sucesso na validação
                    validationSuccess = true;
                    
                    // Ocultar todas as etapas
                    etapa1.addClass('d-none');
                    etapa2.addClass('d-none');
                    etapa3.addClass('d-none');

                    // Mostrar tela de sucesso
                    successScreen.removeClass('d-none').addClass('zoom-in');
                } else {
                    // Código inválido - adicionar classe is-invalid e mostrar mensagem
                    codigoConfirmacao.addClass('is-invalid');
                    feedbackCodigoConfirmacao.text(response.message || 'Código inválido. Verifique o código enviado e tente novamente.');
                    shakeElement(codigoConfirmacao);
                    codigoConfirmacao.focus().select();
                }
            },
            error: function(xhr) {
                hideLoadingOverlay();
                resetButton(btnConfirmarCodigo);

                let errorMessage = 'Erro ao validar código e concluir cashbacks.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                codigoConfirmacao.addClass('is-invalid');
                feedbackCodigoConfirmacao.text(errorMessage);
                shakeElement(codigoConfirmacao);
                codigoConfirmacao.focus().select();
            }
        });
    });

    // Validação em tempo real nos inputs
    valorBusca.on('input', validarTempoReal);

    // Remover estado de erro ao editar o código
    codigoConfirmacao.on('input', function() {
        $(this).removeClass('is-invalid');
        feedbackCodigoConfirmacao.text('');
    });

    // Enter key support
    valorBusca.on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            btnBuscar.click();
        }
    });

    codigoConfirmacao.on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            btnConfirmarCodigo.click();
        }
    });

    // Auto-focus e auto-reset ao abrir modal
    modal.on('shown.bs.modal', function() {
        resetModal();
        setTimeout(() => {
            valorBusca.focus();
        }, 100);
    });

    let validationSuccess = false;

    // Remove foco e reseta modal ao fechar
    modal.on('hide.bs.modal', function() {
        const activeElem = document.activeElement;
        if (this.contains(activeElem)) {
            activeElem.blur();
        }

        setTimeout(() => {
          if (!validationSuccess) {
              resetModal();
          }
        }, 300);
    });

    // Recarregar página quando modal for fechado após sucesso
    modal.on('hidden.bs.modal', function() {
        if (validationSuccess) {
            validationSuccess = false;
            window.location.reload();
        }
    });

    // Inicialização
    aplicarMascara();
    updateLabels();
});

// Função para reenviar código de validação de cashback
window.resendCashbackValidationCode = function() {
    if (!window.dadosClienteCashback || !window.dadosClienteCashback.client_id || !window.lastSendMethod) {
        alert('Erro: Dados necessários para reenvio não encontrados.');
        return;
    }

    const resendButton = $('#resendCashbackButton');
    const resendSpinner = $('#resendCashbackSpinner');
    const resendButtonText = $('#resendCashbackButtonText');

    // Mostrar loading no botão
    resendSpinner.show();
    resendButtonText.hide();
    resendButton.prop('disabled', true).addClass('disabled');

    // Fazer requisição para reenviar código
    $.ajax({
        url: '{{ route("cashbacks.send-validation-code") }}',
        method: 'POST',
        data: {
            client_id: window.dadosClienteCashback.client_id,
            send_method: window.lastSendMethod,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            resendButton.prop('disabled', false);
            resendButtonText.show();
            resendSpinner.hide();

            if (response.success) {
                // Reiniciar timer
                startResendCashbackTimer();
                
                // Mostrar mensagem de sucesso (opcional)
                $('#feedbackCodigoConfirmacao').removeClass('text-danger').addClass('text-success')
                    .text('Código reenviado com sucesso!');
                
                // Limpar mensagem após alguns segundos
                setTimeout(() => {
                    $('#feedbackCodigoConfirmacao').removeClass('text-success').text('');
                }, 3000);
            } else {
                // Verificar se é erro de conexão do WhatsApp
                const errorMessage = response.message || 'Erro desconhecido';
                const isWhatsAppConnectionError = response.whatsapp_connection_error ||
                    errorMessage.includes('Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado') ||
                    errorMessage.includes('Chave de instância do WhatsApp não configurada para o negócio');
                
                if (isWhatsAppConnectionError) {
                    Swal.fire({
                        title: 'WhatsApp não conectado!',
                        icon: 'warning',
                        confirmButtonText: 'Fechar',
                        @if(ApiUser::hasPermission('parameters'))
                        text: 'O WhatsApp do seu negócio não está conectado. Conecte-o nos parâmetros do negócio.',
                        cancelButtonText: 'Ir para Parâmetros do Negócio',
                        showCancelButton: true,
                        cancelButtonColor: '#3085d6',
                        reverseButtons: true,
                        @else
                        text: 'O WhatsApp do seu negócio não está conectado. Por favor, consulte o administrador do seu negócio para ser feito a reconexão.',
                        @endif
                    }).then((result) => {
                        @if(ApiUser::hasPermission('parameters'))
                        if (result.dismiss === Swal.DismissReason.cancel) {
                            window.location.href = "{{ route('businesses.parameters') }}";
                        }
                        @endif
                    });
                } else {
                    alert('Erro ao reenviar código: ' + errorMessage);
                }
            }
        },
        error: function(xhr) {
            resendButton.prop('disabled', false);
            resendButtonText.show();
            resendSpinner.hide();

            let errorMessage = 'Erro ao reenviar código de validação.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            // Verificar se é erro de configuração do WhatsApp
            const isWhatsAppConnectionError = errorMessage.includes('Falha ao enviar código por WhatsApp: O WhatsApp do negócio não está conectado') ||
                errorMessage.includes('Chave de instância do WhatsApp não configurada para o negócio');
            
            if (isWhatsAppConnectionError) {
                Swal.fire({
                    title: 'WhatsApp não conectado!',
                    icon: 'warning',
                    confirmButtonText: 'Fechar',
                    @if(ApiUser::hasPermission('parameters'))
                    text: 'O WhatsApp do seu negócio não está conectado. Conecte-o nos parâmetros do negócio.',
                    cancelButtonText: 'Ir para Parâmetros do Negócio',
                    showCancelButton: true,
                    cancelButtonColor: '#3085d6',
                    reverseButtons: true,
                    @else
                    text: 'O WhatsApp do seu negócio não está conectado. Por favor, consulte o administrador do seu negócio para ser feito a reconexão.',
                    @endif
                }).then((result) => {
                    @if(ApiUser::hasPermission('parameters'))
                    if (result.dismiss === Swal.DismissReason.cancel) {
                        window.location.href = "{{ route('businesses.parameters') }}";
                    }
                    @endif
                });
            } else {
                alert(errorMessage);
            }
        }
    });z
}

function closeCashbackModalAndReload() {
    $('#buscarCashbackModal').modal('hide');
}
</script>
