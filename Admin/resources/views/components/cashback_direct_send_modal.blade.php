@php
    use App\Helpers\ApiUser;

    $user = ApiUser::get();
    $parameters = $user['business_selected']['parameters'];

    $modalId = 'sendCashbackDirectModal';
@endphp

<div class="modal fade" id="{{ $modalId }}" tabindex="-1" role="dialog" aria-labelledby="{{ $modalId }}Label"
     aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <form action="#" method="POST" id="sendCashbackDirectForm">
            @csrf
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-gift mr-2" style="color: var(--main-color);"></i>
                        Enviar Cashback Direto
                    </h5>
                    <button id="direct_close_cashback_direct" type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Tipo de Envio -->
                    <div class="form-group">
                        <label for="direct_cashback_sending_type">Tipo de Envio <span style="color: red">*</span></label>
                        <select class="form-control" id="direct_cashback_sending_type" name="sending_type" required disabled>
                            <option value="" disabled selected>Selecione o tipo de envio</option>
                            @php
                                $options = [];
                                if (ApiUser::get()['business_selected']['plan']['authorizedFunctions']['email_sending']) {
                                    $options[] = 'EMAIL';
                                }
                                if (ApiUser::get()['business_selected']['plan']['authorizedFunctions']['whatsapp_sending']) {
                                    $options[] = 'WHATSAPP';
                                }

                                $selectedOption = 'EMAIL';
                                if (in_array('WHATSAPP', $options)) {
                                    $selectedOption = 'WHATSAPP';
                                }
                            @endphp

                            @if(in_array('EMAIL', $options))
                                <option value="EMAIL" {{ $selectedOption == 'EMAIL' ? 'selected' : '' }}>Email</option>
                            @endif
                            @if(in_array('WHATSAPP', $options))
                                <option value="WHATSAPP" {{ $selectedOption == 'WHATSAPP' ? 'selected' : '' }}>WhatsApp</option>
                            @endif
                        </select>
                    </div>

                    <!-- Email ou Número de Contato -->
                    <div class="form-group" id="direct_cashback_emailField" style="display: none;">
                        <label for="direct_cashback_email">Email <span style="color: red">*</span></label>
                        <input type="email" class="form-control" id="direct_cashback_email" name="email" disabled>
                    </div>
                    <div class="form-group" id="direct_cashback_phoneField" style="display: none;">
                        <label for="direct_cashback_number_phone">Número de Contato <span style="color: red">*</span></label>
                        <input type="text" class="phone-input form-control" id="direct_cashback_number_phone" name="number_phone" disabled>
                    </div>

                    <!-- Nome do Cliente -->
                    <div class="form-group" id="direct_cashback_nameField" style="display: none;">
                        <label for="direct_cashback_name">Nome do Cliente <span style="color: red">*</span></label>
                        <div id="direct_cashback_nameInputContainer">
                            <input type="text" class="form-control" id="direct_cashback_name" name="name" required disabled>
                        </div>
                        <div>
                            <span id="direct_cashback_nameLoadingIndicator" style="display: none; color: #000;">
                                <style>
                                    @keyframes spin {
                                        0% { transform: rotate(0deg); }
                                        100% { transform: rotate(360deg); }
                                    }
                                    .spinner {
                                        display: inline-block;
                                        width: 1em;
                                        height: 1em;
                                        border: 0.15em solid currentColor;
                                        border-right-color: transparent;
                                        border-radius: 50%;
                                        animation: spin .75s linear infinite;
                                        vertical-align: -0.125em;
                                    }
                                </style>
                                <span class="spinner"></span>
                                Carregando...
                            </span>
                        </div>
                    </div>

                    <!-- Valor do Produto/Serviço -->
                    <div class="form-group" id="direct_cashback_priceField" style="display: none;">
                        <label for="direct_cashback_amount">Valor do Produto/Serviço <span style="color: red">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text border-right-0"
                                  style="border-top-right-radius: 0px; border-bottom-right-radius: 0px">R$</span>
                            <input
                                type="number"
                                placeholder="Ex: 30.00"
                                oninput="handleCashbackInput(this)"
                                onblur="handleCashbackBlur(this)"
                                min="0"
                                step="0.01"
                                class="form-control"
                                id="direct_cashback_amount"
                                name="amount"
                                required
                            >
                        </div>
                    </div>

                    <!-- Campos de Cashback -->
                    <div class="form-row" id="direct_cashback_cashbackFields" style="display: none;">
                        <div class="form-group col-md-6">
                            <label for="direct_cashback_percentage">% Cashback</label>
                            <div class="input-group">
                                <span class="input-group-text border-right-0"
                                      style="border-top-right-radius: 0px; border-bottom-right-radius: 0px">%</span>
                                <input 
                                    type="number"
                                    value="{{ $parameters['cashback_percentage'] ?? '0' }}"
                                    class="form-control"
                                    id="direct_cashback_percentage_input"
                                    min="0"
                                    max="100"
                                    step="0.01"
                                    oninput="handleCashbackInput(document.getElementById('direct_cashback_amount'))"
                                    name="percentage"
                                    disabled
                                    readonly
                                >
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="direct_cashback_final_amount">Valor do Cashback</label>
                            <div class="input-group">
                                <span class="input-group-text border-right-0"
                                      style="border-top-right-radius: 0px; border-bottom-right-radius: 0px">R$</span>
                                <input type="number" placeholder="0,00" class="form-control" id="direct_cashback_final_amount"
                                       disabled readonly>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" disabled>
                        <i class="fas fa-paper-plane mr-2"></i>Enviar Cashback
                    </button>
                    <button id="direct_cancel_cashback_direct" type="button" class="btn btn-secondary" data-dismiss="modal">
                        Cancelar
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@include('components.loading', ['message' => 'Enviando cashback...', 'id' => 'direct_sending_cashback_loading'])
@include('components.loading', ['message' => 'Carregando...', 'id' => 'direct_checking_cashback_loading'])

<script>
    const cashbackEmailEnabled = {{ ApiUser::get()['business_selected']['plan']['authorizedFunctions']['email_sending'] ? 'true' : 'false' }};
    const cashbackWhatsappEnabled = {{ ApiUser::get()['business_selected']['plan']['authorizedFunctions']['whatsapp_sending'] ? 'true' : 'false' }};
    const cashbackWhatsappPhoneNumber = '{{ $parameters["whatsapp_phone_number"] ?? "" }}';
    const cashbackWhatsappInstanceKey = '{{ $parameters["whatsapp_instance_key"] ?? "" }}';

    /**
     * Abre o modal de envio de cashback direto após validar permissões e limites
     */
    async function openCashbackDirectModal() {
        try {
            $('#direct_checking_cashback_loading').modal('show');

            const permissions = validateCashbackPermissions();
            const shipments = await validateCashbackShipments();
            const result = await handleCashbackValidationResult(permissions, shipments);

            $('#direct_checking_cashback_loading').modal('hide');

            switch (result.type) {
                case 'SHOW_WHATSAPP_CONFIG':
                    await showCashbackWhatsAppConfigModal();
                    return;
                case 'SHOW_LIMIT_EXCEEDED':
                    await showCashbackLimitExceededModal();
                    return;
                case 'SHOW_MODAL':
                    clearCashbackInputFields();
                    
                    $('#direct_cashback_sending_type').prop('disabled', false);
                    $('#sendCashbackDirectForm button[type="submit"]').prop('disabled', false);

                    // Sempre mostrar campos de cashback para disparo direto
                    $('#direct_cashback_priceField').show();
                    $('#direct_cashback_cashbackFields').show();
                    $('#direct_cashback_amount').prop('required', true);

                    configureCashbackModalOptions(result.options);
                    $('#sendCashbackDirectModal').modal('show');
                    break;
            }
        } catch (error) {
            $('#direct_checking_cashback_loading').modal('hide');
            console.error('Erro ao verificar envios de cashback:', error);
            await Swal.fire({
                icon: 'error',
                title: 'Erro',
                text: 'Erro ao verificar disponibilidade de envios. Por favor, tente novamente.',
                confirmButtonText: 'Ok'
            });
        } finally {
            $('#direct_checking_cashback_loading').modal('hide');
        }
    }

    /**
     * Retorna as permissões do plano e status da configuração do WhatsApp para cashback
     */
    const validateCashbackPermissions = () => {
        return {
            emailPermission: cashbackEmailEnabled,
            whatsappPermission: cashbackWhatsappEnabled,
            hasWhatsappConfig: cashbackWhatsappPhoneNumber && cashbackWhatsappInstanceKey
        };
    };

    /**
     * Verifica a quantidade de envios disponíveis para email e WhatsApp (cashback)
     * @returns {Promise<Object>} Objeto com os limites restantes de envios
     * @throws {Error} Se houver erro na requisição ou resposta inválida
     */
    const validateCashbackShipments = async () => {
        const response = await fetch("{{ env('API_URL') }}/api/evaluations/business/validate-shipments", {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Erro na requisição: ' + response.status);
        }

        const data = await response.json();

        if (data.error) {
            throw new Error(data.msg || 'Erro ao verificar disponibilidade de envios');
        }

        if (!data.data) {
            throw new Error('Resposta inválida da API');
        }

        return {
            remainingEmail: cashbackEmailEnabled ? data.data.remaining_email_shipments : 0,
            remainingWhatsapp: cashbackWhatsappEnabled ? data.data.remaining_whatsapp_shipments : 0
        };
    };

    /**
     * Processa o resultado das validações para cashback direto
     */
    const handleCashbackValidationResult = async (permissions, shipments) => {
        const {emailPermission, whatsappPermission, hasWhatsappConfig} = permissions;
        const {remainingEmail, remainingWhatsapp} = shipments;

        const availableOptions = [];

        if (whatsappPermission && remainingWhatsapp > 0 && hasWhatsappConfig) {
            availableOptions.push('WHATSAPP');
        }

        if (emailPermission && remainingEmail > 0) {
            availableOptions.push('EMAIL');
        }

        if (availableOptions.length > 0) {
            return { type: 'SHOW_MODAL', options: availableOptions }
        } else {
            if (whatsappPermission && remainingWhatsapp > 0 && !hasWhatsappConfig) {
                return { type: 'SHOW_WHATSAPP_CONFIG' };
            } else {
                return { type: 'SHOW_LIMIT_EXCEEDED' };
            }
        }
    };

    const showCashbackWhatsAppConfigModal = async () => {
        await Swal.fire({
            icon: 'warning',
            title: 'WhatsApp Não Configurado',
            text: 'O WhatsApp não está configurado. Por favor, configure o número e a chave de instância do WhatsApp.',
            confirmButtonText: 'Ok',
            @if(ApiUser::hasPermission('parameters'))
            showCancelButton: true,
            cancelButtonText: 'Configurar WhatsApp',
            cancelButtonColor: '#3085d6'
            @endif
        }).then((result) => {
            @if(ApiUser::hasPermission('parameters'))
            if (result.dismiss === Swal.DismissReason.cancel) {
                window.location.href = "{{ route('businesses.parameters') }}";
            }
            @endif
        });
    };

    const showCashbackLimitExceededModal = async () => {
        await Swal.fire({
            icon: 'warning',
            title: 'Limite de Envios Atingido',
            text: 'Você atingiu o limite de envios disponíveis.',
            confirmButtonText: 'Ok',
            @if(ApiUser::hasPermission('dashboard'))
            showCancelButton: true,
            cancelButtonText: 'Ver Limites de Envio',
            cancelButtonColor: '#3085d6'
            @endif
        }).then((result) => {
            @if(ApiUser::hasPermission('dashboard'))
            if (result.dismiss === Swal.DismissReason.cancel) {
                window.location.href = "{{ route('dashboard.index') }}";
            }
            @endif
        });
    };

    const configureCashbackModalOptions = (options) => {
        const sendingTypeSelect = $('#direct_cashback_sending_type');
        sendingTypeSelect.empty();

        if (options.length === 1) {
            sendingTypeSelect.append(`<option value="${options[0]}">${options[0] === 'EMAIL' ? 'Email' : 'WhatsApp'}</option>`);
            sendingTypeSelect.val(options[0]).trigger('change');
        } else {
            sendingTypeSelect.append('<option value="" disabled selected>Selecione o tipo de envio</option>');
            options.forEach(opt => {
                sendingTypeSelect.append(`<option value="${opt}">${opt === 'EMAIL' ? 'Email' : 'WhatsApp'}</option>`);
            });
            if (options.includes('WHATSAPP')) {
                sendingTypeSelect.val('WHATSAPP').trigger('change');
            } else {
                sendingTypeSelect.val('EMAIL').trigger('change');
            }
        }
    };

    $(document).ready(function () {
        applyCashbackPhoneMask();
        handleCashbackSendingTypeChange();

        // Função para verificar o contato (email ou telefone) no backend - cashback
        async function checkCashbackUserContact(contactValue, contactType) {
            const nameInput = $('#direct_cashback_name');
            const nameLoadingIndicator = $('#direct_cashback_nameLoadingIndicator');
            const nameInputContainer = $('#direct_cashback_nameInputContainer');

            if (!contactValue) {
                if (nameInput.is(':disabled')) {
                    nameInput.prop('disabled', false).val('');
                }
                nameInputContainer.show();
                nameLoadingIndicator.hide();
                return;
            }

            nameInputContainer.hide();
            nameLoadingIndicator.show();
            nameInput.val('');
            
            try {
                const response = await fetch("{{ env('API_URL') }}/api/users/check-contact", {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contact_value: contactValue,
                        contact_type: contactType
                    })
                });

                nameLoadingIndicator.hide();
                nameInputContainer.show();

                if (!response.ok) {
                    console.error('Erro na requisição da API:', response.status, response.statusText);
                    nameInput.prop('disabled', false);
                    return;
                }

                const data = await response.json();

                if (data.error) {
                    console.error('Erro retornado pela API:', data.error);
                    nameInput.prop('disabled', false);
                    return;
                }

                if (data.found) {
                    nameInput.val(data.name).prop('disabled', true);
                } else {
                    nameInput.val('').prop('disabled', false);
                }

            } catch (error) {
                nameLoadingIndicator.hide();
                nameInputContainer.show();
                nameInput.prop('disabled', false);
                console.error('Erro ao verificar contato:', error);
            }
        }

        // Função auxiliar para validar formato básico de email
        function isValidCashbackEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        let debounceTimerCashbackEmail;
        $('#direct_cashback_email').on('input', function() {
            clearTimeout(debounceTimerCashbackEmail);
            const emailValue = $(this).val();

            if ($(this).is(':enabled') && $('#direct_cashback_nameField').is(':visible')) {
                if (isValidCashbackEmail(emailValue)) {
                    debounceTimerCashbackEmail = setTimeout(() => {
                        checkCashbackUserContact(emailValue, 'email');
                    }, 500);
                } else {
                    const nameInput = $('#direct_cashback_name');
                    const nameLoadingIndicator = $('#direct_cashback_nameLoadingIndicator');
                    const nameInputContainer = $('#direct_cashback_nameInputContainer');
                    
                    if (nameLoadingIndicator.is(':hidden')) {
                        nameInput.val('').prop('disabled', false);
                        nameInputContainer.show();
                        nameLoadingIndicator.hide();
                    }
                }
            } else {
                clearTimeout(debounceTimerCashbackEmail);
            }
        });

        let debounceTimerCashbackPhone;
        $('#direct_cashback_number_phone').on('input', function() {
            clearTimeout(debounceTimerCashbackPhone);
            
            // Aplica a máscara primeiro para a exibição
            let unmaskedValue = this.value.replace(/\D/g, '');
            if (unmaskedValue.length > 11) {
                unmaskedValue = unmaskedValue.substring(0, 11);
            }
            let maskedValue = formatCashbackPhone(unmaskedValue);
            
            if (this.value !== maskedValue) {
                this.value = maskedValue;
            }

            const phoneValue = unmaskedValue;

            if ($(this).is(':enabled') && $('#direct_cashback_nameField').is(':visible')) {
                if (phoneValue.length === 10 || phoneValue.length === 11) {
                    debounceTimerCashbackPhone = setTimeout(() => {
                        checkCashbackUserContact(phoneValue, 'phone');
                    }, 500);
                } else {
                    const nameInput = $('#direct_cashback_name');
                    const nameLoadingIndicator = $('#direct_cashback_nameLoadingIndicator');
                    const nameInputContainer = $('#direct_cashback_nameInputContainer');
                    
                    if (nameLoadingIndicator.is(':hidden')) {
                        nameInput.val('').prop('disabled', false);
                        nameInputContainer.show();
                        nameLoadingIndicator.hide();
                    }
                }
            } else {
                clearTimeout(debounceTimerCashbackPhone);
            }
        });
    });

    function clearCashbackInputFields() {
        // Limpa campos de contato
        $('#direct_cashback_email').val('');
        $('#direct_cashback_number_phone').val('');
        $('#direct_cashback_name').val('');

        // Limpa valores
        $('#direct_cashback_amount').val('');
        $('#direct_cashback_final_amount').val('');

        // Esconde campos de contato inicialmente
        $('#direct_cashback_emailField').hide();
        $('#direct_cashback_phoneField').hide();
        $('#direct_cashback_nameField').hide();

        // Garante que campos de contato estejam desabilitados e sem required inicialmente
        $('#direct_cashback_email').prop('disabled', true).removeAttr('required');
        $('#direct_cashback_number_phone').prop('disabled', true).removeAttr('required');
        $('#direct_cashback_name').prop('disabled', true).removeAttr('required');

        // Desabilita botão de submit e campos principais inicialmente, mas mantém campo de porcentagem habilitado
        $('#sendCashbackDirectForm button[type="submit"]').prop('disabled', true);
        $('#direct_cashback_sending_type').prop('disabled', true);
        $('#direct_cashback_percentage_input').prop('disabled', false);
    }

    function handleCashbackInput(input) {
        const value = parseFloat(input.value);
        const cashbackAmountInput = document.getElementById('direct_cashback_final_amount');
        const cashbackPercentageInput = document.getElementById('direct_cashback_percentage_input');
        const cashbackPercentage = parseFloat(cashbackPercentageInput.value) || 0;
        
        if (!isNaN(value) && value !== '') {
            cashbackAmountInput.value = (value * (cashbackPercentage / 100)).toFixed(2);
        } else {
            cashbackAmountInput.value = '';
        }
    }

    function handleCashbackBlur(input) {
        const value = parseFloat(input.value);
        if (!isNaN(value)) {
            input.value = value.toFixed(2);
        }
    }

    function applyCashbackPhoneMask() {
        $('#direct_cashback_number_phone').on('input', function () {
            let value = this.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            this.value = formatCashbackPhone(value);
        });
    }

    function formatCashbackPhone(value) {
        value = value.replace(/\D/g, '');

        if (value.length <= 10) {
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2')
                .replace(/(\d{4})(\d{1,4})$/, '$1-$2');
        } else {
            return value
                .replace(/^(\d{2})(\d)/g, '($1) $2')
                .replace(/(\d{5})(\d{1,4})$/, '$1-$2');
        }
    }

    function handleCashbackSendingTypeChange() {
        $('#direct_cashback_sending_type').on('change', function () {
            $('#direct_cashback_name').val('');

            if (!$(this).is(':enabled')) {
                $('#direct_cashback_emailField').hide();
                $('#direct_cashback_phoneField').hide();
                $('#direct_cashback_email').val('');
                $('#direct_cashback_number_phone').val('');
                return;
            }

            var sendingType = $(this).val();

            if (sendingType === 'EMAIL') {
                $('#direct_cashback_emailField').show();
                $('#direct_cashback_email').prop('disabled', false).attr('required', true);

                $('#direct_cashback_phoneField').hide();
                $('#direct_cashback_number_phone').val('').prop('disabled', true).removeAttr('required');

                $('#direct_cashback_nameField').show();
                $('#direct_cashback_name').prop('disabled', false).attr('required', true);
            } else if (sendingType === 'WHATSAPP') {
                $('#direct_cashback_emailField').hide();
                $('#direct_cashback_email').val('').prop('disabled', true).removeAttr('required');

                $('#direct_cashback_phoneField').show();
                $('#direct_cashback_number_phone').prop('disabled', false).attr('required', true);

                $('#direct_cashback_nameField').show();
                $('#direct_cashback_name').prop('disabled', false).attr('required', true);
            } else {
                $('#direct_cashback_emailField').hide();
                $('#direct_cashback_email').val('').prop('disabled', true).removeAttr('required');

                $('#direct_cashback_phoneField').hide();
                $('#direct_cashback_number_phone').val('').prop('disabled', true).removeAttr('required');

                $('#direct_cashback_nameField').hide();
                $('#direct_cashback_name').prop('disabled', true).removeAttr('required');
            }
        });
    }

    $('#sendCashbackDirectForm').on('submit', function (e) {
        e.preventDefault();
        
        // Coleta os dados do formulário
        const rawAmount = $('#direct_cashback_amount').val();
        const processedAmount = parseFloat(rawAmount.replace(',', '.')) || 0;
        
        const formData = {
            sending_type: $('#direct_cashback_sending_type').val(),
            email: $('#direct_cashback_email').val() || null,
            number_phone: $('#direct_cashback_number_phone').val() ? $('#direct_cashback_number_phone').val().replace(/\D/g, '') : null,
            name: $('#direct_cashback_name').val(),
            amount: processedAmount,
            percentage: parseFloat($('#direct_cashback_percentage_input').val().replace(',', '.')) || 0
        };

        // Validações
        if (!formData.sending_type) {
            Swal.fire({ icon: 'error', title: 'Erro de Validação', text: 'O tipo de envio é obrigatório.', confirmButtonText: 'Ok' });
            return;
        }

        if (formData.sending_type === 'EMAIL' && !formData.email) {
            Swal.fire({ icon: 'error', title: 'Erro de Validação', text: 'O email é obrigatório para envio por email.', confirmButtonText: 'Ok' });
            return;
        }

        if (formData.sending_type === 'WHATSAPP' && !formData.number_phone) {
            Swal.fire({ icon: 'error', title: 'Erro de Validação', text: 'O número de telefone é obrigatório para envio por WhatsApp.', confirmButtonText: 'Ok' });
            return;
        }
        
        const nameField = $('#direct_cashback_name');
        if (!nameField.prop('disabled') && !formData.name) {
            Swal.fire({ icon: 'error', title: 'Erro de Validação', text: 'O nome do cliente é obrigatório.', confirmButtonText: 'Ok' });
            return;
        }

        if (isNaN(formData.amount) || formData.amount < 0.01) {
            Swal.fire({ icon: 'error', title: 'Erro de Validação', text: `O valor do produto/serviço deve ser de no mínimo R$ 0.01. Valor digitado: "${rawAmount}"`, confirmButtonText: 'Ok' });
            return;
        }

        // Fecha o modal e mostra loading
        $('#sendCashbackDirectModal').modal('hide');
        $('#direct_sending_cashback_loading').modal('show');

        // Faz a requisição AJAX
        $.ajax({
            url: "{{ env('API_URL') }}/api/cashbacks/direct",
            method: 'POST',
            headers: {
                'Authorization': 'Bearer {{ session("API_TOKEN") }}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(formData),
            success: function(response) {
                $('#direct_sending_cashback_loading').modal('hide');
                
                Swal.fire({
                    icon: 'success',
                    title: 'Cashback Enviado!',
                    text: response.msg || 'O cashback foi criado e enviado com sucesso para o cliente.',
                    confirmButtonText: 'Ok'
                }).then(() => {
                    // Recarrega a página ou atualiza a lista de cashbacks se necessário
                    if (typeof updateCashbacksList === 'function') {
                        updateCashbacksList();
                    }
                });
            },
            error: function(xhr) {
                $('#direct_sending_cashback_loading').modal('hide');
                
                let errorMessage = 'Erro ao enviar cashback. Tente novamente.';
                
                if (xhr.responseJSON) {
                    if (xhr.responseJSON.msg) {
                        errorMessage = xhr.responseJSON.msg;
                    } else if (xhr.responseJSON.errors) {
                        // Se houver erros de validação específicos
                        const errors = xhr.responseJSON.errors;
                        const errorList = Object.values(errors).flat();
                        errorMessage = errorList.join('\n');
                    }
                }

                // Verificar se é erro de WhatsApp não conectado
                let isWhatsAppConnectionError = false;
                
                // Verificar se a mensagem de erro contém variações de "WhatsApp não conectado"
                if (errorMessage) {
                    const whatsappErrorMessages = [
                        'WhatsApp não conectado',
                        'Falha ao enviar cashback por WhatsApp: O WhatsApp do negócio não está conectado',
                        'Chave de instância do WhatsApp não configurada para o negócio'
                    ];
                    
                    isWhatsAppConnectionError = whatsappErrorMessages.some(message =>
                        errorMessage.toLowerCase().includes(message.toLowerCase())
                    );
                }

                if (isWhatsAppConnectionError) {
                    Swal.fire({
                        title: 'WhatsApp não conectado!',
                        icon: 'warning',
                        confirmButtonText: 'Fechar',
                        @if(ApiUser::hasPermission('parameters'))
                        text: 'O WhatsApp do seu negócio não está conectado. Conecte-o nos parâmetros do negócio.',
                        cancelButtonText: 'Ir para Parâmetros do Negócio',
                        showCancelButton: true,
                        cancelButtonColor: '#3085d6',
                        reverseButtons: true,
                        @else
                        text: 'O WhatsApp do seu negócio não está conectado. Por favor, consulte o administrador do seu negócio para ser feito a reconexão.',
                        @endif
                    }).then((result) => {
                        @if(ApiUser::hasPermission('parameters'))
                        if (result.dismiss === Swal.DismissReason.cancel) {
                            window.location.href = "{{ route('businesses.parameters') }}";
                        }
                        @endif
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro ao Enviar Cashback',
                        text: errorMessage,
                        confirmButtonText: 'Ok'
                    });
                }
            }
        });
    });
</script>

@include('components.loading', ['message' => 'Enviando cashback...', 'id' => 'direct_sending_cashback_loading'])