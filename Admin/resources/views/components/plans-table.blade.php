<div class="col-md-12" id="plans">
    <label class="form-title mb-0" for="name">Plano <span style="color: red">*</span></label>
    <div class="form-group mt-0">
        <hr class="my-1 d-none" id="plansSeparator"/>
        <label class="m-0 d-none" for="name" id="planSelectedLabel">Selecionado:</label>
        <div class="form-group mb-0">
            <ul class="list-group" id="plan_selected">
                <!-- Plano selecionado será carregado aqui -->
            </ul>
        </div>
    </div>
    <div class="form-group mb-0 mt-0">
        <input type="text" id="planSearchFormEmployee" class="form-control" placeholder="Pesquisar plano...">
    </div>
    <div class="d-flex justify-content-center loadingPlans pb-1 d-none">
        <div class="spinner-border loadingPlans" role="status">
            <span class="sr-only loadingPlans">Loading...</span>
        </div>
    </div>
    <table class="table table-bordered d-none" id="planTableFormEmployee">
        <thead>
        <tr>
            <th>Nome</th>
            <th>Ações</th>
        </tr>
        </thead>
        <tbody id="planTableFormEmployeeBody">
        <!-- Planos serão carregados aqui -->
        </tbody>
    </table>
    <nav>
        <ul class="pagination" id="planPaginationFormEmployee">
            <!-- Paginação será carregada aqui -->
        </ul>
    </nav>
    <div class="d-none" id="planInputs">
        <!-- Inputs hidden para enviar o plano selecionado -->
    </div>
</div>

<script>
    let planSelectedId = null;
    @if(isset($business['plan_id']) && isset($business['plan_name']))
        planSelectedId = {{ $business['plan_id'] }};
        $('#plan_selected').append(`<li class="list-group-item">{{ $business['plan_name'] }}</li>`);
        $('#planInputs').append(`<input type="hidden" name="plan_id" value="{{ $business['plan_id'] }}">`);
        $('#planInputs').append(`<input type="hidden" name="plan_name" value="{{ $business['plan_name'] }}">`);
        $('#plansSeparator').removeClass('d-none');
        $('#planSelectedLabel').removeClass('d-none');
    @endif

    function loadPlansFormEmployee(page = 1) {
        const searchQuery = $('#planSearchFormEmployee').val();
        const url = `<?= env('API_URL') ?>/api/plans/getActivePlans?search=${searchQuery}&page=${page}&per_page=5`;

        $.ajax({
            url: url,
            type: 'get',
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                $('#planTableFormEmployee').addClass('d-none');
                $('#planPaginationFormEmployee').addClass('d-none');
                $('.loadingPlans').removeClass('d-none');
            },
            success: function (response) {
                $('.loadingPlans').addClass('d-none');
                $('#planTableFormEmployeeBody').empty();
                if (response.plans.data.length > 0) {
                    $.each(response.plans.data, function (index, plan) {
                        let isSelected = plan.id === planSelectedId;
                        $('#planTableFormEmployeeBody').append(`
                        <tr>
                            <td class="align-content-center m-0 pr-1" style="overflow: hidden; text-overflow: ellipsis;"
                                    title="${plan.name}">${plan.name}</td>
                            <td style="width: 70px">
                                <input 
                                    type="radio" 
                                    name="plan" 
                                    id="plan-${plan.id}" 
                                    value="${plan.id}" 
                                    ${isSelected ? 'checked' : ''} 
                                    onclick="selectPlan(${plan.id}, '${plan.name}', this);" 
                                >
                            </td>
                        </tr>
                    `);
                    });
                } else {
                    $('#planTableFormEmployeeBody').append('<tr><td colspan="2">Nenhum plano encontrado</td></tr>');
                }

                $('#planPaginationFormEmployee').empty();
                if (response.plans.last_page > 1) {
                    for (let i = 1; i <= response.plans.last_page; i++) {
                        $('#planPaginationFormEmployee').append(`
                        <li class="page-item ${i === response.plans.current_page ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `);
                    }
                }

                $('#planPaginationFormEmployee a').on('click', function (e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    loadPlansFormEmployee(page);
                });

                $('#planTableFormEmployee').removeClass('d-none');
                $('#planPaginationFormEmployee').removeClass('d-none');
            },
            error: function () {
                $('.loadingPlans').addClass('d-none');
            }
        });
    }

    function selectPlan(planId, planName, button) {
        planSelectedId = planId;
        $('#plan_selected').empty();
        $('#plan_selected').append(`
        <li class="list-group-item">
            ${planName}
        </li>
    `);

        $('#planInputs').empty();
        $('#planInputs').append(`<input type="hidden" name="plan_id" value="${planId}">`);
        $('#planInputs').append(`<input type="hidden" name="plan_name" value="${planName}">`);

        $('#planTableFormEmployee a').each(function () {
            $(this).removeClass('btn-success').addClass('btn-primary');
        });

        $(button).removeClass('btn-primary').addClass('btn-success');

        $('#plansSeparator').removeClass('d-none');
        $('#planSelectedLabel').removeClass('d-none');
    }

    function verifyPlanSelected() {
        if (planSelectedId === null) {
            alert('Selecione um plano');
            return false;
        }
        return true;
    }

    $('#form-update-business').on('submit', function () {
        return verifyPlanSelected();
    });

    $(document).ready(function () {
        loadPlansFormEmployee();
        $('#planSearchFormEmployee').on('keyup', debounce(function () {
            loadPlansFormEmployee();
        }, 500));
    });

</script>
