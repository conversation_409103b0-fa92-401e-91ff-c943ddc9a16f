<?php
    $name_input = $name_input ?? 'password';
?>

<div class="input-group-append">
    <button class="btn" style="background-color: #E9ECEF; border-color: #c1c1c1;" type="button" onclick="togglePassword('<?=$name_input?>')">
        <i id="{{$name_input . '-icon'}}" class="fa fa-eye"></i>
    </button>
</div>

<script>
    function togglePassword(inputId) {
        var passwordInput = $('#' + inputId);
        var type = passwordInput.attr('type');
        if (type === 'password') {
            passwordInput.attr('type', 'text');
            $('#' + inputId + '-icon').removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordInput.attr('type', 'password');
            $('#' + inputId + '-icon').removeClass('fa-eye-slash').addClass('fa-eye');
        }
    }
</script>