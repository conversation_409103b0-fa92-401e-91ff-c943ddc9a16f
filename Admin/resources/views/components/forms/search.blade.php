<form class='d-inline-flex flex-fill {{ $class ?? "" }}' action="{{ $route }}" method="{{ $method ?? 'post' }}" id="searchForm">
    @csrf
    <input type="text" class="form-control mr-2" name="search" aria-describedby="search" placeholder="{{ $placeholder ?? 'Faça uma busca' }}" value="{{ $search ?? old('search') }}">
    @if(isset($whoCreated))
        @foreach($whoCreated as $item)
        <input type="hidden" name="who_created[]" value="{{ $item }}">
        @endforeach
    @endif
    @if(isset($filterName))
    <input type="hidden" name="filterName" value="{{ $filterName }}">
    @endif
    @include('components.buttons.search_button')
    @if(isset($extraFields))
        {!! $extraFields !!}
    @endif
</form>
