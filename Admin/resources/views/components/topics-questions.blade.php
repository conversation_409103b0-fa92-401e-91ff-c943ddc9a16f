@php use App\Helpers\ApiUser;use App\Helpers\EmployeePermissions; @endphp
<div class="col-md-12">
    <label class="form-title" for="questions">{{$title ?? 'Lista de Questões'}} <span
            style="color: red">*</span></label>
    <div class="form-group">
        <input type="text" id="questionDescription" class="form-control" placeholder="Descrição da questão">
        <div class="d-flex mt-2">
            <select id="questionType" class="form-control mr-2" style="flex: 2;">
                <option value="five_star">5 Estrelas</option>
                <option value="yes_no">Sim ou Não</option>
            </select>
            <button type="button" class="btn btn-primary" id="addQuestionButton">Adicionar</button>
        </div>
    </div>
    <ul class="list-group" id="questionsList">
        <!-- Lista de questões adicionadas será carregada aqui -->
    </ul>
    @php
        $showQuestionsBusiness = $topic != null && $topic['default'] && ApiUser::hasUserFunction(EmployeePermissions::DEFAULT_TOPICS);
    @endphp
    @if($showQuestionsBusiness)
        <hr class="mt-3">
        <label class="form-title" for="questionsBusiness">Lista de Questões do
            Negócio {{ ApiUser::get()['business_selected'] !== null ? ' - ' . ApiUser::get()['business_selected']['name'] : '' }}
            <span style="color: red">*</span></label>
        <div class="form-group">
            <input type="text" id="questionDescriptionBusiness" class="form-control"
                   placeholder="Descrição da questão de negócio">
            <div class="d-flex mt-2">
                <select id="questionTypeBusiness" class="form-control mr-2" style="flex: 2;">
                    <option value="five_star">5 Estrelas</option>
                    <option value="yes_no">Sim ou Não</option>
                </select>
                <button type="button" class="btn btn-primary" id="addQuestionBusinessButton">Adicionar</button>
            </div>
        </div>
        <ul class="list-group" id="questionsListBusiness">
            <!-- Lista de questões especificas de negócio -->
        </ul>
    @endif
    <input type="hidden" name="questions" id="questions">
    @if($showQuestionsBusiness)
        <input type="hidden" name="questions_business" id="questions_business">
    @endif
</div>

<script>
    class Question {
        constructor(description, evaluation_type, order, id = null) {
            this.id = id;
            this.description = description;
            this.evaluation_type = evaluation_type;
            this.order = order;
            @if($showQuestionsBusiness)
                this.business_id = {{ApiUser::get()['business_selected'] !== null ? ApiUser::get()['business_selected']['id'] : 'null'}};
            @endif
        }
    }

    let form = document.getElementById('form-topic');

    let questions = @json($topic['questions'] ?? []);

    let questionsOrder = [];
    let questionsBusinessOrder = [];
    let questionsToRemove = [];
    for (let i = 0; i < questions.length; i++) {
        questionsOrder.push(new Question(
            questions[i].description,
            questions[i].evaluation_type,
            questions[i].order,
            questions[i].id
        ));
    }

    @if($showQuestionsBusiness)
    let questionsBusiness = @json($topic['questions_business'] ?? []);
    for (let i = 0; i < questionsBusiness.length; i++) {
        questionsBusinessOrder.push(new Question(
            questionsBusiness[i].description,
            questionsBusiness[i].evaluation_type,
            questionsBusiness[i].order,
            questionsBusiness[i].id
        ));
    }

    function addQuestionBusinessToList(description, type, order = null, id = null) {
        const questionsToOrder = document.getElementById("questionsListBusiness");
        const questionOrder = order !== null ? order : questionsBusinessOrder.length + 1;
        let textType = type === 'yes_no' ? 'Sim ou Não' : '5 Estrelas';

        const question = document.createElement('div');
        question.className = "d-flex align-content-center mb-1 w-100";
        question.id = `business-${questionOrder}-group`;
        question.style.border = "1px solid #ebecec";
        question.style.borderRadius = "5px";
        question.style.padding = "10px";
        question.style.minWidth = "300px";
        question.style.wordBreak = "break-word";

        question.innerHTML = `
        <p class="w-100 align-content-center m-0 pr-1">${description} - (${textType})</p>

        <div class="d-flex align-content-center">
            <button type="button" class="btn btn-light btn-up fa fa-arrow-up mr-1" onclick="moveUpBusiness(${questionOrder})"></button>
            <button type="button" class="btn btn-light btn-down fa fa-arrow-down mr-1" onclick="moveDownBusiness(${questionOrder})"></button>
        </div>
        <button type="button" class="btn btn-danger fa fa-trash ml-1" onclick="removeQuestionBusiness(${questionOrder})"></button>
    `;
        questionsToOrder.appendChild(question);
        if (order === null) {
            questionsBusinessOrder.push(new Question(description, type, questionOrder, id));
        }
        ajustOrderButtonsBusiness();
        updateQuestionsBusinessInput();
    }

    @endif

    @if($showQuestionsBusiness)
    $('#addQuestionBusinessButton').on('click', function () {
        if ($('#questionDescriptionBusiness').val() === '') {
            alert('Descrição da questão de negócio é obrigatória');
            return;
        }
        if (questionsBusinessOrder.some(question => question.description === $('#questionDescriptionBusiness').val())) {
            alert('Já existe uma questão com essa descrição');
            return;
        }
        let description = $('#questionDescriptionBusiness').val();
        let type = $('#questionTypeBusiness').val();
        if (description && type) {
            addQuestionBusinessToList(description, type);
            $('#questionDescriptionBusiness').val('');
        }
    });

    function renderQuestionsBusiness() {
        const questionsList = document.getElementById("questionsListBusiness");
        questionsList.innerHTML = '';
        questionsBusinessOrder.forEach((question, index) => {
            addQuestionBusinessToList(question.description, question.evaluation_type, index + 1);
        });
        updateQuestionsBusinessInput();
        ajustOrderButtonsBusiness();
    }
    @endif

    $('#addQuestionButton').on('click', function () {
        if ($('#questionDescription').val() === '') {
            alert('Descrição da questão é obrigatória');
            return;
        }
        if (questionsOrder.some(question => question.description === $('#questionDescription').val())) {
            alert('Já existe uma questão com essa descrição');
            return;
        }
        let description = $('#questionDescription').val();
        let type = $('#questionType').val();
        if (description && type) {
            addQuestionToList(description, type);
            $('#questionDescription').val('');
        }
    });

    function initQuestions() {
        questions.forEach(question => {
            addQuestionToList(question.description, question.evaluation_type, question.order);
        });
        @if($showQuestionsBusiness)
        questionsBusiness.forEach(question => {
            addQuestionBusinessToList(question.description, question.evaluation_type, question.order);
        });
        @endif
    }

    function addQuestionToList(description, type, order = null, id = null) {
        const questionsToOrder = document.getElementById("questionsList");
        const questionOrder = order !== null ? order : questionsOrder.length + 1;
        let textType = '';
        if (type === 'yes_no') {
            textType = 'Sim ou Não';
        } else if (type === 'five_star') {
            textType = '5 Estrelas';
        }
        const question = document.createElement('div');
        question.className = "d-flex align-content-center mb-1 w-100";
        question.id = `${questionOrder}-group`;
        question.style.border = "1px solid #ebecec";
        question.style.borderRadius = "5px";
        question.style.padding = "10px";
        question.style.minWidth = "300px";
        question.style.wordBreak = "break-word";

        question.innerHTML = `
      <p class="w-100 align-content-center m-0 pr-1">${description} - (${textType})</p>

      <div class="d-flex align-content-center">
           <button type="button" class="btn btn-light btn-up fa fa-arrow-up mr-1" onclick="moveUp(${questionOrder})"></button>
           <button type="button" class="btn btn-light btn-down fa fa-arrow-down mr-1" onclick="moveDown(${questionOrder})"></button>
      </div>
      <button type="button" class="btn btn-danger fa fa-trash ml-1" onclick="removeQuestion(${questionOrder})"></button>
    `;
        questionsToOrder.appendChild(question);
        if (order === null) {
            questionsOrder.push(new Question(description, type, questionOrder, id));
        }
        ajustOrderButtons();
        updateQuestionsInput();
    }

    function verifyQuestions() {
        if (questionsOrder.length === 0) {
            $('#loadingCreateTopic').modal('hide');
            alert('Adicione ao menos uma questão');
            return false;
        }

        return true;
    }

    function ajustOrder() {
        questionsOrder.forEach((question, index) => {
            question.order = index + 1;
        });
        renderQuestions();
    }

    function updateQuestionsInput() {
        const questionInput = document.getElementById('questions');
        questionInput.value = JSON.stringify(questionsOrder);
    }

    @if($showQuestionsBusiness)
    function updateQuestionsBusinessInput() {
        const questionInput = document.getElementById('questions_business');
        questionInput.value = JSON.stringify(questionsBusinessOrder);
    }
    @endif

    function removeQuestion(order) {
        const questionIndex = questionsOrder.findIndex(question => question.order === order);
        if (questionIndex > -1) {
            questionsOrder.splice(questionIndex, 1);
            document.getElementById(`${order}-group`).remove();
            ajustOrderButtons();
            updateQuestionsInput();
        }
    }

    function moveUp(order) {
        const index = questionsOrder.findIndex(question => question.order === order);
        if (index > 0) {
            [questionsOrder[index], questionsOrder[index - 1]] = [questionsOrder[index - 1], questionsOrder[index]];
            ajustOrder();
        }
    }

    function moveDown(order) {
        const index = questionsOrder.findIndex(question => question.order === order);
        if (index < questionsOrder.length - 1) {
            [questionsOrder[index], questionsOrder[index + 1]] = [questionsOrder[index + 1], questionsOrder[index]];
            ajustOrder();
        }
    }

    function renderQuestions() {
        const questionsList = document.getElementById("questionsList");
        questionsList.innerHTML = '';
        questionsOrder.forEach((question, index) => {
            addQuestionToList(question.description, question.evaluation_type, index + 1);
        });
        updateQuestionsInput();
        ajustOrderButtons();
    }

    function ajustOrderButtons() {
        const questionGroups = document.querySelectorAll('[id$="-group"]');
        questionGroups.forEach(group => {
            const buttons = group.querySelectorAll('.btn-up, .btn-down');
            buttons.forEach(button => {
                button.classList.remove('disabled');
                button.style.cursor = 'pointer';
            });
        });

        const firstGroup = questionGroups[0];
        if (firstGroup) {
            const upButton = firstGroup.querySelector('.btn-up');
            if (upButton) upButton.classList.add('disabled');
        }

        const lastGroup = questionGroups[questionGroups.length - 1];
        if (lastGroup) {
            const downButton = lastGroup.querySelector('.btn-down');
            if (downButton) downButton.classList.add('disabled');
        }
    }

    @if($showQuestionsBusiness)
    function ajustOrderButtonsBusiness() {
        const questionGroups = document.querySelectorAll('[id^="business-"][id$="-group"]');
        questionGroups.forEach(group => {
            const buttons = group.querySelectorAll('.btn-up, .btn-down');
            buttons.forEach(button => {
                button.classList.remove('disabled');
                button.style.cursor = 'pointer';
            });
        });

        const firstGroup = questionGroups[0];
        if (firstGroup) {
            const upButton = firstGroup.querySelector('.btn-up');
            if (upButton) upButton.classList.add('disabled');
        }

        const lastGroup = questionGroups[questionGroups.length - 1];
        if (lastGroup) {
            const downButton = lastGroup.querySelector('.btn-down');
            if (downButton) downButton.classList.add('disabled');
        }
    }

    function moveUpBusiness(order) {
        const index = questionsBusinessOrder.findIndex(question => question.order === order);
        if (index > 0) {
            [questionsBusinessOrder[index], questionsBusinessOrder[index - 1]] = [questionsBusinessOrder[index - 1], questionsBusinessOrder[index]];
            ajustBusinessOrder();
        }
    }

    function moveDownBusiness(order) {
        const index = questionsBusinessOrder.findIndex(question => question.order === order);
        if (index < questionsBusinessOrder.length - 1) {
            [questionsBusinessOrder[index], questionsBusinessOrder[index + 1]] = [questionsBusinessOrder[index + 1], questionsBusinessOrder[index]];
            ajustBusinessOrder();
        }
    }

    function ajustBusinessOrder() {
        questionsBusinessOrder.forEach((question, index) => {
            question.order = index + 1;
        });
        renderQuestionsBusiness();
    }

    function removeQuestionBusiness(order) {
        const questionIndex = questionsBusinessOrder.findIndex(question => question.order === order);
        if (questionIndex > -1) {
            questionsBusinessOrder.splice(questionIndex, 1);
            document.getElementById(`business-${order}-group`).remove();
            ajustOrderButtonsBusiness();
            updateQuestionsBusinessInput();
        }
    }
    @endif
</script>
