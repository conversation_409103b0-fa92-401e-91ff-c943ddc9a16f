@extends('layouts.app', ['activePage' => 'businesses'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-12" style="margin-bottom: 2rem;">
                        <div class="card card_conteudo">
                            <div class="card-header"
                                 style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                                <div class="row">
                                    <div class="col-9 d-flex justify-content-between align-items-center"
                                         style="margin-top: 9px; margin-bottom:6px">
                                        <h5 class="mb-0 card-title"
                                            style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                            Alterar Administrador do Negócio
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                {{-- Form de busca por CPF --}}
                                <form action="{{ route('businesses.editAdmin', $business['id']) }}" id="form-search-employee" method="GET">
                                    @csrf
                                    <div class="d-flex  align-items-center justify-content-center">
                                        <div class="d-flex flex-column align-items-center justify-content-center col-md-6 border p-4">
                                            <h5>Buscar Funcionário</h5>
                                            <div class="form-group w-100">
                                                <input type="text" class="form-control" id="cpf_search"
                                                       name="cpf_search" placeholder="Digite o CPF do funcionário"
                                                       value="{{ old('cpf_search')?? $cpfSearch ?? $employee['cpf'] ?? '' }}"
                                                       required>
                                            </div>
                                            <div class="form-group w-100">
                                                <button id="submit-button" type="submit" class="btn btn-primary w-100">
                                                    Buscar
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                {{-- Form para atualizar o admin --}}
                                <form action="{{ route('businesses.updateAdmin', $business['id']) }}" id="form-administrator" method="POST">
                                    @csrf
                                    @include('businesses.create.employee_form')

                                    <input type="hidden" name="employee_id" value="{{ $business['employee_id'] ?? '' }}">
                                    <input type="hidden" name="isCreateNewEmployeeAndUser" value="{{ $isCreateNewEmployeeAndUser ? '1' : '0' }}">
                                    <input type="hidden" name="isEditEmployee" value="{{ $isEditEmployee ? '1' : '0' }}">
                                    <input type="hidden" name="isCreateNewEmployee" value="{{ $isCreateNewEmployee ? '1' : '0' }}">

                                    <div class="col-md-12">
                                        <hr>
                                    </div>
                                    <div class="col-md-12 d-flex justify-content-between" style="margin-top: 20px;">
                                        <a class="btn btn-light"
                                           href='{{route('businesses.show', $business['id'])}}'>Voltar</a>
                                        <button id="submit-step-three" type="button"
                                                onclick="validateEmailAdm('form-administrator');"
                                                @if(!$isCreateNewEmployeeAndUser  && !$isCreateNewEmployee && !$isEditEmployee) disabled @endif
                                                class="btn btn-primary">Salvar Alterações
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        async function validateEmailAdm(formId)  {
            const form = document.getElementById(formId);

            let phone_number_1 = $('#phone_number_1').val();
            phone_number_1 = phone_number_1.replace(/\D/g, '');
            if (phone_number_1.length < 10 || phone_number_1.length > 11) {
                alert('Informe um telefone válido.');
                return;
            }

            let phone_validated = $('#phone_validated').val();
            if (phone_validated != '1') {
                alert('O telefone não foi validado. Por favor, valide o telefone antes de prosseguir.');
                return;
            }

            const email = document.getElementById('email').value;
            const cpf = document.getElementById('cpf').value.replace(/\D/g, '');
            const oldEmail = "{{ $employee['email'] ?? '' }}";

            let emailExist = false;
            let validated = false;
            await $.ajax({
                url:"<?= env('API_URL') ?>/api/employees/validate/email_exist",
                type:'get',
                data:{
                    email: email,
                    cpf: cpf,
                    idEmployee: "<?= $business['employee_id'] ?? '' ?>",
                    isCreateNewEmployeeAndUser: "<?= $isCreateNewEmployeeAndUser ?>",
                    isEditEmployee: "<?= $isEditEmployee ?>",
                    isCreateNewEmployee: "<?= $isCreateNewEmployee ?>",
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {
                    emailExist = response.exist;
                    validated = true;
                },
                error: function (response, textStatus, msg) {
                    alert('Falha ao validar o email');
                }
            });
            if (!validated) {
                return;
            }
            if (emailExist) {
                alert('O email já está cadastrado');
                return;
            }

            if (email === oldEmail) {
                form.submit();
            } else {
                confirmEmployeeEmail(formId);
            }

        }

        $('#form-search-employee').submit(function (e) {
            e.preventDefault();
            let cpf = $('#cpf_search').val().replace(/\D/g, '');
            if (cpf.length < 11 || !cpfIsValid(cpf)) {
                alert('CPF inválido');
                return;
            }
            this.submit();
        });
    </script>
@endsection
