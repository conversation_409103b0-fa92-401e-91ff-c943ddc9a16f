<div class="modal" tabindex="-1" role="dialog" id="{{ $modalId }}">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aprova<PERSON></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body overflow-auto">
                <div class="row">
                    <div class="col">
                        <p>Você deseja aprovar ou rejeitar o <b>{{ $business['name'] }}</b>?</p>
                    </div>
                </div>
            </div>
                <div class="modal-footer">
                    <form action="{{ route('businesses.reject', $business['id']) }}" method="POST">
                        @csrf
                        @method('post')
                        <input type="hidden" id="business_id-{{ $business['id'] }}" name="business_id" value="{{ $business['id'] }}">
                        <button type="submit" class="btn btn-danger">Rejeitar</button>
                    </form>
                    <form action="{{ route('businesses.approve', $business['id']) }}" method="POST">
                        @csrf
                        @method('post')
                        <input type="hidden" id="business_id-{{ $business['id'] }}" name="business_id" value="{{ $business['id'] }}">
                        <button type="submit" class="btn btn-success">Aprovar</button>
                    </form>
                </div>
        </div>
    </div>
</div>
