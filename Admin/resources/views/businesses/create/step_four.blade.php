@extends('layouts.app', ['activePage' => 'businesses'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">


            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-12" style="margin-bottom: 2rem;">
                        <div class="card card_conteudo">
                            <div class="card-header"
                                 style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                                <div class="row">
                                    <div class="col-9 d-flex justify-content-between align-items-center"
                                         style="margin-top: 9px; margin-bottom:6px">
                                        <h5 class="mb-0 card-title"
                                            style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                            Finalização</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-row">
                                    <div class="col-md-4" style="margin-top: 10px; margin-bottom: 10px;">
                                        <img
                                            src="{{ old('logo', isset($business['logo']) ? $business['logo'] : asset('images/no_logo.png')) }}"
                                            alt="Logo" id="logo-preview" class="img-fluid rounded mx-auto d-block"
                                            style="max-height: 340px;"
                                        >
                                    </div>

                                    <div class="col-md-8">
                                        <div class="col-md-12 form-row">
                                            <div class="col-md-6">
                                                <label for="name">Nome fantasia do negócio</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['name']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="name">CNPJ do negócio</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['cnpj']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="name">Razão social do negócio</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['corporate_reason']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="plan">Plano</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['plan_name']}} " disabled>
                                            </div>
                                        </div>
                                        <div class="col-md-12 form-row mt-md-4">
                                            <div class="col-md-6">
                                                <label for="name">CEP</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['cep']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="name">Bairro</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['neighborhood']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="name">Rua</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['street']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="name">Número</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['number']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="name">Complemento</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['complement']}} " disabled>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="name">Cidade</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['city']}} " disabled>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="name">Estado</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['state']}} " disabled>
                                            </div>
                                        </div>
                                        <div class="col-md-12 form-row mt-md-4">
                                            @if ($business['url_facebook'])
                                                <div class="col-md-6">
                                                    <label for="name">
                                                        <i class="fab fa-facebook fa-fw"></i>
                                                        Link Facebook
                                                    </label>
                                                    <input type="text" class="form-control" name="name"
                                                        value="{{ $business['url_facebook']}} " disabled>
                                                </div>
                                            @endif
                                            @if ($business['url_google'])
                                                <div class="col-md-6">
                                                    <label for="name">
                                                        <i class="fab fa-google fa-fw"></i>
                                                        Link Google
                                                    </label>
                                                    <input type="text" class="form-control" name="name"
                                                        value="{{ $business['url_google']}} " disabled>
                                                </div>
                                            @endif
                                            @if ($business['url_instagram'])
                                                <div class="col-md-6">
                                                    <label for="name">
                                                        <i class="fab fa-instagram fa-fw"></i>
                                                        Link Instagram
                                                    </label>
                                                    <input type="text" class="form-control" name="name"
                                                        value="{{ $business['url_instagram']}} " disabled>
                                                </div>
                                            @endif
                                            @if ($business['url_linkedin'])
                                                <div class="col-md-6">
                                                    <label for="name">
                                                        <i class="fab fa-linkedin fa-fw"></i>
                                                        Link Linkedin
                                                    </label>
                                                    <input type="text" class="form-control" name="name"
                                                        value="{{ $business['url_linkedin']}} " disabled>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="col-md-12 form-row mt-md-4">
                                            <div class="col-md-6">
                                                <label for="name">Nome do Adminstrador</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['employee']['name']}} " disabled>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="name">CPF do Adminstrador</label>
                                                <input type="text" class="form-control" name="name"
                                                       value="{{ $business['employee']['cpf']}} " disabled>
                                            </div>
                                        </div>
                                        @if ($business['seller_name'])
                                            <div class="col-md-12 form-row mt-md-4">
                                                <div class="col-md-6">
                                                    <label for="name">Nome do Vendedor</label>
                                                    <input type="text" class="form-control" name="name"
                                                        value="{{ $business['seller_name']}} " disabled>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <form action="{{ route('businesses.create.step.four.post') }}"
                                      method="POST">
                                    @csrf
                                    <div class="col-md-12">
                                        <hr>
                                    </div>
                                    <div class="col-md-12 d-flex justify-content-between" style="margin-top: 20px;">
                                        <a class="btn btn-light"
                                           href='{{route('businesses.create.step.three')}}'>Voltar</a>
                                        <button id="submit-button" type="submit" class="btn btn-primary">Finalizar
                                        </button>
                                    </div>
                                </form>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
@endsection
