@extends('layouts.app', ['activePage' => 'businesses'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12" style="margin-bottom: 2rem;">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row">
                            <div class="col-9 d-flex justify-content-between align-items-center"
                                 style="margin-top: 9px; margin-bottom:6px">
                                <h5 class="mb-0 card-title"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Informações do Negócio</h5>
                            </div>
                            <div class="col-3">
                                <span style="color:red">* Campos obrigatórios</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('businesses.create.step.one.post') }}" id="form-update-business"
                              method="POST">
                            @csrf
                            <div class="form-row">
                                <div class="col-md-4" style="margin-top: 10px; margin-bottom: 10px;">
                                    <img
                                        src="{{ isset($business['logo']) &&  $business['logo'] != null ? $business['logo'] : asset('images/no_logo.png') }}"
                                        alt="Logo" id="logo-preview" class="img-fluid rounded mx-auto d-block"
                                        style="max-height: 340px; cursor: pointer; "
                                        onclick="document.getElementById('logo_temp').click()">

                                    <!-- Auxiliary input for file selection -->
                                    <input type="file" class="form-control-file d-none" id="logo_temp"
                                           accept="image/png, image/jpeg, image/jpg, image/gif"
                                           onchange="validateFileSize(this)">

                                    <!-- Actual input that will be submitted with the form -->
                                    <input type="file" class="form-control-file d-none" id="logo" name="logo_file"
                                           accept="image/png, image/jpeg, image/jpg, image/gif">
                                    <input type="hidden" name="logo" id="logo_base64" value="{{ old('logo') }}">
                                    <input type="hidden" name="logo_file" id="logo_file" value="{{ old('logo_file') }}">
                                </div>

                                <div class="col-md-8">
                                    <div class="col-md-12">
                                        <label for="name">Nome fantasia<span style="color:red">*</span></label>
                                        <input type="text" class="form-control" name="name" id="name"
                                               value="{{empty(old('name')) ? $business['name'] : old('name')}}">
                                    </div>
                                    <div class="col-md-12" style="margin-top: 10px">
                                        <label for="description">Descrição (1000 caracteres)</label>
                                        <textarea class="form-control" rows="7"
                                                  name="description" maxlength="1000">{{empty(old('description')) ? $business['description'] : old('description')}}</textarea>
                                    </div>
                                </div>

                                <div class="d-flex col-md-12" style="flex-wrap: wrap">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'cnpj',
                                                'label' => 'CNPJ do negócio',
                                                'value' => old('cnpj') ?? ($business['cnpj'] ?? old('cnpj')),
                                                'placeholder' => 'Digite o CNPJ',
                                                'onchange' =>'checkCNPJ()',
                                                'withErrorText' => true,
                                            ])
                                            <div class="form-check mb-2">
                                                <input type="checkbox" class="form-check-input" id="cnpj_optional" name="cnpj_optional" value="1" onchange="toggleCnpjOptional()" @if(isset($business['cnpj_optional']) && $business['cnpj_optional'] == 1) checked @endif>
                                                <label class="form-check-label" for="cnpj_optional">
                                                    Não tenho CNPJ ou não quero informar
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'corporate_reason',
                                                'label' => 'Razão social',
                                                'value' => old('corporate_reason') ?? ($business['corporate_reason'] ?? old('corporate_reason')),
                                                'placeholder' => 'Digite a razão social',
                                                'withErrorText' => true,
                                                'required' => false,
                                            ])
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex col-md-12 my-3" style="flex-wrap: wrap">
                                    <div class="col-md-12"
                                        style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                        Endereço</div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'cep',
                                                'label' => 'CEP',
                                                'placeholder' => 'Digite o cep',
                                                'value' => old('cep') ?? ($business['cep'] ?? old('cep')),
                                                'withErrorText' => true,
                                                'required' => false,
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'neighborhood',
                                                'label' => 'Bairro',
                                                'value' => old('neighborhood') ?? ($business['neighborhood'] ?? old('neighborhood')),
                                                'placeholder' => 'Digite o nome do bairro',
                                                'withErrorText' => true,
                                                'required' => false,
                                                ])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'street',
                                                'label' => 'Rua',
                                                'placeholder' => 'Digite o nome da rua',
                                                'value' => old('street') ?? ($business['street'] ?? old('street')),
                                                'withErrorText' => true,
                                                'required' => false,
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'number',
                                                'label' => 'Número',
                                                'value' => old('number') ?? ($business['number'] ?? old('number')),
                                                'placeholder' => 'Digite o número (Ex: 01 ou SN)',
                                                'withErrorText' => true,
                                                'required' => false
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'complement',
                                                'label' => 'Complemento',
                                                'placeholder' => 'Digite o complemento',
                                                'value' => old('complement') ?? ($business['complement'] ?? old('complement')),
                                                'withErrorText' => true,
                                                'required' => false
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'city',
                                                'label' => 'Cidade',
                                                'placeholder' => 'Digite o nome da cidade',
                                                'value' => old('city') ?? ($business['city'] ?? old('city')),
                                                'withErrorText' => true,
                                                'readonly' => true,
                                                'required' => false,
                                            ])
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            @include('components.inputs.text', [
                                                'name' => 'state',
                                                'label' => 'Estado',
                                                'placeholder' => 'Digite o estado',
                                                'value' => old('state') ?? ($business['state'] ?? old('state')),
                                                'withErrorText' => true,
                                                'readonly' => true,
                                                'required' => false,
                                            ])
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex col-md-12 my-3" style="flex-wrap: wrap">
                                    <div class="col-md-12"
                                         style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                        Redes sociais
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="url_facebook">
                                                <i class="fab fa-facebook fa-fw"></i>
                                                Link do Facebook
                                            </label>
                                            <input type="text" class="form-control" name="url_facebook"
                                                   id="url_facebook" 
                                                   value="{{ old('url_facebook') ?? ($business['url_facebook'] ?? old('url_facebook')) }}"
                                                   placeholder="https://www.facebook.com/">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="url_google">
                                                <i class="fab fa-google fa-fw"></i>
                                                Link do Google
                                            </label>
                                            <input type="text" class="form-control" name="url_google"
                                                   id="url_google" 
                                                   value="{{ old('url_google') ?? ($business['url_google'] ?? old('url_google')) }}"
                                                   placeholder="https://www.google.com/">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="url_instagram">
                                                <i class="fab fa-instagram fa-fw"></i>
                                                Link do Instagram
                                            </label>
                                            <input type="text" class="form-control" name="url_instagram"
                                                   id="url_instagram" 
                                                   value="{{ old('url_instagram') ?? ($business['url_instagram'] ?? old('url_instagram')) }}"
                                                   placeholder="https://www.instagram.com/">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="url_linkedin">
                                                <i class="fab fa-linkedin fa-fw"></i>
                                                Link do Linkedin
                                            </label>
                                            <input type="text" class="form-control" name="url_linkedin"
                                                   id="url_linkedin" 
                                                   value="{{ old('url_linkedin') ?? ($business['url_linkedin'] ?? old('url_linkedin')) }}"
                                                   placeholder="https://www.linkedin.com/">
                                        </div>
                                    </div>
                                </div>

                                {{-- <div id='form_coordinates' class="form-row col-md-12 align-items-end">
                                    <div class="col-md-4" style="margin-top: 10px;margin-bottom: 10px;">
                                        <label for="latitude">Latitude</label>
                                        <input type="text" class="form-control" name="latitude" id="latitude"
                                               value="{{empty(old('latitude')) ? $business['latitude'] : old('latitude')}}">
                                    </div>
                                    <div class="col-md-4" style="margin-top: 10px;margin-bottom: 10px;">
                                        <label for="longitude">Longitude</label>
                                        <input type="text" class="form-control" name="longitude" id="longitude"
                                               value="{{empty(old('longitude')) ? $business['longitude'] : old('longitude')}}">
                                    </div>
                                    <div class="col-md-4" style="margin-top: 10px;margin-bottom: 10px;">
                                        <button type="button" class="btn btn-primary" data-toggle="modal"
                                                data-target="#map-business" style="width: 100%"
                                                onclick="editLocation()"
                                                id="show-map-button">Escolher Localização
                                        </button>
                                    </div>
                                </div>
                            </div> --}}

                            <div class="col-md-12 d-flex justify-content-between" style="margin-top: 20px;">
                                <a class="btn btn-light" href='{{route('businesses.index')}}'>Voltar</a>
                                <button id="submit-button" type="submit" class="btn btn-primary">Próximo</button>
                            </div>
                        </form>
                        {{-- Modal selecionar localização
                        <div class="modal fade" tabindex="-1" role="dialog" id="map-business-edit"
                             aria-labelledby="map-business-edit" aria-hidden="true">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Selecione a Localização do Negócio</h5>
                                        <button type="button" class="close" data-dismiss="modal"
                                                aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body p-0">
                                        <div class="mapaGoogle" id="map-edit"
                                             style="height: 500px; width: 100%">
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                            Sair
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="setLocation()">
                                            Selecionar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div> --}}

                    </div>
                </div>
            </div>
        </div>
        @endsection

        @push('js')
            <script type="text/javascript"
            src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
            <script type="text/javascript" src="{{ asset('js/validateCnpj.js') }}"></script>
            <script>
                let mapEdit;
                let markersArray = [];

                @if(isset($business['latitude']) && isset($business['longitude']) && $business['latitude'] != null && $business['longitude'] != null)
                let locationSelected = {lat: {{ $business['latitude'] }}, lng: {{ $business['longitude'] }}};
                @else
                let locationSelected = null;
                @endif

                function addMarker(location, map) {
                    removeMarkers(map);
                    const marker = new google.maps.marker.AdvancedMarkerElement({
                        map,
                        position: {lat: location.lat, lng: location.lng},
                        title: 'Localização do serviço',
                    });

                    markersArray.push(marker);
                }

                $(document).ready(applyMasks());

                $("#cep").focusout(function() {
                    $.ajax({
                        url: 'https://viacep.com.br/ws/' + $(this).val() + '/json/',
                        dataType: 'json',
                        success: function(response) {
                            $("#complement").val(response.complemento)
                            $("#street").val(response.logradouro)
                            $("#neighborhood").val(response.bairro)
                            $("#city").val(response.localidade)
                            $("#state").val(response.uf)
                            $("#number").focus()
                        }
                    })
                })

                function applyMasks() {
                    $("#cnpj").mask('00.000.000/0000-00');
                    $("#cep").mask('00000-000');
                }

                function removeMarkers(map) {
                    for (let i = 0; i < markersArray.length; i++) {
                        markersArray[i].setMap(null);
                    }
                }

                function checkCNPJ() {
                    const isOptional = document.getElementById('cnpj_optional').checked;
                    const cnpjInput = document.getElementById('cnpj');
                    let isValid = false;
                    let cnpj = cnpjInput.value.replaceAll('.', '').replaceAll('-', '').replaceAll('/', '');
                    
                    // Se CNPJ é opcional e está vazio, considera válido
                    if (isOptional && cnpj !== '') {
                        cnpjInput.classList.remove("is-invalid");
                        cnpjInput.classList.remove("is-valid");
                        return true;
                    }
    
                    if (cnpj.length === 14) {
                        isValid = cnpjIsValid(cnpj);
                        if (isValid) {
                            cnpjInput.classList.remove("is-invalid");
                            cnpjInput.classList.add("is-valid");
                        } else {
                            cnpjInput.classList.add("is-invalid");
                        }
                    } else {
                        cnpjInput.classList.remove("is-invalid");
                        cnpjInput.classList.remove("is-valid");
                    }
                    return isValid;
                }

                function limparLocalizacao() {
                    locationSelected = null;
                    removeMarkers(mapEdit);
                }

                async function initMapEdit(latitude, longitude) {
                    const {Map} = await google.maps.importLibrary("maps");
                    const {AdvancedMarkerElement, PinElement} = await google.maps.importLibrary("marker");

                    mapEdit = new Map(document.getElementById("map-edit"), {
                        center: {lat: latitude, lng: longitude},
                        zoom: 12,
                        mapId: "1",
                    });

                    mapEdit.addListener("click", (event) => {
                        addMarker({lat: event.latLng.lat(), lng: event.latLng.lng()}, mapEdit);
                        locationSelected = {lat: event.latLng.lat(), lng: event.latLng.lng()};
                        $('#location').val(event.latLng.lat() + ', ' + event.latLng.lng());
                    });
                    @if(isset($business['latitude']) && isset($business['longitude']))
                    addMarker({lat: locationSelected.lat, lng: locationSelected.lng}, mapEdit);
                    @endif
                        @if(!isset($business['latitude']) || $business['latitude'] == null || !isset($business['longitude']) || $business['longitude'] == null)
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            (position) => {
                                const latitude = position.coords.latitude;
                                const longitude = position.coords.longitude;

                                mapEdit.setCenter({lat: latitude, lng: longitude});
                            },
                            (error) => {
                                console.error("Error getting location:", error);
                            }
                        );
                    } else {
                        console.error("Geolocation is not supported by this browser.");
                    }
                    @endif
                }

                function setLocation() {
                    if (locationSelected) {
                        $('#latitude').val(locationSelected.lat);
                        $('#longitude').val(locationSelected.lng);
                        $('#map-business-edit').modal('hide');
                    } else {
                        alert('Selecione uma localização no mapa.');
                    }
                }
            </script>
            <script>
                function editLocation() {
                    $('#map-business-edit').modal('show');
                    initMapEdit({{ $business['latitude']??-8.050000 }}, {{ $business['longitude']??-34.900002 }});
                }

                function edit() {
                    const campos = document.querySelectorAll('.style_campo_estatico');
                    campos.forEach(campo => {
                        campo.removeAttribute('disabled');
                        campo.classList.remove('style_campo_estatico');
                    });

                    document.getElementById('logo').removeAttribute('disabled');
                    //style cursor pointer
                    document.getElementById('logo-preview').style.cursor = 'pointer';
                    document.getElementById('submit-button').removeAttribute('disabled');

                    document.getElementById('edit-button').innerText = 'Cancelar Edição';
                    document.getElementById('edit-button').setAttribute('onclick', 'cancelEdit()');

                    document.getElementById('show-map-button').innerText = 'Editar Localização';
                    document.getElementById('show-map-button').setAttribute('onclick', 'editLocation()');
                    document.getElementById('show-map-button').removeAttribute('data-target');
                    document.getElementById('show-map-button').removeAttribute('data-toggle');
                    document.getElementById('show-map-button').removeAttribute('disabled');


                }



                function validateFileSize(inputFile) {
                    let files = inputFile.files;

                    // If no file was selected, do nothing
                    if (files.length === 0) {
                        return;
                    }

                    let maxSizeInBytes = 5 * 1024 * 1024; // 5MB
                    let isValid = true;

                    for (let i = 0; i < files.length; i++) {
                        if (files[i].size > maxSizeInBytes) {
                            isValid = false;
                            break;
                        }
                    }

                    if (!isValid) {
                        alert('O tamanho máximo permitido para a imagem é de 5MB.');
                        // Clear the temporary input so that the user can re-select
                        inputFile.value = '';
                    } else {
                        // The file is valid, copy it to the actual 'logo' input
                        copyFileInput(inputFile, document.getElementById('logo'));
                        loadLogo(inputFile);
                    }
                }

                function copyFileInput(sourceInput, targetInput) {
                    // Since we cannot set targetInput.files directly, we can create a DataTransfer object
                    const dataTransfer = new DataTransfer();
                    for (let i = 0; i < sourceInput.files.length; i++) {
                        dataTransfer.items.add(sourceInput.files[i]);
                    }
                    targetInput.files = dataTransfer.files;
                }

                function loadLogo(inputFile) {
                    if (inputFile.files.length === 0) {
                        return;
                    }
                    let reader = new FileReader();
                    reader.onload = function (e) {
                        $('#logo-preview').attr('src', e.target.result);
                    };
                    reader.readAsDataURL(inputFile.files[0]);
                }

                $(document).ready(function () {

                    @if(old('logo') != null)
                    const oldName = "{{ old('logo_file') }}";
                    let oldBase64 = "{{ old('logo') }}";


                    // Create a Blob from the base64 string
                    let byteString = atob(oldBase64.split(',')[1]);
                    let mimeString = oldBase64.split(',')[0].split(':')[1].split(';')[0];
                    let ab = new ArrayBuffer(byteString.length);
                    let ia = new Uint8Array(ab);
                    for (let i = 0; i < byteString.length; i++) {
                        ia[i] = byteString.charCodeAt(i);
                    }
                    let blob = new Blob([ab], {type: mimeString});
                    let file = new File([blob], oldName, {type: mimeString});

                    // Create a DataTransfer to simulate the input file
                    let dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    document.getElementById('logo').files = dataTransfer.files;

                    // Display the image in the preview
                    document.getElementById('logo-preview').src = oldBase64;

                    @elseif(isset($business['logo']) && $business['logo'] != null)
                    let businessLogoUrl = "{{ $business['logo'] }}";

                    // Fetch the image and create a File object
                    fetch(businessLogoUrl)
                        .then(res => res.blob())
                        .then(blob => {
                            let file = new File([blob], 'logo.jpg', {type: blob.type});
                            let dataTransfer = new DataTransfer();
                            dataTransfer.items.add(file);
                            document.getElementById('logo').files = dataTransfer.files;
                        })
                        .catch(err => {
                            console.error('Error fetching business logo:', err);
                        });

                    // Display the image in the preview
                    document.getElementById('logo-preview').src = businessLogoUrl;

                    @else
                    const noLogoImage = "{{ asset('images/no_logo.png') }}";
                    document.getElementById('logo-preview').src = noLogoImage;
                    @endif
                });

                let botaoSalvarClicado = false;

                const botaoSalvar = $('#submit-button');
                botaoSalvar.click(function () {
                    botaoSalvarClicado = true;
                });

                const formulario = document.getElementById('form-update-business');
                formulario.addEventListener('submit', function (event) {
                    event.preventDefault();
                    const isOptional = document.getElementById('cnpj_optional').checked;
                    if (!isOptional && !checkCNPJ()) {
                        alert('O CNPJ digitado é inválido!')
                        return;
                    }

                    submitUpdateBusiness(event);
                });

                const campos = formulario.querySelectorAll('input, textarea, select');

                const valoresIniciais = Array.from(campos).reduce((obj, campo) => {
                    obj[campo.name] = campo.value;
                    return obj;
                }, {});

                function mudou() {
                    for (let i = 0; i < campos.length; i++) {
                        const campo = campos[i];
                        if (campo.value !== valoresIniciais[campo.name]) {
                            return true;
                        }
                    }
                    return false;
                }

                function exibirAlertaSaida(event) {
                    if (mudou() && !botaoSalvarClicado) {
                        event.preventDefault();
                        let mensagem = 'Tem certeza que deseja sair desta página?';
                        event.returnValue = mensagem;
                        return mensagem;
                    } else {
                        botaoSalvarClicado = false;
                    }
                }

                window.addEventListener('beforeunload', exibirAlertaSaida);

                function submitUpdateBusiness() {
                    //remove logo base64 input if exists and new file was selected
                    const logoBase64 = document.getElementById('logo_base64');
                    if (logoBase64 !== null && document.getElementById('logo').files.length > 0) {
                        logoBase64.remove();
                    }

                    //if has logo file, create input text with base64 value
                    const logo = document.getElementById('logo');

                    if (logo.files.length > 0) {
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            const base64 = e.target.result;
                            const input = document.createElement('input');
                            input.id = 'logo_base64';
                            input.type = 'hidden';
                            input.name = 'logo';
                            input.value = base64;
                            formulario.appendChild(input);

                            //input with name and type file
                            const input2 = document.createElement('input');
                            input2.id = 'logo_file';
                            input2.type = 'hidden';
                            input2.name = 'logo_file';
                            input2.value = logo.files[0].name;
                            formulario.appendChild(input2);

                            validateName();
                        };
                        reader.readAsDataURL(logo.files[0]);
                    } else {
                        validateName();
                    }
                }

                function validateName() {
                    const name = $('#name').val();
                    const form = document.getElementById('form-update-business');

                    if (name == "") {
                        form.submit();
                        return;
                    }
                    $.ajax({
                        url: "<?= env('API_URL') ?>/api/business/validate/name",
                        type: 'get',
                        data: {
                            name: name,
                        },
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                        },
                        success: function (response) {
                            if (response['exists_active']) {
                                if (confirm("Já existe um negócio com esse nome fantasia, mesmo assim deseja criar um novo?")) {
                                    form.submit();
                                }
                            } else if (response['exists_deleted']) {
                                if (confirm("Já existe um negócio desativado com esse nome fantasia, mesmo assim deseja criar um novo?")) {
                                    form.submit();
                                }
                            } else {
                                form.submit();
                            }

                        },
                        error: function (response, textStatus, msg) {
                            console.log(response, textStatus, msg);
                        }
                    });
                }


                function ajust_layout_mobile() {
                    if ($(window).width() < 750) {
                        $('#form_coordinates').removeClass('form-row col-md-12 align-items-end');
                        $('#form_coordinates').addClass('col-md-8');
                    } else {
                        $('#form_coordinates').removeClass('col-md-8');
                        $('#form_coordinates').addClass('form-row col-md-12 align-items-end');
                    }
                }

                ajust_layout_mobile();
                $(window).resize(function () {
                    ajust_layout_mobile();
                });

                function toggleCnpjOptional() {
                    const isOptional = document.getElementById('cnpj_optional').checked;
                    const cnpjInput = document.getElementById('cnpj');
                    
                    if (isOptional) {
                        cnpjInput.value = '';
                        cnpjInput.setAttribute('disabled', 'disabled');
                        cnpjInput.classList.remove("is-invalid");
                        cnpjInput.classList.remove("is-valid");
                        $('#error-text-cnpj').remove();
                        $('#required-cnpj_input').hide();
                    } else {
                        cnpjInput.removeAttribute('disabled');
                        $('#required-cnpj_input').show();
                    }
                }
                @if(isset($business['cnpj_optional']) && $business['cnpj_optional'] == 1)
                    toggleCnpjOptional();
                @endif
            </script>
        @endpush
