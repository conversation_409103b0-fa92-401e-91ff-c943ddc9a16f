@extends('layouts.app', ['activePage' => 'businesses'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-12" style="margin-bottom: 2rem;">
                        <div class="card card_conteudo">
                            <div class="card-header"
                                 style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                                <div class="row">
                                    <div class="col-9 d-flex justify-content-between align-items-center"
                                         style="margin-top: 9px; margin-bottom:6px">
                                        <h5 class="mb-0 card-title"
                                            style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                            Selecionar Administrador</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <form action="{{ route('businesses.create.step.three') }}" id="form-search-employee"
                                      method="GET">
                                    @csrf
                                    <div class="d-flex  align-items-center justify-content-center">
                                        <div
                                            class="d-flex flex-column align-items-center justify-content-center col-md-6 border p-4">
                                            <h5>Buscar Funcionário</h5>
                                            <div class="form-group w-100">
                                                <input type="text" class="form-control" id="cpf_search"
                                                       name="cpf_search" placeholder="Digite o CPF do funcionário"
                                                       value="{{ old('cpf_search')?? $cpfSearch ?? $employee['cpf'] ?? '' }}"
                                                       required>
                                            </div>
                                            <div class="form-group w-100">
                                                <button id="submit-button" type="submit" class="btn btn-primary w-100">
                                                    Buscar
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                                <form action="{{ route('businesses.create.step.three.post') }}"
                                      id="form-administrator"
                                      method="POST">
                                    @csrf
                                    @include('businesses.create.employee_form')
                                    <div class="col-md-12">
                                        <hr>
                                    </div>
                                    <div class="col-md-12 d-flex justify-content-between" style="margin-top: 20px;">
                                        <a class="btn btn-light"
                                           href='{{route('businesses.create.step.two')}}'>Voltar</a>
                                        <button id="submit-step-three" type="button"
                                           onclick="validateEmailAdm('form-administrator');"
                                           @if(!$isCreateNewEmployeeAndUser  && !$isCreateNewEmployee && !$isEditEmployee) disabled @endif
                                           class="btn btn-primary">Próximo
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        async function validateEmailAdm(formId)  {
            const form = document.getElementById(formId);

            let phone_validated = $('#phone_validated').val();
            if (phone_validated != '1') {
                alert('O telefone não foi validado. Por favor, valide o telefone antes de prosseguir.');
                return;
            }

            const email = document.getElementById('email').value;
            const cpf = document.getElementById('cpf').value.replace(/\D/g, '');
            const oldEmail = "{{ $employee['email'] ?? '' }}";

            let emailExist = false;
            let validated = false;
            await $.ajax({
                url:"<?= env('API_URL') ?>/api/employees/validate/email_exist",
                type:'get',
                data:{
                    email: email,
                    cpf: cpf,
                    idEmployee: "<?= $business['employee_id'] ?? '' ?>",
                    isCreateNewEmployeeAndUser: "<?= $isCreateNewEmployeeAndUser ?>",
                    isEditEmployee: "<?= $isEditEmployee ?>",
                    isCreateNewEmployee: "<?= $isCreateNewEmployee ?>",
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {
                    emailExist = response.exist;
                    validated = true;
                },
                error: function (response, textStatus, msg) {
                    alert('Falha ao validar o email');
                }
            });
            if (!validated) {
                return;
            }
            if (emailExist) {
                alert('O email já está cadastrado');
                return;
            }

            if (email === oldEmail) {
                form.submit();
            } else {
                confirmEmployeeEmail(formId);
            }

        }

        $('#form-search-employee').on('submit', function (e) {
            let cpf = $('#cpf_search').val().replace(/\D/g, '');
            if (cpf.length < 11 || !cpfIsValid(cpf)) {
                e.preventDefault();
                alert('CPF inválido');
            }
        });
    </script>
@endsection
