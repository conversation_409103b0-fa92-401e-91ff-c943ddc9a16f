@if(isset($isCreateNewEmployeeAndUser) && $isCreateNewEmployeeAndUser)
    <div class="card bg-info text-white p-2 my-4">
        Cadastro de um novo funcionário.
    </div>
@endif
@if(isset($isCreateNewEmployee) && $isCreateNewEmployee)
    <div class="card bg-info text-white p-2 my-4">
        Usuário já cadastrado no sistema com outro perfil de login.
    </div>
@endif
@if(isset($isEditEmployee) && $isEditEmployee)
    <div class="card bg-success text-white p-2 my-4">
        <p class="m-0">Funcionário Selecionado: <strong>{{ $employee['name'] }}</strong></p>
    </div>
@endif
<input type="hidden" name="employee_id" value="{{ $business['employee_id'] }}">
<div id="businessSelectedLabel"
     class="@if(!$isCreateNewEmployeeAndUser  && !$isCreateNewEmployee && !$isEditEmployee) d-none @endif col-md-12 form-row">

    <div class="col-md-12"><p class="form-title">Dados pessoais</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'name', 'label' => 'Nome completo', 'value' => $employee['name'] ?? old('name'), 'placeholder' => 'Digite o nome completo do funcionário', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="gender">Gênero</label>
            <select id="gender" name="gender" class="form-select form-control @error('gender') is-invalid @enderror"
                    aria-label="Default select example" required>
                <option value="other">-- Não Informar --</option>
                <option value="m"
                        @if(old('gender') == 'm' || isset($employee)  && $employee != null && $employee['gender'] == 'm') selected @endif >
                    Masculino
                </option>
                <option value="f"
                        @if(old('gender') == 'f' || isset($employee)  && $employee != null && $employee['gender'] == 'f') selected @endif >
                    Feminino
                </option>
            </select>
            @error('gender')
            <span class="invalid-feedback" role="alert">
                <strong>{{$message}}</strong>
            </span>
            @enderror
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.date', ['name' => 'birth_date', 'label' => 'Data de nascimento', 'value' => ($employee['birth_date'] ?? old('birth_date')), 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'cpf', 'label' => 'CPF', 'value' => ($employee['cpf'] ?? old('cpf')?? $cpfSearch), 'placeholder' => 'Digite o CPF', 'onchange' =>'checkCPF()', 'withErrorText' => true, 'readonly' => true])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Endereço</p></div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'cep', 'label' => 'CEP', 'value' => ($employee['address']['cep'] ?? $employee['cep'] ?? old('cep')), 'placeholder' => 'Digite o cep do funcionário', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'neighborhood', 'label' => 'Bairro', 'value' => ($employee['address']['neighborhood'] ?? $employee['neighborhood'] ?? old('neighborhood')), 'placeholder' => 'Digite o nome do bairro', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'street', 'label' => 'Rua', 'value' => ($employee['address']['street'] ?? $employee['street'] ?? old('street')), 'placeholder' => 'Digite o nome da rua', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'number', 'label' => 'Número', 'value' => ($employee['address']['number'] ?? $employee['number'] ?? old('number')), 'placeholder' => 'Digite o número da residência (Ex: 01 ou SN)', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'complement', 'label' => 'Complemento', 'value' => ($employee['address']['complement'] ?? $employee['complement'] ?? old('complement')), 'placeholder' => 'Digite o complemento', 'required' => false, 'withErrorText' => true])
        </div>
    </div>
    <div class="row flex-fill mb-4">
        <div class="col">
            @include('components.inputs.text', ['name' => 'city', 'label' => 'Cidade', 'value' => ($employee['address']['city'] ?? $employee['city'] ?? old('city')), 'readonly' => true, 'withErrorText' => true])
        </div>
        <div class="col">
            @include('components.inputs.text', ['name' => 'state', 'label' => 'Estado', 'value' => ($employee['address']['state'] ?? $employee['state'] ??old('state')), 'readonly' => true, 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Contato</p></div>
    
    @php
        // Logic for initial phone validation status
        $initialPhoneForValidationLogic = '';
        if (isset($employee['phone_number_1']) || old('phone_number_1')) {
            $rawPhone = $employee['phone_number_1'] ?? old('phone_number_1');
            $initialPhoneForValidationLogic = preg_replace('/\D/', '', $rawPhone);
        }
        // Check if it's an edit scenario and the initial phone is valid
        $isInitialPhoneActuallyValid = (isset($employee) && !empty($employee['phone_number_1'])) && preg_match('/^\d{10,11}$/', $initialPhoneForValidationLogic);
        // If it's a new entry but there's an old value (e.g., after validation error), check that
        if (!isset($employee) && old('phone_validated') === '1' && old('phone_number_1')) {
             $isInitialPhoneActuallyValid = preg_match('/^\d{10,11}$/', preg_replace('/\D/', '', old('phone_number_1')));
        }
    @endphp
    
    <div class="col-md-6">  
        @include('components.inputs.phone_number_with_validation', [
            'name' => 'phone_number_1',
            'id' => 'phone_number_1',
            'label' => 'Telefone/Celular 1',
            'class' => 'phone-input',
            'value' => ($employee['phone_number_1'] ?? old('phone_number_1')),
            'placeholder' => 'Digite o número do telefone do funcionário',
            'required' => true,
            'currentClientCpf' => $cpfSearch ?? $employee['cpf'] ?? old('cpf'),
            'validationFieldName' => 'phone_validated',
            'initialValidated' => $isInitialPhoneActuallyValid ? '1' : '0'
        ])
    </div>
    <div class="col-md-6">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'phone_number_2', 'label' => 'Telefone/Celular 2', 'value' => ($employee['phone_number_2'] ?? old('phone_number_2')), 'placeholder' => 'Digite o número do telefone do funcionário', 'required' => false, 'class' => 'phone-input', 'withErrorText' => true])
        </div>
    </div>
    <div class="col-md-12"><p class="form-title">Login</p></div>
    <div class="col-md-12">
        <div class="form-group">
            @include('components.inputs.text', ['name' => 'email', 'type' => 'email', 'label' => 'E-mail', 'value' => ($employee['email'] ?? old('email')), 'placeholder' => 'Digite o e-mail do funcionário', 'withErrorText' => true, 'required' => true])
        </div>
    </div>
    @if($isEditEmployee)
        <div class="col-md-12">
            <div class="form-group">
                <input type="checkbox" id="reset_password" name="reset_password">
                <label for="reset_password">Resetar a senha do funcionário</label>
            </div>
        </div>
    @endif
    <input type="hidden" name="email_validated" id="email_validated"
           value="{{ $employee['email_validated'] ?? old('email_validated') ?? 0}}">
</div>

@include('components.loading', ['message' => 'Aguardando confirmação de email'])
@include('components.modal.modal_confirm_email', ['modalId' => 'modal-confirm-email'])

@push('js')
    <script type="text/javascript"
            src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
    <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>

    <script type="text/javascript">
        // =======================================
        // VARIÁVEIS GLOBAIS
        // =======================================
        
        // CPF do funcionário atual - disponível globalmente para o modal WhatsApp
        window.currentClientCpf = "{{ $cpfSearch ?? $employee['cpf'] ?? old('cpf') ?? '' }}";
        
        const isHomologation = '{{ config("app.env") }}' === 'homologation';
        
        // Função para logs condicionais
        function debugLog(message, data = null) {
            if (isHomologation) {
                if (data) {
                    console.log(message, data);
                } else {
                    console.log(message);
                }
            }
        }

        // =======================================
        // INICIALIZAÇÃO E ALERTAS
        // =======================================
        @if(isset($isCreateNewEmployee) && $isCreateNewEmployee && $cpfSearch != null)
        $(document).ready(function () {
            alert('Encontramos esse usuário já cadastrado no nosso sistema para um outro perfil de login, segue as informações dele abaixo.');
        });
        @endif

        // =======================================
        // FUNÇÕES DE CEP
        // =======================================
        $("#cep").focusout(function () {
            $.ajax({
                url: 'https://viacep.com.br/ws/' + $(this).val().replaceAll('-', '') + '/json/',
                dataType: 'json',
                success: function (response) {
                    debugLog('CEP response:', response);
                    $("#complement").val(response.complemento);
                    $("#street").val(response.logradouro);
                    $("#neighborhood").val(response.bairro);
                    $("#city").val(response.localidade);
                    $("#state").val(response.uf);
                    $("#number").focus();
                }
            });
        });

        // =======================================
        // FUNÇÕES DE MÁSCARAS E VALIDAÇÃO CPF
        // =======================================
        let counter_phone2 = {value: 0};

        function applyMasks() {
            $("#cpf").mask('000.000.000-00');
            $("#cpf_search").mask('000.000.000-00');
            $("#cep").mask('00000-000');
            $("#phone_number_2").mask(getMask('phone_number_2')).keyup(function (event) {
                checkPhoneMask(event.target, event.originalEvent.key, counter_phone2);
            });
        }

        function checkCPF() {
            let cpfInput = document.getElementById('cpf');
            if (cpfInput) {
                let cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');
                if (cpf.length === 11) {
                    let isValid = cpfIsValid(cpf);

                    if (isValid) {
                        cpfInput.classList.remove("is-invalid")
                        cpfInput.classList.add("is-valid")
                    } else {
                        cpfInput.classList.add("is-invalid")
                    }
                } else {
                    cpfInput.classList.remove("is-invalid")
                    cpfInput.classList.remove("is-valid")
                }
            }

            let cpfSearchInput = document.getElementById('cpf_search');
            if (cpfSearchInput) {
                let cpfSearch = cpfSearchInput.value.replaceAll('.', '').replaceAll('-', '');
                if (cpfSearch.length === 11) {
                    let isValid = cpfIsValid(cpfSearch);

                    if (isValid) {
                        cpfSearchInput.classList.remove("is-invalid")
                        cpfSearchInput.classList.add("is-valid")
                    } else {
                        cpfSearchInput.classList.add("is-invalid")
                    }
                } else {
                    cpfSearchInput.classList.remove("is-invalid")
                    cpfSearchInput.classList.remove("is-valid")
                }
            }
        }

        // =======================================
        // FUNÇÕES DE EMAIL
        // =======================================
        function confirmEmployeeEmail(formId) {
            const email = document.getElementById('email').value;
            const name = document.getElementById('name').value;
            const form = document.getElementById(formId);

            if (form.reportValidity() && email) {
                const formData = new FormData();
                formData.append('email', email);
                formData.append('name', name);

                $('#loading').modal('show');

                $.ajax({
                    url: "<?= route('send.email.employee.confirm') ?>",
                    type: 'post',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    beforeSend: function (xhr) {
                        debugLog("enviando email de confirmação");
                    },
                    success: function (response) {
                        const code = response.code;
                        const modal = document.getElementById('modal-confirm-email');
                        modal.dataset.code = code;
                        modal.dataset.formId = formId;
                        modal.dataset.email = email;
                        modal.dataset.name = name;

                        $('#loading').modal('hide');
                        $("#modal-confirm-email").modal('toggle');
                    },
                    error: function (response, textStatus, msg) {
                        $('#loading').modal('hide');
                        alert('Falha ao enviar email de confirmação!');
                    }
                });
            }
        }

        function validateEmailAddress(emailInput, errorElement) {
            let email = emailInput.val();
            const currentEmployeeCpf = "{{ $employee['cpf'] ?? old('cpf') ?? ($cpfSearch ?? '') }}".replace(/\D/g, '');

            emailInput.removeClass('is-invalid');
            errorElement.text('');

            if (email.length === 0) return;

            if (!/\S+@\S+\.\S+/.test(email)) {
                emailInput.addClass('is-invalid');
                errorElement.text('Por favor, insira um endereço de e-mail válido.');
                return;
            }

            $.ajax({
                url: `<?= env('API_URL') ?>/api/user/check-email/${email}`,
                type: 'GET',
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Authorization', 'Bearer <?= session('API_TOKEN') ?>');
                },
                success: function (response) {
                    if (response && response.id && response.cpf != currentEmployeeCpf && response.cpf_status === 'completo') {
                        emailInput.addClass('is-invalid');
                        errorElement.text('Este email já está associado a outro usuário.');
                    } else {
                        emailInput.removeClass('is-invalid');
                        errorElement.text('');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    if (jqXHR.status === 404) {
                        emailInput.removeClass('is-invalid');
                        errorElement.text('');
                    } else {
                        debugLog("Erro ao validar email:", textStatus, errorThrown);
                        emailInput.removeClass('is-invalid');
                        errorElement.text('');
                    }
                },
            });
        }

        // =======================================
        // VALIDAÇÃO DE TELEFONE OBRIGATÓRIA
        // =======================================
        function validatePhoneBeforeSubmit() {
            const phoneValidated = $('#phone_validated').val();
            
            // Verifica se o telefone foi validado
            if (phoneValidated !== '1') {
                showPhoneValidationError('É obrigatório validar o telefone antes de prosseguir. Clique no botão "Validar por WhatsApp".');
                return false;
            }
            
            return true;
        }
        
        function showPhoneValidationError(message) {
            // Remove mensagens de erro anteriores
            $('#phone_number_1').siblings('.phone-validation-error').remove();
            
            // Adiciona nova mensagem de erro
            const errorElement = $('<div class="alert alert-danger phone-validation-error mt-2">' + message + '</div>');
            $('#phone_number_1').closest('.form-group').append(errorElement);
            
            // Destaca o campo de telefone
            $('#phone_number_1').addClass('is-invalid');
            
            // Scroll para o campo de telefone
            $('html, body').animate({
                scrollTop: $('#phone_number_1').offset().top - 100
            }, 500);
            
            // Remove a mensagem após 10 segundos
            setTimeout(function() {
                errorElement.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 10000);
        }

        // =======================================
        // INICIALIZAÇÃO
        // =======================================
        $(document).ready(function() {
            applyMasks();

            // Email validation debounce
            let debounceTimerEmail;
            $('#email').on('input', function() {
                clearTimeout(debounceTimerEmail);
                const emailInput = $(this);
                const errorElement = $('#error-email');
                let email = emailInput.val();

                if (email.length > 0 && /\S+@\S+\.\S+/.test(email)) {
                     debounceTimerEmail = setTimeout(() => {
                        validateEmailAddress(emailInput, errorElement);
                     }, 500);
                } else {
                    emailInput.removeClass('is-invalid');
                    errorElement.text('');
                }
            });
            
            // Listener para sucesso da validação do componente de telefone
            $(document).on('phoneValidation:success', function(event, data) {
                debugLog('[Employee Form] Telefone validado com sucesso pelo componente:', data);
            });
        });

        // Intervalo para verificação do CPF
        setInterval(checkCPF, 1000);
    </script>
@endpush
