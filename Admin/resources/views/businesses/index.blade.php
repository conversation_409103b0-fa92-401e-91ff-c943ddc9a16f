@php use App\Helpers\ApiUser; @endphp
@extends('layouts.app', ['activePage' => 'businesses'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">

            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Negócios</h5>
                            </div>

                            <div class="col-lg-9 col-md-6 col-sm-6 col-2 mb-2 d-flex justify-content-end">
                                <div class="d-flex">
                                    <form action="{{route('businesses.create.step.one')}}" method="get">
                                        <input type="hidden" name="create_new" value="true">
                                        <button type="submit" class="btn btn-success mr-1">
                                            <img class="card-img-left example-card-img-responsive"
                                                 src="{{url('icon/icon_plus.png')}}" width="15px"
                                                 style="margin-top: -2px;"/>
                                            <label class="styleBotaoAdicionar">Adicionar negócio</label>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{route('businesses.index')}}" method="get" id="form_search_businesses">
                                    @csrf
                                    <div class="input-group " id="search_form_desktop">
                                        <input type="text" class="form-control mr-2" name="search"
                                               aria-describedby="search" style="flex: 3;"
                                               placeholder="Nome, CNPJ ou Plano"
                                               value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        <div class="input-group-append mr-2">
                                            <button class="btn btn-light" type="submit" id="btnSearch">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <select class="form-control mr-2" name="filter_disabled"
                                                style="flex: 2;"
                                                aria-describedby="filter_disabled" id="filter_disabled_desktop">
                                            <option value="all"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'all') selected @endif>
                                                Todos os Negócios
                                            </option>
                                            <option value="active"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'active') selected @endif>
                                                Negócios Ativos
                                            </option>
                                            <option value="disabled"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'disabled') selected @endif>
                                                Negócios Desativados
                                            </option>
                                        </select>

                                        @if(ApiUser::hasUserFunction('sellers'))
                                            <select class="form-control mr-2" name="seller_id"
                                                    style="flex: 2;"
                                                    id="seller_filter_desktop">
                                                <option value="" @if(!isset($seller_id) || $seller_id == '') selected @endif>Todos os vendedores</option>
                                                <option value="no_seller" @if(isset($seller_id) && $seller_id == 'no_seller') selected @endif>Negócios sem Vendedor</option>
                                                @foreach ($sellers as $seller)
                                                    <option value="{{ $seller['id'] }}" @if(isset($seller_id) && $seller_id == $seller['id']) selected @endif>{{ $seller['name'] }}</option>
                                                @endforeach
                                            </select>
                                        @endif
                                        <select class="form-control mr-2" name="status"
                                                style="flex: 3;"
                                                aria-describedby="status_filter" id="status_filter_desktop">
                                            <option value="all"
                                                    @if(isset($status) && $status == 'all') selected @endif>
                                                Todos os Status
                                            </option>
                                            <option value="pending_approval"
                                                    @if(isset($status) && $status == 'pending_approval') selected @endif>
                                                Pendente Aprovação
                                            </option>
                                            <option value="approved_registration"
                                                    @if(isset($status) && $status == 'approved_registration') selected @endif>
                                                Cadastro Aprovado
                                            </option>
                                            <option value="rejected_registration"
                                                    @if(isset($status) && $status == 'rejected_registration') selected @endif>
                                                Cadastro Rejeitado
                                            </option>
                                            <option value="expired_registration"
                                                @if(isset($status) && $status == 'expired_registration') selected @endif>
                                                Cadastro Expirado
                                            </option>
                                            <option value="pending_cancellation"
                                                    @if(isset($status) && $status == 'pending_cancellation') selected @endif>
                                                Cancelamento Pendente
                                            </option>
                                            <option value="completed_cancellation"
                                                    @if(isset($status) && $status == 'completed_cancellation') selected @endif>
                                                Cancelamento Concluído
                                            </option>
                                            <option value="reversed_cancellation"
                                                    @if(isset($status) && $status == 'reversed_cancellation') selected @endif>
                                                Cancelamento Revertido
                                            </option>
                                        </select>
                                    </div>
                                    <div class="d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="search"
                                                   aria-describedby="search" placeholder="Nome, CNPJ ou Plano"
                                                   value="{{ isset($search) ? $search : '' }}" id="search_mobile">
                                        </div>
                                        <div class="input-group mb-2">
                                            <button class="btn btn-light btn-block" type="submit">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <select class="form-control mb-2" name="filter_disabled"
                                                aria-describedby="filter_disabled" id="filter_disabled_mobile">
                                            <option value="all"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'all') selected @endif>
                                                Todos os Negócios
                                            </option>
                                            <option value="active"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'active') selected @endif>
                                                Negócios Ativos
                                            </option>
                                            <option value="disabled"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'disabled') selected @endif>
                                                Negócios Desativados
                                            </option>
                                        </select>

                                        @if(ApiUser::hasUserFunction('sellers'))
                                            <select class="form-control mb-2" name="seller_id"
                                                    id="seller_filter_mobile">
                                                <option value="" @if(!isset($seller_id) || $seller_id == '') selected @endif>Todos os vendedores</option>
                                                <option value="no_seller" @if(isset($seller_id) && $seller_id == 'no_seller') selected @endif>Negócios sem Vendedor</option>
                                                @foreach ($sellers as $seller)
                                                    <option value="{{ $seller['id'] }}" @if(isset($seller_id) && $seller_id == $seller['id']) selected @endif>{{ $seller['name'] }}</option>
                                                @endforeach
                                            </select>
                                        @endif
                                        <select class="form-control mb-2" name="status"
                                                aria-describedby="status_filter" id="status_filter_mobile">
                                            <option value="all"
                                                    @if(isset($status) && $status == 'all') selected @endif>
                                                Todos os Status
                                            </option>
                                            <option value="pending_approval"
                                                    @if(isset($status) && $status == 'pending_approval') selected @endif>
                                                Pendente Aprovação
                                            </option>
                                            <option value="approved_registration"
                                                    @if(isset($status) && $status == 'approved_registration') selected @endif>
                                                Cadastro Aprovado
                                            </option>
                                            <option value="rejected_registration"
                                                    @if(isset($status) && $status == 'rejected_registration') selected @endif>
                                                Cadastro Rejeitado
                                            </option>
                                            <option value="expired_registration"
                                                    @if(isset($status) && $status == 'expired_registration') selected @endif>
                                                Cadastro Expirado
                                            </option>
                                            <option value="pending_cancellation"
                                                    @if(isset($status) && $status == 'pending_cancellation') selected @endif>
                                                Cancelamento Pendente
                                            </option>
                                            <option value="completed_cancellation"
                                                    @if(isset($status) && $status == 'completed_cancellation') selected @endif>
                                                Cancelamento Concluído
                                            </option>
                                            <option value="reversed_cancellation"
                                                    @if(isset($status) && $status == 'reversed_cancellation') selected @endif>
                                                Cancelamento Revertido
                                            </option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="businessList-table">
                                <thead>
                                <tr>
                                    <th scope="col" style="text-align: center">Ações</th>
                                    <th scope="col" style="width:20%">Nome</th>
                                    <th scope="col" style="width:20%">CNPJ</th>
                                    <th scope="col" style="width:8%">Ativo</th>
                                    <th scope="col" style="width:14%">Status</th>
                                    <th scope="col" style="width:14%; text-align: center">Data Criação</th>
                                    <th scope="col" style="width:18%">Plano</th>
                                    @if(ApiUser::hasUserFunction('sellers'))
                                        <th scope="col" style="width:14%">Vendedor</th>
                                    @endif
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($businesses as $business)
                                    <tr>
                                        <td style="text-align: center">
                                            <div class="btn-group">
                                                @if($business['deleted_at'] == null)
                                                    <a href="{{ route('businesses.show', $business['id']) }}"
                                                    class="btn btn-primary mr-1">
                                                        <img class="card-img-left example-card-img-responsive"
                                                            alt="Editar"
                                                            src="{{url('icon/icon_editar.png')}}" width="18px"/>
                                                    </a>
                                                    @include('businesses.modal_disable',['modalId' => 'delete-business-'.($business['id'])])
                                                    <div>
                                                        <button class="btn btn-danger mr-1" data-toggle="modal"
                                                                data-target="#delete-business-{{ $business['id'] }}">
                                                            <img class="card-img-left example-card-img-responsive"
                                                                 src="{{url('icon/icon_lixeira.png')}}" width="18px"/>
                                                        </button>
                                                    </div>
                                                    @if(ApiUser::get()['login_type'] === 'admin' && $business['status'] === 'pending_approval')
                                                        @include('businesses.modal_approve',['modalId' => 'approve-business-'.($business['id'])])
                                                        <div>
                                                            <button class="btn btn-primary"
                                                                    data-toggle="modal"
                                                                    data-target="#approve-business-{{ $business['id'] }}">
                                                                <span class="fa fa-bullhorn"></span>
                                                            </button>
                                                        </div>
                                                    @endif
                                                @endif
                                                @if($business['deleted_at'] != null)
                                                    @include('businesses.modal_reactivate',['modalId' => 'reactivate-business-'.($business['id'])])
                                                    <div>
                                                        <button class="btn btn-primary"
                                                                data-toggle="modal"
                                                                data-target="#reactivate-business-{{ $business['id'] }}">
                                                            <span class="fa fa-trash-can-arrow-up"></span>
                                                        </button>
                                                    </div>

                                                    @if(ApiUser::get()['login_type'] === 'admin' && $business['status'] === 'pending_cancellation')
                                                        @include('businesses.modal_cancel',['modalId' => 'cancel-business-'.($business['id'])])
                                                        <div>
                                                            <button class="btn btn-danger ml-1"
                                                                    data-toggle="modal"
                                                                    data-target="#cancel-business-{{ $business['id'] }}">
                                                                <span class="fa fa-ban"></span>
                                                            </button>
                                                        </div>
                                                    @endif
                                                @endif
                                            </div>
                                        </td>
                                        <td style="max-width: 10rem; text-overflow: ellipsis; overflow: hidden;"
                                            title="{{ $business['name'] }}">
                                            <div class="btn-business">
                                                <div>{{ Str::limit($business['name'], 25, '...') }}</div>
                                            </div>
                                        </td>
                                        <td style="max-width: 10rem; text-overflow: ellipsis; overflow: hidden; @if($business['cnpj']== null) text-align: center @endif"
                                            title="{{ $business['cnpj'] ?? '-' }}">
                                            <div class="btn-business">
                                                <div class="cnpjBusiness">{{ $business['cnpj'] ?? '-' }}</div>
                                            </div>
                                        </td>
                                        <td style="max-width: 4rem; text-overflow: ellipsis; overflow: hidden;">
                                            <div class="btn-business">
                                                <div>{{ $business['deleted_at'] == null ? 'Sim' : 'Não' }}</div>
                                            </div>
                                        </td>
                                        @php
                                            $statusBusiness = $business['status'];
                                            $bgClass = '';

                                            switch ($statusBusiness) {
                                                case 'pending_approval':
                                                    $statusBusiness = 'Pendente Aprovação';
                                                    $bgClass = 'bg-warning';
                                                    break;
                                                case 'approved_registration':
                                                    $statusBusiness = 'Cadastro Aprovado';
                                                    $bgClass = 'bg-success';
                                                    break;
                                                case 'rejected_registration':
                                                    $statusBusiness = 'Cadastro Rejeitado';
                                                    $bgClass = 'bg-secondary';
                                                    break;
                                                case 'pending_cancellation':
                                                    $statusBusiness = 'Cancelamento Pendente';
                                                    $bgClass = 'bg-warning';
                                                    break;
                                                case 'completed_cancellation':
                                                    $statusBusiness = 'Cancelamento Concluído';
                                                    $bgClass = 'bg-success';
                                                    break;
                                                case 'reversed_cancellation':
                                                    $statusBusiness = 'Cancelamento Revertido';
                                                    $bgClass = 'bg-secondary';
                                                    break;
                                                case 'expired_registration':
                                                    $statusBusiness = 'Cadastro Expirado';
                                                    $bgClass = 'bg-secondary';
                                                    break;
                                                default:
                                                    $statusBusiness = 'Status não identificado';
                                                    $bgClass = 'bg-secondary';
                                                    break;
                                            }
                                        @endphp
                                        <td style="max-width: 15rem; min-width: 10rem; text-overflow: ellipsis; overflow: hidden;"
                                            title="{{$statusBusiness}}">
                                            <span class="badge {{ $bgClass }} p-2 text-light">{{ $statusBusiness }}</span>
                                        </td>
                                        <td style="max-width: 12rem; text-overflow: ellipsis; overflow: hidden;">
                                            <div class="btn-business" style="text-align: center">
                                                <div>{{ date('d/m/Y', strtotime($business['created_at'])) }}</div>
                                                <div>{{ date('H:i', strtotime($business['created_at'])) }}</div>
                                            </div>
                                        </td>
                                        <td style="max-width: 10rem; text-overflow: ellipsis; overflow: hidden;"
                                            title="{{ $business['plan_name'] }}">
                                            <div class="btn-business">
                                                <a href="javascript:void(0)" onclick="openModalShow('modal-view-plan', {{ $business['plan_id'] }})" class="text-primary" style="text-decoration: none;">
                                                    <div>{{ Str::limit($business['plan_name'], 20, '...') }}</div>
                                                </a>
                                            </div>
                                        </td>
                                        @if(ApiUser::hasUserFunction('sellers'))
                                            <td style="max-width: 10rem; text-overflow: ellipsis; overflow: hidden;" title="{{ isset($business['seller']) && $business['seller'] !== null ? $business['seller']['name'] : '-' }}">
                                                <div class="btn-business">
                                                    <div>{{ isset($business['seller']) && $business['seller'] !== null ? Str::limit($business['seller']['name'], 16, '...') : '-' }}</div>
                                                </div>
                                            </td>
                                        @endif
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        {{ $businesses->onEachSide(0)->links() }}
                        <div class="text-right mt-3">
                            <strong>Valor Total dos Negócios: </strong>R$ {{ number_format($total_value, 2, ',', '') ?? '0,00' }}
                        </div>
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('businesses.subtitle')
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>

    @include('plans.modal_show', ['modalId' => "modal-view-plan"])
    @include('components.loading', ['message' => 'Carregando...'])

    <script>
        $(document).ready(applyMasks());

        function addLoading(containerId) {
            $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
        }

        function openModalShow(modalId, planId) {
            addLoading('plan_show_modal_body')
            $(`#${modalId}`).modal('show');
            $.ajax({
                url: `businesses/show_plan/${planId}`,
                type: 'get',
                data: {},
                success: function(response) {
                    $('#plan_show_modal_body').html(response);
                },
                error: function(response, textStatus, msg) {
                    $('#plan_show_modal_body').html(msg);
                }
            });
        }

        function applyMasks() {
            var CNPJs = document.getElementsByClassName("cnpjBusiness");
            for (let i = 0; i < CNPJs.length; i++) {
                if (CNPJs[i].innerText.length > 0)
                    CNPJs[i].innerText = CNPJs[i].innerText.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1\.$2\.$3/$4-$5");
            };
        }

        let filter_disabled_desktop = document.getElementById('filter_disabled_desktop');
        let filter_disabled_mobile = document.getElementById('filter_disabled_mobile');

        filter_disabled_desktop.addEventListener('change', function () {
            filter_disabled_mobile.value = filter_disabled_desktop.value;
            $('#form_search_businesses').submit();
        });

        filter_disabled_mobile.addEventListener('change', function () {
            filter_disabled_desktop.value = filter_disabled_mobile.value;
            $('#form_search_businesses').submit();
        });

        status_filter_desktop.addEventListener('change', function () {
            status_filter_mobile.value = status_filter_desktop.value;
            $('#form_search_businesses').submit();
        });

        status_filter_mobile.addEventListener('change', function () {
            status_filter_desktop.value = status_filter_mobile.value;
            $('#form_search_businesses').submit();
        });

        @if(ApiUser::hasUserFunction('sellers'))
            let seller_filter_desktop = document.getElementById('seller_filter_desktop');
            let seller_filter_mobile = document.getElementById('seller_filter_mobile');

            seller_filter_desktop.addEventListener('change', function () {
                seller_filter_mobile.value = seller_filter_desktop.value;
                $('#form_search_businesses').submit();
            });

            seller_filter_mobile.addEventListener('change', function () {
                seller_filter_desktop.value = seller_filter_mobile.value;
                $('#form_search_businesses').submit();
            });
        @endif
        let search_desktop = document.getElementById('search_desktop');
        let search_mobile = document.getElementById('search_mobile');

        search_desktop.addEventListener('keyup', function () {
            search_mobile.value = search_desktop.value;
        });

        search_mobile.addEventListener('keyup', function () {
            search_desktop.value = search_mobile.value;
        });

        window.addEventListener('resize', resizeSearchForm);

        function resizeSearchForm() {
            if (window.innerWidth <= 768) {
                $('#search_form_desktop').addClass('d-none');
                $('#search_form_desktop').find('input, select').each(function () {
                    $(this).prop('disabled', true);
                });

                $('#search_form_mobile').removeClass('d-none');
                $('#search_form_mobile').find('input, select').each(function () {
                    $(this).prop('disabled', false);
                });

            } else {
                $('#search_form_desktop').removeClass('d-none');
                $('#search_form_desktop').find('input, select').each(function () {
                    $(this).prop('disabled', false);
                });

                $('#search_form_mobile').addClass('d-none');
                $('#search_form_mobile').find('input, select').each(function () {
                    $(this).prop('disabled', true);
                });
            }
        }

        resizeSearchForm();
    </script>
@endsection
