@php use App\Helpers\StringMask; @endphp
@extends('layouts.app', ['activePage' => 'businesses'])

@section('content')
    <div class="container"> 
      <div class="card card_conteudo">
        <div class="card-header" style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-12 mb-2 row align-items-center flex-nowrap">
                @if(isset($fromSeller) && $fromSeller)
                    <a href="{{route('businesses.seller.index')}}" class="mx-2" style="cursor: pointer">
                        <i class="fas fa-chevron-left fa-fw "></i>
                    </a>
                @endif   
                    <h5 class="card-title mb-0 mr-2 overflow-hidden flex-grow-1"
                        style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                          Detalhes do Negócio
                    </h5>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col">
                <div class="card-body">
                    @php
                        $canEdit = (($business['plan']['authorizedFunctions']['manage_business_profile'] ?? false) && ApiUser::hasUserFunction('manage_business_profile')) || ApiUser::hasUserFunction('update_business');
                        $fieldAttributes = $canEdit ? '' : 'disabled';
                        $fieldClass = $canEdit ? 'form-control' : 'form-control style_campo_estatico';
                        $requiredIndicator = $canEdit ? '<b class="text-danger">*</b>' : '';
                    @endphp
                    
                    <input type="file" class="form-control-file d-none" id="logo"
                           accept="image/png, image/jpeg, image/jpg, image/gif"
                           onchange="validateFileSize(this)" {{ $fieldAttributes }}>
                    <form action="{{ route('businesses.update', $business['id']) }}" id="form-update-business" method="POST">
                        @csrf
                        <div class="form-row">
                            <div class="col-md-4" style="margin-top: 10px">
                                @if($business['logo'])
                                    <div>
                                        <img src="{{$business['logo']}}" alt="Logo"
                                            class="img-fluid rounded mx-auto d-block" style="max-height: 300px; cursor: {{ $canEdit ? 'pointer' : 'default' }}"
                                            id="logo-preview" onclick="{{ $canEdit ? "document.getElementById('logo').click()" : '' }}">
                                    </div>
                                @else
                                    <img src="{{asset('images/no_logo.png')}}" alt="Logo"
                                         id="logo-preview"
                                         class="img-fluid rounded mx-auto d-block" style="max-height: 200px; cursor: {{ $canEdit ? 'pointer' : 'default' }}"
                                         onclick="{{ $canEdit ? "document.getElementById('logo').click()" : '' }}">
                                @endif
                                <button
                                    style="position: absolute; right: 15px; top: 10px; display: {{ ($canEdit && $business['logo']) ? 'block' : 'none' }};"
                                    type="button"
                                    class="btn btn-danger rounded"
                                    id="removeLogo_button"
                                    onclick="removeImage()">
                                    <i class="fas fa-times fa-fw text-white"></i>
                                </button>
                                <input type="hidden" name="logo_remove" id="business_logo_remove">
                            </div>

                            <div class="col-md-8">
                                @if($canEdit)
                                <div class="w-100 text-right">
                                    <span style="color:red">* Campos obrigatórios</span>
                                </div>
                                @endif
                                <div class="col-md-12">
                                    <label for="">Nome fantasia {!! $requiredIndicator !!}</label>
                                    <input type="text" class="{{ $fieldClass }}" name="name"
                                           value="{{ $business['name'] }}" {{ $fieldAttributes }}>
                                </div>
                                <div class="col-md-12" style="margin-top: 10px">
                                    <label for="">Descrição (1000 caracteres)</label>
                                    <textarea class="{{ $fieldClass }}" rows="7" name="description" maxlength="1000"
                                              {{ $fieldAttributes }}>{{ $business['description'] }}</textarea>
                                </div>
                                <div class="col-md-12" style="margin-top: 10px; margin-bottom: 10px;">
                                    <label id="label_cnpj" for="cnpj">CNPJ do negócio @if($business['cnpj']) {!! $requiredIndicator !!} @endif</label>
                                    <input type="text" class="{{ $fieldClass }}"
                                           name="cnpj" id="cnpj"
                                           value="{{ old('cnpj', $business['cnpj']) }}" onchange="checkCNPJ()" @if($business['cnpj'] == null) disabled @endif
                                            {{ $fieldAttributes }}>
                                    <div class="form-check mt-2">
                                        <input type="checkbox" class="form-check-input" id="cnpj_optional" name="cnpj_optional" value="1" onchange="toggleCnpjOptional()" {{ $fieldAttributes }} {{ !$business['cnpj'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="cnpj_optional">
                                            Não tenho CNPJ ou não quero informar
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-12" style="margin-top: 10px; margin-bottom: 10px;">
                                    <label for="corporate_reason">Razão social {!! $requiredIndicator !!}</label>
                                    <input type="text" class="{{ $fieldClass }}"
                                           name="corporate_reason" id="corporate_reason"
                                           value="{{ old('corporate_reason', $business['corporate_reason']) }}"
                                           min="0" required {{ $fieldAttributes }}>
                                </div>
                            </div>

                            <div class="d-flex col-md-12 my-3" style="flex-wrap: wrap">
                                <div class="col-md-12"
                                     style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                    Administrador</div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">Nome {!! $requiredIndicator !!}</label>
                                        <input type="text" class="form-control style_campo_estatico" name="admin_name"
                                               id="name" value="{{ $business['admin']['name'] }}" required disabled>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cpf">CPF {!! $requiredIndicator !!}</label>
                                        <input type="text" class="form-control style_campo_estatico" name="admin_cpf"
                                               id="cpf" value="{{ StringMask::cpf($business['admin']['cpf'])}}"
                                               required disabled>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone_number_1">Telefone</label>
                                        <input type="text" class="form-control style_campo_estatico" name="admin_phone_number_1"
                                              id="phone_number_1" value="{{ StringMask::phone($business['admin']['phone_number_1']) }}"
                                              disabled>
                                    </div>
                                </div>
                                @if($canEdit && ApiUser::hasUserFunction('update_business'))
                                <div class="col-md-12" id="edit-admin">
                                    <button type="button" class="btn btn-primary" data-toggle="modal"
                                            onclick="window.location.href = '{{ route('businesses.editAdmin', $business['id'])}}'"
                                            style="width: 100%">
                                        Alterar Administrador
                                    </button>
                                </div>
                                @endif
                            </div>
                            <div class="d-flex col-md-12 my-3" style="flex-wrap: wrap">
                                <div class="col-md-12"
                                     style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                    Endereço</div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cep">CEP {!! $requiredIndicator !!}</label>
                                        <input type="text" class="{{ $fieldClass }}" name="cep"
                                               id="cep" value="{{ old('cep') ?? ($business['cep'] ?? old('cep')) }}"
                                               required {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="neighborhood">Bairro {!! $requiredIndicator !!}</label>
                                        <input type="text" class="{{ $fieldClass }}" name="neighborhood"
                                               id="neighborhood" value="{{ old('neighborhood', $business['neighborhood']) }}"
                                               required {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="street">Rua {!! $requiredIndicator !!}</label>
                                        <input type="text" class="{{ $fieldClass }}" name="street"
                                               id="street" value="{{ old('street', $business['street']) }}" required {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="number">Número {!! $requiredIndicator !!}</label>
                                        <input type="text" class="{{ $fieldClass }}" name="number"
                                               id="number" value="{{ old('number', $business['number']) }}" required {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="complement">Complemento</label>
                                        <input type="text" class="{{ $fieldClass }}" name="complement"
                                               id="complement" value="{{ old('complement', $business['complement']) }}"
                                               {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="city">Cidade {!! $requiredIndicator !!}</label>
                                        <input type="text" class="form-control" name="city"
                                               id="city" value="{{ old('city', $business['city']) }}" required readonly>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="state">Estado {!! $requiredIndicator !!}</label>
                                        <input type="text" class="form-control" name="state"
                                               id="state" value="{{ old('state', $business['state']) }}" required readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex col-md-12 my-3" style="flex-wrap: wrap">
                                <div class="col-md-12"
                                     style="font-size:20px; font-family:Arial, Helvetica, sans-serif; color:#2494c7; margin-bottom:5px">
                                    Redes sociais
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="url_facebook">
                                            <i class="fab fa-facebook fa-fw"></i>
                                            Link do Facebook
                                        </label>
                                        <input type="text" class="{{ $fieldClass }}" name="url_facebook"
                                               id="url_facebook" 
                                               value="{{ old('url_facebook') ?? ($business['url_facebook'] ?? old('url_facebook')) }}"
                                               placeholder="https://www.facebook.com/"
                                               {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="url_google">
                                            <i class="fab fa-google fa-fw"></i>
                                            Link do Google <span class="text-danger">(Passo a passo no menu de suporte)</span>
                                        </label>
                                        <input type="text" class="{{ $fieldClass }}" name="url_google"
                                               id="url_google" 
                                               value="{{ old('url_google') ?? ($business['url_google'] ?? old('url_google')) }}"
                                               placeholder="https://www.google.com/"
                                               {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="url_instagram">
                                            <i class="fab fa-instagram fa-fw"></i>
                                            Link do Instagram
                                        </label>
                                        <input type="text" class="{{ $fieldClass }}" name="url_instagram"
                                               id="url_instagram" 
                                               value="{{ old('url_instagram') ?? ($business['url_instagram'] ?? old('url_instagram')) }}"
                                               placeholder="https://www.instagram.com/"
                                               {{ $fieldAttributes }}>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="url_linkedin">
                                            <i class="fab fa-linkedin fa-fw"></i>
                                            Link do Linkedin
                                        </label>
                                        <input type="text" class="{{ $fieldClass }}" name="url_linkedin"
                                               id="url_linkedin" 
                                               value="{{ old('url_linkedin') ?? ($business['url_linkedin'] ?? old('url_linkedin')) }}"
                                               placeholder="https://www.linkedin.com/"
                                               {{ $fieldAttributes }}>
                                    </div>
                                </div>
                            </div>
                            @if(ApiUser::hasUserFunction('update_business'))
                                <div class="form-row col-md-12 align-items-end">
                                    <hr width="100%">
                                    <div class="col-12 d-flex justify-content-between align-items-center"
                                         style="margin-top: 9px; margin-bottom:6px">
                                        <h5 class="card-title mb-0"
                                            style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                            Administrativo</h5>
                                    </div>
                                </div>

                                <div class="form-row col-md-12 align-items-end">
                                    @if($canEdit)
                                    <div class="col-md-4" style="margin-top: 10px;margin-bottom: 10px">
                                        <label for="plan_id">Plano {!! $requiredIndicator !!}</label>
                                        <select class="form-control" id="planSelect" name="plan_id">
                                            @foreach($plans as $plan)
                                                <option
                                                    value="{{ $plan['id'] }}" {{ $business['plan']['id'] == $plan['id'] ? 'selected' : '' }}>
                                                    {{ $plan['name'] }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @else
                                    <div class="col-md-4" style="margin-top: 10px;margin-bottom: 10px">
                                        <label for="">Plano</label>
                                        <input type="text" class="form-control style_campo_estatico" name="plan"
                                               id="plan" value="{{ $business['plan']['name'] }}" disabled>
                                    </div>
                                    @endif

                                    @if($business['seller'] != null && !$canEdit)
                                        <div class="col-md-4" style="margin-top: 10px;margin-bottom: 10px">
                                            <label for="">Vendedor</label>
                                            <input type="text" class="form-control style_campo_estatico" name="seller"
                                                   id="seller" value="{{ $business['seller']['name'] }}" disabled>
                                        </div>
                                    @endif

                                    @if($sellers != null && $canEdit)
                                        <div class="col-md-4" style="margin-top: 10px;margin-bottom: 10px">
                                            <label for="seller_id">Vendedor</label>
                                            <select class="form-control" id="sellerSelect" name="seller_id">
                                                <option value=''></option>
                                                @foreach($sellers as $seller)
                                                    <option
                                                        value="{{ $seller['id'] }}" {{ $business['seller'] != null && $business['seller']['id'] == $seller['id'] ? 'selected' : '' }}>
                                                        {{ $seller['name'] }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    @endif
                                </div>
                            @elseif(ApiUser::hasPermission('manage_business_profile'))
                                @if ($business['seller'] != null)
                                    <input type="hidden" name="seller_id" value="{{ $business['seller']['id'] }}" readonly>
                                @endif
                                <input type="hidden" name="plan_id" value="{{ $business['plan']['id'] }}" readonly>
                            @endif
                        </div>
                        @if($canEdit)
                            <div class="modal-footer">
                                <button id="submit-button" type="submit" class="btn btn-success">Atualizar Negócio</button>
                            </div>
                        @endif
                    </form>

                    @if(ApiUser::isAdminOfBusiness($business))
                        <hr>
                        <div class="mt-4 text-center">
                            <p>
                                Para cancelar o seu negócio, entre em contato com o nosso suporte no número 
                                <a href="https://wa.me/5548992229532" target="_blank">(48) 99222-9532</a>
                            </p>
                        </div>

                        {{-- Modal de confirmação de cancelamento --
                        <div class="modal fade" id="cancelBusinessModal" tabindex="-1" role="dialog"
                             aria-labelledby="cancelBusinessModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered" role="document">
                                <div class="modal-content" style="border-radius: 12px;">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="cancelBusinessModalLabel">Confirmar cancelamento</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <p>
                                            Tem certeza de que deseja cancelar o cadastro do negócio
                                            <strong>{{ $business['name'] }}</strong>?
                                        </p>
                                        <p>
                                            O pagamento recorrente será cancelado. Será cobrado apenas o pagamento pendente do último mês corrente.
                                            Após a confirmação, o <strong>{{ $business['name'] }}</strong> sairá da lista dos seus negócios e não será mais possível
                                            selecioná-lo.
                                        </p>
                                        @if(count(ApiUser::get()['businesses']) == 1)
                                            <p class="text-danger">
                                                <strong>Atenção:</strong> Você será deslogado do sistema após o cancelamento, pois não terá mais negócios cadastrados.
                                            </p>
                                        @endif
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                            Fechar
                                        </button>
                                        <form action="{{ route('business.cancel', $business['id']) }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-danger">
                                                Confirmar cancelamento
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div> --}}
                    @endif

                </div>

            </div>
        </div>
      </div>
    </div>
@endsection

@push('js')
    <script type="text/javascript"
    src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.0/jquery.mask.js"></script>
    <script type="text/javascript" src="{{ asset('js/validateCnpj.js') }}"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            // Apply masks immediately on page load
            applyMasks();
            
            // Set up CEP search when focus is lost
            setupCepSearch();
            
            // Set up form submission controls
            setupFormSubmission();
            
            // Set up warning for unsaved changes
            setupUnsavedChangesWarning();
        });
        
        function applyMasks() {
            $("#cnpj").mask('00.000.000/0000-00');
            $("#cep").mask('00000-000');
        }
        
        function setupCepSearch() {
            $("#cep").focusout(function() {
                if ($(this).val().length == 9) {
                    $.ajax({
                        url: 'https://viacep.com.br/ws/' + $(this).val() + '/json/',
                        dataType: 'json',
                        success: function (response) {
                            $("#complement").val(response.complemento)
                            $("#street").val(response.logradouro)
                            $("#neighborhood").val(response.bairro)
                            $("#city").val(response.localidade)
                            $("#state").val(response.uf)
                            $("#number").focus()
                        }
                    })
                } else {
                    $("#city").val('')
                    $("#state").val('')
                }
            });
        }
        
        function toggleCnpjOptional() {
            const isOptional = document.getElementById('cnpj_optional').checked;
            const cnpjInput = document.getElementById('cnpj');
            
            if (isOptional) {
                cnpjInput.value = '';
                cnpjInput.setAttribute('disabled', 'disabled');
                //required
                cnpjInput.removeAttribute('required');
                cnpjInput.classList.remove("is-invalid");
                cnpjInput.classList.remove("is-valid");
                //remove *
                document.getElementById('label_cnpj').innerHTML = 'CNPJ do negócio';
            } else {
                cnpjInput.removeAttribute('disabled');
                cnpjInput.setAttribute('required', 'required');
                checkCNPJ();
                //add *
                document.getElementById('label_cnpj').innerHTML = 'CNPJ do negócio <b class="text-danger">*</b>';
            }
        }

        @if($business['cnpj'] == null)
            toggleCnpjOptional();
        @endif

        function checkCNPJ() {
            const isOptional = document.getElementById('cnpj_optional').checked;
            const cnpjInput = document.getElementById('cnpj');
            const submitButton = document.getElementById('submit-button');
            let isValid = false;
            let cnpj = cnpjInput.value.replaceAll('.', '').replaceAll('-', '').replaceAll('/', '');
            
            // Se CNPJ é opcional e está marcado, considera válido
            if (isOptional) {
                cnpjInput.classList.remove("is-invalid");
                cnpjInput.classList.remove("is-valid");
                submitButton.disabled = false;
                return true;
            }
            
            if (cnpj.length === 14) {
                isValid = cnpjIsValid(cnpj);
                if (isValid) {
                    cnpjInput.classList.remove("is-invalid");
                    cnpjInput.classList.add("is-valid");
                    submitButton.disabled = false;
                } else {
                    cnpjInput.classList.add("is-invalid");
                    submitButton.disabled = true;
                }
            } else {
                cnpjInput.classList.remove("is-invalid");
                cnpjInput.classList.remove("is-valid");
                submitButton.disabled = false;
            }
            return isValid;
        }

        function removeImage() {
            const inputFile = document.getElementById('logo');
            const inputFlagRemoveImg = document.getElementById('business_logo_remove');

            inputFile.value = '';
            inputFile.files = null;
            inputFlagRemoveImg.value = true;
            loadLogo(inputFile, true);
        }

        function validateFileSize(inputFile) {
            let files = inputFile.files;
            let maxSizeInBytes = 5 * 1024 * 1024; // 5MB
            let isValid = true;

            for (let i = 0; i < files.length; i++) {
                if (files[i].size > maxSizeInBytes) {
                    isValid = false;
                    break;
                }
            }

            if (!isValid) {
                inputFile.value = '';
                inputFile.files = null;
                alert('O tamanho máximo permitido para a imagem é de 5MB.');
            } else {
                loadLogo(inputFile);
            }
        }

        function loadLogo(inputFile, isRemoving = false) {
            if (isRemoving) {
                const logoPath = "{{asset('images/no_logo.png')}}"
                $('#logo-preview').attr('src', logoPath);
                document.getElementById('removeLogo_button').style.display = 'none';
                return;
            }
            let reader = new FileReader();
            reader.onload = function (e) {
                $('#logo-preview').attr('src', e.target.result);
            };
            reader.readAsDataURL(inputFile.files[0]);
            document.getElementById('removeLogo_button').style.display = 'block';
        }
        
        function setupFormSubmission() {
            let botaoSalvarClicado = false;
            const botaoSalvar = $('#submit-button');
            
            botaoSalvar.click(function () {
                botaoSalvarClicado = true;
            });
            
            const formulario = document.getElementById('form-update-business');
            formulario.addEventListener('submit', function (event) {
                event.preventDefault();
                
                // Verificar se existe administrador
                const adminName = document.querySelector('input[name="admin_name"]').value;
                if (!adminName) {
                    alert('É necessário adicionar um administrador para o negócio antes de salvar');
                    return;
                }

                @if($canEdit)
                if (!checkCNPJ()) {
                    alert('O CNPJ digitado é inválido!')
                    return;
                }
                @endif
                submitUpdateBusiness(event);
            });
        }
        
        function setupUnsavedChangesWarning() {
            const formulario = document.getElementById('form-update-business');
            const campos = formulario.querySelectorAll('input, textarea, select');
            let botaoSalvarClicado = false;
            
            const valoresIniciais = Array.from(campos).reduce((obj, campo) => {
                obj[campo.name] = campo.value;
                return obj;
            }, {});

            function mudou() {
                for (let i = 0; i < campos.length; i++) {
                    const campo = campos[i];
                    if (campo.value !== valoresIniciais[campo.name]) {
                        return true;
                    }
                }
                return false;
            }

            function exibirAlertaSaida(event) {
                if (mudou() && !botaoSalvarClicado) {
                    event.preventDefault();
                    let mensagem = 'Tem certeza que deseja sair desta página?';
                    event.returnValue = mensagem;
                    return mensagem;
                } else {
                    botaoSalvarClicado = false;
                }
            }

            window.addEventListener('beforeunload', exibirAlertaSaida);
            
            $('#submit-button').click(function() {
                botaoSalvarClicado = true;
            });
        }
        
        function submitUpdateBusiness() {
            const formulario = document.getElementById('form-update-business');
            
            //remove logo base64 input if exists
            const logoBase64 = document.getElementById('logo_base64');
            if (logoBase64) {
                logoBase64.remove();
            }

            //if has logo file, create input text with base64 value
            const logo = document.getElementById('logo');

            if (logo.files && logo.files.length > 0) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const base64 = e.target.result;
                    const input = document.createElement('input');
                    input.id = 'logo_base64';
                    input.type = 'hidden';
                    input.name = 'logo';
                    input.value = base64;
                    formulario.appendChild(input);

                    //input with name and type file
                    const input2 = document.createElement('input');
                    input2.id = 'logo_file';
                    input2.type = 'hidden';
                    input2.name = 'logo_file';
                    input2.value = logo.files[0].name;
                    formulario.appendChild(input2);

                    formulario.submit();
                };
                reader.readAsDataURL(logo.files[0]);
            } else {
                formulario.submit();
            }
        }
    </script>
@endpush
