<?php
    $layout = \App\Helpers\ApiUser::get() != null ? 'layouts.app' : 'layouts.guest';

    // Verifica se existe um sellerCpf passado via query string
    $sellerCpfParam = request()->query('sellerCpf', null);
?>

@extends($layout)

@section('content')
    @if(isset($error))
        <div class="container mt-4">
            <div class="alert alert-danger" role="alert">
                {{ $error }}
            </div>
        </div>
    @else
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-12" style="margin-bottom: 2rem;">
                    <div class="card card_conteudo">
                        <div class="card-header">
                            <h4 class="mb-0">Cadastro do Negócio</h4>
                        </div>
                        <div class="card-body">
                            {{-- Campo CPF para busca --}}
                            <div class="mb-4" id="cpf-search-container">
                                <h5>CPF do Administrador</h5>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="cpf_search"
                                           placeholder="Digite o CPF do Administrador" maxlength="14">
                                </div>
                            </div>

                            {{-- Formulário progressivo (inicialmente escondido) --}}
                            <form
                                action="{{ route('businesses.publicRegister.submit', ['planTag'=> $plan['payment_system_tag']]) }}"
                                id="form-administrator" method="POST" style="display: none;">
                                @csrf
                                <input type="hidden" name="isCreateNewEmployeeAndUser" id="isCreateNewEmployeeAndUser"
                                       value="0">
                                <input type="hidden" name="isEditEmployee" id="isEditEmployee" value="0">
                                <input type="hidden" name="isCreateNewEmployee" id="isCreateNewEmployee" value="0">
                                <input type="hidden" name="cpf" id="cpf" value="">
                                <input type="hidden" name="old_email" id="old_email" value="">
                                <input type="hidden" name="business_deleted" id="business_deleted" value="0">
                                <input type="hidden" name="employee_id" id="employee_id" value="">
                                <input type="hidden" name="email_validated" id="email_validated" value="0">
                                <input type="hidden" name="seller_id" value="{{ $seller['id'] ?? '' }}">
                                <input type="hidden" name="plan_id" value="{{ $plan['id'] }}">

                                {{-- Dados do Administrador --}}
                                <fieldset class="mb-4">
                                    <div><p class="form-title">Dados do Administrador</p></div>
                                    <hr class="col-md-12">
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="name">Nome do Administrador <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="name" id="name"
                                                   placeholder="Digite o nome do administrador" required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="email">E-mail do Administrador <span
                                                    class="text-danger">*</span></label>
                                            <input type="email" class="form-control" name="email" id="email"
                                                   placeholder="Digite o email do administrador" required>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="cpf">CPF do Administrador <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="cpf_admin" id="cpf_admin"
                                                   placeholder="Digite o CPF do administrador" onInput="checkCPF()"
                                                   required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            @include('components.inputs.phone_number_with_validation', [
                                                'name' => 'phone_number',
                                                'id' => 'phone_number',
                                                'label' => 'Telefone do Administrador',
                                                'class' => 'phone-input',
                                                'value' => '',
                                                'placeholder' => 'Digite o telefone do administrador',
                                                'required' => true,
                                                'currentClientCpf' => '',
                                                'validationFieldName' => 'phone_validated',
                                                'includeModal' => true
                                            ])
                                        </div>
                                    </div>
                                </fieldset>

                                {{-- Dados do Negócio --}}
                                <fieldset class="mb-2" style="margin-bottom: 0px">
                                    <div><p class="form-title">Dados do Negócio</p></div>
                                    <hr class="col-md-12">
                                    {{-- Campo CNPJ para busca --}}
                                    <div class="mb-2" id="cnpj-container">
                                        <h5>CNPJ do Negócio <span class="text-danger cnpj-required">*</span></h5>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="cnpj_search"
                                                   placeholder="Digite o CNPJ do Negócio" maxlength="18"
                                                   onInput="checkCNPJ()">
                                        </div>
                                    </div>
                                    {{-- Checkbox para CNPJ opcional --}}
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="is_cnpj_optional" name="is_cnpj_optional" value="1" onchange="toggleCnpjRequired()">
                                            <label class="form-check-label" for="is_cnpj_optional">
                                                Não tenho CNPJ ou não quero informar
                                            </label>
                                        </div>
                                    </div>
                                    <input type="hidden" name="cnpj" id="cnpj" value="">
                                    {{-- Formulário para Informações do Negócio (inicialmente escondido) --}}
                                    <div id="business-info-container" style="display: none;">
                                        <div class="form-row">
                                            <div class="form-group col-md-6">
                                                <label for="business_name">Nome do Negócio <span
                                                        class="text-danger">*</span></label>
                                                <input type="text" class="form-control" name="business_name"
                                                       id="business_name" placeholder="Digite o nome do negócio"
                                                       required>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group col-md-6">
                                                <label for="plan_name">Plano</label>
                                                <input type="text" class="form-control" name="plan_name" id="plan_name"
                                                       value="{{ $plan['name']}}" disabled>
                                            </div>
                                            @if($sellerCpfParam)
                                                <div class="form-group col-md-6">
                                                    <label for="seller_name">Vendedor</label>
                                                    <input type="text" class="form-control" name="seller_name"
                                                           id="seller_name"
                                                           value="{{ $seller['name'] ?? 'Vendedor não identificado' }}"
                                                           disabled>
                                                </div>
                                            @endif
                                        </div>

                                        <div style="margin-left: 20px; margin-top: 0px" class="mb-4 justify-content-start">
                                            <input type="checkbox" class="form-check-input" id="term-checkbox" name="term"
                                                required>
                                            <label class="form-check-label" for="term">Declaro aqui o meu consentimento sobre os
                                                dados passados de acordo com o termo em anexo <a
                                                    href="{{route('data-processing-agreement')}}"
                                                    target="_blank">(clique aqui para ler o termo).</a></label>
                                        </div>

                                        <button type="button" class="btn btn-success btn-block" id="btn-next" disabled
                                                onclick="validateEmailAdm()">Cadastrar
                                        </button>
                                    </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('components.loading', ['message' => ''])
        @include('components.modal.modal_confirm_email', ['modalId' => 'emailValidationModal'])
    @endif
@endsection
@push('js')
    <!-- Validação de CPF -->
    <script type="text/javascript" src="{{ asset('js/validateCpf.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/validateCnpj.js') }}"></script>
    <script>
        // =======================================
        // VARIÁVEIS GLOBAIS
        // =======================================
        
        // CPF do cliente atual - disponível globalmente para o modal WhatsApp
        window.currentClientCpf = '';

        // =======================================
        // FUNÇÕES DE VALIDAÇÃO CNPJ/CPF
        // =======================================
        
        // Função para alternar a obrigatoriedade do CNPJ
        function toggleCnpjRequired() {
            const isCnpjOptional = document.getElementById('is_cnpj_optional').checked;
            const cnpjContainer = document.getElementById('cnpj-container');
            const cnpjSearch = document.getElementById('cnpj_search');
            const businessInfoContainer = document.getElementById('business-info-container');

            if (isCnpjOptional) {
                cnpjSearch.removeAttribute('required');
                cnpjSearch.value = '';
                cnpjSearch.setAttribute('disabled', 'disabled');
                $('#cnpj').val('');
                //remove class is-invalid e is-valid
                cnpjSearch.classList.remove("is-invalid");
                cnpjSearch.classList.remove("is-valid");
                //remove *
                $('.cnpj-required').hide();
                // Mostra os campos quando CNPJ é opcional
                businessInfoContainer.style.display = 'block';
                $('#business_name').val('').prop('disabled', false);
            } else {
                cnpjSearch.setAttribute('required', 'required');
                cnpjSearch.removeAttribute('disabled');
                $('.cnpj-required').show();
                // Esconde os campos quando CNPJ não é opcional
                businessInfoContainer.style.display = 'none';
            }
            checkFormValidity();
        }

        function checkCNPJ() {
            let cnpjInput = document.getElementById('cnpj_search');
            let isValid = false;
            let cnpj = cnpjInput.value.replaceAll('.', '').replaceAll('-', '').replaceAll('/', '');
            
            // Se CNPJ é opcional e está vazio, considera válido
            if (document.getElementById('is_cnpj_optional').checked && !cnpj) {
                cnpjInput.classList.remove("is-invalid");
                cnpjInput.classList.remove("is-valid");
                return true;
            }

            if (cnpj.length === 14) {
                isValid = cnpjIsValid(cnpj);
                if (isValid) {
                    cnpjInput.classList.remove("is-invalid")
                    cnpjInput.classList.add("is-valid")
                } else {
                    cnpjInput.classList.add("is-invalid")
                }
            } else {
                cnpjInput.classList.remove("is-invalid")
                cnpjInput.classList.remove("is-valid")
            }
            return isValid;
        }

        function checkCPF() {
            let cpfInput = document.getElementById('cpf_admin');
            let isValid = false;
            let cpf = cpfInput.value.replaceAll('.', '').replaceAll('-', '');
            if (cpf.length === 11) {
                isValid = cpfIsValid(cpf);
                if (isValid) {
                    cpfInput.classList.remove("is-invalid")
                    cpfInput.classList.add("is-valid")
                } else {
                    cpfInput.classList.add("is-invalid")
                }
            } else {
                cpfInput.classList.remove("is-invalid")
                cpfInput.classList.remove("is-valid")
            }
            window.currentClientCpf = cpf;
            return isValid;
        }

        // =======================================
        // FUNÇÕES DE BUSCA E PREENCHIMENTO
        // =======================================

        function buscarFuncionario(cpf) {
            $.ajax({
                url: "{{ env('API_URL') }}/api/user/show/by-cpf",
                type: 'GET',
                data: {cpf: cpf},
                headers: {
                    'Authorization': 'Bearer {{ session('API_TOKEN') }}'
                },
                beforeSend: function () {
                    $('#loading').modal('show');
                },
                success: function (response) {
                    $('#cpf-search-container').hide();
                    exibirFormulario();
                    preencherCamposAdministrador(response, cpf);
                    $('#cnpj-container').show();
                    $('#loading').modal('hide');
                    checkCPF();
                },
                error: function (xhr) {
                    $('#loading').modal('hide');
                    if (xhr.status === 404) {
                        $('#cpf-search-container').hide();
                        exibirFormulario();
                        limparCamposAdministrador(cpf);
                        $('#cnpj-container').show();
                    } else {
                        alert('Falha ao buscar o funcionário');
                    }
                    checkCPF();
                }
            });
        }

        function buscarNegocio(cnpj) {
            $.ajax({
                url: "{{ env('API_URL') }}/api/businesses/validate/cnpj_in_use",
                type: 'GET',
                data: {cnpj: cnpj},
                headers: {
                    'Authorization': 'Bearer {{ session('API_TOKEN') }}'
                },
                beforeSend: function () {
                    $('#loading').modal('show');
                },
                success: function (response) {
                    $('#loading').modal('hide');
                    const {business, exist, is_deleted} = response;

                    if (exist && !is_deleted) {
                        $('#cnpj_search').addClass("is-invalid");
                        $('#business_name').val('').prop('disabled', true);
                        $('#business_deleted').val('0');
                        $('#btn-next').prop('disabled', true);
                        $('#business-info-container').hide();
                        alert('O CNPJ já está em uso!');
                    } else if (exist && is_deleted) {
                        $('#business_deleted').val('1');
                        $('#business_name').val(business.name).prop('disabled', false);
                        $('#business-info-container').show();
                        $('#btn-next').show();
                        checkFormValidity();
                    } else {
                        $('#business_deleted').val('0');
                        $('#business_name').val('').prop('disabled', false);
                        $('#business-info-container').show();
                        $('#btn-next').show();
                        checkFormValidity();
                    }
                },
                error: function (xhr) {
                    $('#loading').modal('hide');
                    alert('Falha ao validar o CNPJ');
                }
            });
        }

        function preencherCamposAdministrador(response, cpf) {
            const hasEmployee = response.employee != null;
            
            if (hasEmployee) {
                $('#isEditEmployee').val('1');
                $('#isCreateNewEmployee').val('0');
                $('#isCreateNewEmployeeAndUser').val('0');
                $('#employee_id').val(response.employee.id);
            } else {
                $('#isCreateNewEmployee').val('1');
                $('#isEditEmployee').val('0');
                $('#isCreateNewEmployeeAndUser').val('0');
                $('#employee_id').val('');
            }

            // Preenche campos com os dados do usuário
            $('#name').val(response.user.name).prop('disabled', false);
            $('#email').val(response.user.email).prop('disabled', false);
            $('#old_email').val(response.user.email);
            $('#cpf').val(cpf);
            $('#cpf_admin').val(formatCpf(cpf));
            
            // Preenche telefone
            let phone_number = response.user.phone_number_1;
            if (phone_number) {
                let phone_number_mask = formatPhoneNumber(phone_number);
                $('#phone_number').val(phone_number_mask);
                $('#phone_validated').val('1');
                
                // Atualiza a variável global do componente de validação
                if (typeof window['validatedPhoneNumber_phone_number'] !== 'undefined') {
                    window['validatedPhoneNumber_phone_number'] = phone_number.replace(/\D/g, '');
                }
            }
            
            checkFormValidity();
        }

        function limparCamposAdministrador(cpf) {
            $('#isCreateNewEmployeeAndUser').val('1');
            $('#isEditEmployee').val('0');
            $('#isCreateNewEmployee').val('0');
            $('#name').val('').prop('disabled', false);
            $('#email').val('').prop('disabled', false);
            $('#phone_number').val('');
            $('#old_email').val('');
            $('#cpf').val(cpf);
            $('#cpf_admin').val(formatCpf(cpf));
            $('#employee_id').val('');
            $('#phone_validated').val('0');
        }

        function exibirFormulario() {
            $('#form-administrator').show();
        }

        // =======================================
        // FUNÇÕES DE VALIDAÇÃO E CONFIRMAÇÃO
        // =======================================

        function validateEmailAdm() {
            const form = document.getElementById('form-administrator');
            if (!form.checkValidity()) {
                return;
            }
            
            // Verifica se o telefone foi validado
            const isPhoneValidated = $('#phone_validated').val() === '1';
            if (!isPhoneValidated) {
                alert('É necessário validar o telefone pelo WhatsApp antes de continuar.');
                $('#phone_number').focus();
                return;
            }
            
            const email = $('#email').val();
            const name = $('#name').val();
            oldEmail = $('#old_email').val();
            const isCreateNewEmployeeAndUser = $('#isCreateNewEmployeeAndUser').val() === '1';
            const isEditEmployee = $('#isEditEmployee').val() === '1';
            const emailChanged = (email !== oldEmail);

            if (isCreateNewEmployeeAndUser || (isEditEmployee && emailChanged)) {
                $.ajax({
                    url: "{{ env('API_URL') }}/api/businesses/validate/email_in_use",
                    type: 'GET',
                    data: {email: email},
                    beforeSend: function () {
                        $('#loading').modal('show');
                    },
                    success: function (response) {
                        $('#loading').modal('hide');
                        if (response.exist) {
                            alert('Este email já está em uso.');
                            return;
                        } else {
                            sendEmailConfirmation(email, name, oldEmail);
                        }
                    },
                    error: function (xhr, status, error) {
                        $('#loading').modal('hide');
                        alert('Erro ao validar email.');
                    }
                });
            } else {
                sendEmailConfirmation(email, name, oldEmail);
            }
        }

        function sendEmailConfirmation(email, name, oldEmail) {
            const emailChanged = (email !== oldEmail);
            const form = document.getElementById('form-administrator');

            if (emailChanged) {
                const formData = new FormData();
                formData.append('email', email);
                formData.append('name', name);

                $('#loading').modal('show');

                $.ajax({
                    url: "{{route('send.email.employee.confirm')}}",
                    type: 'post',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (response) {
                        const code = response.code;
                        const modal = document.getElementById('emailValidationModal');
                        modal.dataset.code = code;
                        modal.dataset.formId = 'form-administrator';
                        modal.dataset.email = email;
                        modal.dataset.name = name;

                        $('#loading').modal('hide');
                        $("#emailValidationModal").modal('show');
                    },
                    error: function (response, textStatus, msg) {
                        $('#loading').modal('hide');
                        alert('Falha ao enviar email de confirmação!');
                    }
                });
            } else {
                if (form.checkValidity()) {
                    form.submit();
                }
            }
        }

        // =======================================
        // FUNÇÕES DE FORMATAÇÃO
        // =======================================

        function formatCpf(cpf) {
            cpf = cpf.replace(/\D/g, '');
            return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        }

        function formatPhoneNumber(value) {
            if (!value) return '';
            let cleaned = value.replace(/\D/g, '');
            let part1 = cleaned.slice(0, 2);
            let part2 = '';
            let part3 = '';
            if (cleaned.length > 2) {
                if (cleaned.length > 10) {
                    part2 = cleaned.slice(2, 7);
                    part3 = cleaned.slice(7, 11);
                } else {
                    part2 = cleaned.slice(2, 6);
                    part3 = cleaned.slice(6, 10);
                }
            }
            let formatted = part1 ? '(' + part1 : '';
            formatted += part1.length === 2 ? ') ' : '';
            formatted += part2;
            formatted += part3 ? '-' + part3 : '';
            return formatted;
        }

        // =======================================
        // VERIFICAÇÃO DE VALIDADE DO FORMULÁRIO
        // =======================================

        function checkFormValidity() {
            const form = document.getElementById('form-administrator');
            if (!form) return;

            const isCnpjOptionalElement = document.getElementById('is_cnpj_optional');
            const isCnpjOptional = isCnpjOptionalElement ? isCnpjOptionalElement.checked : false;
            const termCheckbox = $("#term-checkbox");
            const isPhoneValidated = $('#phone_validated').val() === '1';

            let isValid = form.checkValidity() && checkCPF() && 
                         (termCheckbox.length ? termCheckbox.is(":checked") : true) && 
                         isPhoneValidated;

            if (!isCnpjOptional) {
                isValid = isValid && checkCNPJ();
            }
            
            $('#btn-next').prop('disabled', !isValid);
        }

        // =======================================
        // INICIALIZAÇÃO E EVENT LISTENERS
        // =======================================

        $(document).ready(function () {
            const form = document.getElementById('form-administrator');

            // Aplicar máscaras
            function applyMasks() {
                $("#cpf_search").mask('000.000.000-00');
                $("#cnpj_search").mask('00.000.000/0000-00');
                $("#cpf_admin").mask('000.000.000-00');
            }
            applyMasks();

            // Event listeners
            $('#cpf_search').on('input', function () {
                let cpf = $(this).val().replace(/\D/g, '');
                if (cpf.length === 11) {
                    if (cpfIsValid(cpf)) {
                        buscarFuncionario(cpf);
                    } else {
                        this.classList.add("is-invalid");
                        setTimeout(() => alert('CPF inválido!'), 100);
                    }
                } else {
                    this.classList.remove("is-invalid");
                }
            });

            $('#cnpj_search').on('input', function () {
                let cnpj = $(this).val().replace(/\D/g, '');
                $('#business-info-container').hide();
                
                if (cnpj.length === 14) {
                    if (cnpjIsValid(cnpj)) {
                        $('#cnpj').val(cnpj);
                        $(this).removeClass('is-invalid').addClass('is-valid');
                        buscarNegocio(cnpj);
                    } else {
                        $(this).removeClass('is-valid').addClass('is-invalid');
                        alert('CNPJ inválido!');
                    }
                } else {
                    $(this).removeClass('is-valid is-invalid');
                }
            });

            $('#cpf_admin').on('input', function () {
                let cpf = $(this).val().replace(/\D/g, '');
                if (cpf.length === 11 && cpfIsValid(cpf)) {
                    buscarFuncionario(cpf);
                }
            });

            // Check form validity on input change
            $('input, select').on('input change', function () {
                checkFormValidity();
            });

            // Listener para mudanças no checkbox de termos
            $(document).on('change', '#term-checkbox', checkFormValidity);

            // Listener para sucesso da validação do telefone
            $(document).on('phoneValidation:success', function(event, data) {
                checkFormValidity();
            });

            checkFormValidity();

            if (form) {
                form.addEventListener('submit', function () {
                    $('#emailValidationModal').modal('hide');
                    $('#loading').modal('show');
                });
            }
        });
    </script>
@endpush
