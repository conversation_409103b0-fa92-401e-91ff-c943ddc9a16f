@php use App\Helpers\ApiUser; @endphp
@extends('layouts.app', ['activePage' => 'businesses'])

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card card_conteudo">
                    <div class="card-header"
                         style="border-top-left-radius: 12px; border-top-right-radius: 12px; background-color: #fff">
                        <div class="row row-cols-lg-2 row-cols-md-1 row-cols-sm-1">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-10 mb-2">
                                <h5 class="card-title mb-0 mr-2"
                                    style="font-size:25px; font-family:Arial, Helvetica, sans-serif; color:#0842A0">
                                    Negócios</h5>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12" id="searchForm">
                                <form action="{{route('businesses.seller.index')}}" method="get" id="form_search_businesses">
                                    @csrf
                                    <div class="input-group" id="search_form_desktop">
                                        <input type="text" class="form-control mr-2" name="search"
                                               aria-describedby="search" style="flex: 3;"
                                               placeholder="Nome, CNPJ ou Plano"
                                               value="{{ isset($search) ? $search : '' }}" id="search_desktop">
                                        <div class="input-group-append mr-2">
                                            <button class="btn btn-light" type="submit" id="btnSearch">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <select class="form-control mr-2" name="filter_disabled"
                                                style="flex: 2;"
                                                aria-describedby="filter_disabled" id="filter_disabled_desktop">
                                            <option value="all"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'all') selected @endif>
                                                Todos os Negócios
                                            </option>
                                            <option value="active"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'active') selected @endif>
                                                Negócios Ativos
                                            </option>
                                            <option value="disabled"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'disabled') selected @endif>
                                                Negócios Desativados
                                            </option>
                                        </select>
                                        @if (ApiUser::get()['registered_by'] == null)
                                        <select class="form-control mr-2" name="seller_id" style="flex: 2;" id="seller_filter_desktop">
                                            <option value="" @if(!isset($seller_id) || $seller_id == '') selected @endif>Todos os vendedores</option>
                                            @foreach ($child_sellers as $seller)
                                                <option value="{{ $seller['id'] }}" @if(isset($seller_id) && $seller_id == $seller['id']) selected @endif>{{ $seller['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @endif
                                    </div>
                                    <div class="d-none" id="search_form_mobile">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="search"
                                                   aria-describedby="search" placeholder="Nome, CNPJ ou Plano"
                                                   value="{{ isset($search) ? $search : '' }}" id="search_mobile">
                                        </div>
                                        <div class="input-group mb-2">
                                            <button class="btn btn-light btn-block" type="submit">
                                                <img class="card-img-left example-card-img-responsive"
                                                     src="{{url('icon/icon_search.png')}}" width="20px"/>
                                            </button>
                                        </div>
                                        <select class="form-control mb-2" name="filter_disabled"
                                                aria-describedby="filter_disabled" id="filter_disabled_mobile">
                                            <option value="all"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'all') selected @endif>
                                                Todos os Negócios
                                            </option>
                                            <option value="active"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'active') selected @endif>
                                                Negócios Ativos
                                            </option>
                                            <option value="disabled"
                                                    @if(isset($filter_disabled) && $filter_disabled == 'disabled') selected @endif>
                                                Negócios Desativados
                                            </option>
                                        </select>
                                        @if (ApiUser::get()['registered_by'] == null)
                                        <select class="form-control" name="seller_id" id="seller_filter_mobile">
                                           <option value="" @if(!isset($seller_id) || $seller_id == '') selected @endif>Todos os vendedores</option>
                                           @foreach ($child_sellers as $seller)
                                               <option value="{{ $seller['id'] }}" @if(isset($seller_id) && $seller_id == $seller['id']) selected @endif>{{ $seller['name'] }}</option>
                                           @endforeach
                                       </select>
                                       @endif
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table" style="overflow: auto;">
                            <table class="table table-striped w-100"
                                   style="white-space: nowrap;" id="businessList-table">
                                <thead>
                                <tr>
                                    <th scope="col" style="width:10%; text-align: center">Ações</th>
                                    <th scope="col" style="width:40%">Nome</th>
                                    <th scope="col" style="width:4rem;">Ativo</th>
                                    <th scope="col" style="width:20%">CNPJ</th>
                                    @if (ApiUser::get()['registered_by'] == null)
                                      <th scope="col" style="width:16%">Vendedor Auxiliar</th>
                                    @endif
                                    <th scope="col" style="width:22%">Plano</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($businesses as $business)
                                    <tr>
                                        <td style="text-align: center;max-width: 10rem;">
                                            <div class="btn-group">
                                                <a href="{{ route('businesses.show', ["id"=>$business['id'], "fromSeller" => true]) }}"
                                                   class="btn btn-primary mr-1">
                                                    <img class="card-img-left example-card-img-responsive"
                                                         src="{{url('icon/icon_visualizar.svg')}}" width="18px"
                                                         alt="Visualizar"/>
                                                </a>
                                                <a href="{{ route('businesses.seller.dashboard', $business['id']) }}"
                                                   class="btn btn-secondary">
                                                    <i class="fas fa-chart-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                        <td style="max-width: 20rem; text-overflow: ellipsis; overflow: hidden;"
                                            title="{{ $business['name'] }}">
                                            <div class="btn-business">
                                                <div>{{ Str::limit($business['name'], 25, '...') }}</div>
                                            </div>
                                        </td>
                                        <td style="max-width: 4rem; text-overflow: ellipsis; overflow: hidden;">
                                            <div class="btn-business">
                                                <div>{{ $business['deleted_at'] == null ? 'Sim' : 'Não' }}</div>
                                            </div>
                                        </td>
                                        <td style="max-width: 10rem; text-overflow: ellipsis; overflow: hidden;"
                                            title="{{ $business['cnpj'] }}">
                                            <div class="btn-business">
                                                <div class="cnpjBusiness">{{ $business['cnpj']?? '-'}}</div>
                                            </div>
                                        </td>
                                        @if (ApiUser::get()['registered_by'] == null)
                                          <td style="max-width: 9rem; text-overflow: ellipsis; overflow: hidden;"
                                              title="{{ (isset($business['seller']) && $business['seller'] != null && ApiUser::get()['id'] == $business['seller']['id']) || $business['seller'] == null ? '-' :
                                                $business['seller']['name'] }}">
                                              <div class="btn-business">
                                                  <div>{{ $business['seller'] == null || (ApiUser::get()['id'] == $business['seller']['id'])  ? '-' :
                                                    Str::limit($business['seller']['name'], 16, '...') }}</div>
                                              </div>
                                          </td>
                                        @endif
                                        <td style="max-width: 11rem; text-overflow: ellipsis; overflow: hidden;"
                                            title="{{ $business['plan']['name'] }}">
                                            <div class="btn-business">
                                                <a href="javascript:void(0)" onclick="openModalShow('modal-view-plan', {{ $business['plan']['id'] }})" class="text-primary" style="text-decoration: none;">
                                                    <div>{{ Str::limit($business['plan']['name'], 20, '...') }}</div>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                        {{ $businesses->onEachSide(0)->links() }}
                        <div class="text-right mt-3">
                            <strong>Valor Total dos Negócios: </strong>R$ {{ number_format($total_value, 2, ',', '') ?? '0,00' }}
                        </div>
                    </div>
                    <div class="card-footer table-legend"
                         style="background-color: #fff; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;">
                        @include('businesses.seller_subtitle')
                    </div>
                </div>
            </div>
        </div>
        @if ($message = Session::get('success'))
            <div class="alert alert-success alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @elseif($message = Session::get('error'))
            <div class="alert alert-danger alert-block shadow fade show"
                 style="position: fixed; z-index: 1000; left: 1rem; bottom: 1rem;">
                <strong>{{$message}}</strong>
            </div>
        @endif
    </div>

    @include('plans.modal_show', ['modalId' => "modal-view-plan"])
    @include('components.loading', ['message' => 'Carregando...'])

    <script>
        $(document).ready(applyMasks());

        function applyMasks() {
            var CNPJs = document.getElementsByClassName("cnpjBusiness");
            for (let i = 0; i < CNPJs.length; i++) {
                if (CNPJs[i].innerText.length > 0)
                    CNPJs[i].innerText = CNPJs[i].innerText.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1\.$2\.$3/$4-$5");
            };
        }

        let filter_disabled_desktop = document.getElementById('filter_disabled_desktop');
        let filter_disabled_mobile = document.getElementById('filter_disabled_mobile');

        @if (ApiUser::get()['registered_by'] == null)
        let seller_filter_desktop = document.getElementById('seller_filter_desktop');
        let seller_filter_mobile = document.getElementById('seller_filter_mobile');
        @endif

        filter_disabled_desktop.addEventListener('change', function () {
            filter_disabled_mobile.value = filter_disabled_desktop.value;
            $('#form_search_businesses').submit();
        });
        
        filter_disabled_mobile.addEventListener('change', function () {
            filter_disabled_desktop.value = filter_disabled_mobile.value;
            $('#form_search_businesses').submit();
        });
        @if (ApiUser::get()['registered_by'] == null)
        seller_filter_desktop.addEventListener('change', function () {
            seller_filter_mobile.value = seller_filter_desktop.value;
            $('#form_search_businesses').submit();
        });
        
        seller_filter_mobile.addEventListener('change', function () {
            seller_filter_desktop.value = seller_filter_mobile.value;
            $('#form_search_businesses').submit();
        });
        @endif

        let search_desktop = document.getElementById('search_desktop');
        let search_mobile = document.getElementById('search_mobile');

        search_desktop.addEventListener('keyup', function () {
            search_mobile.value = search_desktop.value;
        });

        search_mobile.addEventListener('keyup', function () {
            search_desktop.value = search_mobile.value;
        });

        window.addEventListener('resize', resizeSearchForm);

        function resizeSearchForm() {
            if (window.innerWidth <= 575) {
                $('#search_form_desktop').addClass('d-none');
                $('#search_form_desktop').find('input').each(function () {
                    $(this).prop('disabled', true);
                });

                $('#search_form_mobile').removeClass('d-none');
                $('#search_form_mobile').find('input').each(function () {
                    $(this).prop('disabled', false);
                });
            } else {
                $('#search_form_desktop').removeClass('d-none');
                $('#search_form_desktop').find('input').each(function () {
                    $(this).prop('disabled', false);
                });

                $('#search_form_mobile').addClass('d-none');
                $('#search_form_mobile').find('input').each(function () {
                    $(this).prop('disabled', true);
                });
            }
        }

        resizeSearchForm();
    </script>
@endsection

@push('js')
<script type="text/javascript">
    function addLoading(containerId) {
        $(`#${containerId}`).html('<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
    }

    function openModalShow(modalId, planId) {
        addLoading('plan_show_modal_body')
        $(`#${modalId}`).modal('show');
        $.ajax({
            url: `seller/show_plan/${planId}`,
            type: 'get',
            data: {},
            success: function(response) {
                $('#plan_show_modal_body').html(response);
            },
            error: function(response, textStatus, msg) {
                $('#plan_show_modal_body').html(msg);
            }
        });
    }
</script>
@endpush
