## AdminLaravel - ADMIN

## Instalação

- Instalar o **laravel** (versão 10)
    - `composer global require laravel/installer`
- Instalar o [XAMPP](https://www.apachefriends.org/pt_br/index.html) ( PHP 8.1 ).
  - PHP 8.1.17: https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.1.17/xampp-windows-x64-8.1.17-0-VS16-installer.exe
- Instalar o [Composer](https://getcomposer.org/download/).

- Executar o comando `composer install`.
    - Caso ocorra o erro `'InvalidArgumentException in Compiler.php line 36: Please provide a valid cache path.'`, verifique as seguintes pastas estão criadas em **storage/framework/**: `cache`, `views`, `sessions`.
- Criar o banco de dados `servicos-prestados` do tipo `utf8_unicode_ci`.
- Popular o banco de dados `php artisan migrate --seed` ( caso o banco de dados já esteja criado, execute o comando `php artisan migrate:fresh --seed` ).
- Configurar o arquivo hosts ( C:\Windows\System32\drivers\etc\hosts ) com o seguinte conteúdo:
```
    127.0.0.1	maisservicosprestados.com
```
- Configurar o **.env** do Admin para apontar para a API:
  - `API_URL=http://maisservicosprestados.com:8080`. Coloquem a porta que a API está rodando.
- Executar o comando `php artisan serve` para iniciar o servidor.
- Quando alterar alguma rota, ou precisar limpar o cache, execute o script `clear.bat`.
## Detalhes do projeto
- Login default:
    - **E-mail**: <EMAIL>
    - **Senha**: 123456
## Iniciar o servidor
- No Admin, executar o comando `php artisan serve --port 8001` para iniciar o servidor.
- Na API, executar o comando `php artisan serve --port 8080` para iniciar o servidor.

### Dica para rodar os dois servidores simultaneamente
- Criar uma arquivo "start +Serviços Prestados.bat" com o seguinte conteúdo:
```
@echo off
cd /d X:\+SistemasTI\ServicosPrestados\Api
start php artisan serve --port 8080
cd /d X:\+SistemasTI\ServicosPrestados\Admin
start php artisan serve --port 8001
```
- Alterar o caminho do diretório para o caminho do projeto em sua máquina.
- Alterar as portas caso necessário.
