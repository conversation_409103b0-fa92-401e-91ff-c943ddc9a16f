# Plano de Integração WhatsApp

## 1. Migration

Execute o seguinte comando para criar a migration:

```bash
php artisan make:migration add_whatsapp_fields_to_business_parameters
```

Conteúdo da migration:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->string('whatsapp_instance_key')->nullable();
            $table->boolean('whatsapp_instance_connected')->default(false);
            $table->string('whatsapp_phone_number')->nullable();
            $table->string('whatsapp_api_key')->nullable();
        });
    }

    public function down()
    {
        Schema::table('business_parameters', function (Blueprint $table) {
            $table->dropColumn([
                'whatsapp_instance_key',
                'whatsapp_instance_connected',
                'whatsapp_phone_number',
                'whatsapp_api_key'
            ]);
        });
    }
};
```

## 2. Novas Funções para ApiWhatsapp Helper

```php
/**
 * Inicializa uma nova instância do WhatsApp para um negócio
 */
public static function initBusinessInstance(string $businessId): array
{
    try {
        $instanceKey = "VisaoNegocio_{$businessId}";
        $baseUrl = env('WHATSAPP_API_URL');
        
        $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
            ->get("{$baseUrl}/instance/init", [
                'key' => $instanceKey
            ]);

        return $response->json();
    } catch (Exception $e) {
        throw new Exception("Falha ao inicializar instância do WhatsApp: " . $e->getMessage());
    }
}

/**
 * Obtém o QR Code em base64 para uma instância
 */
public static function getInstanceQrCode(string $instanceKey): string
{
    try {
        $baseUrl = env('WHATSAPP_API_URL');
        
        $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
            ->get("{$baseUrl}/instance/qrbase64", [
                'key' => $instanceKey
            ]);

        $data = $response->json();
        if ($data['error']) {
            throw new Exception($data['message']);
        }

        return $data['qrcode'];
    } catch (Exception $e) {
        throw new Exception("Falha ao obter QR Code: " . $e->getMessage());
    }
}

/**
 * Verifica o status de uma instância
 */
public static function checkInstanceStatus(string $instanceKey): array
{
    try {
        $baseUrl = env('WHATSAPP_API_URL');
        
        $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
            ->get("{$baseUrl}/instance/info", [
                'key' => $instanceKey
            ]);

        return $response->json();
    } catch (Exception $e) {
        throw new Exception("Falha ao verificar status da instância: " . $e->getMessage());
    }
}

/**
 * Desconecta uma instância
 */
public static function logoutInstance(string $instanceKey): array
{
    try {
        $baseUrl = env('WHATSAPP_API_URL');
        
        $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
            ->delete("{$baseUrl}/instance/logout", [
                'key' => $instanceKey
            ]);

        return $response->json();
    } catch (Exception $e) {
        throw new Exception("Falha ao desconectar instância: " . $e->getMessage());
    }
}

/**
 * Envia mensagem usando a instância específica do negócio
 */
public static function sendMessageWithInstance(
    string $instanceKey,
    string $userPhone,
    string $title,
    string $description,
    ?string $businessName = null,
    ?string $fallBackEmail = null,
    array $options = [],
    int $attempts = 0
): array {
    try {
        $baseUrl = env('WHATSAPP_API_URL');

        if (!isset($businessName)) {
            $businessName = config('app.name');
        }

        $userPhone = self::formatPhone($userPhone, $attempts > 0);
        $message = self::buildMessage($title, $description, $businessName, $options);

        $response = Http::withToken(env('WHATSAPP_API_TOKEN'))
            ->post("{$baseUrl}/message/text", [
                'key' => $instanceKey,
                'id' => $userPhone,
                'message' => $message
            ]);

        $payload = $response->json();

        if (isset($payload['error']) && $payload['error'] === true) {
            if ($payload['message'] === 'Error: no account exists' && $attempts === 0) {
                return self::sendMessageWithInstance(
                    $instanceKey,
                    $userPhone,
                    $title,
                    $description,
                    $businessName,
                    $fallBackEmail,
                    $options,
                    $attempts + 1
                );
            } else if ($fallBackEmail) {
                $message = self::buildMessage($title, $description, $businessName, [
                    'noSignatures' => true,
                    'noHeader' => true,
                    'noTitle' => true
                ]);
                Mail::to($fallBackEmail)->send(new WhatsappFallbackMail(
                    $title,
                    $message,
                    $businessName
                ));
                return ['error' => true, 'message' => 'Fallback to email'];
            }
        }

        return $payload;
    } catch (Exception $e) {
        throw new Exception("Falha no envio da mensagem via WhatsApp: " . $e->getMessage());
    }
}
```

## 3. Próximos Passos

1. Executar a migration
2. Implementar as alterações no `BusinessParametersController`
3. Atualizar a interface em `parameters.blade.php`
4. Criar rotas para os novos endpoints
5. Testar a integração

Posso mudar para o modo Code para começar a implementação?